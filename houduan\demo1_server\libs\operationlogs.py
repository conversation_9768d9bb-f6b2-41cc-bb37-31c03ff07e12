#!/usr/bin/env python
# -*- coding: utf-8 -*-
from firewall import models
from demo1_server.apps.log.models import UserLog
from datetime import datetime


def operationlogs(object_model, model_type, user, before_update_model=None, dict_update_fields=None, msg=None):
    """
    操作日志
    :param object_model: 需要删除，修改的旧model对象实例或新增的model对象实例
    :param model_type: 类型 (自己定义)
        (0, '增加'),
        (1, '删除'),
        (2, '修改')
    :param user: 进行操作的用户
    :param before_update_model: 修改操作时，传入原model用作比较更新的字段
    :param dict_update_fields: 修改操作时更新的字段键值对--字典格式
    :param msg: 自定义日志内容 (如果日志格式非‘A由aa变为bb’，可使用此参数)
    :return:
    """
    content = ''
    model_id = object_model.id

    # 删除操作时记录日志
    if model_type == 1:
        if isinstance(object_model, models.Firewall):
            content = "删除models.Firewall：['{}'".format(model_id)
            model_keys = models.Firewall._meta.fields
        elif isinstance(object_model, models.FirewallScript):
            content = "删除models.FirewallScript：['{}'".format(model_id)
            model_keys = models.FirewallScript._meta.fields
        else:
            # 后续有新的model类更新可以写在这
            pass
        for key in model_keys[1:]:
            # print(key.name)
            value = object_model.__getattribute__(key.name)
            # print(type(value))
            content += " '{}'".format(value)
        content += ']'
    # 修改操作时记录日志
    elif model_type == 2:
        if isinstance(object_model, models.Firewall):
            model_keys = models.Firewall._meta.fields
            # print(model_keys)
        else:
            # 后续有新的model类更新可以写在这
            pass
        content = '修改models.Firewall(id={})：'.format(model_id)
        # 此处默认进行修改操作时，自增列id不修改
        for key in model_keys[1:]:
            old_value = before_update_model.__getattribute__(key.name)
            # print(old_value)
            new_value = object_model.__getattribute__(key.name)
            # print(new_value)
            if new_value == old_value:
                pass
            else:
                content += "[{}由'{}'修改为'{}']".format(key.name, old_value, new_value)
    # 增加操作时记录日志
    else:
        if isinstance(object_model, models.Firewall):
            content = "添加models.Firewall：['{}'".format(model_id)
            model_keys = models.Firewall._meta.fields
        elif isinstance(object_model, models.FirewallScript):
            content = "添加models.FirewallScript：['{}'".format(model_id)
            model_keys = models.FirewallScript._meta.fields
        else:
            # 后续有新的model类更新可以写在这
            pass
        if model_keys:
            for key in model_keys[1:]:
                # print(key.name)
                value = object_model.__getattribute__(key.name)
                # print(type(value))
                content += " '{}'".format(value)
            content += ']'

    #  存储日志
    if content == '':
        pass
    else:
        obj_operationlogs = UserLog()
        obj_operationlogs.content = content
        obj_operationlogs.type = model_type
        obj_operationlogs.user = user
        obj_operationlogs.save()


def getmodelfield(modelname, id):
    """获取model 指定字段的 verbose_name属性值"""
    fielddic = {}
    u = modelname.objects.filter(id=id)
    tmp = u.to_dict()
    # for field in modelname._meta.fields:
    #     fielddic[field.name] = field.verbose_name
    return tmp


def compare(oldstr, newstr, field, msg):
    """
    生成操作日志详细记录
    :param oldstr: 原值
    :param newstr: 新值
    :param field: 目标字段
    :return: content
    """
    content = ''

    if isinstance(newstr, list):  # 将list转为str类型，list对象存储到数据库后，为str类型
        newstr = str(newstr)

    if oldstr == newstr:  # 值未变化，不做处理
        pass
    else:
        if not msg:
            content = ('"%s"由"%s"变为"%s";' % (field, oldstr, newstr))
        else:
            content = ('%s"%s"由"%s"变为"%s";' % (msg, field, oldstr, newstr))
    return content
