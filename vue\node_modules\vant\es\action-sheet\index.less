@import '../style/var';
@import '../style/mixins/hairline';

.van-action-sheet {
  display: flex;
  flex-direction: column;
  max-height: @action-sheet-max-height;
  overflow: hidden;
  color: @action-sheet-item-text-color;

  &__content {
    flex: 1 auto;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
  }

  &__item,
  &__cancel {
    display: block;
    width: 100%;
    padding: 14px @padding-md;
    font-size: @action-sheet-item-font-size;
    background-color: @action-sheet-item-background;
    border: none;
    cursor: pointer;

    &:active {
      background-color: @active-color;
    }
  }

  &__item {
    line-height: @action-sheet-item-line-height;

    &--loading,
    &--disabled {
      color: @action-sheet-item-disabled-text-color;

      &:active {
        background-color: @action-sheet-item-background;
      }
    }

    &--disabled {
      cursor: not-allowed;
    }

    &--loading {
      cursor: default;
    }
  }

  &__cancel {
    flex-shrink: 0;
    box-sizing: border-box;
    color: @action-sheet-cancel-text-color;
  }

  &__subname {
    margin-top: @padding-xs;
    color: @action-sheet-subname-color;
    font-size: @action-sheet-subname-font-size;
    line-height: @action-sheet-subname-line-height;
  }

  &__gap {
    display: block;
    height: @action-sheet-cancel-padding-top;
    background-color: @action-sheet-cancel-padding-color;
  }

  &__header {
    flex-shrink: 0;
    font-weight: @font-weight-bold;
    font-size: @action-sheet-header-font-size;
    line-height: @action-sheet-header-height;
    text-align: center;
  }

  &__description {
    position: relative;
    flex-shrink: 0;
    padding: 20px @padding-md;
    color: @action-sheet-description-color;
    font-size: @action-sheet-description-font-size;
    line-height: @action-sheet-description-line-height;
    text-align: center;

    &::after {
      .hairline-bottom(@cell-border-color, @padding-md, @padding-md);
    }
  }

  &__loading-icon .van-loading__spinner {
    width: @action-sheet-loading-icon-size;
    height: @action-sheet-loading-icon-size;
  }

  &__close {
    position: absolute;
    top: 0;
    right: 0;
    padding: @action-sheet-close-icon-padding;
    color: @action-sheet-close-icon-color;
    font-size: @action-sheet-close-icon-size;
    line-height: inherit;

    &:active {
      color: @action-sheet-close-icon-active-color;
    }
  }
}
