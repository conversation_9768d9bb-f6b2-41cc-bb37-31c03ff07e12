"""demo1_server URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/2.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from demo1_server.apps.learning import views
from django.urls import path


urlpatterns = [
    path('getbreading_base/', views.GetBreading_base.as_view()),
    path('getequipment/', views.equipment.as_view()),
    path('select_equipment/', views.SelectEquipment.as_view()),
    path('cultiver/', views.cultiver.as_view()),
    path('caicon/', views.caicon.as_view()),
    path('getcamera/', views.GetCamera.as_view()),
    path('getfeedersensing/', views.GetFeeder_sensing.as_view()),
    path('getpeoplefeed/', views.Getpeoplefeed.as_view()),
    path('getweather/', views.WeatherView.as_view()),
]


