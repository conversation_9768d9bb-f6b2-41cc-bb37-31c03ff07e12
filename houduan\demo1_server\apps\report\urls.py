"""demo1_server URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/2.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from demo1_server.apps.report import views
from django.urls import path


urlpatterns = [
    # path('addmask/', views.AddMask.as_view()),
    # path('addreportequip/', views.AddReportEquip.as_view()),
    # path('getmask/', views.GetMask.as_view()),
    # path('getreport/', views.GetReportEquip.as_view()),
    # path('delreport/', views.DeleteReport.as_view()),
    # path('delmask/', views.DeleteMask.as_view()),
    # path('updatereport/', views.UpdateReport.as_view()),
    # path('updatemask/', views.UpdateMask.as_view()),
    # path('getmaskinfo/', views.GetMaskinfo.as_view()),
    path('getHistoryEquipmentByBasename/',views.getHistoryEquipmentByBasename.as_view()),#获取设备列表
    path('getHistoryData/',views.getHistoryData.as_view()),
    path('getBaseEquipment/',views.getBaseEquipment.as_view()),#获取基地和设备的二级关系
    path('getBaseEquipment3/',views.getBaseEquipment3.as_view()),#三级关系
    path('getReportData/',views.getReportData.as_view()),#获取设备数据
    path('AddEquipment/',views.AddEquipment.as_view()),#新增设备
    path('DeleteEquipment/',views.DeleteEquipment.as_view()),#删除设备
    path('uniqueEQUIPMENT_ID/',views.uniqueEQUIPMENT_ID.as_view()),#判断设备ID是否可用
    #以下是新增的两个表的新增、获取、编辑和删除路由
    path('Addbasic_data_fish_pond/', views.Addbasic_data_fish_pond.as_view(), ),
    path('Getbasic_data_fish_pond/', views.Getbasic_data_fish_pond.as_view(), ),
    path('Deletebasic_data_fish_pond/', views.Deletebasic_data_fish_pond.as_view(), ),
    path('Editbasic_data_fish_pond/', views.Editbasic_data_fish_pond.as_view()),
    path('AddProductionRecord/', views.AddProductionRecord.as_view()),
    path('GetProductionRecords/', views.GetProductionRecords.as_view()),
    path('DeleteProductionRecord/', views.DeleteProductionRecord.as_view()),
    path('EditProductionRecord/', views.EditProductionRecord.as_view()),

    path('TouSi/AddSettings/', views.AddSettings.as_view()),
    path('TouSi/GetSettings/', views.GetSettings.as_view()),
    path('TouSi/GetDailyData/', views.GetPlanData.as_view()),
    path('TouSi/EditDailyData/', views.EditSettings.as_view()),
    path('TouSi/DeleteSetting/', views.DeleteSettings.as_view()),
]

