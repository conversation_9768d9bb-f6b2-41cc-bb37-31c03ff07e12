@import 'mixins/mixins';
@import 'common/var';

@include b(descriptions-item) {
  
  @include e(container) {
    display: flex;
  }

  @include e(label) {
    &.has-colon {
      &::after {
        content: ':';
        position: relative;
        top: -0.5px;
      }
    }
    &.is-bordered-label {
      font-weight: bold;
      color: $--color-text-secondary;
      background: $--descriptions-item-bordered-label-background;
    }

    &:not(.is-bordered-label) {
      margin-right: 10px;
    }
  }

  @include e(content) {

  }
}
