from django.shortcuts import render, HttpResponse
from rest_framework.views import APIView
from rest_framework.response import Response
from django.contrib import auth
from django.db.models import F
from django.views.generic import View
from django.core.cache import cache
from demo1_server.libs import Argument, JsonParser, human_datetime, json_response
from .models import User
import time, uuid, json

def queryDict(sql, params=None):
    '''
    查询结果返回字典类型数据
    :param sql:
    :param params:
    :return:
    '''
    with connection.cursor() as cursor:
        cursor.execute(sql, params=params)
        col_names = [desc[0] for desc in cursor.description]
        row = cursor.fetchall()
        rowList = []
        for list in row:
            tMap = dict(zip(col_names, list))
            rowList.append(tMap)
        return rowList

from django.db import connection, transaction
class UserView(View):
    # print("22222222222222222"),
    # print(View),
    # print("22222222222222222"),
    def get(self, request):
        cursor = connection.cursor()
        page=request.GET.get("page")
        limit=request.GET.get("limit")
        sql = "SELECT * from user limit {page},{limit};".format(page=page,limit=limit);
        cursor.execute(sql)
        queryset_fw = cursor.fetchall()
        ip_fw = queryDict(sql)
        cursor = connection.cursor()
        sql = "SELECT count(1) as aa from user;";
        cursor.execute(sql)
        queryset_fw = cursor.fetchall()
        total = queryDict(sql)

        return json_response({"code":200,"total":total[0]["aa"],"data":ip_fw})

    def post(self, request):
        form, error = JsonParser(
            Argument('username', help='请输入登录名'),
            Argument('password', help='请输入密码'),
            Argument('nickname', help='请输入姓名'),
            Argument('role_id', type=int, help='请选择角色'),
        ).parse(request.body)
        if error is None:
            if User.objects.filter(username=form.username, deleted_by_id__isnull=True).exists():
                return json_response(error=f'已存在登录名为【{form.username}】的用户')
            form.password_hash = User.make_password(form.pop('password'))
            form.created_by = request.user
            User.objects.create(**form)
        return json_response(error=error)

    def patch(self, request):
        form, error = JsonParser(
            Argument('id', type=int, help='请指定操作对象'),
            Argument('username', required=False),
            Argument('password', required=False),
            Argument('nickname', required=False),
            Argument('role_id', required=False),
            Argument('is_active', type=bool, required=False),
        ).parse(request.body, True)
        if error is None:
            if form.get('password'):
                form.token_expired = 0
                form.password_hash = User.make_password(form.pop('password'))
            if 'username' in form:
                if User.objects.filter(username=form.username, deleted_by_id__isnull=True).exclude(id=form.id).exists():
                    return json_response(error=f'已存在登录名为【{form.username}】的用户')
            if 'is_active' in form:
                user = User.objects.get(pk=form.id)
                cache.delete(user.username)
            User.objects.filter(pk=form.pop('id')).update(**form)
        return json_response(error=error)

    def delete(self, request):
        form, error = JsonParser(
            Argument('id', type=int, help='请指定操作对象')
        ).parse(request.GET)
        if error is None:
            user = User.objects.filter(pk=form.id).first()
            user.role_id = None
            user.deleted_at = human_datetime()
            user.deleted_by = request.user
            user.save()
        return json_response(error=error)





def login(request):
    print("11111111111111111"),
    print(request),
    print(request.body)
    print("11111111111111111"),
    form, error = JsonParser(
        Argument('username', help='请输入用户名'),
        Argument('password', help='请输入密码'),
    ).parse(request.body)
    if error is None:
        x_real_ip = request.META.get('REMOTE_ADDR', '')
        user = User.objects.filter(username=form.username).first()
        if user and not user.is_active:
            return json_response(error="账户已被系统禁用")
        if user and user.deleted_by is None:
            if user.verify_password(form.password):
                return handle_user_info(user, x_real_ip)

        value = cache.get_or_set(form.username, 0, 86400)
        if value >= 3:
            if user and user.is_active:
                user.is_active = False
                user.save()
            return json_response(error='账户已被系统禁用')
        cache.set(form.username, value + 1, 86400)
        return json_response(error="用户名或密码错误，连续多次错误账户将会被禁用")
    return json_response(error=error)


def handle_user_info(user, x_real_ip):
    cache.delete(user.username)
    token_isvalid = user.access_token and len(user.access_token) == 32 and user.token_expired >= time.time()
    user.access_token = user.access_token if token_isvalid else uuid.uuid4().hex
    user.token_expired = time.time() + 8 * 60 * 60
    user.last_login = human_datetime()
    user.last_ip = x_real_ip
    user.save()
    return json_response({
        'token': user.access_token,
        'nickname': user.nickname,
        'is_supper': user.is_supper,
        'has_real_ip': True if x_real_ip else False,
    })


def logout(request):
    request.user.token_expired = 0
    request.user.save()
    return json_response()


def info(request):
    access_token = request.META.get('HTTP_X_TOKEN') or request.GET.get('xtoken')
    user = User.objects.filter(access_token=access_token).first()
    return json_response({
        'token': user.access_token,
        'nickname': user.nickname,
        'roles': ["admin"],
        'introduction': "物联网"
    })
