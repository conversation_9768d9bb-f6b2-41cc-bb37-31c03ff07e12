{
	"root": true,

	"extends": "@ljharb",

	"env": {
		"es6": true,
		"es2017": true,
		"es2020": true,
		"es2021": true,
	},

	"globals": {
		"AggregateError": false,
	},

	"rules": {
		"array-bracket-newline": 0,
		"array-element-newline": 0,
		"complexity": 0,
		"eqeqeq": [2, "allow-null"],
		"func-name-matching": 0,
		"id-length": 0,
		"max-lines-per-function": [2, 80],
		"max-params": [2, 4],
		"max-statements": 0,
		"max-statements-per-line": [2, { "max": 2 }],
		"multiline-comment-style": 0,
		"no-magic-numbers": 0,
		"operator-linebreak": [2, "before"],
		"sort-keys": 0,
	},

	"overrides": [
		{
			"files": "test/**",
			"rules": {
				"max-lines-per-function": 0,
				"new-cap": 0,
			},
		},
	],
}
