{"_args": [["@babel/compat-data@7.27.2", "F:\\桌面\\上海海洋大屏2期\\phone"]], "_development": true, "_from": "@babel/compat-data@7.27.2", "_id": "@babel/compat-data@7.27.2", "_inBundle": false, "_integrity": "sha512-TUtMJYRPyUb/9aU8f3K0mjmjf6M9N5Woshn2CS6nqJSeJtTtQcpLUXjGt9vbF8ZGff0El99sWkLgzwW3VXnxZQ==", "_location": "/@babel/compat-data", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@babel/compat-data@7.27.2", "name": "@babel/compat-data", "escapedName": "@babel%2fcompat-data", "scope": "@babel", "rawSpec": "7.27.2", "saveSpec": null, "fetchSpec": "7.27.2"}, "_requiredBy": ["/@babel/helper-compilation-targets", "/@babel/preset-env", "/babel-plugin-polyfill-corejs2"], "_resolved": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.27.2.tgz", "_spec": "7.27.2", "_where": "F:\\桌面\\上海海洋大屏2期\\phone", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "description": "The compat-data to determine required Babel plugins", "devDependencies": {"@mdn/browser-compat-data": "^6.0.8", "core-js-compat": "^3.41.0", "electron-to-chromium": "^1.5.140"}, "engines": {"node": ">=6.9.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js", "./overlapping-plugins": "./overlapping-plugins.js", "./plugin-bugfixes": "./plugin-bugfixes.js"}, "homepage": "https://github.com/babel/babel#readme", "keywords": ["babel", "compat-table", "compat-data"], "license": "MIT", "name": "@babel/compat-data", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-compat-data"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.mjs && node ./scripts/build-modules-support.mjs && node ./scripts/build-bugfixes-targets.mjs"}, "type": "commonjs", "version": "7.27.2"}