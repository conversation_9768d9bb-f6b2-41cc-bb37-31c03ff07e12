{"_args": [["@babel/helper-remap-async-to-generator@7.27.1", "F:\\桌面\\上海海洋大屏2期\\phone"]], "_development": true, "_from": "@babel/helper-remap-async-to-generator@7.27.1", "_id": "@babel/helper-remap-async-to-generator@7.27.1", "_inBundle": false, "_integrity": "sha512-7fiA521aVw8lSPeI4ZOD3vRFkoqkJcS+z4hFo82bFSH/2tNd6eJ5qCVMS5OzDmZh/kaHQeBaeyxK6wljcPtveA==", "_location": "/@babel/helper-remap-async-to-generator", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@babel/helper-remap-async-to-generator@7.27.1", "name": "@babel/helper-remap-async-to-generator", "escapedName": "@babel%2fhelper-remap-async-to-generator", "scope": "@babel", "rawSpec": "7.27.1", "saveSpec": null, "fetchSpec": "7.27.1"}, "_requiredBy": ["/@babel/plugin-transform-async-generator-functions", "/@babel/plugin-transform-async-to-generator"], "_resolved": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.27.1.tgz", "_spec": "7.27.1", "_where": "F:\\桌面\\上海海洋大屏2期\\phone", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "dependencies": {"@babel/helper-annotate-as-pure": "^7.27.1", "@babel/helper-wrap-function": "^7.27.1", "@babel/traverse": "^7.27.1"}, "description": "Helper function to remap async functions to generators", "devDependencies": {"@babel/core": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-helper-remap-async-to-generator", "license": "MIT", "main": "./lib/index.js", "name": "@babel/helper-remap-async-to-generator", "peerDependencies": {"@babel/core": "^7.0.0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-helper-remap-async-to-generator"}, "type": "commonjs", "version": "7.27.1"}