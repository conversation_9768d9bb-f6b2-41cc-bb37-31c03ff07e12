(function e(t,n){if(typeof exports==="object"&&typeof module==="object")module.exports=n();else if(typeof define==="function"&&define.amd)define([],n);else{var i=n();for(var r in i)(typeof exports==="object"?exports:t)[r]=i[r]}})(window,function(){return function(n){var i={};function r(e){if(i[e]){return i[e].exports}var t=i[e]={i:e,l:false,exports:{}};n[e].call(t.exports,t,t.exports,r);t.l=true;return t.exports}r.m=n;r.c=i;r.d=function(e,t,n){if(!r.o(e,t)){Object.defineProperty(e,t,{enumerable:true,get:n})}};r.r=function(e){if(typeof Symbol!=="undefined"&&Symbol.toStringTag){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"})}Object.defineProperty(e,"__esModule",{value:true})};r.t=function(t,e){if(e&1)t=r(t);if(e&8)return t;if(e&4&&typeof t==="object"&&t&&t.__esModule)return t;var n=Object.create(null);r.r(n);Object.defineProperty(n,"default",{enumerable:true,value:t});if(e&2&&typeof t!="string")for(var i in t)r.d(n,i,function(e){return t[e]}.bind(null,i));return n};r.n=function(t){var e=t&&t.__esModule?function e(){return t["default"]}:function e(){return t};r.d(e,"a",e);return e};r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)};r.p="";return r(r.s=3)}([function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:true});var i=function(){function i(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||false;i.configurable=true;if("value"in i)i.writable=true;Object.defineProperty(e,i.key,i)}}return function(e,t,n){if(t)i(e.prototype,t);if(n)i(e,n);return e}}();function r(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var a=function(){function e(){r(this,e);this._keyStr="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="}i(e,[{key:"$",value:function e(t){var n=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]*))$/;var i=/^(?:\s*(<[\w\W]+>)[^>]*|.([\w-]*))$/;if(n.test(t)){var r=n.exec(t);return document.getElementById(r[2])}else if(i.test(t)){var a=i.exec(t);var o=document.getElementsByTagName("*");var s=[];for(var l=0,u=o.length;l<u;l++){if(o[l].className.match(new RegExp("(\\s|^)"+a[2]+"(\\s|$)"))){s.push(o[l])}}return s}}},{key:"dateFormat",value:function e(t,n){var i={"M+":t.getMonth()+1,"d+":t.getDate(),"h+":t.getHours(),"m+":t.getMinutes(),"s+":t.getSeconds(),"q+":Math.floor((t.getMonth()+3)/3),S:t.getMilliseconds()};if(/(y+)/.test(n)){n=n.replace(RegExp.$1,(t.getFullYear()+"").substr(4-RegExp.$1.length))}for(var r in i){if(new RegExp("("+r+")").test(n)){n=n.replace(RegExp.$1,RegExp.$1.length===1?i[r]:("00"+i[r]).substr((""+i[r]).length))}}return n}},{key:"downloadFile",value:function e(t,n){var i=t;if(!(t instanceof Blob||t instanceof File)){i=new Blob([t])}var r=window.URL.createObjectURL(i);var a=window.document.createElement("a");a.href=r;a.download=n;var o=document.createEvent("MouseEvents");o.initEvent("click",true,true);a.dispatchEvent(o)}},{key:"createxmlDoc",value:function e(){var t;var n=["MSXML2.DOMDocument","MSXML2.DOMDocument.5.0","MSXML2.DOMDocument.4.0","MSXML2.DOMDocument.3.0","Microsoft.XmlDom"];for(var i=0,r=n.length;i<r;i++){try{t=new ActiveXObject(n[i]);break}catch(e){t=document.implementation.createDocument("","",null);break}}t.async="false";return t}},{key:"parseXmlFromStr",value:function e(t){if(null===t||""===t){return null}var n=this.createxmlDoc();if(navigator.appName==="Netscape"||navigator.appName==="Opera"){var i=new DOMParser;n=i.parseFromString(t,"text/xml")}else{n.loadXML(t)}return n}},{key:"encode",value:function e(t){var n="";var i;var r;var a;var o;var s;var l;var u;var f=0;t=this._utf8_encode(t);while(f<t.length){i=t.charCodeAt(f++);r=t.charCodeAt(f++);a=t.charCodeAt(f++);o=i>>2;s=(i&3)<<4|r>>4;l=(r&15)<<2|a>>6;u=a&63;if(isNaN(r)){l=u=64}else if(isNaN(a)){u=64}n=n+this._keyStr.charAt(o)+this._keyStr.charAt(s)+this._keyStr.charAt(l)+this._keyStr.charAt(u)}return n}},{key:"decode",value:function e(t){var n="";var i;var r;var a;var o;var s;var l;var u;var f=0;t=t.replace(/[^A-Za-z0-9+/=]/g,"");while(f<t.length){o=this._keyStr.indexOf(t.charAt(f++));s=this._keyStr.indexOf(t.charAt(f++));l=this._keyStr.indexOf(t.charAt(f++));u=this._keyStr.indexOf(t.charAt(f++));i=o<<2|s>>4;r=(s&15)<<4|l>>2;a=(l&3)<<6|u;n=n+String.fromCharCode(i);if(l!==64){n=n+String.fromCharCode(r)}if(u!==64){n=n+String.fromCharCode(a)}}n=this._utf8_decode(n);return n}},{key:"_utf8_encode",value:function e(t){t=t.replace(/\r\n/g,"\n");var n="";for(var i=0;i<t.length;i++){var r=t.charCodeAt(i);if(r<128){n+=String.fromCharCode(r)}else if(r>127&&r<2048){n+=String.fromCharCode(r>>6|192);n+=String.fromCharCode(r&63|128)}else{n+=String.fromCharCode(r>>12|224);n+=String.fromCharCode(r>>6&63|128);n+=String.fromCharCode(r&63|128)}}return n}},{key:"_utf8_decode",value:function e(t){var n="";var i=0;var r=0;var a=0;while(i<t.length){r=t.charCodeAt(i);if(r<128){n+=String.fromCharCode(r);i++}else if(r>191&&r<224){a=t.charCodeAt(i+1);n+=String.fromCharCode((r&31)<<6|a&63);i+=2}else{a=t.charCodeAt(i+1);var o=t.charCodeAt(i+2);n+=String.fromCharCode((r&15)<<12|(a&63)<<6|o&63);i+=3}}return n}},{key:"isFirefox",value:function e(){var t=false;var n=navigator.userAgent.toLowerCase();var i="";var r=-1;if(n.match(/firefox\/([\d.]+)/)){i=n.match(/firefox\/([\d.]+)/)[1];r=parseInt(i.split(".")[0],10);if(r>-1){t=true}}return t}},{key:"isSafari",value:function e(){var t=false;var n=navigator.userAgent.toLowerCase();var i="";var r=-1;if(n.match(/version\/([\d.]+).safari./)){i=n.match(/version\/([\d.]+).safari./)[1];r=parseInt(i.split(".")[0],10);if(r>-1){t=true}}return t}},{key:"isEdge",value:function e(){return navigator.userAgent.toLowerCase().indexOf("edge")>-1}},{key:"isIOS",value:function e(){var t=navigator.userAgent;var n=!!t.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/);return n}}]);return e}();var o=t.oTool=new a},function(nn,rn,e){var an,on;
/*!
 * jQuery JavaScript Library v3.6.0
 * https://jquery.com/
 *
 * Includes Sizzle.js
 * https://sizzlejs.com/
 *
 * Copyright OpenJS Foundation and other contributors
 * Released under the MIT license
 * https://jquery.org/license
 *
 * Date: 2021-03-02T17:08Z
 */
(function(e,t){"use strict";if(true&&typeof nn.exports==="object"){nn.exports=e.document?t(e,true):function(e){if(!e.document){throw new Error("jQuery requires a window with a document")}return t(e)}}else{t(e)}})(typeof window!=="undefined"?window:this,function(_,O){"use strict";var t=[];var F=Object.getPrototypeOf;var s=t.slice;var L=t.flat?function(e){return t.flat.call(e)}:function(e){return t.concat.apply([],e)};var U=t.push;var N=t.indexOf;var q={};var H=q.toString;var W=q.hasOwnProperty;var Y=W.toString;var V=Y.call(Object);var v={};var m=function e(t){return typeof t==="function"&&typeof t.nodeType!=="number"&&typeof t.item!=="function"};var y=function e(t){return t!=null&&t===t.window};var C=_.document;var j={type:true,src:true,nonce:true,noModule:true};function J(e,t,n){n=n||C;var i,r,a=n.createElement("script");a.text=e;if(t){for(i in j){r=t[i]||t.getAttribute&&t.getAttribute(i);if(r){a.setAttribute(i,r)}}}n.head.appendChild(a).parentNode.removeChild(a)}function g(e){if(e==null){return e+""}return typeof e==="object"||typeof e==="function"?q[H.call(e)]||"object":typeof e}var G="3.6.0",D=function(e,t){return new D.fn.init(e,t)};D.fn=D.prototype={jquery:G,constructor:D,length:0,toArray:function(){return s.call(this)},get:function(e){if(e==null){return s.call(this)}return e<0?this[e+this.length]:this[e]},pushStack:function(e){var t=D.merge(this.constructor(),e);t.prevObject=this;return t},each:function(e){return D.each(this,e)},map:function(n){return this.pushStack(D.map(this,function(e,t){return n.call(e,t,e)}))},slice:function(){return this.pushStack(s.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},even:function(){return this.pushStack(D.grep(this,function(e,t){return(t+1)%2}))},odd:function(){return this.pushStack(D.grep(this,function(e,t){return t%2}))},eq:function(e){var t=this.length,n=+e+(e<0?t:0);return this.pushStack(n>=0&&n<t?[this[n]]:[])},end:function(){return this.prevObject||this.constructor()},push:U,sort:t.sort,splice:t.splice};D.extend=D.fn.extend=function(){var e,t,n,i,r,a,o=arguments[0]||{},s=1,l=arguments.length,u=false;if(typeof o==="boolean"){u=o;o=arguments[s]||{};s++}if(typeof o!=="object"&&!m(o)){o={}}if(s===l){o=this;s--}for(;s<l;s++){if((e=arguments[s])!=null){for(t in e){i=e[t];if(t==="__proto__"||o===i){continue}if(u&&i&&(D.isPlainObject(i)||(r=Array.isArray(i)))){n=o[t];if(r&&!Array.isArray(n)){a=[]}else if(!r&&!D.isPlainObject(n)){a={}}else{a=n}r=false;o[t]=D.extend(u,a,i)}else if(i!==undefined){o[t]=i}}}}return o};D.extend({expando:"jQuery"+(G+Math.random()).replace(/\D/g,""),isReady:true,error:function(e){throw new Error(e)},noop:function(){},isPlainObject:function(e){var t,n;if(!e||H.call(e)!=="[object Object]"){return false}t=F(e);if(!t){return true}n=W.call(t,"constructor")&&t.constructor;return typeof n==="function"&&Y.call(n)===V},isEmptyObject:function(e){var t;for(t in e){return false}return true},globalEval:function(e,t,n){J(e,{nonce:t&&t.nonce},n)},each:function(e,t){var n,i=0;if(X(e)){n=e.length;for(;i<n;i++){if(t.call(e[i],i,e[i])===false){break}}}else{for(i in e){if(t.call(e[i],i,e[i])===false){break}}}return e},makeArray:function(e,t){var n=t||[];if(e!=null){if(X(Object(e))){D.merge(n,typeof e==="string"?[e]:e)}else{U.call(n,e)}}return n},inArray:function(e,t,n){return t==null?-1:N.call(t,e,n)},merge:function(e,t){var n=+t.length,i=0,r=e.length;for(;i<n;i++){e[r++]=t[i]}e.length=r;return e},grep:function(e,t,n){var i,r=[],a=0,o=e.length,s=!n;for(;a<o;a++){i=!t(e[a],a);if(i!==s){r.push(e[a])}}return r},map:function(e,t,n){var i,r,a=0,o=[];if(X(e)){i=e.length;for(;a<i;a++){r=t(e[a],a,n);if(r!=null){o.push(r)}}}else{for(a in e){r=t(e[a],a,n);if(r!=null){o.push(r)}}}return L(o)},guid:1,support:v});if(typeof Symbol==="function"){D.fn[Symbol.iterator]=t[Symbol.iterator]}D.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),function(e,t){q["[object "+t+"]"]=t.toLowerCase()});function X(e){var t=!!e&&"length"in e&&e.length,n=g(e);if(m(e)||y(e)){return false}return n==="array"||t===0||typeof t==="number"&&t>0&&t-1 in e}var e=
/*!
 * Sizzle CSS Selector Engine v2.3.6
 * https://sizzlejs.com/
 *
 * Copyright JS Foundation and other contributors
 * Released under the MIT license
 * https://js.foundation/
 *
 * Date: 2021-02-16
 */
function(O){var e,d,b,a,F,p,L,U,P,l,u,w,_,r,C,v,o,s,m,D="sizzle"+1*new Date,f=O.document,T=0,N=0,q=i(),H=i(),W=i(),y=i(),Y=function(e,t){if(e===t){u=true}return 0},V={}.hasOwnProperty,t=[],j=t.pop,J=t.push,M=t.push,G=t.slice,g=function(e,t){var n=0,i=e.length;for(;n<i;n++){if(e[n]===t){return n}}return-1},X="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|"+"ismap|loop|multiple|open|readonly|required|scoped",h="[\\x20\\t\\r\\n\\f]",n="(?:\\\\[\\da-fA-F]{1,6}"+h+"?|\\\\[^\\r\\n\\f]|[\\w-]|[^\0-\\x7f])+",K="\\["+h+"*("+n+")(?:"+h+"*([*^$|!~]?=)"+h+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+n+"))|)"+h+"*\\]",$=":("+n+")(?:\\(("+"('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|"+"((?:\\\\.|[^\\\\()[\\]]|"+K+")*)|"+".*"+")\\)|)",Z=new RegExp(h+"+","g"),S=new RegExp("^"+h+"+|((?:^|[^\\\\])(?:\\\\.)*)"+h+"+$","g"),Q=new RegExp("^"+h+"*,"+h+"*"),ee=new RegExp("^"+h+"*([>+~]|"+h+")"+h+"*"),te=new RegExp(h+"|>"),ne=new RegExp($),ie=new RegExp("^"+n+"$"),c={ID:new RegExp("^#("+n+")"),CLASS:new RegExp("^\\.("+n+")"),TAG:new RegExp("^("+n+"|[*])"),ATTR:new RegExp("^"+K),PSEUDO:new RegExp("^"+$),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+h+"*(even|odd|(([+-]|)(\\d*)n|)"+h+"*(?:([+-]|)"+h+"*(\\d+)|))"+h+"*\\)|)","i"),bool:new RegExp("^(?:"+X+")$","i"),needsContext:new RegExp("^"+h+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+h+"*((?:-\\d)?\\d*)"+h+"*\\)|)(?=[^-]|$)","i")},re=/HTML$/i,ae=/^(?:input|select|textarea|button)$/i,oe=/^h\d$/i,k=/^[^{]+\{\s*\[native \w/,se=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,le=/[+~]/,x=new RegExp("\\\\[\\da-fA-F]{1,6}"+h+"?|\\\\([^\\r\\n\\f])","g"),R=function(e,t){var n="0x"+e.slice(1)-65536;return t?t:n<0?String.fromCharCode(n+65536):String.fromCharCode(n>>10|55296,n&1023|56320)},ue=/([\0-\x1f\x7f]|^-?\d)|^-$|[^\0-\x1f\x7f-\uFFFF\w-]/g,fe=function(e,t){if(t){if(e==="\0"){return"�"}return e.slice(0,-1)+"\\"+e.charCodeAt(e.length-1).toString(16)+" "}return"\\"+e},he=function(){w()},ce=be(function(e){return e.disabled===true&&e.nodeName.toLowerCase()==="fieldset"},{dir:"parentNode",next:"legend"});try{M.apply(t=G.call(f.childNodes),f.childNodes);t[f.childNodes.length].nodeType}catch(e){M={apply:t.length?function(e,t){J.apply(e,G.call(t))}:function(e,t){var n=e.length,i=0;while(e[n++]=t[i++]){}e.length=n-1}}}function I(t,e,n,i){var r,a,o,s,l,u,f,h=e&&e.ownerDocument,c=e?e.nodeType:9;n=n||[];if(typeof t!=="string"||!t||c!==1&&c!==9&&c!==11){return n}if(!i){w(e);e=e||_;if(C){if(c!==11&&(l=se.exec(t))){if(r=l[1]){if(c===9){if(o=e.getElementById(r)){if(o.id===r){n.push(o);return n}}else{return n}}else{if(h&&(o=h.getElementById(r))&&m(e,o)&&o.id===r){n.push(o);return n}}}else if(l[2]){M.apply(n,e.getElementsByTagName(t));return n}else if((r=l[3])&&d.getElementsByClassName&&e.getElementsByClassName){M.apply(n,e.getElementsByClassName(r));return n}}if(d.qsa&&!y[t+" "]&&(!v||!v.test(t))&&(c!==1||e.nodeName.toLowerCase()!=="object")){f=t;h=e;if(c===1&&(te.test(t)||ee.test(t))){h=le.test(t)&&ge(e.parentNode)||e;if(h!==e||!d.scope){if(s=e.getAttribute("id")){s=s.replace(ue,fe)}else{e.setAttribute("id",s=D)}}u=p(t);a=u.length;while(a--){u[a]=(s?"#"+s:":scope")+" "+B(u[a])}f=u.join(",")}try{M.apply(n,h.querySelectorAll(f));return n}catch(e){y(t,true)}finally{if(s===D){e.removeAttribute("id")}}}}}return U(t.replace(S,"$1"),e,n,i)}function i(){var n=[];function i(e,t){if(n.push(e+" ")>b.cacheLength){delete i[n.shift()]}return i[e+" "]=t}return i}function z(e){e[D]=true;return e}function E(e){var t=_.createElement("fieldset");try{return!!e(t)}catch(e){return false}finally{if(t.parentNode){t.parentNode.removeChild(t)}t=null}}function de(e,t){var n=e.split("|"),i=n.length;while(i--){b.attrHandle[n[i]]=t}}function pe(e,t){var n=t&&e,i=n&&e.nodeType===1&&t.nodeType===1&&e.sourceIndex-t.sourceIndex;if(i){return i}if(n){while(n=n.nextSibling){if(n===t){return-1}}}return e?1:-1}function ve(n){return function(e){var t=e.nodeName.toLowerCase();return t==="input"&&e.type===n}}function me(n){return function(e){var t=e.nodeName.toLowerCase();return(t==="input"||t==="button")&&e.type===n}}function ye(t){return function(e){if("form"in e){if(e.parentNode&&e.disabled===false){if("label"in e){if("label"in e.parentNode){return e.parentNode.disabled===t}else{return e.disabled===t}}return e.isDisabled===t||e.isDisabled!==!t&&ce(e)===t}return e.disabled===t}else if("label"in e){return e.disabled===t}return false}}function A(o){return z(function(a){a=+a;return z(function(e,t){var n,i=o([],e.length,a),r=i.length;while(r--){if(e[n=i[r]]){e[n]=!(t[n]=e[n])}}})})}function ge(e){return e&&typeof e.getElementsByTagName!=="undefined"&&e}d=I.support={};F=I.isXML=function(e){var t=e&&e.namespaceURI,n=e&&(e.ownerDocument||e).documentElement;return!re.test(t||n&&n.nodeName||"HTML")};w=I.setDocument=function(e){var t,n,i=e?e.ownerDocument||e:f;if(i==_||i.nodeType!==9||!i.documentElement){return _}_=i;r=_.documentElement;C=!F(_);if(f!=_&&(n=_.defaultView)&&n.top!==n){if(n.addEventListener){n.addEventListener("unload",he,false)}else if(n.attachEvent){n.attachEvent("onunload",he)}}d.scope=E(function(e){r.appendChild(e).appendChild(_.createElement("div"));return typeof e.querySelectorAll!=="undefined"&&!e.querySelectorAll(":scope fieldset div").length});d.attributes=E(function(e){e.className="i";return!e.getAttribute("className")});d.getElementsByTagName=E(function(e){e.appendChild(_.createComment(""));return!e.getElementsByTagName("*").length});d.getElementsByClassName=k.test(_.getElementsByClassName);d.getById=E(function(e){r.appendChild(e).id=D;return!_.getElementsByName||!_.getElementsByName(D).length});if(d.getById){b.filter["ID"]=function(e){var t=e.replace(x,R);return function(e){return e.getAttribute("id")===t}};b.find["ID"]=function(e,t){if(typeof t.getElementById!=="undefined"&&C){var n=t.getElementById(e);return n?[n]:[]}}}else{b.filter["ID"]=function(e){var n=e.replace(x,R);return function(e){var t=typeof e.getAttributeNode!=="undefined"&&e.getAttributeNode("id");return t&&t.value===n}};b.find["ID"]=function(e,t){if(typeof t.getElementById!=="undefined"&&C){var n,i,r,a=t.getElementById(e);if(a){n=a.getAttributeNode("id");if(n&&n.value===e){return[a]}r=t.getElementsByName(e);i=0;while(a=r[i++]){n=a.getAttributeNode("id");if(n&&n.value===e){return[a]}}}return[]}}}b.find["TAG"]=d.getElementsByTagName?function(e,t){if(typeof t.getElementsByTagName!=="undefined"){return t.getElementsByTagName(e)}else if(d.qsa){return t.querySelectorAll(e)}}:function(e,t){var n,i=[],r=0,a=t.getElementsByTagName(e);if(e==="*"){while(n=a[r++]){if(n.nodeType===1){i.push(n)}}return i}return a};b.find["CLASS"]=d.getElementsByClassName&&function(e,t){if(typeof t.getElementsByClassName!=="undefined"&&C){return t.getElementsByClassName(e)}};o=[];v=[];if(d.qsa=k.test(_.querySelectorAll)){E(function(e){var t;r.appendChild(e).innerHTML="<a id='"+D+"'></a>"+"<select id='"+D+"-\r\\' msallowcapture=''>"+"<option selected=''></option></select>";if(e.querySelectorAll("[msallowcapture^='']").length){v.push("[*^$]="+h+"*(?:''|\"\")")}if(!e.querySelectorAll("[selected]").length){v.push("\\["+h+"*(?:value|"+X+")")}if(!e.querySelectorAll("[id~="+D+"-]").length){v.push("~=")}t=_.createElement("input");t.setAttribute("name","");e.appendChild(t);if(!e.querySelectorAll("[name='']").length){v.push("\\["+h+"*name"+h+"*="+h+"*(?:''|\"\")")}if(!e.querySelectorAll(":checked").length){v.push(":checked")}if(!e.querySelectorAll("a#"+D+"+*").length){v.push(".#.+[+~]")}e.querySelectorAll("\\\f");v.push("[\\r\\n\\f]")});E(function(e){e.innerHTML="<a href='' disabled='disabled'></a>"+"<select disabled='disabled'><option/></select>";var t=_.createElement("input");t.setAttribute("type","hidden");e.appendChild(t).setAttribute("name","D");if(e.querySelectorAll("[name=d]").length){v.push("name"+h+"*[*^$|!~]?=")}if(e.querySelectorAll(":enabled").length!==2){v.push(":enabled",":disabled")}r.appendChild(e).disabled=true;if(e.querySelectorAll(":disabled").length!==2){v.push(":enabled",":disabled")}e.querySelectorAll("*,:x");v.push(",.*:")})}if(d.matchesSelector=k.test(s=r.matches||r.webkitMatchesSelector||r.mozMatchesSelector||r.oMatchesSelector||r.msMatchesSelector)){E(function(e){d.disconnectedMatch=s.call(e,"*");s.call(e,"[s!='']:x");o.push("!=",$)})}v=v.length&&new RegExp(v.join("|"));o=o.length&&new RegExp(o.join("|"));t=k.test(r.compareDocumentPosition);m=t||k.test(r.contains)?function(e,t){var n=e.nodeType===9?e.documentElement:e,i=t&&t.parentNode;return e===i||!!(i&&i.nodeType===1&&(n.contains?n.contains(i):e.compareDocumentPosition&&e.compareDocumentPosition(i)&16))}:function(e,t){if(t){while(t=t.parentNode){if(t===e){return true}}}return false};Y=t?function(e,t){if(e===t){u=true;return 0}var n=!e.compareDocumentPosition-!t.compareDocumentPosition;if(n){return n}n=(e.ownerDocument||e)==(t.ownerDocument||t)?e.compareDocumentPosition(t):1;if(n&1||!d.sortDetached&&t.compareDocumentPosition(e)===n){if(e==_||e.ownerDocument==f&&m(f,e)){return-1}if(t==_||t.ownerDocument==f&&m(f,t)){return 1}return l?g(l,e)-g(l,t):0}return n&4?-1:1}:function(e,t){if(e===t){u=true;return 0}var n,i=0,r=e.parentNode,a=t.parentNode,o=[e],s=[t];if(!r||!a){return e==_?-1:t==_?1:r?-1:a?1:l?g(l,e)-g(l,t):0}else if(r===a){return pe(e,t)}n=e;while(n=n.parentNode){o.unshift(n)}n=t;while(n=n.parentNode){s.unshift(n)}while(o[i]===s[i]){i++}return i?pe(o[i],s[i]):o[i]==f?-1:s[i]==f?1:0};return _};I.matches=function(e,t){return I(e,null,null,t)};I.matchesSelector=function(e,t){w(e);if(d.matchesSelector&&C&&!y[t+" "]&&(!o||!o.test(t))&&(!v||!v.test(t))){try{var n=s.call(e,t);if(n||d.disconnectedMatch||e.document&&e.document.nodeType!==11){return n}}catch(e){y(t,true)}}return I(t,_,null,[e]).length>0};I.contains=function(e,t){if((e.ownerDocument||e)!=_){w(e)}return m(e,t)};I.attr=function(e,t){if((e.ownerDocument||e)!=_){w(e)}var n=b.attrHandle[t.toLowerCase()],i=n&&V.call(b.attrHandle,t.toLowerCase())?n(e,t,!C):undefined;return i!==undefined?i:d.attributes||!C?e.getAttribute(t):(i=e.getAttributeNode(t))&&i.specified?i.value:null};I.escape=function(e){return(e+"").replace(ue,fe)};I.error=function(e){throw new Error("Syntax error, unrecognized expression: "+e)};I.uniqueSort=function(e){var t,n=[],i=0,r=0;u=!d.detectDuplicates;l=!d.sortStable&&e.slice(0);e.sort(Y);if(u){while(t=e[r++]){if(t===e[r]){i=n.push(r)}}while(i--){e.splice(n[i],1)}}l=null;return e};a=I.getText=function(e){var t,n="",i=0,r=e.nodeType;if(!r){while(t=e[i++]){n+=a(t)}}else if(r===1||r===9||r===11){if(typeof e.textContent==="string"){return e.textContent}else{for(e=e.firstChild;e;e=e.nextSibling){n+=a(e)}}}else if(r===3||r===4){return e.nodeValue}return n};b=I.selectors={cacheLength:50,createPseudo:z,match:c,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:true}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:true},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(e){e[1]=e[1].replace(x,R);e[3]=(e[3]||e[4]||e[5]||"").replace(x,R);if(e[2]==="~="){e[3]=" "+e[3]+" "}return e.slice(0,4)},CHILD:function(e){e[1]=e[1].toLowerCase();if(e[1].slice(0,3)==="nth"){if(!e[3]){I.error(e[0])}e[4]=+(e[4]?e[5]+(e[6]||1):2*(e[3]==="even"||e[3]==="odd"));e[5]=+(e[7]+e[8]||e[3]==="odd")}else if(e[3]){I.error(e[0])}return e},PSEUDO:function(e){var t,n=!e[6]&&e[2];if(c["CHILD"].test(e[0])){return null}if(e[3]){e[2]=e[4]||e[5]||""}else if(n&&ne.test(n)&&(t=p(n,true))&&(t=n.indexOf(")",n.length-t)-n.length)){e[0]=e[0].slice(0,t);e[2]=n.slice(0,t)}return e.slice(0,3)}},filter:{TAG:function(e){var t=e.replace(x,R).toLowerCase();return e==="*"?function(){return true}:function(e){return e.nodeName&&e.nodeName.toLowerCase()===t}},CLASS:function(e){var t=q[e+" "];return t||(t=new RegExp("(^|"+h+")"+e+"("+h+"|$)"))&&q(e,function(e){return t.test(typeof e.className==="string"&&e.className||typeof e.getAttribute!=="undefined"&&e.getAttribute("class")||"")})},ATTR:function(n,i,r){return function(e){var t=I.attr(e,n);if(t==null){return i==="!="}if(!i){return true}t+="";return i==="="?t===r:i==="!="?t!==r:i==="^="?r&&t.indexOf(r)===0:i==="*="?r&&t.indexOf(r)>-1:i==="$="?r&&t.slice(-r.length)===r:i==="~="?(" "+t.replace(Z," ")+" ").indexOf(r)>-1:i==="|="?t===r||t.slice(0,r.length+1)===r+"-":false}},CHILD:function(p,e,t,v,m){var y=p.slice(0,3)!=="nth",g=p.slice(-4)!=="last",S=e==="of-type";return v===1&&m===0?function(e){return!!e.parentNode}:function(e,t,n){var i,r,a,o,s,l,u=y!==g?"nextSibling":"previousSibling",f=e.parentNode,h=S&&e.nodeName.toLowerCase(),c=!n&&!S,d=false;if(f){if(y){while(u){o=e;while(o=o[u]){if(S?o.nodeName.toLowerCase()===h:o.nodeType===1){return false}}l=u=p==="only"&&!l&&"nextSibling"}return true}l=[g?f.firstChild:f.lastChild];if(g&&c){o=f;a=o[D]||(o[D]={});r=a[o.uniqueID]||(a[o.uniqueID]={});i=r[p]||[];s=i[0]===T&&i[1];d=s&&i[2];o=s&&f.childNodes[s];while(o=++s&&o&&o[u]||(d=s=0)||l.pop()){if(o.nodeType===1&&++d&&o===e){r[p]=[T,s,d];break}}}else{if(c){o=e;a=o[D]||(o[D]={});r=a[o.uniqueID]||(a[o.uniqueID]={});i=r[p]||[];s=i[0]===T&&i[1];d=s}if(d===false){while(o=++s&&o&&o[u]||(d=s=0)||l.pop()){if((S?o.nodeName.toLowerCase()===h:o.nodeType===1)&&++d){if(c){a=o[D]||(o[D]={});r=a[o.uniqueID]||(a[o.uniqueID]={});r[p]=[T,d]}if(o===e){break}}}}}d-=m;return d===v||d%v===0&&d/v>=0}}},PSEUDO:function(e,a){var t,o=b.pseudos[e]||b.setFilters[e.toLowerCase()]||I.error("unsupported pseudo: "+e);if(o[D]){return o(a)}if(o.length>1){t=[e,e,"",a];return b.setFilters.hasOwnProperty(e.toLowerCase())?z(function(e,t){var n,i=o(e,a),r=i.length;while(r--){n=g(e,i[r]);e[n]=!(t[n]=i[r])}}):function(e){return o(e,0,t)}}return o}},pseudos:{not:z(function(e){var i=[],r=[],s=L(e.replace(S,"$1"));return s[D]?z(function(e,t,n,i){var r,a=s(e,null,i,[]),o=e.length;while(o--){if(r=a[o]){e[o]=!(t[o]=r)}}}):function(e,t,n){i[0]=e;s(i,null,n,r);i[0]=null;return!r.pop()}}),has:z(function(t){return function(e){return I(t,e).length>0}}),contains:z(function(t){t=t.replace(x,R);return function(e){return(e.textContent||a(e)).indexOf(t)>-1}}),lang:z(function(n){if(!ie.test(n||"")){I.error("unsupported lang: "+n)}n=n.replace(x,R).toLowerCase();return function(e){var t;do{if(t=C?e.lang:e.getAttribute("xml:lang")||e.getAttribute("lang")){t=t.toLowerCase();return t===n||t.indexOf(n+"-")===0}}while((e=e.parentNode)&&e.nodeType===1);return false}}),target:function(e){var t=O.location&&O.location.hash;return t&&t.slice(1)===e.id},root:function(e){return e===r},focus:function(e){return e===_.activeElement&&(!_.hasFocus||_.hasFocus())&&!!(e.type||e.href||~e.tabIndex)},enabled:ye(false),disabled:ye(true),checked:function(e){var t=e.nodeName.toLowerCase();return t==="input"&&!!e.checked||t==="option"&&!!e.selected},selected:function(e){if(e.parentNode){e.parentNode.selectedIndex}return e.selected===true},empty:function(e){for(e=e.firstChild;e;e=e.nextSibling){if(e.nodeType<6){return false}}return true},parent:function(e){return!b.pseudos["empty"](e)},header:function(e){return oe.test(e.nodeName)},input:function(e){return ae.test(e.nodeName)},button:function(e){var t=e.nodeName.toLowerCase();return t==="input"&&e.type==="button"||t==="button"},text:function(e){var t;return e.nodeName.toLowerCase()==="input"&&e.type==="text"&&((t=e.getAttribute("type"))==null||t.toLowerCase()==="text")},first:A(function(){return[0]}),last:A(function(e,t){return[t-1]}),eq:A(function(e,t,n){return[n<0?n+t:n]}),even:A(function(e,t){var n=0;for(;n<t;n+=2){e.push(n)}return e}),odd:A(function(e,t){var n=1;for(;n<t;n+=2){e.push(n)}return e}),lt:A(function(e,t,n){var i=n<0?n+t:n>t?t:n;for(;--i>=0;){e.push(i)}return e}),gt:A(function(e,t,n){var i=n<0?n+t:n;for(;++i<t;){e.push(i)}return e})}};b.pseudos["nth"]=b.pseudos["eq"];for(e in{radio:true,checkbox:true,file:true,password:true,image:true}){b.pseudos[e]=ve(e)}for(e in{submit:true,reset:true}){b.pseudos[e]=me(e)}function Se(){}Se.prototype=b.filters=b.pseudos;b.setFilters=new Se;p=I.tokenize=function(e,t){var n,i,r,a,o,s,l,u=H[e+" "];if(u){return t?0:u.slice(0)}o=e;s=[];l=b.preFilter;while(o){if(!n||(i=Q.exec(o))){if(i){o=o.slice(i[0].length)||o}s.push(r=[])}n=false;if(i=ee.exec(o)){n=i.shift();r.push({value:n,type:i[0].replace(S," ")});o=o.slice(n.length)}for(a in b.filter){if((i=c[a].exec(o))&&(!l[a]||(i=l[a](i)))){n=i.shift();r.push({value:n,type:a,matches:i});o=o.slice(n.length)}}if(!n){break}}return t?o.length:o?I.error(e):H(e,s).slice(0)};function B(e){var t=0,n=e.length,i="";for(;t<n;t++){i+=e[t].value}return i}function be(s,e,t){var l=e.dir,u=e.next,f=u||l,h=t&&f==="parentNode",c=N++;return e.first?function(e,t,n){while(e=e[l]){if(e.nodeType===1||h){return s(e,t,n)}}return false}:function(e,t,n){var i,r,a,o=[T,c];if(n){while(e=e[l]){if(e.nodeType===1||h){if(s(e,t,n)){return true}}}}else{while(e=e[l]){if(e.nodeType===1||h){a=e[D]||(e[D]={});r=a[e.uniqueID]||(a[e.uniqueID]={});if(u&&u===e.nodeName.toLowerCase()){e=e[l]||e}else if((i=r[f])&&i[0]===T&&i[1]===c){return o[2]=i[2]}else{r[f]=o;if(o[2]=s(e,t,n)){return true}}}}}return false}}function Pe(r){return r.length>1?function(e,t,n){var i=r.length;while(i--){if(!r[i](e,t,n)){return false}}return true}:r[0]}function we(e,t,n){var i=0,r=t.length;for(;i<r;i++){I(e,t[i],n)}return n}function _e(e,t,n,i,r){var a,o=[],s=0,l=e.length,u=t!=null;for(;s<l;s++){if(a=e[s]){if(!n||n(a,i,r)){o.push(a);if(u){t.push(s)}}}}return o}function Ce(d,p,v,m,y,e){if(m&&!m[D]){m=Ce(m)}if(y&&!y[D]){y=Ce(y,e)}return z(function(e,t,n,i){var r,a,o,s=[],l=[],u=t.length,f=e||we(p||"*",n.nodeType?[n]:n,[]),h=d&&(e||!p)?_e(f,s,d,n,i):f,c=v?y||(e?d:u||m)?[]:t:h;if(v){v(h,c,n,i)}if(m){r=_e(c,l);m(r,[],n,i);a=r.length;while(a--){if(o=r[a]){c[l[a]]=!(h[l[a]]=o)}}}if(e){if(y||d){if(y){r=[];a=c.length;while(a--){if(o=c[a]){r.push(h[a]=o)}}y(null,c=[],r,i)}a=c.length;while(a--){if((o=c[a])&&(r=y?g(e,o):s[a])>-1){e[r]=!(t[r]=o)}}}}else{c=_e(c===t?c.splice(u,c.length):c);if(y){y(null,t,c,i)}else{M.apply(t,c)}}})}function De(e){var r,t,n,i=e.length,a=b.relative[e[0].type],o=a||b.relative[" "],s=a?1:0,l=be(function(e){return e===r},o,true),u=be(function(e){return g(r,e)>-1},o,true),f=[function(e,t,n){var i=!a&&(n||t!==P)||((r=t).nodeType?l(e,t,n):u(e,t,n));r=null;return i}];for(;s<i;s++){if(t=b.relative[e[s].type]){f=[be(Pe(f),t)]}else{t=b.filter[e[s].type].apply(null,e[s].matches);if(t[D]){n=++s;for(;n<i;n++){if(b.relative[e[n].type]){break}}return Ce(s>1&&Pe(f),s>1&&B(e.slice(0,s-1).concat({value:e[s-2].type===" "?"*":""})).replace(S,"$1"),t,s<n&&De(e.slice(s,n)),n<i&&De(e=e.slice(n)),n<i&&B(e))}f.push(t)}}return Pe(f)}function Te(m,y){var g=y.length>0,S=m.length>0,e=function(e,t,n,i,r){var a,o,s,l=0,u="0",f=e&&[],h=[],c=P,d=e||S&&b.find["TAG"]("*",r),p=T+=c==null?1:Math.random()||.1,v=d.length;if(r){P=t==_||t||r}for(;u!==v&&(a=d[u])!=null;u++){if(S&&a){o=0;if(!t&&a.ownerDocument!=_){w(a);n=!C}while(s=m[o++]){if(s(a,t||_,n)){i.push(a);break}}if(r){T=p}}if(g){if(a=!s&&a){l--}if(e){f.push(a)}}}l+=u;if(g&&u!==l){o=0;while(s=y[o++]){s(f,h,t,n)}if(e){if(l>0){while(u--){if(!(f[u]||h[u])){h[u]=j.call(i)}}}h=_e(h)}M.apply(i,h);if(r&&!e&&h.length>0&&l+y.length>1){I.uniqueSort(i)}}if(r){T=p;P=c}return f};return g?z(e):e}L=I.compile=function(e,t){var n,i=[],r=[],a=W[e+" "];if(!a){if(!t){t=p(e)}n=t.length;while(n--){a=De(t[n]);if(a[D]){i.push(a)}else{r.push(a)}}a=W(e,Te(r,i));a.selector=e}return a};U=I.select=function(e,t,n,i){var r,a,o,s,l,u=typeof e==="function"&&e,f=!i&&p(e=u.selector||e);n=n||[];if(f.length===1){a=f[0]=f[0].slice(0);if(a.length>2&&(o=a[0]).type==="ID"&&t.nodeType===9&&C&&b.relative[a[1].type]){t=(b.find["ID"](o.matches[0].replace(x,R),t)||[])[0];if(!t){return n}else if(u){t=t.parentNode}e=e.slice(a.shift().value.length)}r=c["needsContext"].test(e)?0:a.length;while(r--){o=a[r];if(b.relative[s=o.type]){break}if(l=b.find[s]){if(i=l(o.matches[0].replace(x,R),le.test(a[0].type)&&ge(t.parentNode)||t)){a.splice(r,1);e=i.length&&B(a);if(!e){M.apply(n,i);return n}break}}}}(u||L(e,f))(i,t,!C,n,!t||le.test(e)&&ge(t.parentNode)||t);return n};d.sortStable=D.split("").sort(Y).join("")===D;d.detectDuplicates=!!u;w();d.sortDetached=E(function(e){return e.compareDocumentPosition(_.createElement("fieldset"))&1});if(!E(function(e){e.innerHTML="<a href='#'></a>";return e.firstChild.getAttribute("href")==="#"})){de("type|href|height|width",function(e,t,n){if(!n){return e.getAttribute(t,t.toLowerCase()==="type"?1:2)}})}if(!d.attributes||!E(function(e){e.innerHTML="<input/>";e.firstChild.setAttribute("value","");return e.firstChild.getAttribute("value")===""})){de("value",function(e,t,n){if(!n&&e.nodeName.toLowerCase()==="input"){return e.defaultValue}})}if(!E(function(e){return e.getAttribute("disabled")==null})){de(X,function(e,t,n){var i;if(!n){return e[t]===true?t.toLowerCase():(i=e.getAttributeNode(t))&&i.specified?i.value:null}})}return I}(_);D.find=e;D.expr=e.selectors;D.expr[":"]=D.expr.pseudos;D.uniqueSort=D.unique=e.uniqueSort;D.text=e.getText;D.isXMLDoc=e.isXML;D.contains=e.contains;D.escapeSelector=e.escape;var i=function(e,t,n){var i=[],r=n!==undefined;while((e=e[t])&&e.nodeType!==9){if(e.nodeType===1){if(r&&D(e).is(n)){break}i.push(e)}}return i};var K=function(e,t){var n=[];for(;e;e=e.nextSibling){if(e.nodeType===1&&e!==t){n.push(e)}}return n};var $=D.expr.match.needsContext;function u(e,t){return e.nodeName&&e.nodeName.toLowerCase()===t.toLowerCase()}var Z=/^<([a-z][^\/\0>:\x20\t\r\n\f]*)[\x20\t\r\n\f]*\/?>(?:<\/\1>|)$/i;function Q(e,n,i){if(m(n)){return D.grep(e,function(e,t){return!!n.call(e,t,e)!==i})}if(n.nodeType){return D.grep(e,function(e){return e===n!==i})}if(typeof n!=="string"){return D.grep(e,function(e){return N.call(n,e)>-1!==i})}return D.filter(n,e,i)}D.filter=function(e,t,n){var i=t[0];if(n){e=":not("+e+")"}if(t.length===1&&i.nodeType===1){return D.find.matchesSelector(i,e)?[i]:[]}return D.find.matches(e,D.grep(t,function(e){return e.nodeType===1}))};D.fn.extend({find:function(e){var t,n,i=this.length,r=this;if(typeof e!=="string"){return this.pushStack(D(e).filter(function(){for(t=0;t<i;t++){if(D.contains(r[t],this)){return true}}}))}n=this.pushStack([]);for(t=0;t<i;t++){D.find(e,r[t],n)}return i>1?D.uniqueSort(n):n},filter:function(e){return this.pushStack(Q(this,e||[],false))},not:function(e){return this.pushStack(Q(this,e||[],true))},is:function(e){return!!Q(this,typeof e==="string"&&$.test(e)?D(e):e||[],false).length}});var ee,te=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]+))$/,ne=D.fn.init=function(e,t,n){var i,r;if(!e){return this}n=n||ee;if(typeof e==="string"){if(e[0]==="<"&&e[e.length-1]===">"&&e.length>=3){i=[null,e,null]}else{i=te.exec(e)}if(i&&(i[1]||!t)){if(i[1]){t=t instanceof D?t[0]:t;D.merge(this,D.parseHTML(i[1],t&&t.nodeType?t.ownerDocument||t:C,true));if(Z.test(i[1])&&D.isPlainObject(t)){for(i in t){if(m(this[i])){this[i](t[i])}else{this.attr(i,t[i])}}}return this}else{r=C.getElementById(i[2]);if(r){this[0]=r;this.length=1}return this}}else if(!t||t.jquery){return(t||n).find(e)}else{return this.constructor(t).find(e)}}else if(e.nodeType){this[0]=e;this.length=1;return this}else if(m(e)){return n.ready!==undefined?n.ready(e):e(D)}return D.makeArray(e,this)};ne.prototype=D.fn;ee=D(C);var ie=/^(?:parents|prev(?:Until|All))/,re={children:true,contents:true,next:true,prev:true};D.fn.extend({has:function(e){var t=D(e,this),n=t.length;return this.filter(function(){var e=0;for(;e<n;e++){if(D.contains(this,t[e])){return true}}})},closest:function(e,t){var n,i=0,r=this.length,a=[],o=typeof e!=="string"&&D(e);if(!$.test(e)){for(;i<r;i++){for(n=this[i];n&&n!==t;n=n.parentNode){if(n.nodeType<11&&(o?o.index(n)>-1:n.nodeType===1&&D.find.matchesSelector(n,e))){a.push(n);break}}}}return this.pushStack(a.length>1?D.uniqueSort(a):a)},index:function(e){if(!e){return this[0]&&this[0].parentNode?this.first().prevAll().length:-1}if(typeof e==="string"){return N.call(D(e),this[0])}return N.call(this,e.jquery?e[0]:e)},add:function(e,t){return this.pushStack(D.uniqueSort(D.merge(this.get(),D(e,t))))},addBack:function(e){return this.add(e==null?this.prevObject:this.prevObject.filter(e))}});function ae(e,t){while((e=e[t])&&e.nodeType!==1){}return e}D.each({parent:function(e){var t=e.parentNode;return t&&t.nodeType!==11?t:null},parents:function(e){return i(e,"parentNode")},parentsUntil:function(e,t,n){return i(e,"parentNode",n)},next:function(e){return ae(e,"nextSibling")},prev:function(e){return ae(e,"previousSibling")},nextAll:function(e){return i(e,"nextSibling")},prevAll:function(e){return i(e,"previousSibling")},nextUntil:function(e,t,n){return i(e,"nextSibling",n)},prevUntil:function(e,t,n){return i(e,"previousSibling",n)},siblings:function(e){return K((e.parentNode||{}).firstChild,e)},children:function(e){return K(e.firstChild)},contents:function(e){if(e.contentDocument!=null&&F(e.contentDocument)){return e.contentDocument}if(u(e,"template")){e=e.content||e}return D.merge([],e.childNodes)}},function(i,r){D.fn[i]=function(e,t){var n=D.map(this,r,e);if(i.slice(-5)!=="Until"){t=e}if(t&&typeof t==="string"){n=D.filter(t,n)}if(this.length>1){if(!re[i]){D.uniqueSort(n)}if(ie.test(i)){n.reverse()}}return this.pushStack(n)}});var T=/[^\x20\t\r\n\f]+/g;function oe(e){var n={};D.each(e.match(T)||[],function(e,t){n[t]=true});return n}D.Callbacks=function(i){i=typeof i==="string"?oe(i):D.extend({},i);var n,e,t,r,a=[],o=[],s=-1,l=function(){r=r||i.once;t=n=true;for(;o.length;s=-1){e=o.shift();while(++s<a.length){if(a[s].apply(e[0],e[1])===false&&i.stopOnFalse){s=a.length;e=false}}}if(!i.memory){e=false}n=false;if(r){if(e){a=[]}else{a=""}}},u={add:function(){if(a){if(e&&!n){s=a.length-1;o.push(e)}(function n(e){D.each(e,function(e,t){if(m(t)){if(!i.unique||!u.has(t)){a.push(t)}}else if(t&&t.length&&g(t)!=="string"){n(t)}})})(arguments);if(e&&!n){l()}}return this},remove:function(){D.each(arguments,function(e,t){var n;while((n=D.inArray(t,a,n))>-1){a.splice(n,1);if(n<=s){s--}}});return this},has:function(e){return e?D.inArray(e,a)>-1:a.length>0},empty:function(){if(a){a=[]}return this},disable:function(){r=o=[];a=e="";return this},disabled:function(){return!a},lock:function(){r=o=[];if(!e&&!n){a=e=""}return this},locked:function(){return!!r},fireWith:function(e,t){if(!r){t=t||[];t=[e,t.slice?t.slice():t];o.push(t);if(!n){l()}}return this},fire:function(){u.fireWith(this,arguments);return this},fired:function(){return!!t}};return u};function f(e){return e}function se(e){throw e}function le(e,t,n,i){var r;try{if(e&&m(r=e.promise)){r.call(e).done(t).fail(n)}else if(e&&m(r=e.then)){r.call(e,t,n)}else{t.apply(undefined,[e].slice(i))}}catch(e){n.apply(undefined,[e])}}D.extend({Deferred:function(e){var a=[["notify","progress",D.Callbacks("memory"),D.Callbacks("memory"),2],["resolve","done",D.Callbacks("once memory"),D.Callbacks("once memory"),0,"resolved"],["reject","fail",D.Callbacks("once memory"),D.Callbacks("once memory"),1,"rejected"]],r="pending",o={state:function(){return r},always:function(){s.done(arguments).fail(arguments);return this},catch:function(e){return o.then(null,e)},pipe:function(){var r=arguments;return D.Deferred(function(i){D.each(a,function(e,t){var n=m(r[t[4]])&&r[t[4]];s[t[1]](function(){var e=n&&n.apply(this,arguments);if(e&&m(e.promise)){e.promise().progress(i.notify).done(i.resolve).fail(i.reject)}else{i[t[0]+"With"](this,n?[e]:arguments)}})});r=null}).promise()},then:function(t,n,i){var l=0;function u(r,a,o,s){return function(){var n=this,i=arguments,e=function(){var e,t;if(r<l){return}e=o.apply(n,i);if(e===a.promise()){throw new TypeError("Thenable self-resolution")}t=e&&(typeof e==="object"||typeof e==="function")&&e.then;if(m(t)){if(s){t.call(e,u(l,a,f,s),u(l,a,se,s))}else{l++;t.call(e,u(l,a,f,s),u(l,a,se,s),u(l,a,f,a.notifyWith))}}else{if(o!==f){n=undefined;i=[e]}(s||a.resolveWith)(n,i)}},t=s?e:function(){try{e()}catch(e){if(D.Deferred.exceptionHook){D.Deferred.exceptionHook(e,t.stackTrace)}if(r+1>=l){if(o!==se){n=undefined;i=[e]}a.rejectWith(n,i)}}};if(r){t()}else{if(D.Deferred.getStackHook){t.stackTrace=D.Deferred.getStackHook()}_.setTimeout(t)}}}return D.Deferred(function(e){a[0][3].add(u(0,e,m(i)?i:f,e.notifyWith));a[1][3].add(u(0,e,m(t)?t:f));a[2][3].add(u(0,e,m(n)?n:se))}).promise()},promise:function(e){return e!=null?D.extend(e,o):o}},s={};D.each(a,function(e,t){var n=t[2],i=t[5];o[t[1]]=n.add;if(i){n.add(function(){r=i},a[3-e][2].disable,a[3-e][3].disable,a[0][2].lock,a[0][3].lock)}n.add(t[3].fire);s[t[0]]=function(){s[t[0]+"With"](this===s?undefined:this,arguments);return this};s[t[0]+"With"]=n.fireWith});o.promise(s);if(e){e.call(s,s)}return s},when:function(e){var n=arguments.length,t=n,i=Array(t),r=s.call(arguments),a=D.Deferred(),o=function(t){return function(e){i[t]=this;r[t]=arguments.length>1?s.call(arguments):e;if(!--n){a.resolveWith(i,r)}}};if(n<=1){le(e,a.done(o(t)).resolve,a.reject,!n);if(a.state()==="pending"||m(r[t]&&r[t].then)){return a.then()}}while(t--){le(r[t],o(t),a.reject)}return a.promise()}});var ue=/^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/;D.Deferred.exceptionHook=function(e,t){if(_.console&&_.console.warn&&e&&ue.test(e.name)){_.console.warn("jQuery.Deferred exception: "+e.message,e.stack,t)}};D.readyException=function(e){_.setTimeout(function(){throw e})};var fe=D.Deferred();D.fn.ready=function(e){fe.then(e).catch(function(e){D.readyException(e)});return this};D.extend({isReady:false,readyWait:1,ready:function(e){if(e===true?--D.readyWait:D.isReady){return}D.isReady=true;if(e!==true&&--D.readyWait>0){return}fe.resolveWith(C,[D])}});D.ready.then=fe.then;function he(){C.removeEventListener("DOMContentLoaded",he);_.removeEventListener("load",he);D.ready()}if(C.readyState==="complete"||C.readyState!=="loading"&&!C.documentElement.doScroll){_.setTimeout(D.ready)}else{C.addEventListener("DOMContentLoaded",he);_.addEventListener("load",he)}var h=function(e,t,n,i,r,a,o){var s=0,l=e.length,u=n==null;if(g(n)==="object"){r=true;for(s in n){h(e,t,s,n[s],true,a,o)}}else if(i!==undefined){r=true;if(!m(i)){o=true}if(u){if(o){t.call(e,i);t=null}else{u=t;t=function(e,t,n){return u.call(D(e),n)}}}if(t){for(;s<l;s++){t(e[s],n,o?i:i.call(e[s],s,t(e[s],n)))}}}if(r){return e}if(u){return t.call(e)}return l?t(e[0],n):a};var ce=/^-ms-/,de=/-([a-z])/g;function pe(e,t){return t.toUpperCase()}function c(e){return e.replace(ce,"ms-").replace(de,pe)}var S=function(e){return e.nodeType===1||e.nodeType===9||!+e.nodeType};function n(){this.expando=D.expando+n.uid++}n.uid=1;n.prototype={cache:function(e){var t=e[this.expando];if(!t){t={};if(S(e)){if(e.nodeType){e[this.expando]=t}else{Object.defineProperty(e,this.expando,{value:t,configurable:true})}}}return t},set:function(e,t,n){var i,r=this.cache(e);if(typeof t==="string"){r[c(t)]=n}else{for(i in t){r[c(i)]=t[i]}}return r},get:function(e,t){return t===undefined?this.cache(e):e[this.expando]&&e[this.expando][c(t)]},access:function(e,t,n){if(t===undefined||t&&typeof t==="string"&&n===undefined){return this.get(e,t)}this.set(e,t,n);return n!==undefined?n:t},remove:function(e,t){var n,i=e[this.expando];if(i===undefined){return}if(t!==undefined){if(Array.isArray(t)){t=t.map(c)}else{t=c(t);t=t in i?[t]:t.match(T)||[]}n=t.length;while(n--){delete i[t[n]]}}if(t===undefined||D.isEmptyObject(i)){if(e.nodeType){e[this.expando]=undefined}else{delete e[this.expando]}}},hasData:function(e){var t=e[this.expando];return t!==undefined&&!D.isEmptyObject(t)}};var b=new n;var d=new n;var ve=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,me=/[A-Z]/g;function ye(e){if(e==="true"){return true}if(e==="false"){return false}if(e==="null"){return null}if(e===+e+""){return+e}if(ve.test(e)){return JSON.parse(e)}return e}function ge(e,t,n){var i;if(n===undefined&&e.nodeType===1){i="data-"+t.replace(me,"-$&").toLowerCase();n=e.getAttribute(i);if(typeof n==="string"){try{n=ye(n)}catch(e){}d.set(e,t,n)}else{n=undefined}}return n}D.extend({hasData:function(e){return d.hasData(e)||b.hasData(e)},data:function(e,t,n){return d.access(e,t,n)},removeData:function(e,t){d.remove(e,t)},_data:function(e,t,n){return b.access(e,t,n)},_removeData:function(e,t){b.remove(e,t)}});D.fn.extend({data:function(n,e){var t,i,r,a=this[0],o=a&&a.attributes;if(n===undefined){if(this.length){r=d.get(a);if(a.nodeType===1&&!b.get(a,"hasDataAttrs")){t=o.length;while(t--){if(o[t]){i=o[t].name;if(i.indexOf("data-")===0){i=c(i.slice(5));ge(a,i,r[i])}}}b.set(a,"hasDataAttrs",true)}}return r}if(typeof n==="object"){return this.each(function(){d.set(this,n)})}return h(this,function(e){var t;if(a&&e===undefined){t=d.get(a,n);if(t!==undefined){return t}t=ge(a,n);if(t!==undefined){return t}return}this.each(function(){d.set(this,n,e)})},null,e,arguments.length>1,null,true)},removeData:function(e){return this.each(function(){d.remove(this,e)})}});D.extend({queue:function(e,t,n){var i;if(e){t=(t||"fx")+"queue";i=b.get(e,t);if(n){if(!i||Array.isArray(n)){i=b.access(e,t,D.makeArray(n))}else{i.push(n)}}return i||[]}},dequeue:function(e,t){t=t||"fx";var n=D.queue(e,t),i=n.length,r=n.shift(),a=D._queueHooks(e,t),o=function(){D.dequeue(e,t)};if(r==="inprogress"){r=n.shift();i--}if(r){if(t==="fx"){n.unshift("inprogress")}delete a.stop;r.call(e,o,a)}if(!i&&a){a.empty.fire()}},_queueHooks:function(e,t){var n=t+"queueHooks";return b.get(e,n)||b.access(e,n,{empty:D.Callbacks("once memory").add(function(){b.remove(e,[t+"queue",n])})})}});D.fn.extend({queue:function(t,n){var e=2;if(typeof t!=="string"){n=t;t="fx";e--}if(arguments.length<e){return D.queue(this[0],t)}return n===undefined?this:this.each(function(){var e=D.queue(this,t,n);D._queueHooks(this,t);if(t==="fx"&&e[0]!=="inprogress"){D.dequeue(this,t)}})},dequeue:function(e){return this.each(function(){D.dequeue(this,e)})},clearQueue:function(e){return this.queue(e||"fx",[])},promise:function(e,t){var n,i=1,r=D.Deferred(),a=this,o=this.length,s=function(){if(!--i){r.resolveWith(a,[a])}};if(typeof e!=="string"){t=e;e=undefined}e=e||"fx";while(o--){n=b.get(a[o],e+"queueHooks");if(n&&n.empty){i++;n.empty.add(s)}}s();return r.promise(t)}});var Se=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source;var p=new RegExp("^(?:([+-])=|)("+Se+")([a-z%]*)$","i");var P=["Top","Right","Bottom","Left"];var w=C.documentElement;var M=function(e){return D.contains(e.ownerDocument,e)},be={composed:true};if(w.getRootNode){M=function(e){return D.contains(e.ownerDocument,e)||e.getRootNode(be)===e.ownerDocument}}var Pe=function(e,t){e=t||e;return e.style.display==="none"||e.style.display===""&&M(e)&&D.css(e,"display")==="none"};function we(e,t,n,i){var r,a,o=20,s=i?function(){return i.cur()}:function(){return D.css(e,t,"")},l=s(),u=n&&n[3]||(D.cssNumber[t]?"":"px"),f=e.nodeType&&(D.cssNumber[t]||u!=="px"&&+l)&&p.exec(D.css(e,t));if(f&&f[3]!==u){l=l/2;u=u||f[3];f=+l||1;while(o--){D.style(e,t,f+u);if((1-a)*(1-(a=s()/l||.5))<=0){o=0}f=f/a}f=f*2;D.style(e,t,f+u);n=n||[]}if(n){f=+f||+l||0;r=n[1]?f+(n[1]+1)*n[2]:+n[2];if(i){i.unit=u;i.start=f;i.end=r}}return r}var _e={};function Ce(e){var t,n=e.ownerDocument,i=e.nodeName,r=_e[i];if(r){return r}t=n.body.appendChild(n.createElement(i));r=D.css(t,"display");t.parentNode.removeChild(t);if(r==="none"){r="block"}_e[i]=r;return r}function k(e,t){var n,i,r=[],a=0,o=e.length;for(;a<o;a++){i=e[a];if(!i.style){continue}n=i.style.display;if(t){if(n==="none"){r[a]=b.get(i,"display")||null;if(!r[a]){i.style.display=""}}if(i.style.display===""&&Pe(i)){r[a]=Ce(i)}}else{if(n!=="none"){r[a]="none";b.set(i,"display",n)}}}for(a=0;a<o;a++){if(r[a]!=null){e[a].style.display=r[a]}}return e}D.fn.extend({show:function(){return k(this,true)},hide:function(){return k(this)},toggle:function(e){if(typeof e==="boolean"){return e?this.show():this.hide()}return this.each(function(){if(Pe(this)){D(this).show()}else{D(this).hide()}})}});var r=/^(?:checkbox|radio)$/i;var De=/<([a-z][^\/\0>\x20\t\r\n\f]*)/i;var Te=/^$|^module$|\/(?:java|ecma)script/i;(function(){var e=C.createDocumentFragment(),t=e.appendChild(C.createElement("div")),n=C.createElement("input");n.setAttribute("type","radio");n.setAttribute("checked","checked");n.setAttribute("name","t");t.appendChild(n);v.checkClone=t.cloneNode(true).cloneNode(true).lastChild.checked;t.innerHTML="<textarea>x</textarea>";v.noCloneChecked=!!t.cloneNode(true).lastChild.defaultValue;t.innerHTML="<option></option>";v.option=!!t.lastChild})();var x={thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]};x.tbody=x.tfoot=x.colgroup=x.caption=x.thead;x.th=x.td;if(!v.option){x.optgroup=x.option=[1,"<select multiple='multiple'>","</select>"]}function R(e,t){var n;if(typeof e.getElementsByTagName!=="undefined"){n=e.getElementsByTagName(t||"*")}else if(typeof e.querySelectorAll!=="undefined"){n=e.querySelectorAll(t||"*")}else{n=[]}if(t===undefined||t&&u(e,t)){return D.merge([e],n)}return n}function Me(e,t){var n=0,i=e.length;for(;n<i;n++){b.set(e[n],"globalEval",!t||b.get(t[n],"globalEval"))}}var ke=/<|&#?\w+;/;function xe(e,t,n,i,r){var a,o,s,l,u,f,h=t.createDocumentFragment(),c=[],d=0,p=e.length;for(;d<p;d++){a=e[d];if(a||a===0){if(g(a)==="object"){D.merge(c,a.nodeType?[a]:a)}else if(!ke.test(a)){c.push(t.createTextNode(a))}else{o=o||h.appendChild(t.createElement("div"));s=(De.exec(a)||["",""])[1].toLowerCase();l=x[s]||x._default;o.innerHTML=l[1]+D.htmlPrefilter(a)+l[2];f=l[0];while(f--){o=o.lastChild}D.merge(c,o.childNodes);o=h.firstChild;o.textContent=""}}}h.textContent="";d=0;while(a=c[d++]){if(i&&D.inArray(a,i)>-1){if(r){r.push(a)}continue}u=M(a);o=R(h.appendChild(a),"script");if(u){Me(o)}if(n){f=0;while(a=o[f++]){if(Te.test(a.type||"")){n.push(a)}}}}return h}var Re=/^([^.]*)(?:\.(.+)|)/;function o(){return true}function l(){return false}function Ie(e,t){return e===ze()===(t==="focus")}function ze(){try{return C.activeElement}catch(e){}}function Ee(e,t,n,i,r,a){var o,s;if(typeof t==="object"){if(typeof n!=="string"){i=i||n;n=undefined}for(s in t){Ee(e,s,n,i,t[s],a)}return e}if(i==null&&r==null){r=n;i=n=undefined}else if(r==null){if(typeof n==="string"){r=i;i=undefined}else{r=i;i=n;n=undefined}}if(r===false){r=l}else if(!r){return e}if(a===1){o=r;r=function(e){D().off(e);return o.apply(this,arguments)};r.guid=o.guid||(o.guid=D.guid++)}return e.each(function(){D.event.add(this,t,r,i,n)})}D.event={global:{},add:function(t,e,n,i,r){var a,o,s,l,u,f,h,c,d,p,v,m=b.get(t);if(!S(t)){return}if(n.handler){a=n;n=a.handler;r=a.selector}if(r){D.find.matchesSelector(w,r)}if(!n.guid){n.guid=D.guid++}if(!(l=m.events)){l=m.events=Object.create(null)}if(!(o=m.handle)){o=m.handle=function(e){return typeof D!=="undefined"&&D.event.triggered!==e.type?D.event.dispatch.apply(t,arguments):undefined}}e=(e||"").match(T)||[""];u=e.length;while(u--){s=Re.exec(e[u])||[];d=v=s[1];p=(s[2]||"").split(".").sort();if(!d){continue}h=D.event.special[d]||{};d=(r?h.delegateType:h.bindType)||d;h=D.event.special[d]||{};f=D.extend({type:d,origType:v,data:i,handler:n,guid:n.guid,selector:r,needsContext:r&&D.expr.match.needsContext.test(r),namespace:p.join(".")},a);if(!(c=l[d])){c=l[d]=[];c.delegateCount=0;if(!h.setup||h.setup.call(t,i,p,o)===false){if(t.addEventListener){t.addEventListener(d,o)}}}if(h.add){h.add.call(t,f);if(!f.handler.guid){f.handler.guid=n.guid}}if(r){c.splice(c.delegateCount++,0,f)}else{c.push(f)}D.event.global[d]=true}},remove:function(e,t,n,i,r){var a,o,s,l,u,f,h,c,d,p,v,m=b.hasData(e)&&b.get(e);if(!m||!(l=m.events)){return}t=(t||"").match(T)||[""];u=t.length;while(u--){s=Re.exec(t[u])||[];d=v=s[1];p=(s[2]||"").split(".").sort();if(!d){for(d in l){D.event.remove(e,d+t[u],n,i,true)}continue}h=D.event.special[d]||{};d=(i?h.delegateType:h.bindType)||d;c=l[d]||[];s=s[2]&&new RegExp("(^|\\.)"+p.join("\\.(?:.*\\.|)")+"(\\.|$)");o=a=c.length;while(a--){f=c[a];if((r||v===f.origType)&&(!n||n.guid===f.guid)&&(!s||s.test(f.namespace))&&(!i||i===f.selector||i==="**"&&f.selector)){c.splice(a,1);if(f.selector){c.delegateCount--}if(h.remove){h.remove.call(e,f)}}}if(o&&!c.length){if(!h.teardown||h.teardown.call(e,p,m.handle)===false){D.removeEvent(e,d,m.handle)}delete l[d]}}if(D.isEmptyObject(l)){b.remove(e,"handle events")}},dispatch:function(e){var t,n,i,r,a,o,s=new Array(arguments.length),l=D.event.fix(e),u=(b.get(this,"events")||Object.create(null))[l.type]||[],f=D.event.special[l.type]||{};s[0]=l;for(t=1;t<arguments.length;t++){s[t]=arguments[t]}l.delegateTarget=this;if(f.preDispatch&&f.preDispatch.call(this,l)===false){return}o=D.event.handlers.call(this,l,u);t=0;while((r=o[t++])&&!l.isPropagationStopped()){l.currentTarget=r.elem;n=0;while((a=r.handlers[n++])&&!l.isImmediatePropagationStopped()){if(!l.rnamespace||a.namespace===false||l.rnamespace.test(a.namespace)){l.handleObj=a;l.data=a.data;i=((D.event.special[a.origType]||{}).handle||a.handler).apply(r.elem,s);if(i!==undefined){if((l.result=i)===false){l.preventDefault();l.stopPropagation()}}}}}if(f.postDispatch){f.postDispatch.call(this,l)}return l.result},handlers:function(e,t){var n,i,r,a,o,s=[],l=t.delegateCount,u=e.target;if(l&&u.nodeType&&!(e.type==="click"&&e.button>=1)){for(;u!==this;u=u.parentNode||this){if(u.nodeType===1&&!(e.type==="click"&&u.disabled===true)){a=[];o={};for(n=0;n<l;n++){i=t[n];r=i.selector+" ";if(o[r]===undefined){o[r]=i.needsContext?D(r,this).index(u)>-1:D.find(r,this,null,[u]).length}if(o[r]){a.push(i)}}if(a.length){s.push({elem:u,handlers:a})}}}}u=this;if(l<t.length){s.push({elem:u,handlers:t.slice(l)})}return s},addProp:function(t,e){Object.defineProperty(D.Event.prototype,t,{enumerable:true,configurable:true,get:m(e)?function(){if(this.originalEvent){return e(this.originalEvent)}}:function(){if(this.originalEvent){return this.originalEvent[t]}},set:function(e){Object.defineProperty(this,t,{enumerable:true,configurable:true,writable:true,value:e})}})},fix:function(e){return e[D.expando]?e:new D.Event(e)},special:{load:{noBubble:true},click:{setup:function(e){var t=this||e;if(r.test(t.type)&&t.click&&u(t,"input")){Ae(t,"click",o)}return false},trigger:function(e){var t=this||e;if(r.test(t.type)&&t.click&&u(t,"input")){Ae(t,"click")}return true},_default:function(e){var t=e.target;return r.test(t.type)&&t.click&&u(t,"input")&&b.get(t,"click")||u(t,"a")}},beforeunload:{postDispatch:function(e){if(e.result!==undefined&&e.originalEvent){e.originalEvent.returnValue=e.result}}}}};function Ae(e,r,a){if(!a){if(b.get(e,r)===undefined){D.event.add(e,r,o)}return}b.set(e,r,false);D.event.add(e,r,{namespace:false,handler:function(e){var t,n,i=b.get(this,r);if(e.isTrigger&1&&this[r]){if(!i.length){i=s.call(arguments);b.set(this,r,i);t=a(this,r);this[r]();n=b.get(this,r);if(i!==n||t){b.set(this,r,false)}else{n={}}if(i!==n){e.stopImmediatePropagation();e.preventDefault();return n&&n.value}}else if((D.event.special[r]||{}).delegateType){e.stopPropagation()}}else if(i.length){b.set(this,r,{value:D.event.trigger(D.extend(i[0],D.Event.prototype),i.slice(1),this)});e.stopImmediatePropagation()}}})}D.removeEvent=function(e,t,n){if(e.removeEventListener){e.removeEventListener(t,n)}};D.Event=function(e,t){if(!(this instanceof D.Event)){return new D.Event(e,t)}if(e&&e.type){this.originalEvent=e;this.type=e.type;this.isDefaultPrevented=e.defaultPrevented||e.defaultPrevented===undefined&&e.returnValue===false?o:l;this.target=e.target&&e.target.nodeType===3?e.target.parentNode:e.target;this.currentTarget=e.currentTarget;this.relatedTarget=e.relatedTarget}else{this.type=e}if(t){D.extend(this,t)}this.timeStamp=e&&e.timeStamp||Date.now();this[D.expando]=true};D.Event.prototype={constructor:D.Event,isDefaultPrevented:l,isPropagationStopped:l,isImmediatePropagationStopped:l,isSimulated:false,preventDefault:function(){var e=this.originalEvent;this.isDefaultPrevented=o;if(e&&!this.isSimulated){e.preventDefault()}},stopPropagation:function(){var e=this.originalEvent;this.isPropagationStopped=o;if(e&&!this.isSimulated){e.stopPropagation()}},stopImmediatePropagation:function(){var e=this.originalEvent;this.isImmediatePropagationStopped=o;if(e&&!this.isSimulated){e.stopImmediatePropagation()}this.stopPropagation()}};D.each({altKey:true,bubbles:true,cancelable:true,changedTouches:true,ctrlKey:true,detail:true,eventPhase:true,metaKey:true,pageX:true,pageY:true,shiftKey:true,view:true,char:true,code:true,charCode:true,key:true,keyCode:true,button:true,buttons:true,clientX:true,clientY:true,offsetX:true,offsetY:true,pointerId:true,pointerType:true,screenX:true,screenY:true,targetTouches:true,toElement:true,touches:true,which:true},D.event.addProp);D.each({focus:"focusin",blur:"focusout"},function(e,t){D.event.special[e]={setup:function(){Ae(this,e,Ie);return false},trigger:function(){Ae(this,e);return true},_default:function(){return true},delegateType:t}});D.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},function(e,a){D.event.special[e]={delegateType:a,bindType:a,handle:function(e){var t,n=this,i=e.relatedTarget,r=e.handleObj;if(!i||i!==n&&!D.contains(n,i)){e.type=r.origType;t=r.handler.apply(this,arguments);e.type=a}return t}}});D.fn.extend({on:function(e,t,n,i){return Ee(this,e,t,n,i)},one:function(e,t,n,i){return Ee(this,e,t,n,i,1)},off:function(e,t,n){var i,r;if(e&&e.preventDefault&&e.handleObj){i=e.handleObj;D(e.delegateTarget).off(i.namespace?i.origType+"."+i.namespace:i.origType,i.selector,i.handler);return this}if(typeof e==="object"){for(r in e){this.off(r,t,e[r])}return this}if(t===false||typeof t==="function"){n=t;t=undefined}if(n===false){n=l}return this.each(function(){D.event.remove(this,e,n,t)})}});var Be=/<script|<style|<link/i,Oe=/checked\s*(?:[^=]|=\s*.checked.)/i,Fe=/^\s*<!(?:\[CDATA\[|--)|(?:\]\]|--)>\s*$/g;function Le(e,t){if(u(e,"table")&&u(t.nodeType!==11?t:t.firstChild,"tr")){return D(e).children("tbody")[0]||e}return e}function Ue(e){e.type=(e.getAttribute("type")!==null)+"/"+e.type;return e}function Ne(e){if((e.type||"").slice(0,5)==="true/"){e.type=e.type.slice(5)}else{e.removeAttribute("type")}return e}function qe(e,t){var n,i,r,a,o,s,l;if(t.nodeType!==1){return}if(b.hasData(e)){a=b.get(e);l=a.events;if(l){b.remove(t,"handle events");for(r in l){for(n=0,i=l[r].length;n<i;n++){D.event.add(t,r,l[r][n])}}}}if(d.hasData(e)){o=d.access(e);s=D.extend({},o);d.set(t,s)}}function He(e,t){var n=t.nodeName.toLowerCase();if(n==="input"&&r.test(e.type)){t.checked=e.checked}else if(n==="input"||n==="textarea"){t.defaultValue=e.defaultValue}}function I(n,i,r,a){i=L(i);var e,t,o,s,l,u,f=0,h=n.length,c=h-1,d=i[0],p=m(d);if(p||h>1&&typeof d==="string"&&!v.checkClone&&Oe.test(d)){return n.each(function(e){var t=n.eq(e);if(p){i[0]=d.call(this,e,t.html())}I(t,i,r,a)})}if(h){e=xe(i,n[0].ownerDocument,false,n,a);t=e.firstChild;if(e.childNodes.length===1){e=t}if(t||a){o=D.map(R(e,"script"),Ue);s=o.length;for(;f<h;f++){l=e;if(f!==c){l=D.clone(l,true,true);if(s){D.merge(o,R(l,"script"))}}r.call(n[f],l,f)}if(s){u=o[o.length-1].ownerDocument;D.map(o,Ne);for(f=0;f<s;f++){l=o[f];if(Te.test(l.type||"")&&!b.access(l,"globalEval")&&D.contains(u,l)){if(l.src&&(l.type||"").toLowerCase()!=="module"){if(D._evalUrl&&!l.noModule){D._evalUrl(l.src,{nonce:l.nonce||l.getAttribute("nonce")},u)}}else{J(l.textContent.replace(Fe,""),l,u)}}}}}}return n}function We(e,t,n){var i,r=t?D.filter(t,e):e,a=0;for(;(i=r[a])!=null;a++){if(!n&&i.nodeType===1){D.cleanData(R(i))}if(i.parentNode){if(n&&M(i)){Me(R(i,"script"))}i.parentNode.removeChild(i)}}return e}D.extend({htmlPrefilter:function(e){return e},clone:function(e,t,n){var i,r,a,o,s=e.cloneNode(true),l=M(e);if(!v.noCloneChecked&&(e.nodeType===1||e.nodeType===11)&&!D.isXMLDoc(e)){o=R(s);a=R(e);for(i=0,r=a.length;i<r;i++){He(a[i],o[i])}}if(t){if(n){a=a||R(e);o=o||R(s);for(i=0,r=a.length;i<r;i++){qe(a[i],o[i])}}else{qe(e,s)}}o=R(s,"script");if(o.length>0){Me(o,!l&&R(e,"script"))}return s},cleanData:function(e){var t,n,i,r=D.event.special,a=0;for(;(n=e[a])!==undefined;a++){if(S(n)){if(t=n[b.expando]){if(t.events){for(i in t.events){if(r[i]){D.event.remove(n,i)}else{D.removeEvent(n,i,t.handle)}}}n[b.expando]=undefined}if(n[d.expando]){n[d.expando]=undefined}}}}});D.fn.extend({detach:function(e){return We(this,e,true)},remove:function(e){return We(this,e)},text:function(e){return h(this,function(e){return e===undefined?D.text(this):this.empty().each(function(){if(this.nodeType===1||this.nodeType===11||this.nodeType===9){this.textContent=e}})},null,e,arguments.length)},append:function(){return I(this,arguments,function(e){if(this.nodeType===1||this.nodeType===11||this.nodeType===9){var t=Le(this,e);t.appendChild(e)}})},prepend:function(){return I(this,arguments,function(e){if(this.nodeType===1||this.nodeType===11||this.nodeType===9){var t=Le(this,e);t.insertBefore(e,t.firstChild)}})},before:function(){return I(this,arguments,function(e){if(this.parentNode){this.parentNode.insertBefore(e,this)}})},after:function(){return I(this,arguments,function(e){if(this.parentNode){this.parentNode.insertBefore(e,this.nextSibling)}})},empty:function(){var e,t=0;for(;(e=this[t])!=null;t++){if(e.nodeType===1){D.cleanData(R(e,false));e.textContent=""}}return this},clone:function(e,t){e=e==null?false:e;t=t==null?e:t;return this.map(function(){return D.clone(this,e,t)})},html:function(e){return h(this,function(e){var t=this[0]||{},n=0,i=this.length;if(e===undefined&&t.nodeType===1){return t.innerHTML}if(typeof e==="string"&&!Be.test(e)&&!x[(De.exec(e)||["",""])[1].toLowerCase()]){e=D.htmlPrefilter(e);try{for(;n<i;n++){t=this[n]||{};if(t.nodeType===1){D.cleanData(R(t,false));t.innerHTML=e}}t=0}catch(e){}}if(t){this.empty().append(e)}},null,e,arguments.length)},replaceWith:function(){var n=[];return I(this,arguments,function(e){var t=this.parentNode;if(D.inArray(this,n)<0){D.cleanData(R(this));if(t){t.replaceChild(e,this)}}},n)}});D.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(e,o){D.fn[e]=function(e){var t,n=[],i=D(e),r=i.length-1,a=0;for(;a<=r;a++){t=a===r?this:this.clone(true);D(i[a])[o](t);U.apply(n,t.get())}return this.pushStack(n)}});var Ye=new RegExp("^("+Se+")(?!px)[a-z%]+$","i");var Ve=function(e){var t=e.ownerDocument.defaultView;if(!t||!t.opener){t=_}return t.getComputedStyle(e)};var je=function(e,t,n){var i,r,a={};for(r in t){a[r]=e.style[r];e.style[r]=t[r]}i=n.call(e);for(r in t){e.style[r]=a[r]}return i};var Je=new RegExp(P.join("|"),"i");(function(){function e(){if(!u){return}l.style.cssText="position:absolute;left:-11111px;width:60px;"+"margin-top:1px;padding:0;border:0";u.style.cssText="position:relative;display:block;box-sizing:border-box;overflow:scroll;"+"margin:auto;border:1px;padding:1px;"+"width:60%;top:1%";w.appendChild(l).appendChild(u);var e=_.getComputedStyle(u);n=e.top!=="1%";s=t(e.marginLeft)===12;u.style.right="60%";a=t(e.right)===36;i=t(e.width)===36;u.style.position="absolute";r=t(u.offsetWidth/3)===12;w.removeChild(l);u=null}function t(e){return Math.round(parseFloat(e))}var n,i,r,a,o,s,l=C.createElement("div"),u=C.createElement("div");if(!u.style){return}u.style.backgroundClip="content-box";u.cloneNode(true).style.backgroundClip="";v.clearCloneStyle=u.style.backgroundClip==="content-box";D.extend(v,{boxSizingReliable:function(){e();return i},pixelBoxStyles:function(){e();return a},pixelPosition:function(){e();return n},reliableMarginLeft:function(){e();return s},scrollboxSize:function(){e();return r},reliableTrDimensions:function(){var e,t,n,i;if(o==null){e=C.createElement("table");t=C.createElement("tr");n=C.createElement("div");e.style.cssText="position:absolute;left:-11111px;border-collapse:separate";t.style.cssText="border:1px solid";t.style.height="1px";n.style.height="9px";n.style.display="block";w.appendChild(e).appendChild(t).appendChild(n);i=_.getComputedStyle(t);o=parseInt(i.height,10)+parseInt(i.borderTopWidth,10)+parseInt(i.borderBottomWidth,10)===t.offsetHeight;w.removeChild(e)}return o}})})();function Ge(e,t,n){var i,r,a,o,s=e.style;n=n||Ve(e);if(n){o=n.getPropertyValue(t)||n[t];if(o===""&&!M(e)){o=D.style(e,t)}if(!v.pixelBoxStyles()&&Ye.test(o)&&Je.test(t)){i=s.width;r=s.minWidth;a=s.maxWidth;s.minWidth=s.maxWidth=s.width=o;o=n.width;s.width=i;s.minWidth=r;s.maxWidth=a}}return o!==undefined?o+"":o}function Xe(e,t){return{get:function(){if(e()){delete this.get;return}return(this.get=t).apply(this,arguments)}}}var Ke=["Webkit","Moz","ms"],$e=C.createElement("div").style,Ze={};function Qe(e){var t=e[0].toUpperCase()+e.slice(1),n=Ke.length;while(n--){e=Ke[n]+t;if(e in $e){return e}}}function et(e){var t=D.cssProps[e]||Ze[e];if(t){return t}if(e in $e){return e}return Ze[e]=Qe(e)||e}var tt=/^(none|table(?!-c[ea]).+)/,nt=/^--/,it={position:"absolute",visibility:"hidden",display:"block"},rt={letterSpacing:"0",fontWeight:"400"};function at(e,t,n){var i=p.exec(t);return i?Math.max(0,i[2]-(n||0))+(i[3]||"px"):t}function ot(e,t,n,i,r,a){var o=t==="width"?1:0,s=0,l=0;if(n===(i?"border":"content")){return 0}for(;o<4;o+=2){if(n==="margin"){l+=D.css(e,n+P[o],true,r)}if(!i){l+=D.css(e,"padding"+P[o],true,r);if(n!=="padding"){l+=D.css(e,"border"+P[o]+"Width",true,r)}else{s+=D.css(e,"border"+P[o]+"Width",true,r)}}else{if(n==="content"){l-=D.css(e,"padding"+P[o],true,r)}if(n!=="margin"){l-=D.css(e,"border"+P[o]+"Width",true,r)}}}if(!i&&a>=0){l+=Math.max(0,Math.ceil(e["offset"+t[0].toUpperCase()+t.slice(1)]-a-l-s-.5))||0}return l}function st(e,t,n){var i=Ve(e),r=!v.boxSizingReliable()||n,a=r&&D.css(e,"boxSizing",false,i)==="border-box",o=a,s=Ge(e,t,i),l="offset"+t[0].toUpperCase()+t.slice(1);if(Ye.test(s)){if(!n){return s}s="auto"}if((!v.boxSizingReliable()&&a||!v.reliableTrDimensions()&&u(e,"tr")||s==="auto"||!parseFloat(s)&&D.css(e,"display",false,i)==="inline")&&e.getClientRects().length){a=D.css(e,"boxSizing",false,i)==="border-box";o=l in e;if(o){s=e[l]}}s=parseFloat(s)||0;return s+ot(e,t,n||(a?"border":"content"),o,i,s)+"px"}D.extend({cssHooks:{opacity:{get:function(e,t){if(t){var n=Ge(e,"opacity");return n===""?"1":n}}}},cssNumber:{animationIterationCount:true,columnCount:true,fillOpacity:true,flexGrow:true,flexShrink:true,fontWeight:true,gridArea:true,gridColumn:true,gridColumnEnd:true,gridColumnStart:true,gridRow:true,gridRowEnd:true,gridRowStart:true,lineHeight:true,opacity:true,order:true,orphans:true,widows:true,zIndex:true,zoom:true},cssProps:{},style:function(e,t,n,i){if(!e||e.nodeType===3||e.nodeType===8||!e.style){return}var r,a,o,s=c(t),l=nt.test(t),u=e.style;if(!l){t=et(s)}o=D.cssHooks[t]||D.cssHooks[s];if(n!==undefined){a=typeof n;if(a==="string"&&(r=p.exec(n))&&r[1]){n=we(e,t,r);a="number"}if(n==null||n!==n){return}if(a==="number"&&!l){n+=r&&r[3]||(D.cssNumber[s]?"":"px")}if(!v.clearCloneStyle&&n===""&&t.indexOf("background")===0){u[t]="inherit"}if(!o||!("set"in o)||(n=o.set(e,n,i))!==undefined){if(l){u.setProperty(t,n)}else{u[t]=n}}}else{if(o&&"get"in o&&(r=o.get(e,false,i))!==undefined){return r}return u[t]}},css:function(e,t,n,i){var r,a,o,s=c(t),l=nt.test(t);if(!l){t=et(s)}o=D.cssHooks[t]||D.cssHooks[s];if(o&&"get"in o){r=o.get(e,true,n)}if(r===undefined){r=Ge(e,t,i)}if(r==="normal"&&t in rt){r=rt[t]}if(n===""||n){a=parseFloat(r);return n===true||isFinite(a)?a||0:r}return r}});D.each(["height","width"],function(e,u){D.cssHooks[u]={get:function(e,t,n){if(t){return tt.test(D.css(e,"display"))&&(!e.getClientRects().length||!e.getBoundingClientRect().width)?je(e,it,function(){return st(e,u,n)}):st(e,u,n)}},set:function(e,t,n){var i,r=Ve(e),a=!v.scrollboxSize()&&r.position==="absolute",o=a||n,s=o&&D.css(e,"boxSizing",false,r)==="border-box",l=n?ot(e,u,n,s,r):0;if(s&&a){l-=Math.ceil(e["offset"+u[0].toUpperCase()+u.slice(1)]-parseFloat(r[u])-ot(e,u,"border",false,r)-.5)}if(l&&(i=p.exec(t))&&(i[3]||"px")!=="px"){e.style[u]=t;t=D.css(e,u)}return at(e,t,l)}}});D.cssHooks.marginLeft=Xe(v.reliableMarginLeft,function(e,t){if(t){return(parseFloat(Ge(e,"marginLeft"))||e.getBoundingClientRect().left-je(e,{marginLeft:0},function(){return e.getBoundingClientRect().left}))+"px"}});D.each({margin:"",padding:"",border:"Width"},function(r,a){D.cssHooks[r+a]={expand:function(e){var t=0,n={},i=typeof e==="string"?e.split(" "):[e];for(;t<4;t++){n[r+P[t]+a]=i[t]||i[t-2]||i[0]}return n}};if(r!=="margin"){D.cssHooks[r+a].set=at}});D.fn.extend({css:function(e,t){return h(this,function(e,t,n){var i,r,a={},o=0;if(Array.isArray(t)){i=Ve(e);r=t.length;for(;o<r;o++){a[t[o]]=D.css(e,t[o],false,i)}return a}return n!==undefined?D.style(e,t,n):D.css(e,t)},e,t,arguments.length>1)}});function a(e,t,n,i,r){return new a.prototype.init(e,t,n,i,r)}D.Tween=a;a.prototype={constructor:a,init:function(e,t,n,i,r,a){this.elem=e;this.prop=n;this.easing=r||D.easing._default;this.options=t;this.start=this.now=this.cur();this.end=i;this.unit=a||(D.cssNumber[n]?"":"px")},cur:function(){var e=a.propHooks[this.prop];return e&&e.get?e.get(this):a.propHooks._default.get(this)},run:function(e){var t,n=a.propHooks[this.prop];if(this.options.duration){this.pos=t=D.easing[this.easing](e,this.options.duration*e,0,1,this.options.duration)}else{this.pos=t=e}this.now=(this.end-this.start)*t+this.start;if(this.options.step){this.options.step.call(this.elem,this.now,this)}if(n&&n.set){n.set(this)}else{a.propHooks._default.set(this)}return this}};a.prototype.init.prototype=a.prototype;a.propHooks={_default:{get:function(e){var t;if(e.elem.nodeType!==1||e.elem[e.prop]!=null&&e.elem.style[e.prop]==null){return e.elem[e.prop]}t=D.css(e.elem,e.prop,"");return!t||t==="auto"?0:t},set:function(e){if(D.fx.step[e.prop]){D.fx.step[e.prop](e)}else if(e.elem.nodeType===1&&(D.cssHooks[e.prop]||e.elem.style[et(e.prop)]!=null)){D.style(e.elem,e.prop,e.now+e.unit)}else{e.elem[e.prop]=e.now}}}};a.propHooks.scrollTop=a.propHooks.scrollLeft={set:function(e){if(e.elem.nodeType&&e.elem.parentNode){e.elem[e.prop]=e.now}}};D.easing={linear:function(e){return e},swing:function(e){return.5-Math.cos(e*Math.PI)/2},_default:"swing"};D.fx=a.prototype.init;D.fx.step={};var z,lt,ut=/^(?:toggle|show|hide)$/,ft=/queueHooks$/;function ht(){if(lt){if(C.hidden===false&&_.requestAnimationFrame){_.requestAnimationFrame(ht)}else{_.setTimeout(ht,D.fx.interval)}D.fx.tick()}}function ct(){_.setTimeout(function(){z=undefined});return z=Date.now()}function dt(e,t){var n,i=0,r={height:e};t=t?1:0;for(;i<4;i+=2-t){n=P[i];r["margin"+n]=r["padding"+n]=e}if(t){r.opacity=r.width=e}return r}function pt(e,t,n){var i,r=(E.tweeners[t]||[]).concat(E.tweeners["*"]),a=0,o=r.length;for(;a<o;a++){if(i=r[a].call(n,t,e)){return i}}}function vt(e,t,n){var i,r,a,o,s,l,u,f,h="width"in t||"height"in t,c=this,d={},p=e.style,v=e.nodeType&&Pe(e),m=b.get(e,"fxshow");if(!n.queue){o=D._queueHooks(e,"fx");if(o.unqueued==null){o.unqueued=0;s=o.empty.fire;o.empty.fire=function(){if(!o.unqueued){s()}}}o.unqueued++;c.always(function(){c.always(function(){o.unqueued--;if(!D.queue(e,"fx").length){o.empty.fire()}})})}for(i in t){r=t[i];if(ut.test(r)){delete t[i];a=a||r==="toggle";if(r===(v?"hide":"show")){if(r==="show"&&m&&m[i]!==undefined){v=true}else{continue}}d[i]=m&&m[i]||D.style(e,i)}}l=!D.isEmptyObject(t);if(!l&&D.isEmptyObject(d)){return}if(h&&e.nodeType===1){n.overflow=[p.overflow,p.overflowX,p.overflowY];u=m&&m.display;if(u==null){u=b.get(e,"display")}f=D.css(e,"display");if(f==="none"){if(u){f=u}else{k([e],true);u=e.style.display||u;f=D.css(e,"display");k([e])}}if(f==="inline"||f==="inline-block"&&u!=null){if(D.css(e,"float")==="none"){if(!l){c.done(function(){p.display=u});if(u==null){f=p.display;u=f==="none"?"":f}}p.display="inline-block"}}}if(n.overflow){p.overflow="hidden";c.always(function(){p.overflow=n.overflow[0];p.overflowX=n.overflow[1];p.overflowY=n.overflow[2]})}l=false;for(i in d){if(!l){if(m){if("hidden"in m){v=m.hidden}}else{m=b.access(e,"fxshow",{display:u})}if(a){m.hidden=!v}if(v){k([e],true)}c.done(function(){if(!v){k([e])}b.remove(e,"fxshow");for(i in d){D.style(e,i,d[i])}})}l=pt(v?m[i]:0,i,c);if(!(i in m)){m[i]=l.start;if(v){l.end=l.start;l.start=0}}}}function mt(e,t){var n,i,r,a,o;for(n in e){i=c(n);r=t[i];a=e[n];if(Array.isArray(a)){r=a[1];a=e[n]=a[0]}if(n!==i){e[i]=a;delete e[n]}o=D.cssHooks[i];if(o&&"expand"in o){a=o.expand(a);delete e[i];for(n in a){if(!(n in e)){e[n]=a[n];t[n]=r}}}else{t[i]=r}}}function E(o,e,t){var n,s,i=0,r=E.prefilters.length,l=D.Deferred().always(function(){delete a.elem}),a=function(){if(s){return false}var e=z||ct(),t=Math.max(0,u.startTime+u.duration-e),n=t/u.duration||0,i=1-n,r=0,a=u.tweens.length;for(;r<a;r++){u.tweens[r].run(i)}l.notifyWith(o,[u,i,t]);if(i<1&&a){return t}if(!a){l.notifyWith(o,[u,1,0])}l.resolveWith(o,[u]);return false},u=l.promise({elem:o,props:D.extend({},e),opts:D.extend(true,{specialEasing:{},easing:D.easing._default},t),originalProperties:e,originalOptions:t,startTime:z||ct(),duration:t.duration,tweens:[],createTween:function(e,t){var n=D.Tween(o,u.opts,e,t,u.opts.specialEasing[e]||u.opts.easing);u.tweens.push(n);return n},stop:function(e){var t=0,n=e?u.tweens.length:0;if(s){return this}s=true;for(;t<n;t++){u.tweens[t].run(1)}if(e){l.notifyWith(o,[u,1,0]);l.resolveWith(o,[u,e])}else{l.rejectWith(o,[u,e])}return this}}),f=u.props;mt(f,u.opts.specialEasing);for(;i<r;i++){n=E.prefilters[i].call(u,o,f,u.opts);if(n){if(m(n.stop)){D._queueHooks(u.elem,u.opts.queue).stop=n.stop.bind(n)}return n}}D.map(f,pt,u);if(m(u.opts.start)){u.opts.start.call(o,u)}u.progress(u.opts.progress).done(u.opts.done,u.opts.complete).fail(u.opts.fail).always(u.opts.always);D.fx.timer(D.extend(a,{elem:o,anim:u,queue:u.opts.queue}));return u}D.Animation=D.extend(E,{tweeners:{"*":[function(e,t){var n=this.createTween(e,t);we(n.elem,e,p.exec(t),n);return n}]},tweener:function(e,t){if(m(e)){t=e;e=["*"]}else{e=e.match(T)}var n,i=0,r=e.length;for(;i<r;i++){n=e[i];E.tweeners[n]=E.tweeners[n]||[];E.tweeners[n].unshift(t)}},prefilters:[vt],prefilter:function(e,t){if(t){E.prefilters.unshift(e)}else{E.prefilters.push(e)}}});D.speed=function(e,t,n){var i=e&&typeof e==="object"?D.extend({},e):{complete:n||!n&&t||m(e)&&e,duration:e,easing:n&&t||t&&!m(t)&&t};if(D.fx.off){i.duration=0}else{if(typeof i.duration!=="number"){if(i.duration in D.fx.speeds){i.duration=D.fx.speeds[i.duration]}else{i.duration=D.fx.speeds._default}}}if(i.queue==null||i.queue===true){i.queue="fx"}i.old=i.complete;i.complete=function(){if(m(i.old)){i.old.call(this)}if(i.queue){D.dequeue(this,i.queue)}};return i};D.fn.extend({fadeTo:function(e,t,n,i){return this.filter(Pe).css("opacity",0).show().end().animate({opacity:t},e,n,i)},animate:function(t,e,n,i){var r=D.isEmptyObject(t),a=D.speed(e,n,i),o=function(){var e=E(this,D.extend({},t),a);if(r||b.get(this,"finish")){e.stop(true)}};o.finish=o;return r||a.queue===false?this.each(o):this.queue(a.queue,o)},stop:function(r,e,a){var o=function(e){var t=e.stop;delete e.stop;t(a)};if(typeof r!=="string"){a=e;e=r;r=undefined}if(e){this.queue(r||"fx",[])}return this.each(function(){var e=true,t=r!=null&&r+"queueHooks",n=D.timers,i=b.get(this);if(t){if(i[t]&&i[t].stop){o(i[t])}}else{for(t in i){if(i[t]&&i[t].stop&&ft.test(t)){o(i[t])}}}for(t=n.length;t--;){if(n[t].elem===this&&(r==null||n[t].queue===r)){n[t].anim.stop(a);e=false;n.splice(t,1)}}if(e||!a){D.dequeue(this,r)}})},finish:function(o){if(o!==false){o=o||"fx"}return this.each(function(){var e,t=b.get(this),n=t[o+"queue"],i=t[o+"queueHooks"],r=D.timers,a=n?n.length:0;t.finish=true;D.queue(this,o,[]);if(i&&i.stop){i.stop.call(this,true)}for(e=r.length;e--;){if(r[e].elem===this&&r[e].queue===o){r[e].anim.stop(true);r.splice(e,1)}}for(e=0;e<a;e++){if(n[e]&&n[e].finish){n[e].finish.call(this)}}delete t.finish})}});D.each(["toggle","show","hide"],function(e,i){var r=D.fn[i];D.fn[i]=function(e,t,n){return e==null||typeof e==="boolean"?r.apply(this,arguments):this.animate(dt(i,true),e,t,n)}});D.each({slideDown:dt("show"),slideUp:dt("hide"),slideToggle:dt("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(e,i){D.fn[e]=function(e,t,n){return this.animate(i,e,t,n)}});D.timers=[];D.fx.tick=function(){var e,t=0,n=D.timers;z=Date.now();for(;t<n.length;t++){e=n[t];if(!e()&&n[t]===e){n.splice(t--,1)}}if(!n.length){D.fx.stop()}z=undefined};D.fx.timer=function(e){D.timers.push(e);D.fx.start()};D.fx.interval=13;D.fx.start=function(){if(lt){return}lt=true;ht()};D.fx.stop=function(){lt=null};D.fx.speeds={slow:600,fast:200,_default:400};D.fn.delay=function(i,e){i=D.fx?D.fx.speeds[i]||i:i;e=e||"fx";return this.queue(e,function(e,t){var n=_.setTimeout(e,i);t.stop=function(){_.clearTimeout(n)}})};(function(){var e=C.createElement("input"),t=C.createElement("select"),n=t.appendChild(C.createElement("option"));e.type="checkbox";v.checkOn=e.value!=="";v.optSelected=n.selected;e=C.createElement("input");e.value="t";e.type="radio";v.radioValue=e.value==="t"})();var yt,gt=D.expr.attrHandle;D.fn.extend({attr:function(e,t){return h(this,D.attr,e,t,arguments.length>1)},removeAttr:function(e){return this.each(function(){D.removeAttr(this,e)})}});D.extend({attr:function(e,t,n){var i,r,a=e.nodeType;if(a===3||a===8||a===2){return}if(typeof e.getAttribute==="undefined"){return D.prop(e,t,n)}if(a!==1||!D.isXMLDoc(e)){r=D.attrHooks[t.toLowerCase()]||(D.expr.match.bool.test(t)?yt:undefined)}if(n!==undefined){if(n===null){D.removeAttr(e,t);return}if(r&&"set"in r&&(i=r.set(e,n,t))!==undefined){return i}e.setAttribute(t,n+"");return n}if(r&&"get"in r&&(i=r.get(e,t))!==null){return i}i=D.find.attr(e,t);return i==null?undefined:i},attrHooks:{type:{set:function(e,t){if(!v.radioValue&&t==="radio"&&u(e,"input")){var n=e.value;e.setAttribute("type",t);if(n){e.value=n}return t}}}},removeAttr:function(e,t){var n,i=0,r=t&&t.match(T);if(r&&e.nodeType===1){while(n=r[i++]){e.removeAttribute(n)}}}});yt={set:function(e,t,n){if(t===false){D.removeAttr(e,n)}else{e.setAttribute(n,n)}return n}};D.each(D.expr.match.bool.source.match(/\w+/g),function(e,t){var o=gt[t]||D.find.attr;gt[t]=function(e,t,n){var i,r,a=t.toLowerCase();if(!n){r=gt[a];gt[a]=i;i=o(e,t,n)!=null?a:null;gt[a]=r}return i}});var St=/^(?:input|select|textarea|button)$/i,bt=/^(?:a|area)$/i;D.fn.extend({prop:function(e,t){return h(this,D.prop,e,t,arguments.length>1)},removeProp:function(e){return this.each(function(){delete this[D.propFix[e]||e]})}});D.extend({prop:function(e,t,n){var i,r,a=e.nodeType;if(a===3||a===8||a===2){return}if(a!==1||!D.isXMLDoc(e)){t=D.propFix[t]||t;r=D.propHooks[t]}if(n!==undefined){if(r&&"set"in r&&(i=r.set(e,n,t))!==undefined){return i}return e[t]=n}if(r&&"get"in r&&(i=r.get(e,t))!==null){return i}return e[t]},propHooks:{tabIndex:{get:function(e){var t=D.find.attr(e,"tabindex");if(t){return parseInt(t,10)}if(St.test(e.nodeName)||bt.test(e.nodeName)&&e.href){return 0}return-1}}},propFix:{for:"htmlFor",class:"className"}});if(!v.optSelected){D.propHooks.selected={get:function(e){var t=e.parentNode;if(t&&t.parentNode){t.parentNode.selectedIndex}return null},set:function(e){var t=e.parentNode;if(t){t.selectedIndex;if(t.parentNode){t.parentNode.selectedIndex}}}}}D.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){D.propFix[this.toLowerCase()]=this});function A(e){var t=e.match(T)||[];return t.join(" ")}function B(e){return e.getAttribute&&e.getAttribute("class")||""}function Pt(e){if(Array.isArray(e)){return e}if(typeof e==="string"){return e.match(T)||[]}return[]}D.fn.extend({addClass:function(t){var e,n,i,r,a,o,s,l=0;if(m(t)){return this.each(function(e){D(this).addClass(t.call(this,e,B(this)))})}e=Pt(t);if(e.length){while(n=this[l++]){r=B(n);i=n.nodeType===1&&" "+A(r)+" ";if(i){o=0;while(a=e[o++]){if(i.indexOf(" "+a+" ")<0){i+=a+" "}}s=A(i);if(r!==s){n.setAttribute("class",s)}}}}return this},removeClass:function(t){var e,n,i,r,a,o,s,l=0;if(m(t)){return this.each(function(e){D(this).removeClass(t.call(this,e,B(this)))})}if(!arguments.length){return this.attr("class","")}e=Pt(t);if(e.length){while(n=this[l++]){r=B(n);i=n.nodeType===1&&" "+A(r)+" ";if(i){o=0;while(a=e[o++]){while(i.indexOf(" "+a+" ")>-1){i=i.replace(" "+a+" "," ")}}s=A(i);if(r!==s){n.setAttribute("class",s)}}}}return this},toggleClass:function(r,t){var a=typeof r,o=a==="string"||Array.isArray(r);if(typeof t==="boolean"&&o){return t?this.addClass(r):this.removeClass(r)}if(m(r)){return this.each(function(e){D(this).toggleClass(r.call(this,e,B(this),t),t)})}return this.each(function(){var e,t,n,i;if(o){t=0;n=D(this);i=Pt(r);while(e=i[t++]){if(n.hasClass(e)){n.removeClass(e)}else{n.addClass(e)}}}else if(r===undefined||a==="boolean"){e=B(this);if(e){b.set(this,"__className__",e)}if(this.setAttribute){this.setAttribute("class",e||r===false?"":b.get(this,"__className__")||"")}}})},hasClass:function(e){var t,n,i=0;t=" "+e+" ";while(n=this[i++]){if(n.nodeType===1&&(" "+A(B(n))+" ").indexOf(t)>-1){return true}}return false}});var wt=/\r/g;D.fn.extend({val:function(n){var i,e,r,t=this[0];if(!arguments.length){if(t){i=D.valHooks[t.type]||D.valHooks[t.nodeName.toLowerCase()];if(i&&"get"in i&&(e=i.get(t,"value"))!==undefined){return e}e=t.value;if(typeof e==="string"){return e.replace(wt,"")}return e==null?"":e}return}r=m(n);return this.each(function(e){var t;if(this.nodeType!==1){return}if(r){t=n.call(this,e,D(this).val())}else{t=n}if(t==null){t=""}else if(typeof t==="number"){t+=""}else if(Array.isArray(t)){t=D.map(t,function(e){return e==null?"":e+""})}i=D.valHooks[this.type]||D.valHooks[this.nodeName.toLowerCase()];if(!i||!("set"in i)||i.set(this,t,"value")===undefined){this.value=t}})}});D.extend({valHooks:{option:{get:function(e){var t=D.find.attr(e,"value");return t!=null?t:A(D.text(e))}},select:{get:function(e){var t,n,i,r=e.options,a=e.selectedIndex,o=e.type==="select-one",s=o?null:[],l=o?a+1:r.length;if(a<0){i=l}else{i=o?a:0}for(;i<l;i++){n=r[i];if((n.selected||i===a)&&!n.disabled&&(!n.parentNode.disabled||!u(n.parentNode,"optgroup"))){t=D(n).val();if(o){return t}s.push(t)}}return s},set:function(e,t){var n,i,r=e.options,a=D.makeArray(t),o=r.length;while(o--){i=r[o];if(i.selected=D.inArray(D.valHooks.option.get(i),a)>-1){n=true}}if(!n){e.selectedIndex=-1}return a}}}});D.each(["radio","checkbox"],function(){D.valHooks[this]={set:function(e,t){if(Array.isArray(t)){return e.checked=D.inArray(D(e).val(),t)>-1}}};if(!v.checkOn){D.valHooks[this].get=function(e){return e.getAttribute("value")===null?"on":e.value}}});v.focusin="onfocusin"in _;var _t=/^(?:focusinfocus|focusoutblur)$/,Ct=function(e){e.stopPropagation()};D.extend(D.event,{trigger:function(e,t,n,i){var r,a,o,s,l,u,f,h,c=[n||C],d=W.call(e,"type")?e.type:e,p=W.call(e,"namespace")?e.namespace.split("."):[];a=h=o=n=n||C;if(n.nodeType===3||n.nodeType===8){return}if(_t.test(d+D.event.triggered)){return}if(d.indexOf(".")>-1){p=d.split(".");d=p.shift();p.sort()}l=d.indexOf(":")<0&&"on"+d;e=e[D.expando]?e:new D.Event(d,typeof e==="object"&&e);e.isTrigger=i?2:3;e.namespace=p.join(".");e.rnamespace=e.namespace?new RegExp("(^|\\.)"+p.join("\\.(?:.*\\.|)")+"(\\.|$)"):null;e.result=undefined;if(!e.target){e.target=n}t=t==null?[e]:D.makeArray(t,[e]);f=D.event.special[d]||{};if(!i&&f.trigger&&f.trigger.apply(n,t)===false){return}if(!i&&!f.noBubble&&!y(n)){s=f.delegateType||d;if(!_t.test(s+d)){a=a.parentNode}for(;a;a=a.parentNode){c.push(a);o=a}if(o===(n.ownerDocument||C)){c.push(o.defaultView||o.parentWindow||_)}}r=0;while((a=c[r++])&&!e.isPropagationStopped()){h=a;e.type=r>1?s:f.bindType||d;u=(b.get(a,"events")||Object.create(null))[e.type]&&b.get(a,"handle");if(u){u.apply(a,t)}u=l&&a[l];if(u&&u.apply&&S(a)){e.result=u.apply(a,t);if(e.result===false){e.preventDefault()}}}e.type=d;if(!i&&!e.isDefaultPrevented()){if((!f._default||f._default.apply(c.pop(),t)===false)&&S(n)){if(l&&m(n[d])&&!y(n)){o=n[l];if(o){n[l]=null}D.event.triggered=d;if(e.isPropagationStopped()){h.addEventListener(d,Ct)}n[d]();if(e.isPropagationStopped()){h.removeEventListener(d,Ct)}D.event.triggered=undefined;if(o){n[l]=o}}}}return e.result},simulate:function(e,t,n){var i=D.extend(new D.Event,n,{type:e,isSimulated:true});D.event.trigger(i,null,t)}});D.fn.extend({trigger:function(e,t){return this.each(function(){D.event.trigger(e,t,this)})},triggerHandler:function(e,t){var n=this[0];if(n){return D.event.trigger(e,t,n,true)}}});if(!v.focusin){D.each({focus:"focusin",blur:"focusout"},function(n,i){var r=function(e){D.event.simulate(i,e.target,D.event.fix(e))};D.event.special[i]={setup:function(){var e=this.ownerDocument||this.document||this,t=b.access(e,i);if(!t){e.addEventListener(n,r,true)}b.access(e,i,(t||0)+1)},teardown:function(){var e=this.ownerDocument||this.document||this,t=b.access(e,i)-1;if(!t){e.removeEventListener(n,r,true);b.remove(e,i)}else{b.access(e,i,t)}}}})}var Dt=_.location;var Tt={guid:Date.now()};var Mt=/\?/;D.parseXML=function(e){var t,n;if(!e||typeof e!=="string"){return null}try{t=(new _.DOMParser).parseFromString(e,"text/xml")}catch(e){}n=t&&t.getElementsByTagName("parsererror")[0];if(!t||n){D.error("Invalid XML: "+(n?D.map(n.childNodes,function(e){return e.textContent}).join("\n"):e))}return t};var kt=/\[\]$/,xt=/\r?\n/g,Rt=/^(?:submit|button|image|reset|file)$/i,It=/^(?:input|select|textarea|keygen)/i;function zt(n,e,i,r){var t;if(Array.isArray(e)){D.each(e,function(e,t){if(i||kt.test(n)){r(n,t)}else{zt(n+"["+(typeof t==="object"&&t!=null?e:"")+"]",t,i,r)}})}else if(!i&&g(e)==="object"){for(t in e){zt(n+"["+t+"]",e[t],i,r)}}else{r(n,e)}}D.param=function(e,t){var n,i=[],r=function(e,t){var n=m(t)?t():t;i[i.length]=encodeURIComponent(e)+"="+encodeURIComponent(n==null?"":n)};if(e==null){return""}if(Array.isArray(e)||e.jquery&&!D.isPlainObject(e)){D.each(e,function(){r(this.name,this.value)})}else{for(n in e){zt(n,e[n],t,r)}}return i.join("&")};D.fn.extend({serialize:function(){return D.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var e=D.prop(this,"elements");return e?D.makeArray(e):this}).filter(function(){var e=this.type;return this.name&&!D(this).is(":disabled")&&It.test(this.nodeName)&&!Rt.test(e)&&(this.checked||!r.test(e))}).map(function(e,t){var n=D(this).val();if(n==null){return null}if(Array.isArray(n)){return D.map(n,function(e){return{name:t.name,value:e.replace(xt,"\r\n")}})}return{name:t.name,value:n.replace(xt,"\r\n")}}).get()}});var Et=/%20/g,At=/#.*$/,Bt=/([?&])_=[^&]*/,Ot=/^(.*?):[ \t]*([^\r\n]*)$/gm,Ft=/^(?:about|app|app-storage|.+-extension|file|res|widget):$/,Lt=/^(?:GET|HEAD)$/,Ut=/^\/\//,Nt={},qt={},Ht="*/".concat("*"),Wt=C.createElement("a");Wt.href=Dt.href;function Yt(a){return function(e,t){if(typeof e!=="string"){t=e;e="*"}var n,i=0,r=e.toLowerCase().match(T)||[];if(m(t)){while(n=r[i++]){if(n[0]==="+"){n=n.slice(1)||"*";(a[n]=a[n]||[]).unshift(t)}else{(a[n]=a[n]||[]).push(t)}}}}}function Vt(t,r,a,o){var s={},l=t===qt;function u(e){var i;s[e]=true;D.each(t[e]||[],function(e,t){var n=t(r,a,o);if(typeof n==="string"&&!l&&!s[n]){r.dataTypes.unshift(n);u(n);return false}else if(l){return!(i=n)}});return i}return u(r.dataTypes[0])||!s["*"]&&u("*")}function jt(e,t){var n,i,r=D.ajaxSettings.flatOptions||{};for(n in t){if(t[n]!==undefined){(r[n]?e:i||(i={}))[n]=t[n]}}if(i){D.extend(true,e,i)}return e}function Jt(e,t,n){var i,r,a,o,s=e.contents,l=e.dataTypes;while(l[0]==="*"){l.shift();if(i===undefined){i=e.mimeType||t.getResponseHeader("Content-Type")}}if(i){for(r in s){if(s[r]&&s[r].test(i)){l.unshift(r);break}}}if(l[0]in n){a=l[0]}else{for(r in n){if(!l[0]||e.converters[r+" "+l[0]]){a=r;break}if(!o){o=r}}a=a||o}if(a){if(a!==l[0]){l.unshift(a)}return n[a]}}function Gt(e,t,n,i){var r,a,o,s,l,u={},f=e.dataTypes.slice();if(f[1]){for(o in e.converters){u[o.toLowerCase()]=e.converters[o]}}a=f.shift();while(a){if(e.responseFields[a]){n[e.responseFields[a]]=t}if(!l&&i&&e.dataFilter){t=e.dataFilter(t,e.dataType)}l=a;a=f.shift();if(a){if(a==="*"){a=l}else if(l!=="*"&&l!==a){o=u[l+" "+a]||u["* "+a];if(!o){for(r in u){s=r.split(" ");if(s[1]===a){o=u[l+" "+s[0]]||u["* "+s[0]];if(o){if(o===true){o=u[r]}else if(u[r]!==true){a=s[0];f.unshift(s[1])}break}}}}if(o!==true){if(o&&e.throws){t=o(t)}else{try{t=o(t)}catch(e){return{state:"parsererror",error:o?e:"No conversion from "+l+" to "+a}}}}}}}return{state:"success",data:t}}D.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:Dt.href,type:"GET",isLocal:Ft.test(Dt.protocol),global:true,processData:true,async:true,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":Ht,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":true,"text json":JSON.parse,"text xml":D.parseXML},flatOptions:{url:true,context:true}},ajaxSetup:function(e,t){return t?jt(jt(e,D.ajaxSettings),t):jt(D.ajaxSettings,e)},ajaxPrefilter:Yt(Nt),ajaxTransport:Yt(qt),ajax:function(e,t){if(typeof e==="object"){t=e;e=undefined}t=t||{};var f,h,c,n,d,i,p,v,r,a,m=D.ajaxSetup({},t),y=m.context||m,g=m.context&&(y.nodeType||y.jquery)?D(y):D.event,S=D.Deferred(),b=D.Callbacks("once memory"),P=m.statusCode||{},o={},s={},l="canceled",w={readyState:0,getResponseHeader:function(e){var t;if(p){if(!n){n={};while(t=Ot.exec(c)){n[t[1].toLowerCase()+" "]=(n[t[1].toLowerCase()+" "]||[]).concat(t[2])}}t=n[e.toLowerCase()+" "]}return t==null?null:t.join(", ")},getAllResponseHeaders:function(){return p?c:null},setRequestHeader:function(e,t){if(p==null){e=s[e.toLowerCase()]=s[e.toLowerCase()]||e;o[e]=t}return this},overrideMimeType:function(e){if(p==null){m.mimeType=e}return this},statusCode:function(e){var t;if(e){if(p){w.always(e[w.status])}else{for(t in e){P[t]=[P[t],e[t]]}}}return this},abort:function(e){var t=e||l;if(f){f.abort(t)}u(0,t);return this}};S.promise(w);m.url=((e||m.url||Dt.href)+"").replace(Ut,Dt.protocol+"//");m.type=t.method||t.type||m.method||m.type;m.dataTypes=(m.dataType||"*").toLowerCase().match(T)||[""];if(m.crossDomain==null){i=C.createElement("a");try{i.href=m.url;i.href=i.href;m.crossDomain=Wt.protocol+"//"+Wt.host!==i.protocol+"//"+i.host}catch(e){m.crossDomain=true}}if(m.data&&m.processData&&typeof m.data!=="string"){m.data=D.param(m.data,m.traditional)}Vt(Nt,m,t,w);if(p){return w}v=D.event&&m.global;if(v&&D.active++===0){D.event.trigger("ajaxStart")}m.type=m.type.toUpperCase();m.hasContent=!Lt.test(m.type);h=m.url.replace(At,"");if(!m.hasContent){a=m.url.slice(h.length);if(m.data&&(m.processData||typeof m.data==="string")){h+=(Mt.test(h)?"&":"?")+m.data;delete m.data}if(m.cache===false){h=h.replace(Bt,"$1");a=(Mt.test(h)?"&":"?")+"_="+Tt.guid+++a}m.url=h+a}else if(m.data&&m.processData&&(m.contentType||"").indexOf("application/x-www-form-urlencoded")===0){m.data=m.data.replace(Et,"+")}if(m.ifModified){if(D.lastModified[h]){w.setRequestHeader("If-Modified-Since",D.lastModified[h])}if(D.etag[h]){w.setRequestHeader("If-None-Match",D.etag[h])}}if(m.data&&m.hasContent&&m.contentType!==false||t.contentType){w.setRequestHeader("Content-Type",m.contentType)}w.setRequestHeader("Accept",m.dataTypes[0]&&m.accepts[m.dataTypes[0]]?m.accepts[m.dataTypes[0]]+(m.dataTypes[0]!=="*"?", "+Ht+"; q=0.01":""):m.accepts["*"]);for(r in m.headers){w.setRequestHeader(r,m.headers[r])}if(m.beforeSend&&(m.beforeSend.call(y,w,m)===false||p)){return w.abort()}l="abort";b.add(m.complete);w.done(m.success);w.fail(m.error);f=Vt(qt,m,t,w);if(!f){u(-1,"No Transport")}else{w.readyState=1;if(v){g.trigger("ajaxSend",[w,m])}if(p){return w}if(m.async&&m.timeout>0){d=_.setTimeout(function(){w.abort("timeout")},m.timeout)}try{p=false;f.send(o,u)}catch(e){if(p){throw e}u(-1,e)}}function u(e,t,n,i){var r,a,o,s,l,u=t;if(p){return}p=true;if(d){_.clearTimeout(d)}f=undefined;c=i||"";w.readyState=e>0?4:0;r=e>=200&&e<300||e===304;if(n){s=Jt(m,w,n)}if(!r&&D.inArray("script",m.dataTypes)>-1&&D.inArray("json",m.dataTypes)<0){m.converters["text script"]=function(){}}s=Gt(m,s,w,r);if(r){if(m.ifModified){l=w.getResponseHeader("Last-Modified");if(l){D.lastModified[h]=l}l=w.getResponseHeader("etag");if(l){D.etag[h]=l}}if(e===204||m.type==="HEAD"){u="nocontent"}else if(e===304){u="notmodified"}else{u=s.state;a=s.data;o=s.error;r=!o}}else{o=u;if(e||!u){u="error";if(e<0){e=0}}}w.status=e;w.statusText=(t||u)+"";if(r){S.resolveWith(y,[a,u,w])}else{S.rejectWith(y,[w,u,o])}w.statusCode(P);P=undefined;if(v){g.trigger(r?"ajaxSuccess":"ajaxError",[w,m,r?a:o])}b.fireWith(y,[w,u]);if(v){g.trigger("ajaxComplete",[w,m]);if(!--D.active){D.event.trigger("ajaxStop")}}}return w},getJSON:function(e,t,n){return D.get(e,t,n,"json")},getScript:function(e,t){return D.get(e,undefined,t,"script")}});D.each(["get","post"],function(e,r){D[r]=function(e,t,n,i){if(m(t)){i=i||n;n=t;t=undefined}return D.ajax(D.extend({url:e,type:r,dataType:i,data:t,success:n},D.isPlainObject(e)&&e))}});D.ajaxPrefilter(function(e){var t;for(t in e.headers){if(t.toLowerCase()==="content-type"){e.contentType=e.headers[t]||""}}});D._evalUrl=function(e,t,n){return D.ajax({url:e,type:"GET",dataType:"script",cache:true,async:false,global:false,converters:{"text script":function(){}},dataFilter:function(e){D.globalEval(e,t,n)}})};D.fn.extend({wrapAll:function(e){var t;if(this[0]){if(m(e)){e=e.call(this[0])}t=D(e,this[0].ownerDocument).eq(0).clone(true);if(this[0].parentNode){t.insertBefore(this[0])}t.map(function(){var e=this;while(e.firstElementChild){e=e.firstElementChild}return e}).append(this)}return this},wrapInner:function(n){if(m(n)){return this.each(function(e){D(this).wrapInner(n.call(this,e))})}return this.each(function(){var e=D(this),t=e.contents();if(t.length){t.wrapAll(n)}else{e.append(n)}})},wrap:function(t){var n=m(t);return this.each(function(e){D(this).wrapAll(n?t.call(this,e):t)})},unwrap:function(e){this.parent(e).not("body").each(function(){D(this).replaceWith(this.childNodes)});return this}});D.expr.pseudos.hidden=function(e){return!D.expr.pseudos.visible(e)};D.expr.pseudos.visible=function(e){return!!(e.offsetWidth||e.offsetHeight||e.getClientRects().length)};D.ajaxSettings.xhr=function(){try{return new _.XMLHttpRequest}catch(e){}};var Xt={0:200,1223:204},Kt=D.ajaxSettings.xhr();v.cors=!!Kt&&"withCredentials"in Kt;v.ajax=Kt=!!Kt;D.ajaxTransport(function(r){var a,o;if(v.cors||Kt&&!r.crossDomain){return{send:function(e,t){var n,i=r.xhr();i.open(r.type,r.url,r.async,r.username,r.password);if(r.xhrFields){for(n in r.xhrFields){i[n]=r.xhrFields[n]}}if(r.mimeType&&i.overrideMimeType){i.overrideMimeType(r.mimeType)}if(!r.crossDomain&&!e["X-Requested-With"]){e["X-Requested-With"]="XMLHttpRequest"}for(n in e){i.setRequestHeader(n,e[n])}a=function(e){return function(){if(a){a=o=i.onload=i.onerror=i.onabort=i.ontimeout=i.onreadystatechange=null;if(e==="abort"){i.abort()}else if(e==="error"){if(typeof i.status!=="number"){t(0,"error")}else{t(i.status,i.statusText)}}else{t(Xt[i.status]||i.status,i.statusText,(i.responseType||"text")!=="text"||typeof i.responseText!=="string"?{binary:i.response}:{text:i.responseText},i.getAllResponseHeaders())}}}};i.onload=a();o=i.onerror=i.ontimeout=a("error");if(i.onabort!==undefined){i.onabort=o}else{i.onreadystatechange=function(){if(i.readyState===4){_.setTimeout(function(){if(a){o()}})}}}a=a("abort");try{i.send(r.hasContent&&r.data||null)}catch(e){if(a){throw e}}},abort:function(){if(a){a()}}}}});D.ajaxPrefilter(function(e){if(e.crossDomain){e.contents.script=false}});D.ajaxSetup({accepts:{script:"text/javascript, application/javascript, "+"application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(e){D.globalEval(e);return e}}});D.ajaxPrefilter("script",function(e){if(e.cache===undefined){e.cache=false}if(e.crossDomain){e.type="GET"}});D.ajaxTransport("script",function(n){if(n.crossDomain||n.scriptAttrs){var i,r;return{send:function(e,t){i=D("<script>").attr(n.scriptAttrs||{}).prop({charset:n.scriptCharset,src:n.url}).on("load error",r=function(e){i.remove();r=null;if(e){t(e.type==="error"?404:200,e.type)}});C.head.appendChild(i[0])},abort:function(){if(r){r()}}}}});var $t=[],Zt=/(=)\?(?=&|$)|\?\?/;D.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var e=$t.pop()||D.expando+"_"+Tt.guid++;this[e]=true;return e}});D.ajaxPrefilter("json jsonp",function(e,t,n){var i,r,a,o=e.jsonp!==false&&(Zt.test(e.url)?"url":typeof e.data==="string"&&(e.contentType||"").indexOf("application/x-www-form-urlencoded")===0&&Zt.test(e.data)&&"data");if(o||e.dataTypes[0]==="jsonp"){i=e.jsonpCallback=m(e.jsonpCallback)?e.jsonpCallback():e.jsonpCallback;if(o){e[o]=e[o].replace(Zt,"$1"+i)}else if(e.jsonp!==false){e.url+=(Mt.test(e.url)?"&":"?")+e.jsonp+"="+i}e.converters["script json"]=function(){if(!a){D.error(i+" was not called")}return a[0]};e.dataTypes[0]="json";r=_[i];_[i]=function(){a=arguments};n.always(function(){if(r===undefined){D(_).removeProp(i)}else{_[i]=r}if(e[i]){e.jsonpCallback=t.jsonpCallback;$t.push(i)}if(a&&m(r)){r(a[0])}a=r=undefined});return"script"}});v.createHTMLDocument=function(){var e=C.implementation.createHTMLDocument("").body;e.innerHTML="<form></form><form></form>";return e.childNodes.length===2}();D.parseHTML=function(e,t,n){if(typeof e!=="string"){return[]}if(typeof t==="boolean"){n=t;t=false}var i,r,a;if(!t){if(v.createHTMLDocument){t=C.implementation.createHTMLDocument("");i=t.createElement("base");i.href=C.location.href;t.head.appendChild(i)}else{t=C}}r=Z.exec(e);a=!n&&[];if(r){return[t.createElement(r[1])]}r=xe([e],t,a);if(a&&a.length){D(a).remove()}return D.merge([],r.childNodes)};D.fn.load=function(e,t,n){var i,r,a,o=this,s=e.indexOf(" ");if(s>-1){i=A(e.slice(s));e=e.slice(0,s)}if(m(t)){n=t;t=undefined}else if(t&&typeof t==="object"){r="POST"}if(o.length>0){D.ajax({url:e,type:r||"GET",dataType:"html",data:t}).done(function(e){a=arguments;o.html(i?D("<div>").append(D.parseHTML(e)).find(i):e)}).always(n&&function(e,t){o.each(function(){n.apply(this,a||[e.responseText,t,e])})})}return this};D.expr.pseudos.animated=function(t){return D.grep(D.timers,function(e){return t===e.elem}).length};D.offset={setOffset:function(e,t,n){var i,r,a,o,s,l,u,f=D.css(e,"position"),h=D(e),c={};if(f==="static"){e.style.position="relative"}s=h.offset();a=D.css(e,"top");l=D.css(e,"left");u=(f==="absolute"||f==="fixed")&&(a+l).indexOf("auto")>-1;if(u){i=h.position();o=i.top;r=i.left}else{o=parseFloat(a)||0;r=parseFloat(l)||0}if(m(t)){t=t.call(e,n,D.extend({},s))}if(t.top!=null){c.top=t.top-s.top+o}if(t.left!=null){c.left=t.left-s.left+r}if("using"in t){t.using.call(e,c)}else{h.css(c)}}};D.fn.extend({offset:function(t){if(arguments.length){return t===undefined?this:this.each(function(e){D.offset.setOffset(this,t,e)})}var e,n,i=this[0];if(!i){return}if(!i.getClientRects().length){return{top:0,left:0}}e=i.getBoundingClientRect();n=i.ownerDocument.defaultView;return{top:e.top+n.pageYOffset,left:e.left+n.pageXOffset}},position:function(){if(!this[0]){return}var e,t,n,i=this[0],r={top:0,left:0};if(D.css(i,"position")==="fixed"){t=i.getBoundingClientRect()}else{t=this.offset();n=i.ownerDocument;e=i.offsetParent||n.documentElement;while(e&&(e===n.body||e===n.documentElement)&&D.css(e,"position")==="static"){e=e.parentNode}if(e&&e!==i&&e.nodeType===1){r=D(e).offset();r.top+=D.css(e,"borderTopWidth",true);r.left+=D.css(e,"borderLeftWidth",true)}}return{top:t.top-r.top-D.css(i,"marginTop",true),left:t.left-r.left-D.css(i,"marginLeft",true)}},offsetParent:function(){return this.map(function(){var e=this.offsetParent;while(e&&D.css(e,"position")==="static"){e=e.offsetParent}return e||w})}});D.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(t,r){var a="pageYOffset"===r;D.fn[t]=function(e){return h(this,function(e,t,n){var i;if(y(e)){i=e}else if(e.nodeType===9){i=e.defaultView}if(n===undefined){return i?i[r]:e[t]}if(i){i.scrollTo(!a?n:i.pageXOffset,a?n:i.pageYOffset)}else{e[t]=n}},t,e,arguments.length)}});D.each(["top","left"],function(e,n){D.cssHooks[n]=Xe(v.pixelPosition,function(e,t){if(t){t=Ge(e,n);return Ye.test(t)?D(e).position()[n]+"px":t}})});D.each({Height:"height",Width:"width"},function(o,s){D.each({padding:"inner"+o,content:s,"":"outer"+o},function(i,a){D.fn[a]=function(e,t){var n=arguments.length&&(i||typeof e!=="boolean"),r=i||(e===true||t===true?"margin":"border");return h(this,function(e,t,n){var i;if(y(e)){return a.indexOf("outer")===0?e["inner"+o]:e.document.documentElement["client"+o]}if(e.nodeType===9){i=e.documentElement;return Math.max(e.body["scroll"+o],i["scroll"+o],e.body["offset"+o],i["offset"+o],i["client"+o])}return n===undefined?D.css(e,t,r):D.style(e,t,n,r)},s,n?e:undefined,n)}})});D.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(e,t){D.fn[t]=function(e){return this.on(t,e)}});D.fn.extend({bind:function(e,t,n){return this.on(e,null,t,n)},unbind:function(e,t){return this.off(e,null,t)},delegate:function(e,t,n,i){return this.on(t,e,n,i)},undelegate:function(e,t,n){return arguments.length===1?this.off(e,"**"):this.off(t,e||"**",n)},hover:function(e,t){return this.mouseenter(e).mouseleave(t||e)}});D.each(("blur focus focusin focusout resize scroll click dblclick "+"mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave "+"change select submit keydown keypress keyup contextmenu").split(" "),function(e,n){D.fn[n]=function(e,t){return arguments.length>0?this.on(n,null,e,t):this.trigger(n)}});var Qt=/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g;D.proxy=function(e,t){var n,i,r;if(typeof t==="string"){n=e[t];t=e;e=n}if(!m(e)){return undefined}i=s.call(arguments,2);r=function(){return e.apply(t||this,i.concat(s.call(arguments)))};r.guid=e.guid=e.guid||D.guid++;return r};D.holdReady=function(e){if(e){D.readyWait++}else{D.ready(true)}};D.isArray=Array.isArray;D.parseJSON=JSON.parse;D.nodeName=u;D.isFunction=m;D.isWindow=y;D.camelCase=c;D.type=g;D.now=Date.now;D.isNumeric=function(e){var t=D.type(e);return(t==="number"||t==="string")&&!isNaN(e-parseFloat(e))};D.trim=function(e){return e==null?"":(e+"").replace(Qt,"")};if(true){!(an=[],on=function(){return D}.apply(rn,an),on!==undefined&&(nn.exports=on))}var en=_.jQuery,tn=_.$;D.noConflict=function(e){if(_.$===D){_.$=tn}if(e&&_.jQuery===D){_.jQuery=en}return D};if(typeof O==="undefined"){_.jQuery=_.$=D}return D})},function(O,e,t){"use strict";t.r(e);t.d(e,"v1",function(){return h});t.d(e,"v3",function(){return N});t.d(e,"v4",function(){return H});t.d(e,"v5",function(){return J});t.d(e,"NIL",function(){return G});t.d(e,"version",function(){return K});t.d(e,"validate",function(){return o});t.d(e,"stringify",function(){return v});t.d(e,"parse",function(){return d});var n;var i=new Uint8Array(16);function p(){if(!n){n=typeof crypto!=="undefined"&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto)||typeof msCrypto!=="undefined"&&typeof msCrypto.getRandomValues==="function"&&msCrypto.getRandomValues.bind(msCrypto);if(!n){throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported")}}return n(i)}var r=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i;function a(e){return typeof e==="string"&&r.test(e)}var o=a;var s=[];for(var l=0;l<256;++l){s.push((l+256).toString(16).substr(1))}function u(e){var t=arguments.length>1&&arguments[1]!==undefined?arguments[1]:0;var n=(s[e[t+0]]+s[e[t+1]]+s[e[t+2]]+s[e[t+3]]+"-"+s[e[t+4]]+s[e[t+5]]+"-"+s[e[t+6]]+s[e[t+7]]+"-"+s[e[t+8]]+s[e[t+9]]+"-"+s[e[t+10]]+s[e[t+11]]+s[e[t+12]]+s[e[t+13]]+s[e[t+14]]+s[e[t+15]]).toLowerCase();if(!o(n)){throw TypeError("Stringified UUID is invalid")}return n}var v=u;var m;var y;var g=0;var S=0;function f(e,t,n){var i=t&&n||0;var r=t||new Array(16);e=e||{};var a=e.node||m;var o=e.clockseq!==undefined?e.clockseq:y;if(a==null||o==null){var s=e.random||(e.rng||p)();if(a==null){a=m=[s[0]|1,s[1],s[2],s[3],s[4],s[5]]}if(o==null){o=y=(s[6]<<8|s[7])&16383}}var l=e.msecs!==undefined?e.msecs:Date.now();var u=e.nsecs!==undefined?e.nsecs:S+1;var f=l-g+(u-S)/1e4;if(f<0&&e.clockseq===undefined){o=o+1&16383}if((f<0||l>g)&&e.nsecs===undefined){u=0}if(u>=1e4){throw new Error("uuid.v1(): Can't create more than 10M uuids/sec")}g=l;S=u;y=o;l+=122192928e5;var h=((l&268435455)*1e4+u)%4294967296;r[i++]=h>>>24&255;r[i++]=h>>>16&255;r[i++]=h>>>8&255;r[i++]=h&255;var c=l/4294967296*1e4&268435455;r[i++]=c>>>8&255;r[i++]=c&255;r[i++]=c>>>24&15|16;r[i++]=c>>>16&255;r[i++]=o>>>8|128;r[i++]=o&255;for(var d=0;d<6;++d){r[i+d]=a[d]}return t||v(r)}var h=f;function c(e){if(!o(e)){throw TypeError("Invalid UUID")}var t;var n=new Uint8Array(16);n[0]=(t=parseInt(e.slice(0,8),16))>>>24;n[1]=t>>>16&255;n[2]=t>>>8&255;n[3]=t&255;n[4]=(t=parseInt(e.slice(9,13),16))>>>8;n[5]=t&255;n[6]=(t=parseInt(e.slice(14,18),16))>>>8;n[7]=t&255;n[8]=(t=parseInt(e.slice(19,23),16))>>>8;n[9]=t&255;n[10]=(t=parseInt(e.slice(24,36),16))/1099511627776&255;n[11]=t/4294967296&255;n[12]=t>>>24&255;n[13]=t>>>16&255;n[14]=t>>>8&255;n[15]=t&255;return n}var d=c;function b(e){e=unescape(encodeURIComponent(e));var t=[];for(var n=0;n<e.length;++n){t.push(e.charCodeAt(n))}return t}var P="6ba7b810-9dad-11d1-80b4-00c04fd430c8";var w="6ba7b811-9dad-11d1-80b4-00c04fd430c8";var _=function(e,o,s){function t(e,t,n,i){if(typeof e==="string"){e=b(e)}if(typeof t==="string"){t=d(t)}if(t.length!==16){throw TypeError("Namespace must be array-like (16 iterable integer values, 0-255)")}var r=new Uint8Array(16+e.length);r.set(t);r.set(e,t.length);r=s(r);r[6]=r[6]&15|o;r[8]=r[8]&63|128;if(n){i=i||0;for(var a=0;a<16;++a){n[i+a]=r[a]}return n}return v(r)}try{t.name=e}catch(e){}t.DNS=P;t.URL=w;return t};function C(e){if(typeof e==="string"){var t=unescape(encodeURIComponent(e));e=new Uint8Array(t.length);for(var n=0;n<t.length;++n){e[n]=t.charCodeAt(n)}}return D(M(k(e),e.length*8))}function D(e){var t=[];var n=e.length*32;var i="0123456789abcdef";for(var r=0;r<n;r+=8){var a=e[r>>5]>>>r%32&255;var o=parseInt(i.charAt(a>>>4&15)+i.charAt(a&15),16);t.push(o)}return t}function T(e){return(e+64>>>9<<4)+14+1}function M(e,t){e[t>>5]|=128<<t%32;e[T(t)-1]=t;var n=1732584193;var i=-271733879;var r=-1732584194;var a=271733878;for(var o=0;o<e.length;o+=16){var s=n;var l=i;var u=r;var f=a;n=I(n,i,r,a,e[o],7,-680876936);a=I(a,n,i,r,e[o+1],12,-389564586);r=I(r,a,n,i,e[o+2],17,606105819);i=I(i,r,a,n,e[o+3],22,-1044525330);n=I(n,i,r,a,e[o+4],7,-176418897);a=I(a,n,i,r,e[o+5],12,1200080426);r=I(r,a,n,i,e[o+6],17,-1473231341);i=I(i,r,a,n,e[o+7],22,-45705983);n=I(n,i,r,a,e[o+8],7,1770035416);a=I(a,n,i,r,e[o+9],12,-1958414417);r=I(r,a,n,i,e[o+10],17,-42063);i=I(i,r,a,n,e[o+11],22,-1990404162);n=I(n,i,r,a,e[o+12],7,1804603682);a=I(a,n,i,r,e[o+13],12,-40341101);r=I(r,a,n,i,e[o+14],17,-1502002290);i=I(i,r,a,n,e[o+15],22,1236535329);n=z(n,i,r,a,e[o+1],5,-165796510);a=z(a,n,i,r,e[o+6],9,-1069501632);r=z(r,a,n,i,e[o+11],14,643717713);i=z(i,r,a,n,e[o],20,-373897302);n=z(n,i,r,a,e[o+5],5,-701558691);a=z(a,n,i,r,e[o+10],9,38016083);r=z(r,a,n,i,e[o+15],14,-660478335);i=z(i,r,a,n,e[o+4],20,-405537848);n=z(n,i,r,a,e[o+9],5,568446438);a=z(a,n,i,r,e[o+14],9,-1019803690);r=z(r,a,n,i,e[o+3],14,-187363961);i=z(i,r,a,n,e[o+8],20,1163531501);n=z(n,i,r,a,e[o+13],5,-1444681467);a=z(a,n,i,r,e[o+2],9,-51403784);r=z(r,a,n,i,e[o+7],14,1735328473);i=z(i,r,a,n,e[o+12],20,-1926607734);n=E(n,i,r,a,e[o+5],4,-378558);a=E(a,n,i,r,e[o+8],11,-2022574463);r=E(r,a,n,i,e[o+11],16,1839030562);i=E(i,r,a,n,e[o+14],23,-35309556);n=E(n,i,r,a,e[o+1],4,-1530992060);a=E(a,n,i,r,e[o+4],11,1272893353);r=E(r,a,n,i,e[o+7],16,-155497632);i=E(i,r,a,n,e[o+10],23,-1094730640);n=E(n,i,r,a,e[o+13],4,681279174);a=E(a,n,i,r,e[o],11,-358537222);r=E(r,a,n,i,e[o+3],16,-722521979);i=E(i,r,a,n,e[o+6],23,76029189);n=E(n,i,r,a,e[o+9],4,-640364487);a=E(a,n,i,r,e[o+12],11,-421815835);r=E(r,a,n,i,e[o+15],16,530742520);i=E(i,r,a,n,e[o+2],23,-995338651);n=A(n,i,r,a,e[o],6,-198630844);a=A(a,n,i,r,e[o+7],10,1126891415);r=A(r,a,n,i,e[o+14],15,-1416354905);i=A(i,r,a,n,e[o+5],21,-57434055);n=A(n,i,r,a,e[o+12],6,1700485571);a=A(a,n,i,r,e[o+3],10,-1894986606);r=A(r,a,n,i,e[o+10],15,-1051523);i=A(i,r,a,n,e[o+1],21,-2054922799);n=A(n,i,r,a,e[o+8],6,1873313359);a=A(a,n,i,r,e[o+15],10,-30611744);r=A(r,a,n,i,e[o+6],15,-1560198380);i=A(i,r,a,n,e[o+13],21,1309151649);n=A(n,i,r,a,e[o+4],6,-145523070);a=A(a,n,i,r,e[o+11],10,-1120210379);r=A(r,a,n,i,e[o+2],15,718787259);i=A(i,r,a,n,e[o+9],21,-343485551);n=x(n,s);i=x(i,l);r=x(r,u);a=x(a,f)}return[n,i,r,a]}function k(e){if(e.length===0){return[]}var t=e.length*8;var n=new Uint32Array(T(t));for(var i=0;i<t;i+=8){n[i>>5]|=(e[i/8]&255)<<i%32}return n}function x(e,t){var n=(e&65535)+(t&65535);var i=(e>>16)+(t>>16)+(n>>16);return i<<16|n&65535}function F(e,t){return e<<t|e>>>32-t}function R(e,t,n,i,r,a){return x(F(x(x(t,e),x(i,a)),r),n)}function I(e,t,n,i,r,a,o){return R(t&n|~t&i,e,t,r,a,o)}function z(e,t,n,i,r,a,o){return R(t&i|n&~i,e,t,r,a,o)}function E(e,t,n,i,r,a,o){return R(t^n^i,e,t,r,a,o)}function A(e,t,n,i,r,a,o){return R(n^(t|~i),e,t,r,a,o)}var L=C;var U=_("v3",48,L);var N=U;function q(e,t,n){e=e||{};var i=e.random||(e.rng||p)();i[6]=i[6]&15|64;i[8]=i[8]&63|128;if(t){n=n||0;for(var r=0;r<16;++r){t[n+r]=i[r]}return t}return v(i)}var H=q;function W(e,t,n,i){switch(e){case 0:return t&n^~t&i;case 1:return t^n^i;case 2:return t&n^t&i^n&i;case 3:return t^n^i}}function B(e,t){return e<<t|e>>>32-t}function Y(e){var t=[1518500249,1859775393,2400959708,3395469782];var n=[1732584193,4023233417,2562383102,271733878,3285377520];if(typeof e==="string"){var i=unescape(encodeURIComponent(e));e=[];for(var r=0;r<i.length;++r){e.push(i.charCodeAt(r))}}else if(!Array.isArray(e)){e=Array.prototype.slice.call(e)}e.push(128);var a=e.length/4+2;var o=Math.ceil(a/16);var s=new Array(o);for(var l=0;l<o;++l){var u=new Uint32Array(16);for(var f=0;f<16;++f){u[f]=e[l*64+f*4]<<24|e[l*64+f*4+1]<<16|e[l*64+f*4+2]<<8|e[l*64+f*4+3]}s[l]=u}s[o-1][14]=(e.length-1)*8/Math.pow(2,32);s[o-1][14]=Math.floor(s[o-1][14]);s[o-1][15]=(e.length-1)*8&4294967295;for(var h=0;h<o;++h){var c=new Uint32Array(80);for(var d=0;d<16;++d){c[d]=s[h][d]}for(var p=16;p<80;++p){c[p]=B(c[p-3]^c[p-8]^c[p-14]^c[p-16],1)}var v=n[0];var m=n[1];var y=n[2];var g=n[3];var S=n[4];for(var b=0;b<80;++b){var P=Math.floor(b/20);var w=B(v,5)+W(P,m,y,g)+S+t[P]+c[b]>>>0;S=g;g=y;y=B(m,30)>>>0;m=v;v=w}n[0]=n[0]+v>>>0;n[1]=n[1]+m>>>0;n[2]=n[2]+y>>>0;n[3]=n[3]+g>>>0;n[4]=n[4]+S>>>0}return[n[0]>>24&255,n[0]>>16&255,n[0]>>8&255,n[0]&255,n[1]>>24&255,n[1]>>16&255,n[1]>>8&255,n[1]&255,n[2]>>24&255,n[2]>>16&255,n[2]>>8&255,n[2]&255,n[3]>>24&255,n[3]>>16&255,n[3]>>8&255,n[3]&255,n[4]>>24&255,n[4]>>16&255,n[4]>>8&255,n[4]&255]}var V=Y;var j=_("v5",80,V);var J=j;var G="00000000-0000-0000-0000-000000000000";function X(e){if(!o(e)){throw TypeError("Invalid UUID")}return parseInt(e.substr(14,1),16)}var K=X},function(e,t,n){e.exports=n(4)},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:true});t.JSPluginV1=undefined;var i=function(){function i(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||false;i.configurable=true;if("value"in i)i.writable=true;Object.defineProperty(e,i.key,i)}}return function(e,t,n){if(t)i(e.prototype,t);if(n)i(e,n);return e}}();var I=n(0);var z=n(5);var E=n(12);var A=n(14);var B=n(16);var r=n(1);var O=a(r);function a(e){return e&&e.__esModule?e:{default:e}}function F(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var o=function(){var s=-1;var r=0;var f=0;var h=1;var c=40;var d=1024*1024*4;var p=1001;var v=1002;var m=1003;var y=2002;var S=Symbol("OPTIONS");var o=Symbol("CURRENTPLAYRATE");var l=Symbol("CURRENTSOUNDWND");var b=Symbol("MAXWNDNUM");var g=Symbol("MAXWNDNUM");var w=Symbol("DRAWCANVAS");var u=Symbol("SHAPEID");var P=Symbol("WINDOWFULL");var _=Symbol("SINGLEWINDOW");var C=Symbol("FILETMP");var D=Symbol("STATUSTMP");var T=Symbol("UPGRADESTATUSURL");var M=Symbol("CURWNDINDEX");var k=Symbol("CALLBACKFUNCTION");var x=Symbol("PLUGINVERSION");var R=Symbol("CANFULLSCREEN");var e=function(){function a(e){F(this,a);this.oStreamClient=new z.StreamClient;this.oStorageManager=null;this.oJSPlugin=null;if(typeof e.staticPath==="string"){this.staticPath=e.staticPath}var t=this;var n={szId:"playWnd",iMode:0,iType:1,iWidth:400,iHeight:300,iMaxSplit:4,iCurrentSplit:2,szBasePath:"./"};this[S]=Object.assign(n,e);this.iWidth=e.iWidth;this.iHeight=e.iHeight;var i={border:"#000000",borderSelect:"#FFCC00",background:"#4C4B4B"};i=Object.assign(i,e.oStyle);this[S].oStyle=i;if(this[S].iCurrentSplit>this[S].iMaxSplit){this[S].iCurrentSplit=this[S].iMaxSplit}this[o]=1;this[l]=-1;this[b]=this[S].iMaxSplit*this[S].iMaxSplit;this[u]="";this[P]=false;this[_]=null;this[C]=null;this[D]="";this[T]="";this[M]=-1;this[k]=null;this[R]=true;this[x]="V1.2.0 build20190123";this.bPlay=false;t.oStorageManager=new A.StorageManager(this[S].szBasePath+"/transform",{staticPath:this.staticPath});if(typeof t[S].szId==="string"){t.oJSPlugin=(0,O.default)("#"+t[S].szId)}else{t.oJSPlugin=t[S].szId}this[g]=[];for(var r=0;r<this[b];r++){this[g][r]={};this[g][r].bSelect=false;this[g][r].bPlay=false;this[g][r].bPause=false;this[g][r].bRecord=false;this[g][r].oPlayCtrl=null;this[g][r].szPlayType="";this[g][r].szStorageUUID="";this[g][r].szStreamUUID="";this[g][r].aHead=[];this[g][r].bLoad=false;this[g][r].windowID=t[S].szId+"canvas"+r;this[g][r].drawID=t[S].szId+"canvas_draw"+r;this[g][r].iRate=1;this[g][r].bEZoom=false;this[g][r].b3DZoom=false;this[g][r].szSecretKey="";this[g][r].bFrameForward=false;this[g][r].iDecodeType=f;this[g][r].bFirstFrame=false}t.listenBrowserVisibility();if(this[S].iMode===0){t.createWindows()}else if(this[S].iMode===1){}else if(this[S].iMode===2){t.createIMGWindows()}this[w]=new B.ESCanvas(t[S].szId+"canvas_draw0");if(this[S].iType===0){t.oJSPlugin.hide()}t.initEvent();t.EventCallback.windowEventSelect(0)}i(a,[{key:"listenBrowserVisibility",value:function e(){var n=this;document.addEventListener("visibilitychange",function(){if(document.hidden){for(var e=0;e<16;e++){if(n[g][e]&&n[g][e].bLoad){n[g][e].oPlayCtrl.PlayM4_IsVisible(false)}}}else{for(var t=0;t<16;t++){if(n[g][t]&&n[g][t].bLoad){n[g][t].oPlayCtrl.PlayM4_IsVisible(true)}}}},false)}},{key:"createWindows",value:function e(t,n){var i=this;if(t&&n){i.iWidth=t;i.iHeight=n}var r=i.iWidth%i[S].iCurrentSplit;var a=i.iHeight%i[S].iCurrentSplit;var o=(i.iWidth-r-i[S].iCurrentSplit*2)/i[S].iCurrentSplit;var s=(i.iHeight-a-i[S].iCurrentSplit*2)/i[S].iCurrentSplit;var l=(i.iWidth-r)/i[S].iCurrentSplit;var u=(i.iHeight-a)/i[S].iCurrentSplit;var f=i[S].iCurrentSplit;if(typeof i[S].szId==="string"){i.oJSPlugin=(0,O.default)("#"+i[S].szId)}else{i.oJSPlugin=i[S].szId}var h='<div class="'+i[S].szId+'parent-wnd" style="overflow:hidden;width:100%; height:100%; position: relative;">';var c=navigator.userAgent.match(/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i);if(c){for(var d=0;d<1;d++){t=o+(d%f===f-1?r:0);n=s+(d+f>=Math.pow(f,2)?a:0);var p=l+(d%f===f-1?r:0);var v=u+(d+f>=Math.pow(f,2)?a:0);p=p*2;v=v*2;h+='<div style="transform: scale(0.5);-webkit-transform: scale(0.5);-webkit-transform-origin:0 0;text-align:left;transform-origin:0 0; width:'+p+"px;height:"+v+'px;"><div style="float:left; background-color: '+i[S].oStyle.background+'; position: relative; width:100%; height:100%;">'+'<canvas id="'+i[S].szId+"canvas"+d+'" class="'+i[S].szId+'play-window" wid="'+d+'" width="'+p+'" height="'+v+'"></canvas>'+(d===0?'<img style="display:none;" id="playImg'+d+'" src="">':"")+'<canvas id="'+i[S].szId+"canvas_draw"+d+'"  class="'+i[S].szId+'draw-window" style="position:absolute; top:0; left:0;" wid="'+d+'" width='+t+" height="+n+"></canvas>"+"</div></div>"}}else{for(var m=0;m<i[b];m++){t=o+(m%f===f-1?r:0);n=s+(m+f>=Math.pow(f,2)?a:0);var y=l+(m%f===f-1?r:0);var g=u+(m+f>=Math.pow(f,2)?a:0);h+='<div style="float:left; background-color: '+i[S].oStyle.background+"; position: relative; width: "+y+"px; height: "+g+'px;">'+'<canvas id="'+i[S].szId+"canvas"+m+'" class="'+i[S].szId+'play-window" wid="'+m+'" width="'+y+'" height="'+g+'"></canvas>'+(m===0?'<img style="display:none;" id="playImg'+m+'" src="">':"")+'<canvas id="'+i[S].szId+"canvas_draw"+m+'"  class="'+i[S].szId+'draw-window" style="position:absolute; top:0; left:0;" wid="'+m+'" width='+t+" height="+n+"></canvas>"+"</div>"}}h+="</div>";i.oJSPlugin.html(h)}},{key:"createIMGWindows",value:function e(t,n){var i=this;if(t&&n){i.iWidth=t;i.iHeight=n}var r=i.iWidth%i[S].iCurrentSplit;var a=i.iHeight%i[S].iCurrentSplit;var o=(i.iWidth-r-i[S].iCurrentSplit*2)/i[S].iCurrentSplit;var s=(i.iHeight-a-i[S].iCurrentSplit*2)/i[S].iCurrentSplit;var l=(i.iWidth-r)/i[S].iCurrentSplit;var u=(i.iHeight-a)/i[S].iCurrentSplit;var f=i[S].iCurrentSplit;if(typeof i[S].szId==="string"){i.oJSPlugin=(0,O.default)("#"+i[S].szId)}else{i.oJSPlugin=i[S].szId}i.oJSPlugin.html("");var h='<div class="'+i[S].szId+'parent-wnd" style="overflow:hidden;width:100%; height:100%; position: relative;">';for(var c=0;c<i[b];c++){t=o+(c%f===f-1?r:0);n=s+(c+f>=Math.pow(f,2)?a:0);var d=l+(c%f===f-1?r:0);var p=u+(c+f>=Math.pow(f,2)?a:0);h+='<div style="float:left; background-color: '+i[S].oStyle.background+"; position: relative; width: "+d+"px; height: "+p+'px;">'+'<img id="'+i[S].szId+"canvas"+c+'" class="'+i[S].szId+'play-window" wid="'+c+'" width="'+d+'" height="'+p+'"></img>'+'<canvas id="'+i[S].szId+"canvas_draw"+c+'"  class="'+i[S].szId+'draw-window" style="position:absolute; top:0; left:0;" wid="'+c+'" width='+t+" height="+n+"></canvas>"+"</div>"}h+="</div>";i.oJSPlugin.html(h)}},{key:"initCallbackEvent",value:function e(){var n=this;n.EventCallback=function(){return{loadEventHandler:function e(){window.loadEventHandler&&window.loadEventHandler()},zoomEventResponse:function e(){},windowEventSelect:function e(t){if(n[M]===t){return}n[M]=t;if(n[g][t].bEZoom||n[g][t].b3DZoom){(0,O.default)("."+n[S].szId+"draw-window").unbind();n[w].setDrawStatus(false);n[w]=null;n[w]=new B.ESCanvas(n[S].szId+"canvas_draw"+t);n[w].setShapeType("Rect");n[w].setDrawStyle("#ff0000","",0);if(n[g][t].bEZoom){n[w].setDrawStatus(true,function(e){if(e.startPos&&e.endPos){if(e.startPos[0]>e.endPos[0]){n[g][t].oPlayCtrl.PlayM4_SetDisplayRegion(null,false)}else{n[g][t].oPlayCtrl.PlayM4_SetDisplayRegion({left:e.startPos[0],top:e.startPos[1],right:e.endPos[0],bottom:e.endPos[1]},true)}}})}else if(n[g][t].b3DZoom){n[w].setDrawStatus(true,function(e){n[k](e)})}}window.GetSelectWndInfo&&window.GetSelectWndInfo(t)},pluginErrorHandler:function e(t,n,i){window.PluginEventHandler&&window.PluginEventHandler(t,n,i)},windowEventOver:function e(t){window.windowEventOver&&window.windowEventOver(t)},windowEventOut:function e(t){window.windowEventOut&&window.windowEventOut(t)},windowEventUp:function e(t){window.windowEventUp&&window.windowEventUp(t)},windowFullCcreenChange:function e(t){window.windowFullCcreenChange&&window.windowFullCcreenChange(t)},firstFrameDisplay:function e(t,n,i){window.firstFrameDisplay&&window.firstFrameDisplay(t,n,i)},performanceLack:function e(){window.performanceLack&&window.performanceLack()},mouseEvent:function e(t,n,i){var r='<?xml version="1.0"?><MouseEvent><WndIndex>0</WndIndex><EventType>'+t+"</EventType>"+"<Position><x>"+n+"</x><y>"+i+"</y><delta>0</delta></Position></MouseEvent>";window.onMouseEvent&&window.onMouseEvent(r)}}}()}},{key:"initEvent",value:function e(){var a=this;a.initCallbackEvent();a.oJSPlugin.find("."+a[S].szId+"parent-wnd").eq(0).children().each(function(i){var e=this;var r=false;(0,O.default)(e).unbind().bind("mousedown",function(){a.EventCallback.windowEventSelect(parseInt(a.oJSPlugin.find("."+a[S].szId+"parent-wnd").eq(0).children().eq(i).find("."+a[S].szId+"play-window").eq(0).attr("wid"),10))});(0,O.default)(e).bind("mouseover",function(e){a.EventCallback.windowEventOver(i);e.stopPropagation()});(0,O.default)(e).bind("mouseout",function(e){a.EventCallback.windowEventOut(i);e.stopPropagation()});(0,O.default)(e).bind("mousedown",function(e){r=true;var t=e.offsetX/a[w].m_iCanvasWidth;var n=e.offsetY/a[w].m_iCanvasHeight;if(e.button===2){a.EventCallback.mouseEvent(4,t,n)}else if(e.button===0){a.EventCallback.mouseEvent(1,t,n)}e.stopPropagation()});(0,O.default)(e).bind("mousemove",function(e){var t=e.offsetX/a[w].m_iCanvasWidth;var n=e.offsetY/a[w].m_iCanvasHeight;if(r){a.EventCallback.mouseEvent(7,t,n)}else{a.EventCallback.mouseEvent(6,t,n)}e.stopPropagation()});(0,O.default)(e).bind("mousewheel",function(e){var t=e.offsetX/a[w].m_iCanvasWidth;var n=e.offsetY/a[w].m_iCanvasHeight;a.EventCallback.mouseEvent(8,t,n);e.stopPropagation()});(0,O.default)(e).bind("mouseup",function(e){r=false;a.EventCallback.windowEventUp(i);var t=e.offsetX/a[w].m_iCanvasWidth;var n=e.offsetY/a[w].m_iCanvasHeight;if(e.button===2){a.EventCallback.mouseEvent(5,t,n)}else if(e.button===0){a.EventCallback.mouseEvent(3,t,n)}});var t=(new Date).getTime();function n(){var e=document.getElementsByTagName("body")[0];var t=document.getElementsByTagName("html")[0];switch(window.orientation){case-90:case 90:setTimeout(function(){a.JS_FullScreenDisplay(true);t.style["overflow-y"]="hidden";t.style["height"]="100%";e.style["overflow-y"]="hidden";e.style["height"]="100%"},200);break;case 0:case 180:setTimeout(function(){a.JS_CancelFullScreenDisplay(true);t.style["overflow-y"]="auto";e.style["overflow-y"]="auto"},200);break;default:break}}window.addEventListener("orientationchange",n,false)});if(typeof document.fullScreen!=="undefined"){document.addEventListener("fullscreenchange",function(){var e=document.fullscreen||false;a.EventCallback.windowFullCcreenChange(e)})}else if(typeof document.webkitIsFullScreen!=="undefined"){document.addEventListener("webkitfullscreenchange",function(){var e=document.webkitIsFullScreen||false;a.EventCallback.windowFullCcreenChange(e)})}else if(typeof document.mozFullScreen!=="undefined"){document.addEventListener("mozfullscreenchange",function(){var e=document.mozFullScreen||false;a.EventCallback.windowFullCcreenChange(e)})}}},{key:"updateWnd",value:function e(){var t=this;var n=t.oJSPlugin.find("."+t[S].szId+"parent-wnd").eq(0).children().length;var i=t.iWidth%t[S].iCurrentSplit;var r=t.iHeight%t[S].iCurrentSplit;var a=(t.iWidth-i-t[S].iCurrentSplit*2)/t[S].iCurrentSplit;var o=(t.iHeight-r-t[S].iCurrentSplit*2)/t[S].iCurrentSplit;var s=(t.iWidth-i)/t[S].iCurrentSplit;var l=(t.iHeight-r)/t[S].iCurrentSplit;var u=t[S].iCurrentSplit;for(var f=0;f<n;f++){var h=a+(f%u===u-1?i:0);var c=o+(f+u>=Math.pow(u,2)?r:0);var d=s+(f%u===u-1?i:0);var p=l+(f+u>=Math.pow(u,2)?r:0);t.oJSPlugin.find("."+t[S].szId+"parent-wnd").eq(0).children().eq(f).width(d);t.oJSPlugin.find("."+t[S].szId+"parent-wnd").eq(0).children().eq(f).height(p);t.oJSPlugin.find("."+t[S].szId+"parent-wnd").eq(0).children().eq(f).find("."+t[S].szId+"draw-window").attr("width",h);t.oJSPlugin.find("."+t[S].szId+"parent-wnd").eq(0).children().eq(f).find("."+t[S].szId+"draw-window").attr("height",c);t.oJSPlugin.find("."+t[S].szId+"parent-wnd").eq(0).children().eq(f).find("."+t[S].szId+"play-window").attr("width",d);t.oJSPlugin.find("."+t[S].szId+"parent-wnd").eq(0).children().eq(f).find("."+t[S].szId+"play-window").attr("height",p)}}},{key:"cbPlayCtrlCallback",value:function e(t,n,a,i,r,o,s){var l=this;if(!(0,O.default)("#"+l[g][a].windowID).length){return}var u=false;if(i&&r){u=true}l[g][a].bLoad=true;l.oStreamClient.openStream(t,n,function(e){if(e.bHead&&!l[g][a].bPlay){l[g][a].bPlay=true;l.bPlay=true;l[g][a].aHead=new Uint8Array(e.buf);l[g][a].oPlayCtrl.PlayM4_OpenStream(e.buf,c,1024*1024*2);if(l[g][a].szSecretKey!==""){l[g][a].oPlayCtrl.PlayM4_SetSecretKey(1,l[g][a].szSecretKey,128);l[g][a].szSecretKey=""}if(l[g][a].aHead[8]===4){l[g][a].oPlayCtrl.PlayM4_SetStreamOpenMode(0)}else{l[g][a].oPlayCtrl.PlayM4_SetStreamOpenMode(1)}l[g][a].oPlayCtrl.PlayM4_SetInputBufSize(d);l[g][a].oPlayCtrl.PlayM4_Play(l[g][a].windowID)}else{var t=new Uint8Array(e.buf);var n=l[g][a].oPlayCtrl.PlayM4_GetInputBufSize();var i=l[g][a].oPlayCtrl.PlayM4_GetYUVBufSize();if(i===2&&!l[g][a].bFirstFrame){l[g][a].bFirstFrame=true;l[g][a].oPlayCtrl.PlayM4_GetFrameResolution(function(e,t){l.EventCallback.firstFrameDisplay(a,e,t)})}var r=l[g][a].oPlayCtrl.PlayM4_GetDecodeFrameType();if(n>d*.5&&n<d*.8&&l[g][a].iRate===1){if(r!==h&&!l[g][a].bFrameForward){l[g][a].oPlayCtrl.PlayM4_SetDecodeFrameType(h);l.EventCallback.performanceLack()}}else if(n>=d*.8){}if(i>10&&i<15&&!l[g][a].bFrameForward){if(r!==h){l[g][a].oPlayCtrl.PlayM4_SetDecodeFrameType(h);l.EventCallback.performanceLack()}}else if(i>15){}if(i<10&&n<d*.5){if(r!==f&&l[g][a].iRate===1){l[g][a].oPlayCtrl.PlayM4_SetDecodeFrameType(f)}}if(e.statusString){l.EventCallback.pluginErrorHandler(a,p,e)}else if(e.type&&e.type==="exception"){l.EventCallback.pluginErrorHandler(a,v,e)}else{l[g][a].oPlayCtrl.PlayM4_InputData(t,t.length)}}if(l[g][a].szStorageUUID){l.oStorageManager.inputData(l[g][a].szStorageUUID,e.buf)}e=null},function(){if(l[g][a].bPlay){l.EventCallback.pluginErrorHandler(a,m);l[g][a].bPlay=false;l.bPlay=false;l[g][a].bFrameForward=false;l[g][a].iRate=1;if(l[g][a].oPlayCtrl){l[g][a].oPlayCtrl.PlayM4_Stop();l[g][a].oPlayCtrl.PlayM4_CloseStream()}}}).then(function(e){l[g][a].szStreamUUID=e;l.oStreamClient.startPlay(e,i,r).then(function(){if(u){l[g][a].szPlayType="playback";l[g][a].iRate=1;l[g][a].oPlayCtrl.PlayM4_PlayRate(l[g][a].iRate)}else{l[g][a].szPlayType="realplay"}o()},function(e){s(e)})},function(e){s(e)})}},{key:"cbPlayIMGCallback",value:function e(t,n,i,r,a,o,s){var l=this;if(!(0,O.default)("#"+l[g][i].windowID).length){return}l[g][i].bPlay=true;l.bPlay=true;l.oStreamClient.openStream(t,n,function(e){if(!e.bHead){var t=new Blob([e.buf],{type:"image/jpeg"});var n=URL.createObjectURL(t);if((0,O.default)("#"+l[S].szId+"canvas"+i).length){document.getElementById(l[S].szId+"canvas"+i).src=n}}},function(){if(l[g][i].bPlay){l.EventCallback.pluginErrorHandler(i,m);l[g][i].bPlay=false;l.bPlay=false}}).then(function(e){l[g][i].szStreamUUID=e;l.oStreamClient.startPlay(e,r,a).then(function(){l[g][i].szPlayType="realplay";o()},function(e){s(e)})},function(e){s(e)})}},{key:"JS_UpdateWindowStyle",value:function e(t){var n=this;this[S].oStyle=t;n.updateWnd()}},{key:"JS_GetPluginVersion",value:function e(){var t=this;return t[x]}},{key:"JS_ArrangeWindow",value:function e(t){var n=this;if(t<n[S].iMaxSplit){n[S].iCurrentSplit=t}else{n[S].iCurrentSplit=n[S].iMaxSplit}if(I.oTool.isFirefox()){for(var i=0;i<n[S].iMaxSplit*n[S].iMaxSplit;i++){if(n[g][i].oPlayCtrl){n[g][i].oPlayCtrl.PlayM4_ClearCanvas()}}}n.updateWnd();n.EventCallback.windowEventSelect(0)}},{key:"JS_SetSecretKey",value:function e(t,n){if(t<0){return-1}if(n===""||typeof n==="undefined"){return-1}this[g][t].szSecretKey=n;return 0}},{key:"JS_Play",value:function e(i,r,a,o,s){console.log("JS_Play");var l=this;l.playURL=r.playURL;l.szUrl=i;var t=new Promise(function(t,n){if(a<0||a>l[b]-1){n();return}if(l[g][a].bFrameForward){n();return}if(l[g][a].bPlay){l.JS_Stop(a)}if(l[S].iMode===0){setTimeout(function(){l[g][a].bFirstFrame=false;l[g][a].iDecodeType=f;if(l[g][a].oPlayCtrl){l.cbPlayCtrlCallback(i,r,a,o,s,t,n)}else{l[g][a].oPlayCtrl=new E.JSPlayCtrl(l[S].szBasePath+"/playctrl/",function(e){if(e.cmd==="loaded"&&!l[g][a].bLoad){l.cbPlayCtrlCallback(i,r,a,o,s,t,n)}else if(e.cmd==="OnebyOne"){if(!e.status){if(!l[g][a].bPause){l.oStreamClient.pause(l[g][a].szStreamUUID);l[g][a].bPause=true}}else{if(l[g][a].bPause){l.oStreamClient.resume(l[g][a].szStreamUUID);l[g][a].bPause=false}}}else if(e.cmd==="GetFrameData"){}},a,l.staticPath)}},200)}else if(l[S].iMode===1){}else if(l[S].iMode===2){var e=(0,O.default)("."+l[S].szId+"play-window").eq(a);e.show();l.cbPlayIMGCallback(i,r,a,o,s,t,n)}});return t}},{key:"JS_Seek",value:function e(n,i,r){var a=this;var t=new Promise(function(e,t){if(n<0||n>a[b]-1){t();return}if(!a[g][n].bPlay){t();return}console.log("seek，清理缓存");a[g][n].oPlayCtrl.aVideoYUVBuffer.splice(0,a[g][n].oPlayCtrl.aVideoYUVBuffer.length);a.oStreamClient.seek(a[g][n].szStreamUUID,i,r).then(function(){e()},function(e){t(e)})});return t}},{key:"JS_GetSdkVersion",value:function e(){var t=this;return t[g][0].oPlayCtrl.PlayM4_GetSdkVersion()}},{key:"JS_isInited",value:function e(){var t=this;return t[g][0].bLoad}},{key:"JS_SetLostFrameMode",value:function e(t){var n=this;return n[g][0].oPlayCtrl.PlayM4_SetLostFrameMode(t)}},{key:"JS_DestroyWorker",value:function e(){var t=this;t[g].forEach(function(e){if(e.bPlay){e.oPlayCtrl.PlayM4_CloseStream()}if(e.oPlayCtrl){e.oPlayCtrl.PlayM4_Destroy();e.oPlayCtrl=null;e.bLoad=false}});t[w].setShapeType("")}},{key:"JS_Stop",value:function e(n){var i=this;var t=new Promise(function(t,e){if(n<0||n>i[b]-1){e();return}if(i[g][n].szStorageUUID){i.JS_StopSave(n)}if(i[g][n].bEZoom){i.JS_DisableZoom(n)}if(i[l]===n){i[l]=-1}i.oStreamClient.stopAll(i[g][n].szStreamUUID).then(function(){i[g][n].bPlay=false;i.bPlay=false;i[g][n].bFrameForward=false;i[g][n].iRate=1;if(i[S].iMode===0){if(i[g][n].oPlayCtrl){i[g][n].oPlayCtrl.PlayM4_Stop&&i[g][n].oPlayCtrl.PlayM4_Stop();i[g][n].oPlayCtrl.PlayM4_CloseStream&&i[g][n].oPlayCtrl.PlayM4_CloseStream()}}else if(i[S].iMode===2){var e=(0,O.default)("."+i[S].szId+"play-window").eq(n);e.hide()}setTimeout(function(){t()},500)},function(){setTimeout(function(){e()},500)})});return t}},{key:"JS_Pause",value:function e(a){console.log("JS_Pause");var o=this;var t=new Promise(function(i,r){if(a<0||a>o[b]-1){r();return}if(o[g][a].szStorageUUID){o.JS_StopSave(a)}var e=function e(t){if(t.fileUint8Array){console.log("暂停缓存截图数据 start");o[g][a].tmpCapturePictureData=t.fileUint8Array}};o.JS_CapturePicture(0,""+(new Date).getTime(),"JPEG",e,true);if(!o[g][a].bPlay){r();return}if(o[g][a].bFrameForward){r();return}o.JS_GetOSDTime(0).then(function(e){var t=new Date(e*1e3);var n=""+t.getFullYear()+(t.getMonth()>8?t.getMonth()+1:"0"+(t.getMonth()+1))+(t.getDate()>9?t.getDate():"0"+t.getDate())+(t.getHours()>9?t.getHours():"0"+t.getHours())+(t.getMinutes()>9?t.getMinutes():"0"+t.getMinutes())+(t.getSeconds()>9?t.getSeconds():"0"+t.getSeconds());o[g][a].pauseTime=n;o.oStreamClient.stopAll(o[g][a].szStreamUUID).then(function(){o[g][a].bPlay=false;o.bPlay=false;o[g][a].bFrameForward=false;o[g][a].iRate=1;if(o[S].iMode===0){if(o[g][a].oPlayCtrl){o[g][a].oPlayCtrl.PlayM4_Stop&&o[g][a].oPlayCtrl.PlayM4_Stop();o[g][a].oPlayCtrl.PlayM4_CloseStream&&o[g][a].oPlayCtrl.PlayM4_CloseStream()}}else if(o[S].iMode===2){var e=(0,O.default)("."+o[S].szId+"play-window").eq(a);e.hide()}setTimeout(function(){o[g][a].bPause=true;i()},500)},function(){setTimeout(function(){r()},500)})})});return t}},{key:"JS_Resume",value:function e(i){console.log("JS_Resume");var r=0;var a=this;var t=new Promise(function(e,t){if(r<0||r>a[b]-1){t();return}if(i){if(a[g][r].pauseTime){a[g][r].pauseTime=a[g][r].pauseTime.slice(0,14-i.length)+i}else{a[g][r].pauseTime=i}}if(a.playURL.indexOf("playback")!==-1){var n=a[g][r].pauseTime;console.log("pauseTime",n);a.playURL=a.playURL.replace(/begin=[0-9]{8}T[0-9]{6}Z/gi,"begin="+n.slice(0,8)+"T"+n.slice(8,14)+"Z")}a.JS_Play(a.szUrl,{playURL:a.playURL},r).then(function(){e()}).catch(function(){t()});a[g][r].bPause=false;e()});return t}},{key:"JS_Slow",value:function e(n){var i=this;var t=new Promise(function(e,t){if(n<0||n>i[b]-1){t();return}if(!i[g][n].bPlay){t();return}if(i[g][n].iRate===-8){t();return}if(i[g][n].bFrameForward){t();return}if(i[g][n].iRate<0&&i[g][n].iRate>-8){i[g][n].iRate*=2}if(i[g][n].iRate===1){i[g][n].iRate*=-2}if(i[g][n].iRate>1){i[g][n].iRate/=2}i.oStreamClient.setPlayRate(i[g][n].szStreamUUID,i[g][n].iRate).then(function(){if(i[g][n].iRate<2){i[g][n].oPlayCtrl.PlayM4_SetDecodeFrameType(f)}else{console.log("单线程倍速，只解析I帧",i[g][n].iRate);i[g][n].oPlayCtrl.PlayM4_SetDecodeFrameType(h);i[g][n].oPlayCtrl.PlayM4_SetIFrameDecInterval(0)}i[g][n].oPlayCtrl.PlayM4_PlayRate(i[g][n].iRate);e()},function(e){t(e)})});return t}},{key:"JS_Fast",value:function e(n,i){var r=this;var t=new Promise(function(e,t){if(n<0||n>r[b]-1){t();return}if(!r[g][n].bPlay){t();return}if(i){switch(i){case 1:case 2:case 4:r[g][n].iRate=i;break;case 3:r[g][n].iRate=3;break;default:r[g][n].iRate=this.iRate;break}}else{if(r[g][n].bFrameForward){t();return}if(r[g][n].iRate===8){t();return}if(r[g][n].iRate===-2){r[g][n].iRate=1}else if(r[g][n].iRate<-2){r[g][n].iRate/=2}else if(r[g][n].iRate>0&&r[g][n].iRate<8){r[g][n].iRate*=2}}r.oStreamClient.setPlayRate(r[g][n].szStreamUUID,r[g][n].iRate).then(function(){if(r[g][n].iRate<2){console.log("单线程倍速，全解析",r[g][n].iRate);r[g][n].oPlayCtrl.PlayM4_SetDecodeFrameType(f)}else{console.log("单线程倍速，只解析I帧",r[g][n].iRate);r[g][n].oPlayCtrl.PlayM4_SetDecodeFrameType(h);if(r[g][n].iRate===8){r[g][n].oPlayCtrl.PlayM4_SetIFrameDecInterval(2)}else{r[g][n].oPlayCtrl.PlayM4_SetIFrameDecInterval(0)}}r[g][n].oPlayCtrl.PlayM4_PlayRate(r[g][n].iRate);e()},function(e){t(e)})});return t}},{key:"JS_Speed",value:function e(n){var i=this;var r=0;var t=new Promise(function(e,t){if(r<0||r>i[b]-1){t();return}if(!i[g][r].bPlay){t();return}if(i[g][r].bFrameForward){t();return}if(i[g][r].iRate===8){t();return}i[g][r].iRate=n;i.oStreamClient.setPlayRate(i[g][r].szStreamUUID,i[g][r].iRate).then(function(){if(i[g][r].iRate<=2||i[g][r].iRate===3){console.log("单线程倍速，全解",i[g][r].iRate);i[g][r].oPlayCtrl.PlayM4_SetDecodeFrameType(f)}else{console.log("单线程倍速，只解析I帧",i[g][r].iRate);i[g][r].oPlayCtrl.PlayM4_SetDecodeFrameType(h)}i[g][r].oPlayCtrl.PlayM4_PlayRate(i[g][r].iRate);e()},function(e){t(e)})});return t}},{key:"JS_Transmission",value:function e(i,r){var a=this;var t=new Promise(function(t,n){if(i<0||i>a[b]-1){n();return}if(!a[g][i].szStreamUUID){n();return}a.oStreamClient.transmission(a[g][i].szStreamUUID,r).then(function(e){t(e)},function(e){n(e)})});return t}},{key:"JS_FrameForward",value:function e(n){var i=this;var t=new Promise(function(e,t){if(n<0||n>i[b]-1){t();return}if(!i[g][n].bPlay){t();return}if(i[g][n].iRate!==1){i[g][n].iRate=1;i[o]=i[g][n].iRate;i.oStreamClient.setPlayRate(i[g][n].szStreamUUID,i[g][n].iRate).then(function(){i[g][n].oPlayCtrl.PlayM4_PlayRate(i[g][n].iRate);i[g][n].oPlayCtrl.PlayM4_SetDecodeFrameType(f);i[g][n].oPlayCtrl.PlayM4_OneByOne();i[g][n].bFrameForward=true},function(e){t(e)})}else{i[g][n].oPlayCtrl.PlayM4_PlayRate(i[g][n].iRate);i[g][n].oPlayCtrl.PlayM4_SetDecodeFrameType(f);i[g][n].oPlayCtrl.PlayM4_OneByOne();i[g][n].bFrameForward=true}e()});return t}},{key:"JS_GetOSDTime",value:function e(n){var r=this;var t=new Promise(function(i,e){if(n<0||n>r[b]-1){e(s);return}if(!r[g][n].bPlay){e(s);return}var t=r[g][n].oPlayCtrl.PlayM4_GetOSDTime(function(e){var t=I.oTool.isIOS()||I.oTool.isSafari()||I.oTool.isEdge()?"/":" ";var n=Date.parse(e.replace(/-/g,t))/1e3;i(n)});if(t!==0){e(s);return}});return t}},{key:"JS_OpenSound",value:function e(t){var n=this;if(t<0||t>n[b]-1){return s}if(!n[g][t].bPlay){return s}if(n[l]===t){return s}if(n[l]!==-1){n[g][n[l]].oPlayCtrl.PlayM4_StopSound()}if(n[g][t].oPlayCtrl.PlayM4_PlaySound(t)!==1){return s}n[l]=t;return r}},{key:"JS_GetVolume",value:function e(t,n){var i=this;i[g][t].oPlayCtrl.PlayM4_GetVolume(function(e){n(e)})}},{key:"JS_SetVolume",value:function e(t,n){var i=this;if(i[g][t].oPlayCtrl.PlayM4_SetVolume(n)!==0){return s}return r}},{key:"JS_CloseSound",value:function e(){var t=this;var n=t[l];if(n<0||n>t[b]-1){return s}if(!t[g][n].bPlay){return s}if(t[g][n].oPlayCtrl.PlayM4_StopSound()!==1){return s}t[l]=-1;return r}},{key:"JS_EnableZoom",value:function e(t){var n=this;if(t<0||t>n[b]-1){return s}if(!n[g][t].bPlay){return s}(0,O.default)("."+n[S].szId+"draw-window").unbind();this[w]=new B.ESCanvas(n[S].szId+"canvas_draw"+t);this[w].setShapeType("Rect");this[w].setDrawStyle("#ff0000","",0);this[w].setDrawStatus(true,function(e){if(e.startPos&&e.endPos){if(e.startPos[0]>e.endPos[0]){n[g][t].oPlayCtrl.PlayM4_SetDisplayRegion(null,false)}else{n[g][t].oPlayCtrl.PlayM4_SetDisplayRegion({left:e.startPos[0],top:e.startPos[1],right:e.endPos[0],bottom:e.endPos[1]},true)}}});n[g][t].bEZoom=true;return r}},{key:"JS_DisableZoom",value:function e(t){var n=this;if(t<0||t>n[b]-1){return s}if(!n[g][t].bPlay){return s}this[w].setDrawStatus(false);if(this[g][t].oPlayCtrl.PlayM4_SetDisplayRegion(null,false)!==0){return s}this[g][t].bEZoom=false;return r}},{key:"JS_Enable3DZoom",value:function e(t,n){var i=this;if(t<0||t>i[b]-1){return s}if(!i[g][t].bPlay){return s}(0,O.default)("."+i[S].szId+"draw-window").unbind();this[k]=n;this[w]=new B.ESCanvas(i[S].szId+"canvas_draw"+t);this[w].setShapeType("Rect");this[w].setDrawStyle("#ff0000","",0);this[w].setDrawStatus(true,function(e){n(e)});i[g][t].b3DZoom=true;return r}},{key:"JS_Disable3DZoom",value:function e(t){var n=this;if(t<0||t>n[b]-1){return s}if(!n[g][t].bPlay){return s}this[w].setDrawStatus(false);this[g][t].b3DZoom=false;return r}},{key:"JS_CapturePicture",value:function e(r,a,o,s,l){var u=this;console.log("JS_CapturePicture");var t=new Promise(function(t,n){if(r<0||r>u[b]-1){n();return}if(!u[g][r].bPlay&&u[g][r].bPause&&u[g][r].tmpCapturePictureData){console.log("不在播放状态，使用缓存的截图");i(u[g][r].tmpCapturePictureData);return}if(!u[g][r].bPlay){n();return}if(!o){o="JPEG"}if(o==="BMP"){u[g][r].oPlayCtrl.PlayM4_GetBMP(function(e){if(e===6){n(y)}else{if(s){s(e)}else{I.oTool.downloadFile(e,a+".BMP")}t()}})}else if(o==="JPEG"){u[g][r].oPlayCtrl.PlayM4_GetJPEG(function(e){if(e===6){n(y)}else{i(e);t()}})}function i(e){if(s){var t=function e(t){var n=new Uint8Array(t);var i="";for(var r=0;r<n.length;r++){i+=String.fromCharCode(n[r])}return"data:image/jpeg;base64,"+window.btoa(i)};s({fileName:a+".jpeg",fileUint8Array:e,base64:t(e)});if(l){return false}I.oTool.downloadFile(e,a+".jpeg")}else{I.oTool.downloadFile(e,a+".jpeg")}}});return t}},{key:"JS_StopRealPlayAll",value:function e(){var n=this;n.oStreamClient.stopAll();n[g].forEach(function(e,t){if(e.bPlay){if(e.szStorageUUID){n.JS_StopSave(t)}if(e.bEZoom){n.JS_DisableZoom(t)}if(n[S].iMode===0){e.oPlayCtrl.PlayM4_Stop();e.oPlayCtrl.PlayM4_CloseStream()}else if(n[S].iMode===2){(0,O.default)("."+n[S].szId+"play-window").hide()}}e.bPlay=false});n.bPlay=false;n[l]=-1}},{key:"JS_StartSave",value:function e(a,o,s){var l=this;var t=new Promise(function(t,e){var n=0;if(s){n=s.iPackage}if(a<0||a>l[b]-1){e();return}if(!l[g][a].bPlay){e();return}if(o.indexOf(".mp4")<0){o=o+".mp4"}var i=l[g][a].aHead;var r=0;if(l[g][a].szPlayType==="playback"){r=1}l.oStorageManager.startRecord(o,i,2,r,{cbEventHandler:function e(t){l.EventCallback.pluginErrorHandler(a,t)},iPackage:n}).then(function(e){l[g][a].szStorageUUID=e;t()},function(){e()})});return t}},{key:"JS_StopSave",value:function e(n){var i=this;var t=new Promise(function(e,t){if(!i[g][n].szStorageUUID){t();return}i.oStorageManager.stopRecord(i[g][n].szStorageUUID).then(function(){i[g][n].szStorageUUID="";e()},function(e){t(e)})});return t}},{key:"JS_GetLocalConfig",value:function e(){return""}},{key:"JS_SetLocalConfig",value:function e(){return 0}},{key:"JS_SetGridInfo",value:function e(t){if(t===null||typeof t==="undefined"){return-1}var n="#ff0000";if(t.drawColor){n=t.drawColor}this[w].setDrawStyle(n);this[w].setShapesInfoByType("Grid",[{szGridMap:t.gridMap,iGridColNum:t.gridColNum,iGridRowNum:t.gridRowNum}]);return 0}},{key:"JS_GetGridInfo",value:function e(){if(!this[w]){return{}}var t=this[w].getShapesInfoByType("Grid")[0];if(!t){return{iGridRowNum:18,iGridColNum:22,szGridMap:""}}return{gridColNum:t.iGridColNum,gridRowNum:t.iGridRowNum,gridMap:t.szGridMap}}},{key:"JS_SetDrawShapeInfo",value:function e(t,n){if(typeof t==="undefined"||t===""){return-1}this[w].setShapeType(t);this[w].setDrawStyle(n.szDrawColor||"",n.szFillColor||"",n.iTranslucent||0);if(n.iMaxShapeSupport&&n.iMaxShapeSupport>0){this[w].setMaxShapeSupport(n.iMaxShapeSupport)}if(n.iMaxShapeSupport>0){this[w].setCurrentShapeInfo({szId:"",szTips:n.szTips||"",iMinClosed:3,iMaxPointNum:n.iMaxPointSupport,iPolygonType:1,szDrawColor:n.szDrawColor||"",szFillColor:n.szFillColor||"",iTranslucent:n.iTranslucent||0})}}},{key:"JS_SetPolygonInfo",value:function e(t){if(typeof t==="undefined"||!t.length){return-1}var n=[];if(t.length>0){for(var i=0,r=t.length;i<r;i++){var a=t[i].aPoint;if(a.length>0){n.push(t[i])}}}if(n.length>0){this[w].setShapesInfoByType("Polygon",n);return 0}return-1}},{key:"JS_GetPolygonInfo",value:function e(){var t=[];var n=this[w].getShapesInfoByType("Polygon");for(var i=0,r=n.length;i<r;i++){var a=n[i];var o={aPoint:a.aPoint,bClosed:a.bClosed,szTips:a.szTips};t.push(o)}return t}},{key:"JS_SetLineInfo",value:function e(t){if(typeof t==="undefined"||!t.length){return-1}var n=[];if(t.length>0){for(var i=0,r=t.length;i<r;i++){var a=t[i].aPoint;if(a.length>0){n.push(t[i])}}}if(n.length>0){this[w].setShapesInfoByType("Line",n);return 0}return-1}},{key:"JS_GetLineInfo",value:function e(){var t=[];var n=this[w].getShapesInfoByType("Line");for(var i=0,r=n.length;i<r;i++){var a=n[i];var o={iLineType:a.iLineType,aPoint:a.aPoint,szTips:a.szTips};t.push(o)}return t}},{key:"JS_SetRectInfo",value:function e(t){if(typeof t==="undefined"||!t.length){return-1}var n=[];if(t.length>0){for(var i=0,r=t.length;i<r;i++){var a=t[i].aPoint;if(a.length>0){n.push(t[i])}}}if(n.length>0){this[w].setShapesInfoByType("Rect",n);return 0}return-1}},{key:"JS_GetRectInfo",value:function e(){var t=[];var n=this[w].getShapesInfoByType("Rect");for(var i=0,r=n.length;i<r;i++){var a=n[i];var o={aPoint:a.aPoint,szTips:a.szTips};t.push(o)}return t}},{key:"JS_SetRegionInfo",value:function e(t){var a=this;this[w].clearAllShape();var n=I.oTool.parseXmlFromStr(t);this[w].setDrawStyle("#ff0000","#343434",.3);if((0,O.default)(n).find("DetectionRegionInfo").length>0){this[w].setShapeType("Rect");var i=parseInt((0,O.default)(n).find("MaxRegionNum").eq(0).text(),10);this[w].setMaxShapeSupport(i);this[w].m_szDisplayMode=(0,O.default)(n).find("DisplayMode").eq(0).text();this[w].m_szVideoFormat=(0,O.default)(n).find("videoFormat").eq(0).text();this[w].m_iHorizontalResolution=parseInt((0,O.default)(n).find("HorizontalResolution").eq(0).text(),10);this[w].m_iVerticalResolution=parseInt((0,O.default)(n).find("VerticalResolution").eq(0).text(),10);var o=[];(0,O.default)(n).find("DetectionRegion").each(function(){var e=[];for(var t=0,n=(0,O.default)(this).find("positionX").length;t<n;t++){var i=Math.round((0,O.default)(this).find("positionX").eq(t).text())*a[w].m_iCanvasWidth/a[w].m_iHorizontalResolution;var r=(a[w].m_iVerticalResolution-Math.round((0,O.default)(this).find("positionY").eq(t).text()))*a[w].m_iCanvasHeight/a[w].m_iVerticalResolution;e.push([i,r])}if(e.length>0&&!(e[0][0]===0&&e[1][0]===0&&e[2][0]===0&&e[3][0]===0)){o.push({aPoint:e,iEditType:a[w].m_szDisplayMode==="transparent"?1:0,szDrawColor:"#ff0000",szFillColor:"#343434",iTranslucent:.3})}});this[w].setShapesInfoByType("Rect",o)}else if((0,O.default)(n).find("MoveDetection").length>0){this[w].setShapeType("Grid");var r=parseInt((0,O.default)(n).find("columnGranularity").eq(0).text(),10);var s=parseInt((0,O.default)(n).find("rowGranularity").eq(0).text(),10);var l=(0,O.default)(n).find("gridMap").eq(0).text();this[w].setShapesInfoByType("Grid",[{szGridMap:l,iGridColNum:r,iGridRowNum:s}])}return 0}},{key:"JS_GetRegionInfo",value:function e(){if(!this[w]){return""}var t=this[w].getShapeType();var n='<?xml version="1.0" encoding="utf-8"?>';if(t==="Rect"){n+="<DetectionRegionInfo>";n+="<videoFormat>"+this[w].m_szVideoFormat+"</videoFormat><RegionType>roi</RegionType>";n+="<ROI><HorizontalResolution>"+this[w].m_iHorizontalResolution+"</HorizontalResolution><VerticalResolution>"+this[w].m_iVerticalResolution+"</VerticalResolution></ROI>";n+="<DisplayMode>"+this[w].m_szDisplayMode+"</DisplayMode><MaxRegionNum>"+this[w].getMaxShapeSupport()+"</MaxRegionNum>";n+="<DetectionRegionList>";var i=this[w].getShapesInfoByType("Rect");for(var r=0,a=i.length;r<a;r++){var o=i[r].aPoint;n+="<DetectionRegion><RegionCoordinatesList>";n+="<RegionCoordinates><positionX>"+Math.round(o[3][0]*this[w].m_iHorizontalResolution/this[w].m_iCanvasWidth)+"</positionX><positionY>"+(this[w].m_iVerticalResolution-Math.round(o[3][1]*this[w].m_iVerticalResolution/this[w].m_iCanvasHeight))+"</positionY></RegionCoordinates>";n+="<RegionCoordinates><positionX>"+Math.round(o[2][0]*this[w].m_iHorizontalResolution/this[w].m_iCanvasWidth)+"</positionX><positionY>"+(this[w].m_iVerticalResolution-Math.round(o[2][1]*this[w].m_iVerticalResolution/this[w].m_iCanvasHeight))+"</positionY></RegionCoordinates>";n+="<RegionCoordinates><positionX>"+Math.round(o[1][0]*this[w].m_iHorizontalResolution/this[w].m_iCanvasWidth)+"</positionX><positionY>"+(this[w].m_iVerticalResolution-Math.round(o[1][1]*this[w].m_iVerticalResolution/this[w].m_iCanvasHeight))+"</positionY></RegionCoordinates>";n+="<RegionCoordinates><positionX>"+Math.round(o[0][0]*this[w].m_iHorizontalResolution/this[w].m_iCanvasWidth)+"</positionX><positionY>"+(this[w].m_iVerticalResolution-Math.round(o[0][1]*this[w].m_iVerticalResolution/this[w].m_iCanvasHeight))+"</positionY></RegionCoordinates>";n+="</RegionCoordinatesList></DetectionRegion>"}n+="</DetectionRegionList>";n+="</DetectionRegionInfo>"}else if(t==="Grid"){var s=this[w].getShapesInfoByType("Grid")[0];if(!s){s={iGridRowNum:18,iGridColNum:22,szGridMap:""}}n+="<MoveDetection><videoFormat>PAL</videoFormat><RegionType>grid</RegionType>";n+="<Grid><rowGranularity>"+s.iGridRowNum+"</rowGranularity><columnGranularity>"+s.iGridColNum+"</columnGranularity></Grid>";n+="<DisplayMode>transparent</DisplayMode>";n+="<gridMap>"+s.szGridMap+"</gridMap></MoveDetection>"}return n}},{key:"JS_SetDrawStatus",value:function e(t){if(!this[w]){return-1}this[w].setDrawStatus(t);return 0}},{key:"JS_ClearRegion",value:function e(){if(!this[w]){return-1}this[w].clearAllShape();return 0}},{key:"JS_GetTextOverlay",value:function e(){if(!this[w]){return""}var t='<?xml version="1.0" encoding="utf-8"?>';t+="<OSD>";t+="<videoResolutionWidth>"+this[w].m_iHorizontalResolution+"</videoResolutionWidth>";t+="<videoResolutionHeight>"+this[w].m_iVerticalResolution+"</videoResolutionHeight>";var n="";var i="";var r="";var a=this[w].getShapesInfoByType("RectOSD");for(var o=0,s=a.length;o<s;o++){var l=a[o];var u=Math.round(l.iPositionX*this[w].m_iHorizontalResolution/this[w].m_iCanvasWidth);var f=Math.round(l.iPositionY*this[w].m_iVerticalResolution/this[w].m_iCanvasHeight);if(l.szOSDType==="overlay-date"){n+="<DateTimeOverlay><Type>"+l.szDateStyle+"</Type>";n+="<clockType>"+l.szClockType+"</clockType>";n+="<displayWeek>"+l.szDisplayWeek+"</displayWeek>";n+="<enabled>"+l.szEnabled+"</enabled>";n+="<alignment>"+l.szAlignment+"</alignment>";n+="<positionX>"+u+"</positionX><positionY>"+f+"</positionY></DateTimeOverlay>"}else if(l.szOSDType==="overlay-ch"){i+="<channelNameOverlay><enabled>"+l.szEnabled+"</enabled>";i+="<ChannelName>"+l.szText+"</ChannelName>";i+="<alignment>"+l.szAlignment+"</alignment>";i+="<positionX>"+u+"</positionX><positionY>"+f+"</positionY></channelNameOverlay>"}else if(l.szOSDType==="overlay-text"){r+="<TextOverlay><id>"+l.szId+"</id><enabled>"+l.szEnabled+"</enabled>";r+="<alignment>"+l.szAlignment+"</alignment>";r+="<displayText>"+l.szText+"</displayText>";r+="<positionX>"+u+"</positionX><positionY>"+f+"</positionY></TextOverlay>"}}t+=n;t+=i;t+="<TextOverlayList>";t+=r;t+="</TextOverlayList>";t+="</OSD>";return t}},{key:"JS_SetTextOverlay",value:function e(t){var o=this;this[w].setMaxShapeSupport(20);var n=I.oTool.parseXmlFromStr(t);this[w].clearShapeByType("RectOSD");if((0,O.default)(n).find("OSD").length>0){this[w].setDrawStyle("#ff0000","#343434",.7);this[w].m_iHorizontalResolution=parseInt((0,O.default)(n).find("videoResolutionWidth").eq(0).text(),10);this[w].m_iVerticalResolution=parseInt((0,O.default)(n).find("videoResolutionHeight").eq(0).text(),10);if((0,O.default)(n).find("channelNameOverlay").length>0){var i=(0,O.default)(n).find("channelNameOverlay").eq(0);var r=(0,O.default)(i).find("ChannelName").eq(0).text();var a=(0,O.default)(i).find("enabled").eq(0).text();var s=Math.round((0,O.default)(i).find("positionX").eq(0).text())*this[w].m_iCanvasWidth/this[w].m_iHorizontalResolution;var l=Math.round((0,O.default)(i).find("positionY").eq(0).text())*this[w].m_iCanvasHeight/this[w].m_iVerticalResolution;var u=(0,O.default)(i).find("alignment").eq(0).text()||"0";this[w].addOSDShape(r,a,s,l,{szOSDType:"overlay-ch",szAlignment:u})}if((0,O.default)(n).find("DateTimeOverlay").length>0){var f=(0,O.default)(n).find("DateTimeOverlay").eq(0);var h=(0,O.default)(f).find("enabled").eq(0).text();var c=(0,O.default)(f).find("Type").eq(0).text()||(0,O.default)(f).find("type").eq(0).text();var d=(0,O.default)(f).find("displayWeek").eq(0).text();var p=(0,O.default)(f).find("clockType").eq(0).text();var v="";var m="";var y=["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"];var g=new Date;if(d==="true"){m=y[g.getDay()]}if(p==="24hour"){p=""}else{p="AM/PM"}switch(c){case"0":v="YYYY-MM-DD "+m+" hh:mm:ss "+p;break;case"1":v="MM-DD-YYYY "+m+" hh:mm:ss "+p;break;case"2":v="CHR-YYYY-MM-DD "+m+" hh:mm:ss "+p;break;case"3":v="CHR-MM-DD-YYYY "+m+" hh:mm:ss "+p;break;case"4":v="DD-MM-YYYY "+m+" hh:mm:ss "+p;break;case"5":v="CHR-DD-MM-YYYY "+m+" hh:mm:ss "+p;break;default:break}var S=Math.round((0,O.default)(f).find("positionX").eq(0).text())*this[w].m_iCanvasWidth/this[w].m_iHorizontalResolution;var b=Math.round((0,O.default)(f).find("positionY").eq(0).text())*this[w].m_iCanvasHeight/this[w].m_iVerticalResolution;var P=(0,O.default)(f).find("alignment").eq(0).text()||"0";this[w].addOSDShape(v,h,S,b,{szOSDType:"overlay-date",szDateStyle:c,szDisplayWeek:d,szClockType:p,szAlignment:P})}if((0,O.default)(n).find("TextOverlayList").length>0){(0,O.default)(n).find("TextOverlayList").eq(0).find("TextOverlay").each(function(){var e=(0,O.default)(this).find("displayText").eq(0).text();var t=(0,O.default)(this).find("enabled").eq(0).text();var n=(0,O.default)(this).find("id").eq(0).text();var i=Math.round((0,O.default)(this).find("positionX").eq(0).text())*o[w].m_iCanvasWidth/o[w].m_iHorizontalResolution;var r=Math.round((0,O.default)(this).find("positionY").eq(0).text())*o[w].m_iCanvasHeight/o[w].m_iVerticalResolution;var a=(0,O.default)(this).find("alignment").eq(0).text()||"0";o[w].addOSDShape(e,t,i,r,{szOSDType:"overlay-text",szId:n,szAlignment:a})})}}return 0}},{key:"JS_ClearSnapInfo",value:function e(t){if(!this[w]){return-1}if(t===0){this[w].clearShapeByType("Rect")}else if(t===1){this[w].clearShapeByType("Polygon")}else if(t===2){this[w].clearShapeByType("Line")}else if(t===3){this[w].clearShapeByType("Rect");this[w].clearShapeByType("Polygon")}else if(t===5){this[w].clearShapeByType("Point")}else{this[w].clearAllShape()}return 0}},{key:"JS_ClearTargetPolygon",value:function e(t){var n=I.oTool.parseXmlFromStr(t);var i=this[w].getAllShapesInfo();var r=i.length;if(r>0){for(var a=0;a<r;a++){var o=(0,O.default)(n).find("id").eq(0).text();if(i[a].szType==="Polygon"){if(i[a].szId===o){this[w].deleteShape(a);break}}}}}},{key:"JS_SetSnapPolygonInfo",value:function e(t){var c=this;this[w].setShapeType("Polygon");this[w].setMaxShapeSupport(20);this[w].setDrawStyle("#FFFF00","#FFFF00",.1);var n=I.oTool.parseXmlFromStr(t);var i=this[w].getAllShapesInfo();var r=i.length;if(r>0){for(var a=0;a<r;a++){var o=(0,O.default)(n).find("id").eq(0).text();if(i[a].szType==="Polygon"){if(i[a].szId===o){this[w].deleteShape(a);break}}}}var d=[];if((0,O.default)(n).find("SnapPolygonList").length>0){(0,O.default)(n).find("SnapPolygonList").eq(0).find("SnapPolygon").each(function(){var e=(0,O.default)(this).find("id").eq(0).text();var t=parseInt((0,O.default)(this).find("polygonType").eq(0).text()||"1",10);var n=(0,O.default)(this).find("Tips").eq(0).text()||(0,O.default)(this).find("tips").eq(0).text();var i=parseInt((0,O.default)(this).find("MinClosed").eq(0).text(),10);var r=parseInt((0,O.default)(this).find("PointNumMax").eq(0).text(),10);var a=parseInt((0,O.default)(this).find("EditType").eq(0).text(),10)||parseInt((0,O.default)(this).find("editType").eq(0).text(),10)||0;var o=(0,O.default)(this).find("isClosed").eq(0).text()==="true";var s="rgb("+(0,O.default)(this).find("r").eq(0).text()+","+(0,O.default)(this).find("g").eq(0).text()+","+(0,O.default)(this).find("b").eq(0).text()+")";var l=s;var u=.1;var f=parseInt((0,O.default)(this).find("RedrawMode").eq(0).text(),10)||0;var h=[];(0,O.default)(this).find("pointList").eq(0).find("point").each(function(e){h[e]=[];h[e][0]=Math.round((0,O.default)(this).find("x").eq(0).text()*c[w].m_iCanvasWidth);h[e][1]=Math.round((0,O.default)(this).find("y").eq(0).text()*c[w].m_iCanvasHeight)});if(h.length>0){d.push({szId:e,iPolygonType:t,iMinClosed:i,iMaxPointNum:r,iEditType:a,aPoint:h,bClosed:o,szTips:n,szDrawColor:s,szFillColor:l,iTranslucent:u,iRedrawMode:f});c[w].setDrawStatus(false)}else{c[w].setCurrentShapeInfo({szId:e,szTips:n,iMinClosed:i,iMaxPointNum:r,iPolygonType:t,szDrawColor:s,szFillColor:l,iTranslucent:u,iRedrawMode:f});c[w].setDrawStatus(true)}})}if(d.length>0){this[w].setShapesInfoByType("Polygon",d)}return 0}},{key:"JS_GetSnapPolygonInfo",value:function e(){var t="<?xml version='1.0' encoding='utf-8'?><SnapPolygonList>";var n=this[w].getShapesInfoByType("Polygon");for(var i=0,r=n.length;i<r;i++){var a=n[i];t+="<SnapPolygon>";t+="<id>"+a.szId+"</id>";t+="<polygonType>"+a.iPolygonType+"</polygonType>";t+="<color>";var o=a.szDrawColor.substring(4,a.szDrawColor.length-1).split(",");t+="<r>"+o[0]+"</r>";t+="<g>"+o[1]+"</g>";t+="<b>"+o[2]+"</b>";t+="</color>";t+="<tips>"+a.szTips+"</tips>";t+="<isClosed>"+a.bClosed+"</isClosed>";var s=a.aPoint;t+="<pointList>";for(var l=0,u=s.length;l<u;l++){t+="<point><x>"+(s[l][0]/this[w].m_iCanvasWidth).toFixed(6)+"</x><y>"+(s[l][1]/this[w].m_iCanvasHeight).toFixed(6)+"</y></point>"}t+="</pointList>";t+="</SnapPolygon>"}t+="</SnapPolygonList>";return t}},{key:"JS_SetSnapDrawMode",value:function e(t,n){var i=this;if(!this[w]){return-1}this[w].setDrawMutiShapeOneTime(false);if(t===0&&n===3){i[w].setDrawStatus(false)}return 0}},{key:"JS_SetSnapLineInfo",value:function e(t){var s=this;this[w].setShapeType("Line");this[w].setMaxShapeSupport(20);this[w].setDrawStyle("#FFFF00","#FFFF00",.1);var n=I.oTool.parseXmlFromStr(t);var i=this[w].getAllShapesInfo();var r=i.length;if(r>0){for(var a=0;a<r;a++){var o=(0,O.default)(n).find("id").eq(0).text();if(i[a].szType==="Line"){if(i[a].szId===o){this[w].deleteShape(a);break}}}}var l=[];if((0,O.default)(n).find("SnapLineList").length>0){(0,O.default)(n).find("SnapLineList").eq(0).find("SnapLine").each(function(){var e=(0,O.default)(this).find("id").eq(0).text();var t=parseInt((0,O.default)(this).find("LineTypeEx").eq(0).text(),10);var n=parseInt((0,O.default)(this).find("CustomType").text()||(0,O.default)(this).find("LineType").text(),10);var i=parseInt((0,O.default)(this).find("ArrowType").text()||0,10);var r=(0,O.default)(this).find("Tips").eq(0).text()||(0,O.default)(this).find("tips").eq(0).text();var a="rgb("+(0,O.default)(this).find("r").eq(0).text()+","+(0,O.default)(this).find("g").eq(0).text()+","+(0,O.default)(this).find("b").eq(0).text()+")";var o=[];o[0]=[];o[1]=[];o[0][0]=Math.round((0,O.default)(this).find("StartPos").eq(0).find("x").eq(0).text()*s[w].m_iCanvasWidth);o[0][1]=Math.round((0,O.default)(this).find("StartPos").eq(0).find("y").eq(0).text()*s[w].m_iCanvasHeight);o[1][0]=Math.round((0,O.default)(this).find("EndPos").eq(0).find("x").eq(0).text()*s[w].m_iCanvasWidth);o[1][1]=Math.round((0,O.default)(this).find("EndPos").eq(0).find("y").eq(0).text()*s[w].m_iCanvasHeight);if(o.length>0){l.push({szId:e,iLineType:t,aPoint:o,szTips:r,iDirection:n,iArrowType:i,szDrawColor:a});s[w].setDrawStatus(false)}})}if(l.length>0){this[w].setShapesInfoByType("Line",l)}return 0}},{key:"JS_GetSnapLineInfo",value:function e(){var t=this;var n="<?xml version='1.0' encoding='utf-8'?><SnapLineList>";var i=this[w].getShapesInfoByType("Line");for(var r=0,a=i.length;r<a;r++){n+="<SnapLine>";n+="<id>"+i[r].szId+"</id>";n+="<LineTypeEx>"+i[r].iLineType+"</LineTypeEx>";n+="<CustomType>0</CustomType><MoveChange>0</MoveChange><ArrowType>"+i[r].iArrowType+"</ArrowType>";n+="<tips>"+i[r].szTips+"</tips>";var o=i[r].aPoint;n+="<StartPos><x>"+(o[0][0]/t[w].m_iCanvasWidth).toFixed(6)+"</x><y>"+(o[0][1]/t[w].m_iCanvasHeight).toFixed(6)+"</y></StartPos>";n+="<EndPos><x>"+(o[1][0]/t[w].m_iCanvasWidth).toFixed(6)+"</x><y>"+(o[1][1]/t[w].m_iCanvasHeight).toFixed(6)+"</y></EndPos>";n+="<LineSelected>false</LineSelected>";if(i[r].aCrossArrowPoint.length>0){n+="<PDCArrow><Sp_x>"+(i[r].aCrossArrowPoint[0][0]/t[w].m_iCanvasWidth).toFixed(6)+"</Sp_x>";n+="<Sp_y>"+(i[r].aCrossArrowPoint[0][1]/t[w].m_iCanvasWidth).toFixed(6)+"</Sp_y>";n+="<Ep_x>"+(i[r].aCrossArrowPoint[1][0]/t[w].m_iCanvasWidth).toFixed(6)+"</Ep_x>";n+="<Ep_y>"+(i[r].aCrossArrowPoint[1][1]/t[w].m_iCanvasWidth).toFixed(6)+"</Ep_y></PDCArrow>"}n+="<PDCShowMark>false</PDCShowMark>";var s=i[r].szDrawColor.split(",")[0].split("(")[1];var l=i[r].szDrawColor.split(",")[1];var u=i[r].szDrawColor.split(",")[2].split(")")[0];n+="<color><r>"+(s||"255")+"</r><g>"+(l||"255")+"</g><b>"+(u||"0")+"</b></color>";n+="</SnapLine>"}n+="</SnapLineList>";return n}},{key:"JS_GetSnapPointInfo",value:function e(){return""}},{key:"JS_SelectShapeById",value:function e(t,n){if(!t){return-1}this[w].selectShapeById(t,n);return 0}},{key:"JS_DeleteChoosedShape",value:function e(){var t=this[w].getAllShapesInfo();var n=t.length;if(n>0){for(var i=0;i<n;i++){if(t[i].bChoosed){this[w].deleteShape(i);return t[i].szId}}return""}return""}},{key:"JS_SetSnapPointInfo",value:function e(t){var i=this;var n=I.oTool.parseXmlFromStr(t);if((0,O.default)(n).find("mode").eq(0).text()==="1"){var r=(0,O.default)(n).find("id").eq(0).text();var a="rgb("+(0,O.default)(n).find("r").eq(0).text()+","+(0,O.default)(n).find("g").eq(0).text()+","+(0,O.default)(n).find("b").eq(0).text()+")";this[w].setShapeType("Point");this[w].setDrawStatus(true);this[w].setMaxShapeSupport(12);i[w].setCurrentShapeInfo({szId:r,szDrawColor:a,iTranslucent:1})}else{var o=[];this[w].clearShapeByType("Point");this[w].setMaxShapeSupport(12);this[w].setDrawStatus(false);if((0,O.default)(n).find("SnapPointList").length>0){(0,O.default)(n).find("SnapPointList").eq(0).find("SnapPoint").each(function(){var e=(0,O.default)(this).find("id").eq(0).text();var t="rgb("+(0,O.default)(this).find("r").eq(0).text()+","+(0,O.default)(this).find("g").eq(0).text()+","+(0,O.default)(this).find("b").eq(0).text()+")";var n=[];n[0]=[];n[0][0]=Math.round((0,O.default)(this).find("positionX").eq(0).text()*i[w].m_iCanvasWidth);n[0][1]=Math.round((0,O.default)(this).find("positionY").eq(0).text()*i[w].m_iCanvasHeight);if(n.length>0){o.push({szId:e,aPoint:n,szDrawColor:t,iTranslucent:1})}})}if(o.length>0){this[w].setShapesInfoByType("Point",o)}}return 0}},{key:"JS_FullScreenDisplay",value:function e(t){var n=this;if(navigator.userAgent.match(/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i)){var i=document.documentElement.clientWidth;var r=document.documentElement.clientHeight;var a=navigator.userAgent.match(/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i);if(a){i=i*2;r=r*2;n.oJSPlugin.find("."+n[S].szId+"parent-wnd").eq(0).css({width:i,height:r,"z-index":1e4})}var o=document.body;o=(0,O.default)("#"+n[S].szId).find("canvas#"+n[S].szId+"canvas0")[0];var s="";if(i>=r){s+="width:"+i+"px;";s+="height:"+r+"px;";s+="-webkit-transform: none";s+="-webkit-transform-origin: 0 0;";s+="transform-origin: 0 0;"}else{s+="width:"+r+"px;";s+="height:"+i+"px;";s+="-webkit-transform: rotate(90deg); transform: rotate(90deg);";s+="-webkit-transform-origin: "+i/2+"px "+i/2+"px;";s+="transform-origin: "+i/2+"px "+i/2+"px;"}s+="position: fixed;top: 0;left: 0;z-index:10";o.style.cssText=s;this[P]=t}else{if(t){var l=n.oJSPlugin.get(0);if(l.requestFullScreen){l.requestFullScreen()}else if(l.webkitRequestFullScreen){l.webkitRequestFullScreen()}else if(l.mozRequestFullScreen){l.mozRequestFullScreen()}else if(l.msRequestFullscreen){l.msRequestFullscreen()}else if(l.oRequestFullscreen){l.oRequestFullscreen()}}this[P]=t}}},{key:"JS_CancelFullScreenDisplay",value:function e(){var t=this;if(navigator.userAgent.match(/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i)){var n=(0,O.default)("."+t[S].szId+"parent-wnd").parent().eq(0).width();var i=(0,O.default)("."+t[S].szId+"parent-wnd").parent().eq(0).height();var r=navigator.userAgent.match(/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i);if(r){n=n*2;i=i*2;t.oJSPlugin.find("."+t[S].szId+"parent-wnd").eq(0).css({width:n,height:i,"z-index":0})}var a=document.body;a=(0,O.default)("#"+t[S].szId).find("canvas#"+t[S].szId+"canvas0")[0];var o="";if(n>=i){o+="width:"+n+"px;";o+="height:"+i+"px;";o+="-webkit-transform: none";o+="-webkit-transform-origin: 0 0;";o+="transform-origin: 0 0;"}else{o+="width:"+i+"px;";o+="height:"+n+"px;";o+="-webkit-transform: rotate(90deg); transform: rotate(90deg);";o+="-webkit-transform-origin: "+n/2+"px "+n/2+"px;";o+="transform-origin: "+n/2+"px "+n/2+"px;"}a.style.cssText=o}else{if(t.oJSPlugin.find("."+t[S].szId+"parent-wnd").eq(0).width()===(0,O.default)(window).width()){return}if(document.exitFullscreen){document.exitFullscreen()}else if(document.webkitCancelFullScreen){document.webkitCancelFullScreen()}else if(document.mozCancelFullScreen){document.mozCancelFullScreen()}}this[_]=null;this[P]=false}},{key:"JS_FullScreenSingle",value:function e(t){var n=this;if(!n[g][t].bPlay){return}var i=document.fullscreen||document.webkitIsFullScreen||document.mozFullScreen||false;var r=n.oJSPlugin.find("."+n[S].szId+"parent-wnd").eq(0).children().eq(t).get(0);if(!i){if(r.requestFullScreen){r.requestFullScreen()}else if(r.webkitRequestFullScreen){r.webkitRequestFullScreen()}else if(r.mozRequestFullScreen){r.mozRequestFullScreen()}n[_]=n.oJSPlugin.find("."+n[S].szId+"parent-wnd").eq(0).children().eq(t)}else{if(n.oJSPlugin.find("."+n[S].szId+"parent-wnd").eq(0).width()===(0,O.default)(window).width()){return}if(document.exitFullscreen){document.exitFullscreen()}else if(document.webkitCancelFullScreen){document.webkitCancelFullScreen()}else if(document.mozCancelFullScreen){document.mozCancelFullScreen()}n[_]=null;n[P]=false}}},{key:"JS_StartDownload",value:function e(t,n,i,r){var a=(0,O.default)(I.oTool.parseXmlFromStr(r)).find("playbackURI").eq(0).text();var o=t+"?playbackURI="+a;var s=".mp4";if(t.indexOf("picture/Streaming/tracks")>0){o=t;s=".jpg"}var l=o.indexOf("&name=")+6;var u=o.indexOf("&size=");i=o.substring(l,u);(0,O.default)("body").append('<a id="jsplugin_download_a" href="'+o+'" download='+i+s+'><li id="jsplugin_download_li"></li></a>');(0,O.default)("#jsplugin_download_li").trigger("click");(0,O.default)("#jsplugin_download_a").remove();return 0}},{key:"JS_Resize",value:function e(r,a){var o=this;var s=this;var l=navigator.userAgent.match(/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i);if(l){r=r*2;a=a*2}setTimeout(function(){var e=document.fullscreen||document.webkitIsFullScreen||document.mozFullScreen||false;if(o[P]&&e){r=(0,O.default)(window).width();a=(0,O.default)(window).height();s.oJSPlugin.css({width:r,height:a})}else{s.oJSPlugin.css({width:l?r/2:r,height:l?a/2:a})}var t=document.body;t=(0,O.default)("#"+s[S].szId).find("canvas#"+s[S].szId+"canvas0")[0];var n="";n+="width:"+r+"px;";n+="height:"+a+"px;";n+="-webkit-transform: none;";n+="-webkit-transform-origin: 0 0;";n+="transform-origin: 0 0;";t.style.cssText=n;o[S].iWidth=r;o[S].iHeight=a;o.iWidth=r;o.iHeight=a;if(I.oTool.isFirefox()){for(var i=0;i<s[S].iMaxSplit*s[S].iMaxSplit;i++){if(s[g][i].oPlayCtrl){s[g][i].oPlayCtrl.PlayM4_ClearCanvas()}}}s.updateWnd();if(s[_]&&e){r=(0,O.default)(window).width();a=(0,O.default)(window).height();s[_].css({width:r,height:a});s[_].find("."+s[S].szId+"play-window").attr("width",r);s[_].find("."+s[S].szId+"play-window").attr("height",a);s[_].find("."+s[S].szId+"draw-window").attr("width",r-2);s[_].find("."+s[S].szId+"draw-window").attr("height",a-2)}if(!e){s[_]=null;s[P]=false}o[w].resizeCanvas();o[w].canvasRedraw()},80)}},{key:"JS_WndCreate",value:function e(t,n,i){var r=this;r.createWindows(n,i);this[w].updateCanvas(r[S].szId+"canvas_draw0");this[w].clearAllShape();if(t===0){r.oJSPlugin.hide()}else{r.oJSPlugin.show()}r.EventCallback.windowEventSelect(0);r.initEvent();r[C]=null}},{key:"JS_ExportDeviceConfig",value:function e(t){(0,O.default)("body").append('<a id="jsplugin_download_a" href="'+t+'"><li id="jsplugin_download_li"></li></a>');(0,O.default)("#jsplugin_download_li").trigger("click");(0,O.default)("#jsplugin_download_a").remove();return 0}},{key:"JS_OpenFileBrowser",value:function e(t,n,i,r,a){var o=this;o[C]=null;var s=window.document.createElement("input");s.type="file";if(n.toLowerCase()==="bmp"){s.accept="image/bmp";s.style.display="none";(0,O.default)("body").append(s)}if(t===0){s.setAttribute("webkitdirectory","")}s.addEventListener("change",function(){if(t===1){o[C]=s.files[0];i[r]=s.files[0].name;a&&a.$digest(function(){i[r]=s.files[0].name})}else if(t===0){o[C]=s.files}});var l=document.createEvent("MouseEvents");l.that.initEvent("click",true,true);s.dispatchEvent(l)}},{key:"JS_UploadFile",value:function e(t,n,i,r){var a=this;var o=0;var s=new XMLHttpRequest;s.onreadystatechange=function(){if(s.readyState===4){if(s.status!==200){o=-1}}};s.open("put",t,false);s.setRequestHeader("Content-Type",r);s.send(a[C]);return o}},{key:"JS_StartAsynUpload",value:function e(t){var n=this;var i=new XMLHttpRequest;i.onreadystatechange=function(){if(i.readyState===4){n[D]=i.responseText}};i.open("put",t,true);i.send(n[C]);return 0}},{key:"JS_StopAsynUpload",value:function e(){var t=this;t[D]=""}},{key:"JS_GetUploadErrorInfo",value:function e(){var t=this;if(typeof t[D]==="string"&&t[D].length>0){return t[D]}return""}},{key:"JS_StartUpgradeEx",value:function e(r,a){var o=this;var t=new Promise(function(t,n){if(!r){n();return s}if(!a){n();return s}o[D]=0;var i=new XMLHttpRequest;i.onreadystatechange=function(){if(i.readyState===4){if(i.status===200){o[D]=100;t()}else{o[D]=1;var e=I.oTool.parseXmlFromStr(i.responseText);if((0,O.default)(e).find("subStatusCode").text()==="lowPrivilege"){n(403)}else{n()}}}};i.open("put",r,true);i.send(o[C]);o[T]=a;setTimeout(function(){t()},3e3)});return t}},{key:"JS_UpgradeStatus",value:function e(){var t=this;if(t[D]===100){return 0}return t[D]}},{key:"JS_UpgradeProgress",value:function e(){var t=this;var n=0;var i=new XMLHttpRequest;i.onreadystatechange=function(){if(i.readyState===4){if(i.status===200){n=parseInt((0,O.default)(I.oTool.parseXmlFromStr(i.responseText)).find("percent").text(),10)}}};i.open("get",t[T],false);i.send(null);if(t[D]===100){return 100}return n}},{key:"JS_StopUpgrade",value:function e(){var t=this;t[C]=null;return 0}},{key:"JS_ExportDeviceLog",value:function e(t,n){n="Log.txt";var i=[];var r=[];i=i.concat((0,O.default)(t).find("searchMatchItem").toArray());for(var a=0;a<i.length;a++){r[a]=[];r[a][0]=(0,O.default)(i[a]).find("logtime").text().replace("T"," ").replace("Z","");r[a][1]=(0,O.default)(i[a]).find("majortype").text();r[a][2]=(0,O.default)(i[a]).find("minortype").text();r[a][3]=(0,O.default)(i[a]).find("channelid").text();r[a][4]=(0,O.default)(i[a]).find("userName").text();r[a][5]=(0,O.default)(i[a]).find("remoteaddress").text()}var o=[];function s(e){o.push(e);var t=e.slice("");if(/^[\u4e00-\u9fa5]/.test(e)){for(var n=0;n<30-t.length*2;n++){o.push(" ")}}else{for(var i=0;i<30-t.length;i++){o.push(" ")}}}s(" ");s((0,O.default)(t).find("laLogTime").text());s((0,O.default)(t).find("laLogMajorType").text());s((0,O.default)(t).find("laLogMinorType").text());s((0,O.default)(t).find("laLogChannel").text());s((0,O.default)(t).find("laLogRemoteUser").text());s((0,O.default)(t).find("laLogRemoteIP").text());o.push("\r\n");for(var l=0;l<r.length;l++){var u=(l+1).toString();s(u);for(var f=0;f<6;f++){s(r[l][f])}o.push("\r\n")}o=o.join("");var h=new Blob([o],{type:"text/plain"});var c=(window.URL||window.webkitURL).createObjectURL(h);var d=window.document.createElement("a");d.href=c;d.download=n;var p=document.createEvent("MouseEvents");p.that.initEvent("click",true,true);d.dispatchEvent(p)}},{key:"outCsv",value:function e(t,n,i,r){var a="";var o="";for(var s=0;s<i.length;s++){o+=(0,O.default)(t).find(i[s]).eq(0).text()+","}o=o.slice(0,-1);a+=o+"\r\n";for(var l=0;l<r.length;l++){o="";for(var u=0;u<i.length;u++){o+='"'+r[l][u]+'",'}o.slice(0,o.length-1);a+=o+"\r\n"}if(a===""){return}var f="";f+=n;var h="\ufeff";if(window.navigator.msSaveOrOpenBlob){var c=h+a;var d=new Blob([decodeURIComponent(encodeURI(c))],{type:"data:text/csv;charset=utf-8,"});navigator.msSaveBlob(d,f+".csv")}else{h="data:text/csv;charset=utf-8,\ufeff";var p=h+a;var v=encodeURI(p);var m=document.createElement("a");m.setAttribute("href",v);m.setAttribute("download",f+".csv");document.body.appendChild(m);m.click()}}},{key:"JS_ExportReport",value:function e(m,y,g){var S=this;var t=new Promise(function(e){var t=[];var n=[];var i=(0,O.default)(m).find("NameList").text().split(",");t=t.concat((0,O.default)(m).find("tDataItem").toArray());for(var r=0;r<t.length;r++){n[r]=[];for(var a=0;a<i.length;a++){n[r][a]=(0,O.default)(t[r]).find(i[a]).text();if(i[a]==="logtime"){n[r][a]=n[r][a].replace("T"," ").replace("Z","")}}}var o=[];if(g===1){S.outCsv(m,y,i,n)}else{var s=function e(t){o.push(t);var n=t.slice("");if(/^[\u4e00-\u9fa5]/.test(t)){for(var i=0;i<30-n.length*2;i++){o.push(" ")}}else{for(var r=0;r<30-n.length;r++){o.push(" ")}}};s(" ");for(var l=0;l<i.length;l++){s((0,O.default)(m).find(i[l]).eq(0).text())}o.push("\r\n");for(var u=0;u<n.length;u++){var f=(u+1).toString();s(f);for(var h=0;h<i.length;h++){s(n[u][h])}o.push("\r\n")}o=o.join("");var c=void 0;c=new Blob([o],{type:"text/plain"});var d=(window.URL||window.webkitURL).createObjectURL(c);var p=window.document.createElement("a");p.href=d;p.download=y;var v=document.createEvent("MouseEvents");v.that.initEvent("click",true,true);p.dispatchEvent(v)}e()});return t}},{key:"JS_GetWndContainer",value:function e(t){var n=this;if(t<0||typeof t==="undefined"||t===null){return-1}return n.oJSPlugin.find("."+n[S].szId+"parent-wnd").eq(0).children().eq(t)[0]}},{key:"JS_GetWndStatus",value:function e(t){if(t<0||typeof t==="undefined"||t===null){return-1}var n={bPlay:this[g][t].bPlay,bSound:this[l]===t,bSelect:this[g][t].bSelect,iRate:this[g][t].iRate};return n}},{key:"JS_SelectWnd",value:function e(t){var n=this;n.oJSPlugin.find("."+n[S].szId+"parent-wnd").eq(0).children().eq(t).mousedown()}},{key:"JS_PlayWithImg",value:function e(t){var n=this;if(this[S].iType!==1){return}var i=(0,O.default)("#"+n[S].szId+"canvas0");var r=(0,O.default)("#"+n[S].szId+"canvas0").width();var a=(0,O.default)("#"+n[S].szId+"canvas0").height();i.hide();var o=(0,O.default)("#playImg0");o.show();o.css({width:r+"px",height:a+"px"});o.attr("src",t)}},{key:"JS_SetCanFullScreen",value:function e(t){this[R]=t}},{key:"JS_OpenPlayerSDKPrintLog",value:function e(t){var n=this;n[g][t].oPlayCtrl.PlayM4_OpenPlayerSDKPrintLog(true)}},{key:"JS_DownloadYUVdata",value:function e(t){var n=this;n[g][t].oPlayCtrl.PlayM4_DownloadYUVdata()}},{key:"JS_DownloadPCMdata",value:function e(t){var n=this;n[g][t].oPlayCtrl.PlayM4_DownloadPCMdata()}},{key:"_JSPlayM4_GetFrameInfo",value:function e(){var t=this;var n=t[g][0].oPlayCtrl;return{width:n.nWidth,height:n.nHeight}}},{key:"_JSPlayM4_SetDisplayRegion",value:function e(t,n,i,r){var a=this;var o=a[g][0].oPlayCtrl;a[g][0].oPlayCtrl.oSuperRender.SR_SetDisplayRect({top:i*a.iHeight/o.nHeight,left:t*a.iWidth/o.nWidth,right:n*a.iWidth/o.nWidth,bottom:r*a.iHeight/o.nHeight})}}]);return a}();return e}();t.JSPluginV1=o},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:true});t.StreamClient=undefined;var i=function(){function i(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||false;i.configurable=true;if("value"in i)i.writable=true;Object.defineProperty(e,i.key,i)}}return function(e,t,n){if(t)i(e.prototype,t);if(n)i(e,n);return e}}();var c=n(2);var r=n(6);var a=n(7);var o=n(8);var s=n(11);function l(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var u=function(){var C=Symbol("WEBSOCKET");var D=Symbol("GETINDEX");var T=Symbol("PROTOCOLVERSION");var M=Symbol("CIPHERSUITES");var h=new r.DirectDeviceCustom;var k=new a.DirectDevice;var x=new o.LiveMedia;var R=new s.LocalService;var e=function(){function e(){l(this,e);this[T]="0.1";this[M]=0;this[C]=[];this.ERRORS={};this[D]=function(e){var t=-1;for(var n=0,i=this[C].length;n<i;n++){if(this[C][n].id===e){t=n;break}}return t}}i(e,[{key:"openStream",value:function e(t,p,v,n){var i=false;if(t.indexOf("[")>-1){i=true}var m=this;var r=t.split("://");var a=r[0];var y="";var g=7681;var S=1;var b=0;var o=false;if(i){y=r[1].split("]:")[0]+"]";g=Math.floor(r[1].split("]:")[1].split("/")[0]);S=Math.floor(r[1].split("]:")[1].split("/")[1]/100);b=Math.floor(r[1].split("]:")[1].split("/")[1]%100)-1}else{y=r[1].split(":")[0];g=Math.floor(r[1].split(":")[1].split("/")[0]);S=Math.floor(r[1].split(":")[1].split("/")[1]/100);b=Math.floor(r[1].split(":")[1].split("/")[1]%100)-1;var s=r[1].split(":")[1].split("/")[2];if(s!==""&&s==="webSocketVideoCtrlProxy"){o=true}}if(S===0){b=0}p=p||{};var l="&sessionID=";if(p.token&&!p.playURL){l="&token="}var u=p.sessionID||p.session||(p.playURL?"":p.token)||"";var P=new window.WebSocket(a+"://"+y+":"+g+(p.mode?"/"+p.mode:"")+(o?"/webSocketVideoCtrlProxy":"")+"?version="+m[T]+"&cipherSuites="+m[M]+l+u+(p.proxy?"&proxy="+p.proxy:""));P.binaryType="arraybuffer";var w=(0,c.v4)();var _=-1;var f=new Promise(function(c,d){P.onopen=function(){if(!p.playURL&&!p.sessionID&&!p.deviceSerial&&!p.token){m[C].push(h.createClientObject(P,w,S,b));c(w)}};P.onmessage=function(e){if(typeof e.data==="string"){var t=JSON.parse(e.data);var n=m[D](w);if(t&&t.version&&t.cipherSuite){m[T]=t.version;m[M]=parseInt(t.cipherSuite,10);if(t&&t.PKD&&t.rand){m[C].push(x.createClientObject(P,w,t.PKD,t.rand,p))}else{var i="live://"+y+":"+g+"/"+S+"/"+b;if(m[M]===-1){m[C].push(R.createClientObject(P,w,i,p))}else{m[C].push(k.createClientObject(P,w,i))}}c(w);return}if(t&&t.sdp){var r=k.getMediaFromSdp(t.sdp);v({bHead:true,buf:r})}if(t&&t.cmd){if(t.cmd==="end"){v({type:"exception",cmd:t.cmd})}}if(t&&t.statusString){if(t.statusString.toLowerCase()==="ok"){if(m[C][n].resolve){m[C][n].resolve(t)}}if(t.statusString.toLowerCase()!=="ok"){var a=k.getError(t);if(n>-1){if(m[C][n].reject){m[C][n].reject(a)}}else{d(a)}}}}else{var o={};var s=new Uint8Array(e.data);_++;if(s.byteLength===64||s.byteLength===40){var l=-1;var u=s.byteLength;for(var f=0;f<u;f++){if(s[f]===73&&s[f+1]===77&&s[f+2]===75&&s[f+3]===72){l=f;break}}if(l!==-1){var h=s.slice(l,l+40);o={bHead:true,buf:h}}else{o={bHead:false,buf:s}}v(o)}else{if(_>0){o={bHead:false,buf:s};v(o)}}s=null;o=null;e=null}};P.onclose=function(){for(var e=0,t=m[C].length;e<t;e++){if(m[C][e].id===w){m[C][e].resolve();m[C].splice(e,1);setTimeout(function(){n()},200);break}}d()}});return f}},{key:"startPlay",value:function e(t,i,r){var a=this;var o=this[D](t);if(i&&r&&a[T]==="0.1"){i=i.replace(/-/g,"").replace(/:/g,"");r=r.replace(/-/g,"").replace(/:/g,"")}var n=new Promise(function(e,t){if(o>-1){a[C][o].resolve=e;a[C][o].reject=t;var n=null;if(!i||!r){if(a[C][o].iCurChannel===0&&a[T]==="0.1"){n=h.zeroPlayCmd(a[C][o].iCurChannel,a[C][o].iCurStream)}else{if(a[T]!=="0.1"){if(a[M]===0){n=x.playCmd(a[C][o])}else if(a[M]===1){n=k.playCmd(a[C][o].playURL)}else if(a[M]===-1){n=R.playCmd(a[C][o])}}else{n=h.playCmd(a[C][o].iCurChannel,a[C][o].iCurStream)}}}else{if(a[T]!=="0.1"){if(a[M]===0){n=x.playbackCmd(a[C][o],i,r)}else if(a[M]===1){n=k.playbackCmd(i,r,a[C][o].playURL)}else if(a[M]===-1){n=R.playbackCmd(a[C][o],i,r)}}else{n=h.playbackCmd(i,r,a[C][o].iCurChannel,a[C][o].iCurStream)}}a[C][o].socket.send(n);if(a[T]==="0.1"){e()}}else{if(a[T]==="0.1"){t()}}});return n}},{key:"singleFrame",value:function e(){}},{key:"setPlayRate",value:function e(o,s){var l=this;var t=new Promise(function(e,t){for(var n=0,i=l[C].length;n<i;n++){if(l[C][n].id===o){if(l[T]==="0.1"){var r=h.playRateCmd(s);l[C][n].socket.send(r);e();break}else{l[C][n].resolve=e;l[C][n].reject=t;var a=k.playRateCmd(s);l[C][n].socket.send(a);e()}}}});return t}},{key:"seek",value:function e(a,o,s){var l=this;var t=new Promise(function(e,t){for(var n=0,i=l[C].length;n<i;n++){if(l[C][n].id===a){l[C][n].resolve=e;l[C][n].reject=t;var r=x.seekCmd(o,s);l[C][n].socket.send(r)}}});return t}},{key:"pause",value:function e(o){var s=this;var t=new Promise(function(e,t){for(var n=0,i=s[C].length;n<i;n++){if(s[C][n].id===o){if(s[T]==="0.1"){var r=h.pauseCmd();s[C][n].socket.send(r);e();break}else{s[C][n].resolve=e;s[C][n].reject=t;var a=k.pauseCmd();s[C][n].socket.send(a)}}}});return t}},{key:"transmission",value:function e(r,a){var o=this;var t=new Promise(function(e,t){for(var n=0,i=o[C].length;n<i;n++){if(o[C][n].id===r){o[C][n].resolve=e;o[C][n].reject=t;o[C][n].socket.send(a)}}});return t}},{key:"resume",value:function e(o){var s=this;var t=new Promise(function(e,t){for(var n=0,i=s[C].length;n<i;n++){if(s[C][n].id===o){if(s[T]==="0.1"){var r=h.resumeCmd();s[C][n].socket.send(r);e();break}else{s[C][n].resolve=e;s[C][n].reject=t;var a=k.resumeCmd();s[C][n].socket.send(a)}}}});return t}},{key:"stop",value:function e(a){var o=this;var t=new Promise(function(e,t){if(!a){t()}else{var n=-1;for(var i=0,r=o[C].length;i<r;i++){if(o[C][i].id===a){n=i;o[C][i].resolve=e;o[C][i].socket.close(1e3,"CLOSE");break}}if(n===-1){t()}}});return t}},{key:"stopAll",value:function e(){var r=this;var t=new Promise(function(e,t){for(var n=0,i=r[C].length;n<i;n++){r[C][n].socket.close(1e3,"CLOSE")}console.log("停止所有流");e()});return t}}]);return e}();return e}();t.StreamClient=u},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:true});var i=function(){function i(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||false;i.configurable=true;if("value"in i)i.writable=true;Object.defineProperty(e,i.key,i)}}return function(e,t,n){if(t)i(e.prototype,t);if(n)i(e,n);return e}}();function r(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var a=function(){var e=function(){function e(){r(this,e)}i(e,[{key:"createClientObject",value:function e(t,n,i,r){return{socket:t,id:n,iCurChannel:i,iCurStream:r,resolve:null,reject:null}}},{key:"zeroPlayCmd",value:function e(t,n){var i=[0,0,0,44,0,0,0,0,0,0,0,0,0,19,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,t+1,0,0,0,n,0,0,4,0];return new Uint8Array(i)}},{key:"playCmd",value:function e(t,n){var i=[0,0,0,44,0,0,0,0,0,0,0,0,0,3,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,t,0,0,0,n,0,0,4,0];return new Uint8Array(i)}},{key:"playbackCmd",value:function e(t,n,i,r){var a=t.split("T")[0];var o=t.split("T")[1];var s="0"+parseInt(a.substring(0,4),10).toString(16);var l=parseInt(a.substring(4,6),10);var u=parseInt(a.substring(6),10);var f=parseInt(o.substring(0,2),10);var h=parseInt(o.substring(2,4),10);var c=parseInt(o.substring(4,6),10);var d=n.split("T")[0];var p=n.split("T")[1];var v="0"+parseInt(d.substring(0,4),10).toString(16);var m=parseInt(d.substring(4,6),10);var y=parseInt(p.substring(0,2),10);var g=parseInt(p.substring(2,4),10);var S=parseInt(p.substring(4,6),10);var b=[0,0,0,96,0,0,0,0,0,0,0,0,0,3,1,2,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,i,0,0,parseInt(s.substring(0,2),16),parseInt(s.substring(2,4),16),0,0,0,l,0,0,0,u,0,0,0,f,0,0,0,h,0,0,0,c,0,0,parseInt(v.substring(0,2),16),parseInt(v.substring(2,4),16),0,0,0,m,0,0,0,u,0,0,0,y,0,0,0,g,0,0,0,S,0,0,0,0,0,0,0,0,r,0,0,0];return new Uint8Array(b)}},{key:"playRateCmd",value:function e(t){var n=(parseInt(t,10)>>>0).toString(16).toLocaleUpperCase().toString(16);for(var i=n.length;i<8;i++){n="0"+n}var r=[0,0,0,0];for(var a=0,o=n.length;a<o;a=a+2){r[Math.floor(a/2)]=parseInt(n.substring(a,a+2),16)}var s=[0,0,0,36,0,0,0,0,0,0,0,0,0,3,1,47,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,r[0],r[1],r[2],r[3]];return new Uint8Array(s)}},{key:"pauseCmd",value:function e(){var t=[0,0,0,32,0,0,0,0,0,0,0,0,0,3,1,7,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];return new Uint8Array(t)}},{key:"resumeCmd",value:function e(){var t=[0,0,0,32,0,0,0,0,0,0,0,0,0,3,1,8,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];return new Uint8Array(t)}}]);return e}();return e}();t.DirectDeviceCustom=a},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:true});var i=function(){function i(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||false;i.configurable=true;if("value"in i)i.writable=true;Object.defineProperty(e,i.key,i)}}return function(e,t,n){if(t)i(e.prototype,t);if(n)i(e,n);return e}}();function r(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var a=3001;var o=3002;var s=3003;var l=function(){var e=function(){function e(){r(this,e)}i(e,[{key:"createClientObject",value:function e(t,n,i){return{socket:t,id:n,playURL:i,resolve:null,reject:null}}},{key:"getMediaFromSdp",value:function e(t){var n=t.indexOf("MEDIAINFO=")+10;var i=t.slice(n,n+80);var r=[];for(var a=0,o=i.length/2;a<o;a++){r[a]=parseInt(i.slice(a*2,a*2+2),16)}return new Uint8Array(r)}},{key:"playCmd",value:function e(t){var n={sequence:0,cmd:"realplay",url:t};return JSON.stringify(n)}},{key:"playbackCmd",value:function e(t,n,i){var r={sequence:0,cmd:"playback",url:i,startTime:t,endTime:n};return JSON.stringify(r)}},{key:"playRateCmd",value:function e(t){var n={sequence:0,cmd:"changespeed",speed:t};return JSON.stringify(n)}},{key:"pauseCmd",value:function e(){var t={sequence:0,cmd:"pause"};return JSON.stringify(t)}},{key:"resumeCmd",value:function e(){var t={sequence:0,cmd:"resume"};return JSON.stringify(t)}},{key:"getError",value:function e(t){var n=a;if(t){if(parseInt(t.statusCode,10)===6&&t.subStatusCode==="streamLimit"){n=o}else if(parseInt(t.statusCode,10)===4&&t.subStatusCode==="badAuthorization"){n=s}}return{iErrorNum:n,oError:t}}}]);return e}();return e}();t.DirectDevice=l},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:true});t.LiveMedia=undefined;var i=function(){function i(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||false;i.configurable=true;if("value"in i)i.writable=true;Object.defineProperty(e,i.key,i)}}return function(e,t,n){if(t)i(e.prototype,t);if(n)i(e,n);return e}}();var r=n(9);var a=s(r);var o=n(10);var l=s(o);function s(e){return e&&e.__esModule?e:{default:e}}function u(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var f=function(){var e=function(){function e(){u(this,e)}i(e,[{key:"createClientObject",value:function e(t,n,i,r,a){var o=l.default.AES.encrypt((new Date).getTime().toString(),l.default.enc.Hex.parse("1234567891234567123456789123456712345678912345671234567891234567"),{mode:l.default.mode.CBC,iv:l.default.enc.Hex.parse("12345678912345671234567891234567"),padding:l.default.pad.Pkcs7}).ciphertext.toString();if(o.length<64){o=o+o}var s=l.default.AES.encrypt((new Date).getTime().toString(),l.default.enc.Hex.parse("12345678912345671234567891234567"),{mode:l.default.mode.CBC,iv:l.default.enc.Hex.parse("12345678912345671234567891234567"),padding:l.default.pad.Pkcs7}).ciphertext.toString();return{socket:t,id:n,PKD:i,rand:r,playURL:a.playURL||"",auth:a.auth||"",token:a.token||"",key:o,iv:s,resolve:null,reject:null}}},{key:"playCmd",value:function e(t){var n={sequence:0,cmd:"realplay",url:t.playURL,key:a.default.encrypt(t.iv+":"+t.key,t.PKD).cipher.split("?")[0],authorization:l.default.AES.encrypt(t.rand+":"+t.auth,l.default.enc.Hex.parse(t.key),{mode:l.default.mode.CBC,iv:l.default.enc.Hex.parse(t.iv),padding:l.default.pad.Pkcs7}).ciphertext.toString(),token:l.default.AES.encrypt(t.token,l.default.enc.Hex.parse(t.key),{mode:l.default.mode.CBC,iv:l.default.enc.Hex.parse(t.iv),padding:l.default.pad.Pkcs7}).ciphertext.toString()};return JSON.stringify(n)}},{key:"playbackCmd",value:function e(t,n,i){var r={sequence:0,cmd:"playback",url:t.playURL,key:a.default.encrypt(t.iv+":"+t.key,t.PKD).cipher.split("?")[0],authorization:l.default.AES.encrypt(t.rand+":"+t.auth,l.default.enc.Hex.parse(t.key),{mode:l.default.mode.CBC,iv:l.default.enc.Hex.parse(t.iv),padding:l.default.pad.Pkcs7}).ciphertext.toString(),token:l.default.AES.encrypt(t.token,l.default.enc.Hex.parse(t.key),{mode:l.default.mode.CBC,iv:l.default.enc.Hex.parse(t.iv),padding:l.default.pad.Pkcs7}).ciphertext.toString(),startTime:n,endTime:i};return JSON.stringify(r)}},{key:"seekCmd",value:function e(t,n){var i={sequence:0,cmd:"seek",startTime:t,endTime:n};return JSON.stringify(i)}}]);return e}();return e}();t.LiveMedia=f},function(L,U,N){"use strict";var q=typeof Symbol==="function"&&typeof Symbol.iterator==="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol==="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};var e={appName:"Netscape",appVersion:40};var t,H=0xdeadbeefcafe,n=(H&16777215)==15715070;function m(e,t,n){e!=null&&("number"==typeof e?this.fromNumber(e,t,n):t==null&&"string"!=typeof e?this.fromString(e,256):this.fromString(e,t))}function y(){return new m(null)}function W(e,t,n,i,r,a){for(;--a>=0;){var o=t*this[e++]+n[i]+r,r=Math.floor(o/67108864);n[i++]=o&67108863}return r}function Y(e,t,n,i,r,a){var o=t&32767;for(t>>=15;--a>=0;){var s=this[e]&32767,l=this[e++]>>15,u=t*s+l*o,s=o*s+((u&32767)<<15)+n[i]+(r&1073741823),r=(s>>>30)+(u>>>15)+t*l+(r>>>30);n[i++]=s&1073741823}return r}function V(e,t,n,i,r,a){var o=t&16383;for(t>>=14;--a>=0;){var s=this[e]&16383,l=this[e++]>>14,u=t*s+l*o,s=o*s+((u&16383)<<14)+n[i]+r,r=(s>>28)+(u>>14)+t*l;n[i++]=s&268435455}return r}n&&e.appName=="Microsoft Internet Explorer"?(m.prototype.am=Y,t=30):n&&e.appName!="Netscape"?(m.prototype.am=W,t=26):(m.prototype.am=V,t=28);m.prototype.DB=t;m.prototype.DM=(1<<t)-1;m.prototype.DV=1<<t;var r=52;m.prototype.FV=Math.pow(2,r);m.prototype.F1=r-t;m.prototype.F2=2*t-r;var j="0123456789abcdefghijklmnopqrstuvwxyz",a=[],o,s;o="0".charCodeAt(0);for(s=0;s<=9;++s){a[o++]=s}o="a".charCodeAt(0);for(s=10;s<36;++s){a[o++]=s}o="A".charCodeAt(0);for(s=10;s<36;++s){a[o++]=s}function u(e){return j.charAt(e)}function f(e,t){var n=a[e.charCodeAt(t)];return n==null?-1:n}function J(e){for(var t=this.t-1;t>=0;--t){e[t]=this[t]}e.t=this.t;e.s=this.s}function G(e){this.t=1;this.s=e<0?-1:0;e>0?this[0]=e:e<-1?this[0]=e+DV:this.t=0}function p(e){var t=y();t.fromInt(e);return t}function X(e,t){var n;if(t==16)n=4;else if(t==8)n=3;else if(t==256)n=8;else if(t==2)n=1;else if(t==32)n=5;else if(t==4)n=2;else{this.fromRadix(e,t);return}this.s=this.t=0;for(var i=e.length,r=!1,a=0;--i>=0;){var o=n==8?e[i]&255:f(e,i);o<0?e.charAt(i)=="-"&&(r=!0):(r=!1,a==0?this[this.t++]=o:a+n>this.DB?(this[this.t-1]|=(o&(1<<this.DB-a)-1)<<a,this[this.t++]=o>>this.DB-a):this[this.t-1]|=o<<a,a+=n,a>=this.DB&&(a-=this.DB))}if(n==8&&(e[0]&128)!=0)this.s=-1,a>0&&(this[this.t-1]|=(1<<this.DB-a)-1<<a);this.clamp();r&&m.ZERO.subTo(this,this)}function K(){for(var e=this.s&this.DM;this.t>0&&this[this.t-1]==e;){--this.t}}function $(e){if(this.s<0)return"-"+this.negate().toString(e);if(e==16)e=4;else if(e==8)e=3;else if(e==2)e=1;else if(e==32)e=5;else if(e==64)e=6;else if(e==4)e=2;else return this.toRadix(e);var t=(1<<e)-1,n,i=!1,r="",a=this.t,o=this.DB-a*this.DB%e;if(a-- >0){if(o<this.DB&&(n=this[a]>>o)>0)i=!0,r=u(n);for(;a>=0;){o<e?(n=(this[a]&(1<<o)-1)<<e-o,n|=this[--a]>>(o+=this.DB-e)):(n=this[a]>>(o-=e)&t,o<=0&&(o+=this.DB,--a)),n>0&&(i=!0),i&&(r+=u(n))}}return i?r:"0"}function Z(){var e=y();m.ZERO.subTo(this,e);return e}function Q(){return this.s<0?this.negate():this}function ee(e){var t=this.s-e.s;if(t!=0)return t;var n=this.t,t=n-e.t;if(t!=0)return t;for(;--n>=0;){if((t=this[n]-e[n])!=0)return t}return 0}function g(e){var t=1,n;if((n=e>>>16)!=0)e=n,t+=16;if((n=e>>8)!=0)e=n,t+=8;if((n=e>>4)!=0)e=n,t+=4;if((n=e>>2)!=0)e=n,t+=2;e>>1!=0&&(t+=1);return t}function te(){return this.t<=0?0:this.DB*(this.t-1)+g(this[this.t-1]^this.s&this.DM)}function ne(e,t){var n;for(n=this.t-1;n>=0;--n){t[n+e]=this[n]}for(n=e-1;n>=0;--n){t[n]=0}t.t=this.t+e;t.s=this.s}function ie(e,t){for(var n=e;n<this.t;++n){t[n-e]=this[n]}t.t=Math.max(this.t-e,0);t.s=this.s}function re(e,t){var n=e%this.DB,i=this.DB-n,r=(1<<i)-1,a=Math.floor(e/this.DB),o=this.s<<n&this.DM,s;for(s=this.t-1;s>=0;--s){t[s+a+1]=this[s]>>i|o,o=(this[s]&r)<<n}for(s=a-1;s>=0;--s){t[s]=0}t[a]=o;t.t=this.t+a+1;t.s=this.s;t.clamp()}function ae(e,t){t.s=this.s;var n=Math.floor(e/this.DB);if(n>=this.t)t.t=0;else{var i=e%this.DB,r=this.DB-i,a=(1<<i)-1;t[0]=this[n]>>i;for(var o=n+1;o<this.t;++o){t[o-n-1]|=(this[o]&a)<<r,t[o-n]=this[o]>>i}i>0&&(t[this.t-n-1]|=(this.s&a)<<r);t.t=this.t-n;t.clamp()}}function oe(e,t){for(var n=0,i=0,r=Math.min(e.t,this.t);n<r;){i+=this[n]-e[n],t[n++]=i&this.DM,i>>=this.DB}if(e.t<this.t){for(i-=e.s;n<this.t;){i+=this[n],t[n++]=i&this.DM,i>>=this.DB}i+=this.s}else{for(i+=this.s;n<e.t;){i-=e[n],t[n++]=i&this.DM,i>>=this.DB}i-=e.s}t.s=i<0?-1:0;i<-1?t[n++]=this.DV+i:i>0&&(t[n++]=i);t.t=n;t.clamp()}function se(e,t){var n=this.abs(),i=e.abs(),r=n.t;for(t.t=r+i.t;--r>=0;){t[r]=0}for(r=0;r<i.t;++r){t[r+n.t]=n.am(0,i[r],t,r,0,n.t)}t.s=0;t.clamp();this.s!=e.s&&m.ZERO.subTo(t,t)}function le(e){for(var t=this.abs(),n=e.t=2*t.t;--n>=0;){e[n]=0}for(n=0;n<t.t-1;++n){var i=t.am(n,t[n],e,2*n,0,1);if((e[n+t.t]+=t.am(n+1,2*t[n],e,2*n+1,i,t.t-n-1))>=t.DV)e[n+t.t]-=t.DV,e[n+t.t+1]=1}e.t>0&&(e[e.t-1]+=t.am(n,t[n],e,2*n,0,1));e.s=0;e.clamp()}function ue(e,t,n){var i=e.abs();if(!(i.t<=0)){var r=this.abs();if(r.t<i.t)t!=null&&t.fromInt(0),n!=null&&this.copyTo(n);else{n==null&&(n=y());var a=y(),o=this.s,e=e.s,s=this.DB-g(i[i.t-1]);s>0?(i.lShiftTo(s,a),r.lShiftTo(s,n)):(i.copyTo(a),r.copyTo(n));i=a.t;r=a[i-1];if(r!=0){var l=r*(1<<this.F1)+(i>1?a[i-2]>>this.F2:0),u=this.FV/l,l=(1<<this.F1)/l,f=1<<this.F2,h=n.t,c=h-i,d=t==null?y():t;a.dlShiftTo(c,d);n.compareTo(d)>=0&&(n[n.t++]=1,n.subTo(d,n));m.ONE.dlShiftTo(i,d);for(d.subTo(a,a);a.t<i;){a[a.t++]=0}for(;--c>=0;){var p=n[--h]==r?this.DM:Math.floor(n[h]*u+(n[h-1]+f)*l);if((n[h]+=a.am(0,p,n,c,0,i))<p){a.dlShiftTo(c,d);for(n.subTo(d,n);n[h]<--p;){n.subTo(d,n)}}}t!=null&&(n.drShiftTo(i,t),o!=e&&m.ZERO.subTo(t,t));n.t=i;n.clamp();s>0&&n.rShiftTo(s,n);o<0&&m.ZERO.subTo(n,n)}}}}function fe(e){var t=y();this.abs().divRemTo(e,null,t);this.s<0&&t.compareTo(m.ZERO)>0&&e.subTo(t,t);return t}function S(e){this.m=e}function he(e){return e.s<0||e.compareTo(this.m)>=0?e.mod(this.m):e}function ce(e){return e}function de(e){e.divRemTo(this.m,null,e)}function pe(e,t,n){e.multiplyTo(t,n);this.reduce(n)}function ve(e,t){e.squareTo(t);this.reduce(t)}S.prototype.convert=he;S.prototype.revert=ce;S.prototype.reduce=de;S.prototype.mulTo=pe;S.prototype.sqrTo=ve;function me(){if(this.t<1)return 0;var e=this[0];if((e&1)==0)return 0;var t=e&3,t=t*(2-(e&15)*t)&15,t=t*(2-(e&255)*t)&255,t=t*(2-((e&65535)*t&65535))&65535,t=t*(2-e*t%this.DV)%this.DV;return t>0?this.DV-t:-t}function b(e){this.m=e;this.mp=e.invDigit();this.mpl=this.mp&32767;this.mph=this.mp>>15;this.um=(1<<e.DB-15)-1;this.mt2=2*e.t}function ye(e){var t=y();e.abs().dlShiftTo(this.m.t,t);t.divRemTo(this.m,null,t);e.s<0&&t.compareTo(m.ZERO)>0&&this.m.subTo(t,t);return t}function ge(e){var t=y();e.copyTo(t);this.reduce(t);return t}function Se(e){for(;e.t<=this.mt2;){e[e.t++]=0}for(var t=0;t<this.m.t;++t){var n=e[t]&32767,i=n*this.mpl+((n*this.mph+(e[t]>>15)*this.mpl&this.um)<<15)&e.DM,n=t+this.m.t;for(e[n]+=this.m.am(0,i,e,t,0,this.m.t);e[n]>=e.DV;){e[n]-=e.DV,e[++n]++}}e.clamp();e.drShiftTo(this.m.t,e);e.compareTo(this.m)>=0&&e.subTo(this.m,e)}function be(e,t){e.squareTo(t);this.reduce(t)}function Pe(e,t,n){e.multiplyTo(t,n);this.reduce(n)}b.prototype.convert=ye;b.prototype.revert=ge;b.prototype.reduce=Se;b.prototype.mulTo=Pe;b.prototype.sqrTo=be;function we(){return(this.t>0?this[0]&1:this.s)==0}function _e(e,t){if(e>4294967295||e<1)return m.ONE;var n=y(),i=y(),r=t.convert(this),a=g(e)-1;for(r.copyTo(n);--a>=0;){if(t.sqrTo(n,i),(e&1<<a)>0)t.mulTo(i,r,n);else var o=n,n=i,i=o}return t.revert(n)}function Ce(e,t){var n;n=e<256||t.isEven()?new S(t):new b(t);return this.exp(e,n)}m.prototype.copyTo=J;m.prototype.fromInt=G;m.prototype.fromString=X;m.prototype.clamp=K;m.prototype.dlShiftTo=ne;m.prototype.drShiftTo=ie;m.prototype.lShiftTo=re;m.prototype.rShiftTo=ae;m.prototype.subTo=oe;m.prototype.multiplyTo=se;m.prototype.squareTo=le;m.prototype.divRemTo=ue;m.prototype.invDigit=me;m.prototype.isEven=we;m.prototype.exp=_e;m.prototype.toString=$;m.prototype.negate=Z;m.prototype.abs=Q;m.prototype.compareTo=ee;m.prototype.bitLength=te;m.prototype.mod=fe;m.prototype.modPowInt=Ce;m.ZERO=p(0);m.ONE=p(1);function De(){var e=y();this.copyTo(e);return e}function Te(){if(this.s<0){if(this.t==1)return this[0]-this.DV;else{if(this.t==0)return-1}}else if(this.t==1)return this[0];else if(this.t==0)return 0;return(this[1]&(1<<32-this.DB)-1)<<this.DB|this[0]}function Me(){return this.t==0?this.s:this[0]<<24>>24}function ke(){return this.t==0?this.s:this[0]<<16>>16}function xe(e){return Math.floor(Math.LN2*this.DB/Math.log(e))}function Re(){return this.s<0?-1:this.t<=0||this.t==1&&this[0]<=0?0:1}function Ie(e){e==null&&(e=10);if(this.signum()==0||e<2||e>36)return"0";var t=this.chunkSize(e),t=Math.pow(e,t),n=p(t),i=y(),r=y(),a="";for(this.divRemTo(n,i,r);i.signum()>0;){a=(t+r.intValue()).toString(e).substr(1)+a,i.divRemTo(n,i,r)}return r.intValue().toString(e)+a}function ze(e,t){this.fromInt(0);t==null&&(t=10);for(var n=this.chunkSize(t),i=Math.pow(t,n),r=!1,a=0,o=0,s=0;s<e.length;++s){var l=f(e,s);l<0?e.charAt(s)=="-"&&this.signum()==0&&(r=!0):(o=t*o+l,++a>=n&&(this.dMultiply(i),this.dAddOffset(o,0),o=a=0))}a>0&&(this.dMultiply(Math.pow(t,a)),this.dAddOffset(o,0));r&&m.ZERO.subTo(this,this)}function Ee(e,t,n){if("number"==typeof t){if(e<2)this.fromInt(1);else{this.fromNumber(e,n);this.testBit(e-1)||this.bitwiseTo(m.ONE.shiftLeft(e-1),l,this);for(this.isEven()&&this.dAddOffset(1,0);!this.isProbablePrime(t);){this.dAddOffset(2,0),this.bitLength()>e&&this.subTo(m.ONE.shiftLeft(e-1),this)}}}else{var n=[],i=e&7;n.length=(e>>3)+1;t.nextBytes(n);i>0?n[0]&=(1<<i)-1:n[0]=0;this.fromString(n,256)}}function Ae(){var e=this.t,t=[];t[0]=this.s;var n=this.DB-e*this.DB%8,i,r=0;if(e-- >0){if(n<this.DB&&(i=this[e]>>n)!=(this.s&this.DM)>>n)t[r++]=i|this.s<<this.DB-n;for(;e>=0;){if(n<8?(i=(this[e]&(1<<n)-1)<<8-n,i|=this[--e]>>(n+=this.DB-8)):(i=this[e]>>(n-=8)&255,n<=0&&(n+=this.DB,--e)),(i&128)!=0&&(i|=-256),r==0&&(this.s&128)!=(i&128)&&++r,r>0||i!=this.s)t[r++]=i}}return t}function Be(e){return this.compareTo(e)==0}function Oe(e){return this.compareTo(e)<0?this:e}function Fe(e){return this.compareTo(e)>0?this:e}function Le(e,t,n){var i,r,a=Math.min(e.t,this.t);for(i=0;i<a;++i){n[i]=t(this[i],e[i])}if(e.t<this.t){r=e.s&this.DM;for(i=a;i<this.t;++i){n[i]=t(this[i],r)}n.t=this.t}else{r=this.s&this.DM;for(i=a;i<e.t;++i){n[i]=t(r,e[i])}n.t=e.t}n.s=t(this.s,e.s);n.clamp()}function Ue(e,t){return e&t}function Ne(e){var t=y();this.bitwiseTo(e,Ue,t);return t}function l(e,t){return e|t}function qe(e){var t=y();this.bitwiseTo(e,l,t);return t}function h(e,t){return e^t}function He(e){var t=y();this.bitwiseTo(e,h,t);return t}function c(e,t){return e&~t}function We(e){var t=y();this.bitwiseTo(e,c,t);return t}function Ye(){for(var e=y(),t=0;t<this.t;++t){e[t]=this.DM&~this[t]}e.t=this.t;e.s=~this.s;return e}function Ve(e){var t=y();e<0?this.rShiftTo(-e,t):this.lShiftTo(e,t);return t}function je(e){var t=y();e<0?this.lShiftTo(-e,t):this.rShiftTo(e,t);return t}function Je(e){if(e==0)return-1;var t=0;(e&65535)==0&&(e>>=16,t+=16);(e&255)==0&&(e>>=8,t+=8);(e&15)==0&&(e>>=4,t+=4);(e&3)==0&&(e>>=2,t+=2);(e&1)==0&&++t;return t}function Ge(){for(var e=0;e<this.t;++e){if(this[e]!=0)return e*this.DB+Je(this[e])}return this.s<0?this.t*this.DB:-1}function Xe(e){for(var t=0;e!=0;){e&=e-1,++t}return t}function Ke(){for(var e=0,t=this.s&this.DM,n=0;n<this.t;++n){e+=Xe(this[n]^t)}return e}function $e(e){var t=Math.floor(e/this.DB);return t>=this.t?this.s!=0:(this[t]&1<<e%this.DB)!=0}function Ze(e,t){var n=m.ONE.shiftLeft(e);this.bitwiseTo(n,t,n);return n}function Qe(e){return this.changeBit(e,l)}function et(e){return this.changeBit(e,c)}function tt(e){return this.changeBit(e,h)}function nt(e,t){for(var n=0,i=0,r=Math.min(e.t,this.t);n<r;){i+=this[n]+e[n],t[n++]=i&this.DM,i>>=this.DB}if(e.t<this.t){for(i+=e.s;n<this.t;){i+=this[n],t[n++]=i&this.DM,i>>=this.DB}i+=this.s}else{for(i+=this.s;n<e.t;){i+=e[n],t[n++]=i&this.DM,i>>=this.DB}i+=e.s}t.s=i<0?-1:0;i>0?t[n++]=i:i<-1&&(t[n++]=this.DV+i);t.t=n;t.clamp()}function it(e){var t=y();this.addTo(e,t);return t}function rt(e){var t=y();this.subTo(e,t);return t}function at(e){var t=y();this.multiplyTo(e,t);return t}function ot(){var e=y();this.squareTo(e);return e}function st(e){var t=y();this.divRemTo(e,t,null);return t}function lt(e){var t=y();this.divRemTo(e,null,t);return t}function ut(e){var t=y(),n=y();this.divRemTo(e,t,n);return[t,n]}function ft(e){this[this.t]=this.am(0,e-1,this,0,0,this.t);++this.t;this.clamp()}function ht(e,t){if(e!=0){for(;this.t<=t;){this[this.t++]=0}for(this[t]+=e;this[t]>=this.DV;){this[t]-=this.DV,++t>=this.t&&(this[this.t++]=0),++this[t]}}}function d(){}function P(e){return e}function ct(e,t,n){e.multiplyTo(t,n)}function dt(e,t){e.squareTo(t)}d.prototype.convert=P;d.prototype.revert=P;d.prototype.mulTo=ct;d.prototype.sqrTo=dt;function pt(e){return this.exp(e,new d)}function vt(e,t,n){var i=Math.min(this.t+e.t,t);n.s=0;for(n.t=i;i>0;){n[--i]=0}var r;for(r=n.t-this.t;i<r;++i){n[i+this.t]=this.am(0,e[i],n,i,0,this.t)}for(r=Math.min(e.t,t);i<r;++i){this.am(0,e[i],n,i,0,t-i)}n.clamp()}function mt(e,t,n){--t;var i=n.t=this.t+e.t-t;for(n.s=0;--i>=0;){n[i]=0}for(i=Math.max(t-this.t,0);i<e.t;++i){n[this.t+i-t]=this.am(t-i,e[i],n,0,0,this.t+i-t)}n.clamp();n.drShiftTo(1,n)}function w(e){this.r2=y();this.q3=y();m.ONE.dlShiftTo(2*e.t,this.r2);this.mu=this.r2.divide(e);this.m=e}function yt(e){if(e.s<0||e.t>2*this.m.t)return e.mod(this.m);else if(e.compareTo(this.m)<0)return e;else{var t=y();e.copyTo(t);this.reduce(t);return t}}function gt(e){return e}function St(e){e.drShiftTo(this.m.t-1,this.r2);if(e.t>this.m.t+1)e.t=this.m.t+1,e.clamp();this.mu.multiplyUpperTo(this.r2,this.m.t+1,this.q3);for(this.m.multiplyLowerTo(this.q3,this.m.t+1,this.r2);e.compareTo(this.r2)<0;){e.dAddOffset(1,this.m.t+1)}for(e.subTo(this.r2,e);e.compareTo(this.m)>=0;){e.subTo(this.m,e)}}function bt(e,t){e.squareTo(t);this.reduce(t)}function Pt(e,t,n){e.multiplyTo(t,n);this.reduce(n)}w.prototype.convert=yt;w.prototype.revert=gt;w.prototype.reduce=St;w.prototype.mulTo=Pt;w.prototype.sqrTo=bt;function wt(e,t){var n=e.bitLength(),i,r=p(1),a;if(n<=0)return r;else i=n<18?1:n<48?3:n<144?4:n<768?5:6;a=n<8?new S(t):t.isEven()?new w(t):new b(t);var o=[],s=3,l=i-1,u=(1<<i)-1;o[1]=a.convert(this);if(i>1){n=y();for(a.sqrTo(o[1],n);s<=u;){o[s]=y(),a.mulTo(n,o[s-2],o[s]),s+=2}}for(var f=e.t-1,h,c=!0,d=y(),n=g(e[f])-1;f>=0;){n>=l?h=e[f]>>n-l&u:(h=(e[f]&(1<<n+1)-1)<<l-n,f>0&&(h|=e[f-1]>>this.DB+n-l));for(s=i;(h&1)==0;){h>>=1,--s}if((n-=s)<0)n+=this.DB,--f;if(c)o[h].copyTo(r),c=!1;else{for(;s>1;){a.sqrTo(r,d),a.sqrTo(d,r),s-=2}s>0?a.sqrTo(r,d):(s=r,r=d,d=s);a.mulTo(d,o[h],r)}for(;f>=0&&(e[f]&1<<n)==0;){a.sqrTo(r,d),s=r,r=d,d=s,--n<0&&(n=this.DB-1,--f)}}return a.revert(r)}function _t(e){var t=this.s<0?this.negate():this.clone(),e=e.s<0?e.negate():e.clone();if(t.compareTo(e)<0)var n=t,t=e,e=n;var n=t.getLowestSetBit(),i=e.getLowestSetBit();if(i<0)return t;n<i&&(i=n);i>0&&(t.rShiftTo(i,t),e.rShiftTo(i,e));for(;t.signum()>0;){(n=t.getLowestSetBit())>0&&t.rShiftTo(n,t),(n=e.getLowestSetBit())>0&&e.rShiftTo(n,e),t.compareTo(e)>=0?(t.subTo(e,t),t.rShiftTo(1,t)):(e.subTo(t,e),e.rShiftTo(1,e))}i>0&&e.lShiftTo(i,e);return e}function Ct(e){if(e<=0)return 0;var t=this.DV%e,n=this.s<0?e-1:0;if(this.t>0)if(t==0)n=this[0]%e;else for(var i=this.t-1;i>=0;--i){n=(t*n+this[i])%e}return n}function Dt(e){var t=e.isEven();if(this.isEven()&&t||e.signum()==0)return m.ZERO;for(var n=e.clone(),i=this.clone(),r=p(1),a=p(0),o=p(0),s=p(1);n.signum()!=0;){for(;n.isEven();){n.rShiftTo(1,n);if(t){if(!r.isEven()||!a.isEven())r.addTo(this,r),a.subTo(e,a);r.rShiftTo(1,r)}else a.isEven()||a.subTo(e,a);a.rShiftTo(1,a)}for(;i.isEven();){i.rShiftTo(1,i);if(t){if(!o.isEven()||!s.isEven())o.addTo(this,o),s.subTo(e,s);o.rShiftTo(1,o)}else s.isEven()||s.subTo(e,s);s.rShiftTo(1,s)}n.compareTo(i)>=0?(n.subTo(i,n),t&&r.subTo(o,r),a.subTo(s,a)):(i.subTo(n,i),t&&o.subTo(r,o),s.subTo(a,s))}if(i.compareTo(m.ONE)!=0)return m.ZERO;if(s.compareTo(e)>=0)return s.subtract(e);if(s.signum()<0)s.addTo(e,s);else return s;return s.signum()<0?s.add(e):s}var _=[2,3,5,7,11,13,17,19,23,29,31,37,41,43,47,53,59,61,67,71,73,79,83,89,97,101,103,107,109,113,127,131,137,139,149,151,157,163,167,173,179,181,191,193,197,199,211,223,227,229,233,239,241,251,257,263,269,271,277,281,283,293,307,311,313,317,331,337,347,349,353,359,367,373,379,383,389,397,401,409,419,421,431,433,439,443,449,457,461,463,467,479,487,491,499,503,509,521,523,541,547,557,563,569,571,577,587,593,599,601,607,613,617,619,631,641,643,647,653,659,661,673,677,683,691,701,709,719,727,733,739,743,751,757,761,769,773,787,797,809,811,821,823,827,829,839,853,857,859,863,877,881,883,887,907,911,919,929,937,941,947,953,967,971,977,983,991,997],Tt=67108864/_[_.length-1];function Mt(e){var t,n=this.abs();if(n.t==1&&n[0]<=_[_.length-1]){for(t=0;t<_.length;++t){if(n[0]==_[t])return!0}return!1}if(n.isEven())return!1;for(t=1;t<_.length;){for(var i=_[t],r=t+1;r<_.length&&i<Tt;){i*=_[r++]}for(i=n.modInt(i);t<r;){if(i%_[t++]==0)return!1}}return n.millerRabin(e)}function kt(e){var t=this.subtract(m.ONE),n=t.getLowestSetBit();if(n<=0)return!1;var i=t.shiftRight(n),e=e+1>>1;if(e>_.length)e=_.length;for(var r=y(),a=0;a<e;++a){r.fromInt(_[Math.floor(Math.random()*_.length)]);var o=r.modPow(i,this);if(o.compareTo(m.ONE)!=0&&o.compareTo(t)!=0){for(var s=1;s++<n&&o.compareTo(t)!=0;){if(o=o.modPowInt(2,this),o.compareTo(m.ONE)==0)return!1}if(o.compareTo(t)!=0)return!1}}return!0}m.prototype.chunkSize=xe;m.prototype.toRadix=Ie;m.prototype.fromRadix=ze;m.prototype.fromNumber=Ee;m.prototype.bitwiseTo=Le;m.prototype.changeBit=Ze;m.prototype.addTo=nt;m.prototype.dMultiply=ft;m.prototype.dAddOffset=ht;m.prototype.multiplyLowerTo=vt;m.prototype.multiplyUpperTo=mt;m.prototype.modInt=Ct;m.prototype.millerRabin=kt;m.prototype.clone=De;m.prototype.intValue=Te;m.prototype.byteValue=Me;m.prototype.shortValue=ke;m.prototype.signum=Re;m.prototype.toByteArray=Ae;m.prototype.equals=Be;m.prototype.min=Oe;m.prototype.max=Fe;m.prototype.and=Ne;m.prototype.or=qe;m.prototype.xor=He;m.prototype.andNot=We;m.prototype.not=Ye;m.prototype.shiftLeft=Ve;m.prototype.shiftRight=je;m.prototype.getLowestSetBit=Ge;m.prototype.bitCount=Ke;m.prototype.testBit=$e;m.prototype.setBit=Qe;m.prototype.clearBit=et;m.prototype.flipBit=tt;m.prototype.add=it;m.prototype.subtract=rt;m.prototype.multiply=at;m.prototype.divide=st;m.prototype.remainder=lt;m.prototype.divideAndRemainder=ut;m.prototype.modPow=wt;m.prototype.modInverse=Dt;m.prototype.pow=pt;m.prototype.gcd=_t;m.prototype.isProbablePrime=Mt;m.prototype.square=ot;(function(r,a,l,o,s,u,f){function h(e){var t,n,s=this,i=e.length,r=0,a=s.i=s.j=s.m=0;s.S=[];s.c=[];for(i||(e=[i++]);r<l;){s.S[r]=r++}for(r=0;r<l;r++){t=s.S[r],a=a+t+e[r%i]&l-1,n=s.S[a],s.S[r]=n,s.S[a]=t}s.g=function(e){var t=s.S,n=s.i+1&l-1,i=t[n],r=s.j+i&l-1,a=t[r];t[n]=a;t[r]=i;for(var o=t[i+a&l-1];--e;){n=n+1&l-1,i=t[n],r=r+i&l-1,a=t[r],t[n]=a,t[r]=i,o=o*l+t[i+a&l-1]}s.i=n;s.j=r;return o};s.g(l)}function c(e,t,n,i,r){n=[];r=typeof e==="undefined"?"undefined":q(e);if(t&&r=="object")for(i in e){if(i.indexOf("S")<5)try{n.push(c(e[i],t-1))}catch(e){}}return n.length?n:e+(r!="string"?"\0":"")}function d(e,t,n,i){e+="";for(i=n=0;i<e.length;i++){var r=t,a=i&l-1,o=(n^=t[i&l-1]*19)+e.charCodeAt(i);r[a]=o&l-1}e="";for(i in t){e+=String.fromCharCode(t[i])}return e}a.seedrandom=function(e,t){var n=[],i,e=d(c(t?[e,r]:arguments.length?e:[(new Date).getTime(),r,window],3),n);i=new h(n);d(i.S,r);a.random=function(){for(var e=i.g(o),t=f,n=0;e<s;){e=(e+n)*l,t*=l,n=i.g(1)}for(;e>=u;){e/=2,t/=2,n>>>=1}return(e+n)/t};return e};f=a.pow(l,o);s=a.pow(2,s);u=s*2;d(a.random(),r)})([],Math,256,6,52);function C(){}function xt(e){var t;for(t=0;t<e.length;t++){e[t]=Math.floor(Math.random()*256)}}C.prototype.nextBytes=xt;function D(){this.j=this.i=0;this.S=[]}function Rt(e){var t,n,i;for(t=0;t<256;++t){this.S[t]=t}for(t=n=0;t<256;++t){n=n+this.S[t]+e[t%e.length]&255,i=this.S[t],this.S[t]=this.S[n],this.S[n]=i}this.j=this.i=0}function It(){var e;this.i=this.i+1&255;this.j=this.j+this.S[this.i]&255;e=this.S[this.i];this.S[this.i]=this.S[this.j];this.S[this.j]=e;return this.S[e+this.S[this.i]&255]}D.prototype.init=Rt;D.prototype.next=It;function zt(){return new D}var T=256,M,k,x;function Et(e){k[x++]^=e&255;k[x++]^=e>>8&255;k[x++]^=e>>16&255;k[x++]^=e>>24&255;x>=T&&(x-=T)}function At(){Et((new Date).getTime())}if(k==null){k=[];x=0;var R;if(e.appName=="Netscape"&&e.appVersion<"5"&&window.crypto){var Bt=window.crypto.random(32);for(R=0;R<Bt.length;++R){k[x++]=Bt.charCodeAt(R)&255}}for(;x<T;){R=Math.floor(65536*Math.random()),k[x++]=R>>>8,k[x++]=R&255}x=0;At()}function Ot(){if(M==null){At();M=zt();M.init(k);for(x=0;x<k.length;++x){k[x]=0}x=0}return M.next()}function Ft(e){var t;for(t=0;t<e.length;++t){e[t]=Ot()}}function I(){}I.prototype.nextBytes=Ft;function Lt(e){function y(e,t){var n=(e&65535)+(t&65535);return(e>>16)+(t>>16)+(n>>16)<<16|n&65535}function g(e,t){return e>>>t|e<<32-t}e=function(e){for(var e=e.replace(/\r\n/g,"\n"),t="",n=0;n<e.length;n++){var i=e.charCodeAt(n);i<128?t+=String.fromCharCode(i):(i>127&&i<2048?t+=String.fromCharCode(i>>6|192):(t+=String.fromCharCode(i>>12|224),t+=String.fromCharCode(i>>6&63|128)),t+=String.fromCharCode(i&63|128))}return t}(e);return function(e){for(var t="",n=0;n<e.length*4;n++){t+="0123456789abcdef".charAt(e[n>>2]>>(3-n%4)*8+4&15)+"0123456789abcdef".charAt(e[n>>2]>>(3-n%4)*8&15)}return t}(function(e,t){var n=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298],i=[1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225],r=Array(64),a,o,s,l,u,f,h,c,d,p,v,m;e[t>>5]|=128<<24-t%32;e[(t+64>>9<<4)+15]=t;for(d=0;d<e.length;d+=16){a=i[0];o=i[1];s=i[2];l=i[3];u=i[4];f=i[5];h=i[6];c=i[7];for(p=0;p<64;p++){r[p]=p<16?e[p+d]:y(y(y(g(r[p-2],17)^g(r[p-2],19)^r[p-2]>>>10,r[p-7]),g(r[p-15],7)^g(r[p-15],18)^r[p-15]>>>3),r[p-16]),v=y(y(y(y(c,g(u,6)^g(u,11)^g(u,25)),u&f^~u&h),n[p]),r[p]),m=y(g(a,2)^g(a,13)^g(a,22),a&o^a&s^o&s),c=h,h=f,f=u,u=y(l,v),l=s,s=o,o=a,a=y(v,m)}i[0]=y(a,i[0]);i[1]=y(o,i[1]);i[2]=y(s,i[2]);i[3]=y(l,i[3]);i[4]=y(u,i[4]);i[5]=y(f,i[5]);i[6]=y(h,i[6]);i[7]=y(c,i[7])}return i}(function(e){for(var t=[],n=0;n<e.length*8;n+=8){t[n>>5]|=(e.charCodeAt(n/8)&255)<<24-n%32}return t}(e),e.length*8))}var Ut={hex:function e(t){return Lt(t)}};function Nt(e){function t(e,t){return e<<t|e>>>32-t}function n(e){var t="",n,i;for(n=7;n>=0;n--){i=e>>>n*4&15,t+=i.toString(16)}return t}var i,r,a=Array(80),o=1732584193,s=4023233417,l=2562383102,u=271733878,f=3285377520,h,c,d,p,v,e=function(e){for(var e=e.replace(/\r\n/g,"\n"),t="",n=0;n<e.length;n++){var i=e.charCodeAt(n);i<128?t+=String.fromCharCode(i):(i>127&&i<2048?t+=String.fromCharCode(i>>6|192):(t+=String.fromCharCode(i>>12|224),t+=String.fromCharCode(i>>6&63|128)),t+=String.fromCharCode(i&63|128))}return t}(e);h=e.length;var m=[];for(i=0;i<h-3;i+=4){r=e.charCodeAt(i)<<24|e.charCodeAt(i+1)<<16|e.charCodeAt(i+2)<<8|e.charCodeAt(i+3),m.push(r)}switch(h%4){case 0:i=2147483648;break;case 1:i=e.charCodeAt(h-1)<<24|8388608;break;case 2:i=e.charCodeAt(h-2)<<24|e.charCodeAt(h-1)<<16|32768;break;case 3:i=e.charCodeAt(h-3)<<24|e.charCodeAt(h-2)<<16|e.charCodeAt(h-1)<<8|128}for(m.push(i);m.length%16!=14;){m.push(0)}m.push(h>>>29);m.push(h<<3&4294967295);for(e=0;e<m.length;e+=16){for(i=0;i<16;i++){a[i]=m[e+i]}for(i=16;i<=79;i++){a[i]=t(a[i-3]^a[i-8]^a[i-14]^a[i-16],1)}r=o;h=s;c=l;d=u;p=f;for(i=0;i<=19;i++){v=t(r,5)+(h&c|~h&d)+p+a[i]+1518500249&4294967295,p=d,d=c,c=t(h,30),h=r,r=v}for(i=20;i<=39;i++){v=t(r,5)+(h^c^d)+p+a[i]+1859775393&4294967295,p=d,d=c,c=t(h,30),h=r,r=v}for(i=40;i<=59;i++){v=t(r,5)+(h&c|h&d|c&d)+p+a[i]+2400959708&4294967295,p=d,d=c,c=t(h,30),h=r,r=v}for(i=60;i<=79;i++){v=t(r,5)+(h^c^d)+p+a[i]+3395469782&4294967295,p=d,d=c,c=t(h,30),h=r,r=v}o=o+r&4294967295;s=s+h&4294967295;l=l+c&4294967295;u=u+d&4294967295;f=f+p&4294967295}v=n(o)+n(s)+n(l)+n(u)+n(f);return v.toLowerCase()}var qt={hex:function e(t){return Nt(t)}},Ht=function e(t){function s(e,t){var n,i,r,a,o;r=e&2147483648;a=t&2147483648;n=e&1073741824;i=t&1073741824;o=(e&1073741823)+(t&1073741823);return n&i?o^2147483648^r^a:n|i?o&1073741824?o^3221225472^r^a:o^1073741824^r^a:o^r^a}function n(e,t,n,i,r,a,o){e=s(e,s(s(t&n|~t&i,r),o));return s(e<<a|e>>>32-a,t)}function i(e,t,n,i,r,a,o){e=s(e,s(s(t&i|n&~i,r),o));return s(e<<a|e>>>32-a,t)}function r(e,t,n,i,r,a,o){e=s(e,s(s(t^n^i,r),o));return s(e<<a|e>>>32-a,t)}function a(e,t,n,i,r,a,o){e=s(e,s(s(n^(t|~i),r),o));return s(e<<a|e>>>32-a,t)}function o(e){var t="",n="",i;for(i=0;i<=3;i++){n=e>>>i*8&255,n="0"+n.toString(16),t+=n.substr(n.length-2,2)}return t}var l=[],u,f,h,c,d,p,v,m,t=function(e){for(var e=e.replace(/\r\n/g,"\n"),t="",n=0;n<e.length;n++){var i=e.charCodeAt(n);i<128?t+=String.fromCharCode(i):(i>127&&i<2048?t+=String.fromCharCode(i>>6|192):(t+=String.fromCharCode(i>>12|224),t+=String.fromCharCode(i>>6&63|128)),t+=String.fromCharCode(i&63|128))}return t}(t),l=function(e){var t,n=e.length;t=n+8;for(var i=((t-t%64)/64+1)*16,r=Array(i-1),a=0,o=0;o<n;){t=(o-o%4)/4,a=o%4*8,r[t]|=e.charCodeAt(o)<<a,o++}r[(o-o%4)/4]|=128<<o%4*8;r[i-2]=n<<3;r[i-1]=n>>>29;return r}(t);d=1732584193;p=4023233417;v=2562383102;m=271733878;for(t=0;t<l.length;t+=16){u=d,f=p,h=v,c=m,d=n(d,p,v,m,l[t+0],7,3614090360),m=n(m,d,p,v,l[t+1],12,3905402710),v=n(v,m,d,p,l[t+2],17,606105819),p=n(p,v,m,d,l[t+3],22,3250441966),d=n(d,p,v,m,l[t+4],7,4118548399),m=n(m,d,p,v,l[t+5],12,1200080426),v=n(v,m,d,p,l[t+6],17,2821735955),p=n(p,v,m,d,l[t+7],22,4249261313),d=n(d,p,v,m,l[t+8],7,1770035416),m=n(m,d,p,v,l[t+9],12,2336552879),v=n(v,m,d,p,l[t+10],17,4294925233),p=n(p,v,m,d,l[t+11],22,2304563134),d=n(d,p,v,m,l[t+12],7,1804603682),m=n(m,d,p,v,l[t+13],12,4254626195),v=n(v,m,d,p,l[t+14],17,2792965006),p=n(p,v,m,d,l[t+15],22,1236535329),d=i(d,p,v,m,l[t+1],5,4129170786),m=i(m,d,p,v,l[t+6],9,3225465664),v=i(v,m,d,p,l[t+11],14,643717713),p=i(p,v,m,d,l[t+0],20,3921069994),d=i(d,p,v,m,l[t+5],5,3593408605),m=i(m,d,p,v,l[t+10],9,38016083),v=i(v,m,d,p,l[t+15],14,3634488961),p=i(p,v,m,d,l[t+4],20,3889429448),d=i(d,p,v,m,l[t+9],5,568446438),m=i(m,d,p,v,l[t+14],9,3275163606),v=i(v,m,d,p,l[t+3],14,4107603335),p=i(p,v,m,d,l[t+8],20,1163531501),d=i(d,p,v,m,l[t+13],5,2850285829),m=i(m,d,p,v,l[t+2],9,4243563512),v=i(v,m,d,p,l[t+7],14,1735328473),p=i(p,v,m,d,l[t+12],20,2368359562),d=r(d,p,v,m,l[t+5],4,4294588738),m=r(m,d,p,v,l[t+8],11,2272392833),v=r(v,m,d,p,l[t+11],16,1839030562),p=r(p,v,m,d,l[t+14],23,4259657740),d=r(d,p,v,m,l[t+1],4,2763975236),m=r(m,d,p,v,l[t+4],11,1272893353),v=r(v,m,d,p,l[t+7],16,4139469664),p=r(p,v,m,d,l[t+10],23,3200236656),d=r(d,p,v,m,l[t+13],4,681279174),m=r(m,d,p,v,l[t+0],11,3936430074),v=r(v,m,d,p,l[t+3],16,3572445317),p=r(p,v,m,d,l[t+6],23,76029189),d=r(d,p,v,m,l[t+9],4,3654602809),m=r(m,d,p,v,l[t+12],11,3873151461),v=r(v,m,d,p,l[t+15],16,530742520),p=r(p,v,m,d,l[t+2],23,3299628645),d=a(d,p,v,m,l[t+0],6,4096336452),m=a(m,d,p,v,l[t+7],10,1126891415),v=a(v,m,d,p,l[t+14],15,2878612391),p=a(p,v,m,d,l[t+5],21,4237533241),d=a(d,p,v,m,l[t+12],6,1700485571),m=a(m,d,p,v,l[t+3],10,2399980690),v=a(v,m,d,p,l[t+10],15,4293915773),p=a(p,v,m,d,l[t+1],21,2240044497),d=a(d,p,v,m,l[t+8],6,1873313359),m=a(m,d,p,v,l[t+15],10,4264355552),v=a(v,m,d,p,l[t+6],15,2734768916),p=a(p,v,m,d,l[t+13],21,1309151649),d=a(d,p,v,m,l[t+4],6,4149444226),m=a(m,d,p,v,l[t+11],10,3174756917),v=a(v,m,d,p,l[t+2],15,718787259),p=a(p,v,m,d,l[t+9],21,3951481745),d=s(d,u),p=s(p,f),v=s(v,h),m=s(m,c)}return(o(d)+o(p)+o(v)+o(m)).toLowerCase()};function z(e,t){return new m(e,t)}function Wt(e,t){for(var n="",i=0;i+t<e.length;){n+=e.substring(i,i+t)+"\n",i+=t}return n+e.substring(i,e.length)}function Yt(e){return e<16?"0"+e.toString(16):e.toString(16)}function Vt(e,t){if(t<e.length+11)throw"Message too long for RSA (n="+t+", l="+e.length+")";for(var n=[],i=e.length-1;i>=0&&t>0;){var r=e.charCodeAt(i--);r<128?n[--t]=r:r>127&&r<2048?(n[--t]=r&63|128,n[--t]=r>>6|192):(n[--t]=r&63|128,n[--t]=r>>6&63|128,n[--t]=r>>12|224)}n[--t]=0;i=new I;for(r=[];t>2;){for(r[0]=0;r[0]==0;){i.nextBytes(r)}n[--t]=r[0]}n[--t]=2;n[--t]=0;return new m(n)}function E(){this.n=null;this.e=0;this.coeff=this.dmq1=this.dmp1=this.q=this.p=this.d=null}function jt(e,t){e!=null&&t!=null&&e.length>0&&t.length>0?(this.n=z(e,16),this.e=parseInt(t,16)):alert("Invalid RSA public key")}function Jt(e){return e.modPowInt(this.e,this.n)}function Gt(e){e=Vt(e,this.n.bitLength()+7>>3);if(e==null)return null;e=this.doPublic(e);if(e==null)return null;e=e.toString(16);return(e.length&1)==0?e:"0"+e}E.prototype.doPublic=Jt;E.prototype.setPublic=jt;E.prototype.encrypt=Gt;function Xt(e,t){for(var n=e.toByteArray(),i=0;i<n.length&&n[i]==0;){++i}if(n.length-i!=t-1||n[i]!=2)return null;for(++i;n[i]!=0;){if(++i>=n.length)return null}for(var r="";++i<n.length;){var a=n[i]&255;a<128?r+=String.fromCharCode(a):a>191&&a<224?(r+=String.fromCharCode((a&31)<<6|n[i+1]&63),++i):(r+=String.fromCharCode((a&15)<<12|(n[i+1]&63)<<6|n[i+2]&63),i+=2)}return r}function Kt(e,t,n){e!=null&&t!=null&&e.length>0&&t.length>0?(this.n=z(e,16),this.e=parseInt(t,16),this.d=z(n,16)):alert("Invalid RSA private key")}function $t(e,t,n,i,r,a,o,s){e!=null&&t!=null&&e.length>0&&t.length>0?(this.n=z(e,16),this.e=parseInt(t,16),this.d=z(n,16),this.p=z(i,16),this.q=z(r,16),this.dmp1=z(a,16),this.dmq1=z(o,16),this.coeff=z(s,16)):alert("Invalid RSA private key")}function Zt(e,t){var n=new C,i=e>>1;this.e=parseInt(t,16);for(var r=new m(t,16);;){for(;;){if(this.p=new m(e-i,1,n),this.p.subtract(m.ONE).gcd(r).compareTo(m.ONE)==0&&this.p.isProbablePrime(10))break}for(;;){if(this.q=new m(i,1,n),this.q.subtract(m.ONE).gcd(r).compareTo(m.ONE)==0&&this.q.isProbablePrime(10))break}if(this.p.compareTo(this.q)<=0){var a=this.p;this.p=this.q;this.q=a}var a=this.p.subtract(m.ONE),o=this.q.subtract(m.ONE),s=a.multiply(o);if(s.gcd(r).compareTo(m.ONE)==0){this.n=this.p.multiply(this.q);this.d=r.modInverse(s);this.dmp1=this.d.mod(a);this.dmq1=this.d.mod(o);this.coeff=this.q.modInverse(this.p);break}}}function Qt(e){if(this.p==null||this.q==null)return e.modPow(this.d,this.n);for(var t=e.mod(this.p).modPow(this.dmp1,this.p),e=e.mod(this.q).modPow(this.dmq1,this.q);t.compareTo(e)<0;){t=t.add(this.p)}return t.subtract(e).multiply(this.coeff).mod(this.p).multiply(this.q).add(e)}function en(e){e=this.doPrivate(z(e,16));return e==null?null:Xt(e,this.n.bitLength()+7>>3)}E.prototype.doPrivate=Qt;E.prototype.setPrivate=Kt;E.prototype.setPrivateEx=$t;E.prototype.generate=Zt;E.prototype.decrypt=en;var A=[];A.sha1="3021300906052b0e03021a05000414";A.sha256="3031300d060960864801650304020105000420";var B=[];B.sha1=qt.hex;B.sha256=Ut.hex;function O(e,t,n){t/=4;for(var e=(0,B[n])(e),n="00"+A[n]+e,e="",t=t-4-n.length,i=0;i<t;i+=2){e+="ff"}return sPaddedMessageHex="0001"+e+n}function tn(e,t){var n=O(e,this.n.bitLength(),t);return this.doPrivate(z(n,16)).toString(16)}function nn(e){e=O(e,this.n.bitLength(),"sha1");return this.doPrivate(z(e,16)).toString(16)}function rn(e){e=O(e,this.n.bitLength(),"sha256");return this.doPrivate(z(e,16)).toString(16)}function an(e,t,n){var i=new E;i.setPublic(t,n);return i.doPublic(e)}function on(e,t,n){return an(e,t,n).toString(16).replace(/^1f+00/,"")}function sn(e){for(var t in A){var n=A[t],i=n.length;if(e.substring(0,i)==n)return[t,e.substring(i)]}return[]}function ln(e,t,n,i){t=on(t,n,i);n=sn(t);if(n.length==0)return!1;t=n[1];e=(0,B[n[0]])(e);return t==e}function un(e,t){var n=z(e,16);return ln(t,n,this.n.toString(16),this.e.toString(16))}function fn(e,t){var t=t.replace(/[ \n]+/g,""),n=this.doPublic(z(t,16)).toString(16).replace(/^1f+00/,""),i=sn(n);if(i.length==0)return!1;n=i[1];i=(0,B[i[0]])(e);return n==i}E.prototype.signString=tn;E.prototype.signStringWithSHA1=nn;E.prototype.signStringWithSHA256=rn;E.prototype.verifyString=fn;E.prototype.verifyHexSignatureForMessage=un;var F=function(){var u={Sbox:[99,124,119,123,242,107,111,197,48,1,103,43,254,215,171,118,202,130,201,125,250,89,71,240,173,212,162,175,156,164,114,192,183,253,147,38,54,63,247,204,52,165,229,241,113,216,49,21,4,199,35,195,24,150,5,154,7,18,128,226,235,39,178,117,9,131,44,26,27,110,90,160,82,59,214,179,41,227,47,132,83,209,0,237,32,252,177,91,106,203,190,57,74,76,88,207,208,239,170,251,67,77,51,133,69,249,2,127,80,60,159,168,81,163,64,143,146,157,56,245,188,182,218,33,16,255,243,210,205,12,19,236,95,151,68,23,196,167,126,61,100,93,25,115,96,129,79,220,34,42,144,136,70,238,184,20,222,94,11,219,224,50,58,10,73,6,36,92,194,211,172,98,145,149,228,121,231,200,55,109,141,213,78,169,108,86,244,234,101,122,174,8,186,120,37,46,28,166,180,198,232,221,116,31,75,189,139,138,112,62,181,102,72,3,246,14,97,53,87,185,134,193,29,158,225,248,152,17,105,217,142,148,155,30,135,233,206,85,40,223,140,161,137,13,191,230,66,104,65,153,45,15,176,84,187,22],ShiftRowTab:[0,5,10,15,4,9,14,3,8,13,2,7,12,1,6,11]};u.Init=function(){u.Sbox_Inv=Array(256);for(var e=0;e<256;e++){u.Sbox_Inv[u.Sbox[e]]=e}u.ShiftRowTab_Inv=Array(16);for(e=0;e<16;e++){u.ShiftRowTab_Inv[u.ShiftRowTab[e]]=e}u.xtime=Array(256);for(e=0;e<128;e++){u.xtime[e]=e<<1,u.xtime[128+e]=e<<1^27}};u.Done=function(){delete u.Sbox_Inv;delete u.ShiftRowTab_Inv;delete u.xtime};u.ExpandKey=function(e){var t=e.length,n,i=1;switch(t){case 16:n=176;break;case 24:n=208;break;case 32:n=240;break;default:alert("my.ExpandKey: Only key lengths of 16, 24 or 32 bytes allowed!")}for(var r=t;r<n;r+=4){var a=e.slice(r-4,r);if(r%t==0){if(a=[u.Sbox[a[1]]^i,u.Sbox[a[2]],u.Sbox[a[3]],u.Sbox[a[0]]],(i<<=1)>=256)i^=283}else t>24&&r%t==16&&(a=[u.Sbox[a[0]],u.Sbox[a[1]],u.Sbox[a[2]],u.Sbox[a[3]]]);for(var o=0;o<4;o++){e[r+o]=e[r+o-t]^a[o]}}};u.Encrypt=function(e,t){var n=t.length;u.AddRoundKey(e,t.slice(0,16));for(var i=16;i<n-16;i+=16){u.SubBytes(e,u.Sbox),u.ShiftRows(e,u.ShiftRowTab),u.MixColumns(e),u.AddRoundKey(e,t.slice(i,i+16))}u.SubBytes(e,u.Sbox);u.ShiftRows(e,u.ShiftRowTab);u.AddRoundKey(e,t.slice(i,n))};u.Decrypt=function(e,t){var n=t.length;u.AddRoundKey(e,t.slice(n-16,n));u.ShiftRows(e,u.ShiftRowTab_Inv);u.SubBytes(e,u.Sbox_Inv);for(n-=32;n>=16;n-=16){u.AddRoundKey(e,t.slice(n,n+16)),u.MixColumns_Inv(e),u.ShiftRows(e,u.ShiftRowTab_Inv),u.SubBytes(e,u.Sbox_Inv)}u.AddRoundKey(e,t.slice(0,16))};u.SubBytes=function(e,t){for(var n=0;n<16;n++){e[n]=t[e[n]]}};u.AddRoundKey=function(e,t){for(var n=0;n<16;n++){e[n]^=t[n]}};u.ShiftRows=function(e,t){for(var n=[].concat(e),i=0;i<16;i++){e[i]=n[t[i]]}};u.MixColumns=function(e){for(var t=0;t<16;t+=4){var n=e[t+0],i=e[t+1],r=e[t+2],a=e[t+3],o=n^i^r^a;e[t+0]^=o^u.xtime[n^i];e[t+1]^=o^u.xtime[i^r];e[t+2]^=o^u.xtime[r^a];e[t+3]^=o^u.xtime[a^n]}};u.MixColumns_Inv=function(e){for(var t=0;t<16;t+=4){var n=e[t+0],i=e[t+1],r=e[t+2],a=e[t+3],o=n^i^r^a,s=u.xtime[o],l=u.xtime[u.xtime[s^n^r]]^o;o^=u.xtime[u.xtime[s^i^a]];e[t+0]^=l^u.xtime[n^i];e[t+1]^=o^u.xtime[i^r];e[t+2]^=l^u.xtime[r^a];e[t+3]^=o^u.xtime[a^n]}};return u}(),hn=function(){var l={};F.Init();l.b256to64=function(e){var t,n,i,r="",a=0,o=0,s=e.length;for(i=0;i<s;i++){n=e.charCodeAt(i),o==0?(r+="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(n>>2&63),t=(n&3)<<4):o==1?(r+="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(t|n>>4&15),t=(n&15)<<2):o==2&&(r+="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(t|n>>6&3),a+=1,r+="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(n&63)),a+=1,o+=1,o==3&&(o=0)}o>0&&(r+="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(t),r+="=");o==1&&(r+="=");return r};l.b64to256=function(e){var t,n,i="",r=0,a=0,o=e.length;for(n=0;n<o;n++){t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".indexOf(e.charAt(n)),t>=0&&(r&&(i+=String.fromCharCode(a|t>>6-r&255)),r=r+2&7,a=t<<r&255)}return i};l.b16to64=function(e){var t,n,i="";e.length%2==1&&(e="0"+e);for(t=0;t+3<=e.length;t+=3){n=parseInt(e.substring(t,t+3),16),i+="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(n>>6)+"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(n&63)}t+1==e.length?(n=parseInt(e.substring(t,t+1),16),i+="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(n<<2)):t+2==e.length&&(n=parseInt(e.substring(t,t+2),16),i+="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(n>>2)+"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt((n&3)<<4));for(;(i.length&3)>0;){i+="="}return i};l.b64to16=function(e){var t="",n,i=0,r;for(n=0;n<e.length;++n){if(e.charAt(n)=="=")break;v="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".indexOf(e.charAt(n));v<0||(i==0?(t+=u(v>>2),r=v&3,i=1):i==1?(t+=u(r<<2|v>>4),r=v&15,i=2):i==2?(t+=u(r),t+=u(v>>2),r=v&3,i=3):(t+=u(r<<2|v>>4),t+=u(v&15),i=0))}i==1&&(t+=u(r<<2));return t};l.string2bytes=function(e){for(var t=[],n=0;n<e.length;n++){t.push(e.charCodeAt(n))}return t};l.bytes2string=function(e){for(var t="",n=0;n<e.length;n++){t+=String.fromCharCode(e[n])}return t};l.blockXOR=function(e,t){for(var n=Array(16),i=0;i<16;i++){n[i]=e[i]^t[i]}return n};l.blockIV=function(){var e=new I,t=Array(16);e.nextBytes(t);return t};l.pad16=function(e){var t=e.slice(0),n=(16-e.length%16)%16;for(i=e.length;i<e.length+n;i++){t.push(0)}return t};l.depad=function(e){for(e=e.slice(0);e[e.length-1]==0;){e=e.slice(0,e.length-1)}return e};l.encryptAESCBC=function(e,t){var n=t.slice(0);F.ExpandKey(n);for(var i=l.string2bytes(e),i=l.pad16(i),r=l.blockIV(),a=0;a<i.length/16;a++){var o=i.slice(a*16,a*16+16),s=r.slice(a*16,a*16+16),o=l.blockXOR(s,o);F.Encrypt(o,n);r=r.concat(o)}n=l.bytes2string(r);return l.b256to64(n)};l.decryptAESCBC=function(e,t){var n=t.slice(0);F.ExpandKey(n);for(var e=l.b64to256(e),i=l.string2bytes(e),r=[],a=1;a<i.length/16;a++){var o=i.slice(a*16,a*16+16),s=i.slice((a-1)*16,(a-1)*16+16);F.Decrypt(o,n);o=l.blockXOR(s,o);r=r.concat(o)}r=l.depad(r);return l.bytes2string(r)};l.wrap60=function(e){for(var t="",n=0;n<e.length;n++){n%60==0&&n!=0&&(t+="\n"),t+=e[n]}return t};l.generateAESKey=function(){var e=Array(16);(new I).nextBytes(e);return e};l.generateRSAKey=function(e,t){Math.seedrandom(Ut.hex(e));var n=new E;n.generate(t,"10001");return n};l.publicKeyString=function(e){return pubkey=e.n.toString(16)};l.publicKeyID=function(e){return Ht(e)};l.publicKeyFromString=function(e){var e=e.split("|")[0],t=new E;t.setPublic(e,"10001");return t};l.encrypt=function(e,t,n){var i="";try{var r=l.publicKeyFromString(t);i+=r.encrypt(e)+"?"}catch(e){return{status:"Invalid public key"}}return{status:"success",cipher:i}};l.decrypt=function(e,t){var n=e.split("?"),i=t.decrypt(n[0]);return{status:"success",plaintext:i,signature:"unsigned"}};return l}();L.exports=hn},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:true});var _=_||function(l,i){var e={},t=e.lib={},r=function e(){},n=t.Base={extend:function e(t){r.prototype=this;var n=new r;t&&n.mixIn(t);n.hasOwnProperty("init")||(n.init=function(){n.$super.init.apply(this,arguments)});n.init.prototype=n;n.$super=this;return n},create:function e(){var t=this.extend();t.init.apply(t,arguments);return t},init:function e(){},mixIn:function e(t){for(var n in t){t.hasOwnProperty(n)&&(this[n]=t[n])}t.hasOwnProperty("toString")&&(this.toString=t.toString)},clone:function e(){return this.init.prototype.extend(this)}},u=t.WordArray=n.extend({init:function e(t,n){t=this.words=t||[];this.sigBytes=n!=i?n:4*t.length},toString:function e(t){return(t||o).stringify(this)},concat:function e(t){var n=this.words,i=t.words,r=this.sigBytes;t=t.sigBytes;this.clamp();if(r%4)for(var a=0;a<t;a++){n[r+a>>>2]|=(i[a>>>2]>>>24-8*(a%4)&255)<<24-8*((r+a)%4)}else if(65535<i.length)for(a=0;a<t;a+=4){n[r+a>>>2]=i[a>>>2]}else n.push.apply(n,i);this.sigBytes+=t;return this},clamp:function e(){var t=this.words,n=this.sigBytes;t[n>>>2]&=4294967295<<32-8*(n%4);t.length=l.ceil(n/4)},clone:function e(){var t=n.clone.call(this);t.words=this.words.slice(0);return t},random:function e(t){for(var n=[],i=0;i<t;i+=4){n.push(4294967296*l.random()|0)}return new u.init(n,t)}}),a=e.enc={},o=a.Hex={stringify:function e(t){var n=t.words;t=t.sigBytes;for(var i=[],r=0;r<t;r++){var a=n[r>>>2]>>>24-8*(r%4)&255;i.push((a>>>4).toString(16));i.push((a&15).toString(16))}return i.join("")},parse:function e(t){for(var n=t.length,i=[],r=0;r<n;r+=2){i[r>>>3]|=parseInt(t.substr(r,2),16)<<24-4*(r%8)}return new u.init(i,n/2)}},s=a.Latin1={stringify:function e(t){var n=t.words;t=t.sigBytes;for(var i=[],r=0;r<t;r++){i.push(String.fromCharCode(n[r>>>2]>>>24-8*(r%4)&255))}return i.join("")},parse:function e(t){for(var n=t.length,i=[],r=0;r<n;r++){i[r>>>2]|=(t.charCodeAt(r)&255)<<24-8*(r%4)}return new u.init(i,n)}},f=a.Utf8={stringify:function e(t){try{return decodeURIComponent(escape(s.stringify(t)))}catch(e){throw Error("Malformed UTF-8 data")}},parse:function e(t){return s.parse(unescape(encodeURIComponent(t)))}},h=t.BufferedBlockAlgorithm=n.extend({reset:function e(){this._data=new u.init;this._nDataBytes=0},_append:function e(t){"string"==typeof t&&(t=f.parse(t));this._data.concat(t);this._nDataBytes+=t.sigBytes},_process:function e(t){var n=this._data,i=n.words,r=n.sigBytes,a=this.blockSize,o=r/(4*a),o=t?l.ceil(o):l.max((o|0)-this._minBufferSize,0);t=o*a;r=l.min(4*t,r);if(t){for(var s=0;s<t;s+=a){this._doProcessBlock(i,s)}s=i.splice(0,t);n.sigBytes-=r}return new u.init(s,r)},clone:function e(){var t=n.clone.call(this);t._data=this._data.clone();return t},_minBufferSize:0});t.Hasher=h.extend({cfg:n.extend(),init:function e(t){this.cfg=this.cfg.extend(t);this.reset()},reset:function e(){h.reset.call(this);this._doReset()},update:function e(t){this._append(t);this._process();return this},finalize:function e(t){t&&this._append(t);return this._doFinalize()},blockSize:16,_createHelper:function e(n){return function(e,t){return new n.init(t).finalize(e)}},_createHmacHelper:function e(n){return function(e,t){return new c.HMAC.init(n,t).finalize(e)}}});var c=e.algo={};return e}(Math);(function(){var e=_,u=e.lib.WordArray;e.enc.Base64={stringify:function e(t){var n=t.words,i=t.sigBytes,r=this._map;t.clamp();t=[];for(var a=0;a<i;a+=3){for(var o=(n[a>>>2]>>>24-8*(a%4)&255)<<16|(n[a+1>>>2]>>>24-8*((a+1)%4)&255)<<8|n[a+2>>>2]>>>24-8*((a+2)%4)&255,s=0;4>s&&a+.75*s<i;s++){t.push(r.charAt(o>>>6*(3-s)&63))}}if(n=r.charAt(64))for(;t.length%4;){t.push(n)}return t.join("")},parse:function e(t){var n=t.length,i=this._map,r=i.charAt(64);r&&(r=t.indexOf(r),-1!=r&&(n=r));for(var r=[],a=0,o=0;o<n;o++){if(o%4){var s=i.indexOf(t.charAt(o-1))<<2*(o%4),l=i.indexOf(t.charAt(o))>>>6-2*(o%4);r[a>>>2]|=(s|l)<<24-8*(a%4);a++}}return u.create(r,a)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="}})();(function(o){function C(e,t,n,i,r,a,o){e=e+(t&n|~t&i)+r+o;return(e<<a|e>>>32-a)+t}function D(e,t,n,i,r,a,o){e=e+(t&i|n&~i)+r+o;return(e<<a|e>>>32-a)+t}function T(e,t,n,i,r,a,o){e=e+(t^n^i)+r+o;return(e<<a|e>>>32-a)+t}function M(e,t,n,i,r,a,o){e=e+(n^(t|~i))+r+o;return(e<<a|e>>>32-a)+t}for(var e=_,t=e.lib,n=t.WordArray,i=t.Hasher,t=e.algo,k=[],r=0;64>r;r++){k[r]=4294967296*o.abs(o.sin(r+1))|0}t=t.MD5=i.extend({_doReset:function e(){this._hash=new n.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function e(t,n){for(var i=0;16>i;i++){var r=n+i,a=t[r];t[r]=(a<<8|a>>>24)&16711935|(a<<24|a>>>8)&4278255360}var i=this._hash.words,r=t[n+0],a=t[n+1],o=t[n+2],s=t[n+3],l=t[n+4],u=t[n+5],f=t[n+6],h=t[n+7],c=t[n+8],d=t[n+9],p=t[n+10],v=t[n+11],m=t[n+12],y=t[n+13],g=t[n+14],S=t[n+15],b=i[0],P=i[1],w=i[2],_=i[3],b=C(b,P,w,_,r,7,k[0]),_=C(_,b,P,w,a,12,k[1]),w=C(w,_,b,P,o,17,k[2]),P=C(P,w,_,b,s,22,k[3]),b=C(b,P,w,_,l,7,k[4]),_=C(_,b,P,w,u,12,k[5]),w=C(w,_,b,P,f,17,k[6]),P=C(P,w,_,b,h,22,k[7]),b=C(b,P,w,_,c,7,k[8]),_=C(_,b,P,w,d,12,k[9]),w=C(w,_,b,P,p,17,k[10]),P=C(P,w,_,b,v,22,k[11]),b=C(b,P,w,_,m,7,k[12]),_=C(_,b,P,w,y,12,k[13]),w=C(w,_,b,P,g,17,k[14]),P=C(P,w,_,b,S,22,k[15]),b=D(b,P,w,_,a,5,k[16]),_=D(_,b,P,w,f,9,k[17]),w=D(w,_,b,P,v,14,k[18]),P=D(P,w,_,b,r,20,k[19]),b=D(b,P,w,_,u,5,k[20]),_=D(_,b,P,w,p,9,k[21]),w=D(w,_,b,P,S,14,k[22]),P=D(P,w,_,b,l,20,k[23]),b=D(b,P,w,_,d,5,k[24]),_=D(_,b,P,w,g,9,k[25]),w=D(w,_,b,P,s,14,k[26]),P=D(P,w,_,b,c,20,k[27]),b=D(b,P,w,_,y,5,k[28]),_=D(_,b,P,w,o,9,k[29]),w=D(w,_,b,P,h,14,k[30]),P=D(P,w,_,b,m,20,k[31]),b=T(b,P,w,_,u,4,k[32]),_=T(_,b,P,w,c,11,k[33]),w=T(w,_,b,P,v,16,k[34]),P=T(P,w,_,b,g,23,k[35]),b=T(b,P,w,_,a,4,k[36]),_=T(_,b,P,w,l,11,k[37]),w=T(w,_,b,P,h,16,k[38]),P=T(P,w,_,b,p,23,k[39]),b=T(b,P,w,_,y,4,k[40]),_=T(_,b,P,w,r,11,k[41]),w=T(w,_,b,P,s,16,k[42]),P=T(P,w,_,b,f,23,k[43]),b=T(b,P,w,_,d,4,k[44]),_=T(_,b,P,w,m,11,k[45]),w=T(w,_,b,P,S,16,k[46]),P=T(P,w,_,b,o,23,k[47]),b=M(b,P,w,_,r,6,k[48]),_=M(_,b,P,w,h,10,k[49]),w=M(w,_,b,P,g,15,k[50]),P=M(P,w,_,b,u,21,k[51]),b=M(b,P,w,_,m,6,k[52]),_=M(_,b,P,w,s,10,k[53]),w=M(w,_,b,P,p,15,k[54]),P=M(P,w,_,b,a,21,k[55]),b=M(b,P,w,_,c,6,k[56]),_=M(_,b,P,w,S,10,k[57]),w=M(w,_,b,P,f,15,k[58]),P=M(P,w,_,b,y,21,k[59]),b=M(b,P,w,_,l,6,k[60]),_=M(_,b,P,w,v,10,k[61]),w=M(w,_,b,P,o,15,k[62]),P=M(P,w,_,b,d,21,k[63]);i[0]=i[0]+b|0;i[1]=i[1]+P|0;i[2]=i[2]+w|0;i[3]=i[3]+_|0},_doFinalize:function e(){var t=this._data,n=t.words,i=8*this._nDataBytes,r=8*t.sigBytes;n[r>>>5]|=128<<24-r%32;var a=o.floor(i/4294967296);n[(r+64>>>9<<4)+15]=(a<<8|a>>>24)&16711935|(a<<24|a>>>8)&4278255360;n[(r+64>>>9<<4)+14]=(i<<8|i>>>24)&16711935|(i<<24|i>>>8)&4278255360;t.sigBytes=4*(n.length+1);this._process();t=this._hash;n=t.words;for(i=0;4>i;i++){r=n[i],n[i]=(r<<8|r>>>24)&16711935|(r<<24|r>>>8)&4278255360}return t},clone:function e(){var t=i.clone.call(this);t._hash=this._hash.clone();return t}});e.MD5=i._createHelper(t);e.HmacMD5=i._createHmacHelper(t)})(Math);(function(){var e=_,t=e.lib,n=t.Base,f=t.WordArray,t=e.algo,i=t.EvpKDF=n.extend({cfg:n.extend({keySize:4,hasher:t.MD5,iterations:1}),init:function e(t){this.cfg=this.cfg.extend(t)},compute:function e(t,n){for(var i=this.cfg,r=i.hasher.create(),a=f.create(),o=a.words,s=i.keySize,i=i.iterations;o.length<s;){l&&r.update(l);var l=r.update(t).finalize(n);r.reset();for(var u=1;u<i;u++){l=r.finalize(l),r.reset()}a.concat(l)}a.sigBytes=4*s;return a}});e.EvpKDF=function(e,t,n){return i.create(n).compute(e,t)}})();_.lib.Cipher||function(o){var e=_,t=e.lib,n=t.Base,s=t.WordArray,i=t.BufferedBlockAlgorithm,r=e.enc.Base64,a=e.algo.EvpKDF,l=t.Cipher=i.extend({cfg:n.extend(),createEncryptor:function e(t,n){return this.create(this._ENC_XFORM_MODE,t,n)},createDecryptor:function e(t,n){return this.create(this._DEC_XFORM_MODE,t,n)},init:function e(t,n,i){this.cfg=this.cfg.extend(i);this._xformMode=t;this._key=n;this.reset()},reset:function e(){i.reset.call(this);this._doReset()},process:function e(t){this._append(t);return this._process()},finalize:function e(t){t&&this._append(t);return this._doFinalize()},keySize:4,ivSize:4,_ENC_XFORM_MODE:1,_DEC_XFORM_MODE:2,_createHelper:function e(r){return{encrypt:function e(t,n,i){return("string"==typeof n?p:d).encrypt(r,t,n,i)},decrypt:function e(t,n,i){return("string"==typeof n?p:d).decrypt(r,t,n,i)}}}});t.StreamCipher=l.extend({_doFinalize:function e(){return this._process(!0)},blockSize:1});var u=e.mode={},f=function e(t,n,i){var r=this._iv;r?this._iv=o:r=this._prevBlock;for(var a=0;a<i;a++){t[n+a]^=r[a]}},h=(t.BlockCipherMode=n.extend({createEncryptor:function e(t,n){return this.Encryptor.create(t,n)},createDecryptor:function e(t,n){return this.Decryptor.create(t,n)},init:function e(t,n){this._cipher=t;this._iv=n}})).extend();h.Encryptor=h.extend({processBlock:function e(t,n){var i=this._cipher,r=i.blockSize;f.call(this,t,n,r);i.encryptBlock(t,n);this._prevBlock=t.slice(n,n+r)}});h.Decryptor=h.extend({processBlock:function e(t,n){var i=this._cipher,r=i.blockSize,a=t.slice(n,n+r);i.decryptBlock(t,n);f.call(this,t,n,r);this._prevBlock=a}});u=u.CBC=h;h=(e.pad={}).Pkcs7={pad:function e(t,n){for(var i=4*n,i=i-t.sigBytes%i,r=i<<24|i<<16|i<<8|i,a=[],o=0;o<i;o+=4){a.push(r)}i=s.create(a,i);t.concat(i)},unpad:function e(t){t.sigBytes-=t.words[t.sigBytes-1>>>2]&255}};t.BlockCipher=l.extend({cfg:l.cfg.extend({mode:u,padding:h}),reset:function e(){l.reset.call(this);var t=this.cfg,n=t.iv,t=t.mode;if(this._xformMode==this._ENC_XFORM_MODE)var i=t.createEncryptor;else i=t.createDecryptor,this._minBufferSize=1;this._mode=i.call(t,this,n&&n.words)},_doProcessBlock:function e(t,n){this._mode.processBlock(t,n)},_doFinalize:function e(){var t=this.cfg.padding;if(this._xformMode==this._ENC_XFORM_MODE){t.pad(this._data,this.blockSize);var n=this._process(!0)}else n=this._process(!0),t.unpad(n);return n},blockSize:4});var c=t.CipherParams=n.extend({init:function e(t){this.mixIn(t)},toString:function e(t){return(t||this.formatter).stringify(this)}}),u=(e.format={}).OpenSSL={stringify:function e(t){var n=t.ciphertext;t=t.salt;return(t?s.create([1398893684,1701076831]).concat(t).concat(n):n).toString(r)},parse:function e(t){t=r.parse(t);var n=t.words;if(1398893684==n[0]&&1701076831==n[1]){var i=s.create(n.slice(2,4));n.splice(0,4);t.sigBytes-=16}return c.create({ciphertext:t,salt:i})}},d=t.SerializableCipher=n.extend({cfg:n.extend({format:u}),encrypt:function e(t,n,i,r){r=this.cfg.extend(r);var a=t.createEncryptor(i,r);n=a.finalize(n);a=a.cfg;return c.create({ciphertext:n,key:i,iv:a.iv,algorithm:t,mode:a.mode,padding:a.padding,blockSize:t.blockSize,formatter:r.format})},decrypt:function e(t,n,i,r){r=this.cfg.extend(r);n=this._parse(n,r.format);return t.createDecryptor(i,r).finalize(n.ciphertext)},_parse:function e(t,n){return"string"==typeof t?n.parse(t,this):t}}),e=(e.kdf={}).OpenSSL={execute:function e(t,n,i,r){r||(r=s.random(8));t=a.create({keySize:n+i}).compute(t,r);i=s.create(t.words.slice(n),4*i);t.sigBytes=4*n;return c.create({key:t,iv:i,salt:r})}},p=t.PasswordBasedCipher=d.extend({cfg:d.cfg.extend({kdf:e}),encrypt:function e(t,n,i,r){r=this.cfg.extend(r);i=r.kdf.execute(i,t.keySize,t.ivSize);r.iv=i.iv;t=d.encrypt.call(this,t,n,i.key,r);t.mixIn(i);return t},decrypt:function e(t,n,i,r){r=this.cfg.extend(r);n=this._parse(n,r.format);i=r.kdf.execute(i,t.keySize,t.ivSize,n.salt);r.iv=i.iv;return d.decrypt.call(this,t,n,i.key,r)}})}();(function(){for(var e=_,t=e.lib.BlockCipher,n=e.algo,s=[],r=[],i=[],a=[],o=[],l=[],u=[],f=[],h=[],c=[],d=[],p=0;256>p;p++){d[p]=128>p?p<<1:p<<1^283}for(var v=0,m=0,p=0;256>p;p++){var y=m^m<<1^m<<2^m<<3^m<<4,y=y>>>8^y&255^99;s[v]=y;r[y]=v;var g=d[v],S=d[g],b=d[S],P=257*d[y]^16843008*y;i[v]=P<<24|P>>>8;a[v]=P<<16|P>>>16;o[v]=P<<8|P>>>24;l[v]=P;P=16843009*b^65537*S^257*g^16843008*v;u[y]=P<<24|P>>>8;f[y]=P<<16|P>>>16;h[y]=P<<8|P>>>24;c[y]=P;v?(v=g^d[d[d[b^g]]],m^=d[d[m]]):v=m=1}var w=[0,1,2,4,8,16,32,64,128,27,54],n=n.AES=t.extend({_doReset:function e(){for(var t=this._key,n=t.words,i=t.sigBytes/4,t=4*((this._nRounds=i+6)+1),r=this._keySchedule=[],a=0;a<t;a++){if(a<i)r[a]=n[a];else{var o=r[a-1];a%i?6<i&&4==a%i&&(o=s[o>>>24]<<24|s[o>>>16&255]<<16|s[o>>>8&255]<<8|s[o&255]):(o=o<<8|o>>>24,o=s[o>>>24]<<24|s[o>>>16&255]<<16|s[o>>>8&255]<<8|s[o&255],o^=w[a/i|0]<<24);r[a]=r[a-i]^o}}n=this._invKeySchedule=[];for(i=0;i<t;i++){a=t-i,o=i%4?r[a]:r[a-4],n[i]=4>i||4>=a?o:u[s[o>>>24]]^f[s[o>>>16&255]]^h[s[o>>>8&255]]^c[s[o&255]]}},encryptBlock:function e(t,n){this._doCryptBlock(t,n,this._keySchedule,i,a,o,l,s)},decryptBlock:function e(t,n){var i=t[n+1];t[n+1]=t[n+3];t[n+3]=i;this._doCryptBlock(t,n,this._invKeySchedule,u,f,h,c,r);i=t[n+1];t[n+1]=t[n+3];t[n+3]=i},_doCryptBlock:function e(t,n,i,r,a,o,s,l){for(var u=this._nRounds,f=t[n]^i[0],h=t[n+1]^i[1],c=t[n+2]^i[2],d=t[n+3]^i[3],p=4,v=1;v<u;v++){var m=r[f>>>24]^a[h>>>16&255]^o[c>>>8&255]^s[d&255]^i[p++],y=r[h>>>24]^a[c>>>16&255]^o[d>>>8&255]^s[f&255]^i[p++],g=r[c>>>24]^a[d>>>16&255]^o[f>>>8&255]^s[h&255]^i[p++],d=r[d>>>24]^a[f>>>16&255]^o[h>>>8&255]^s[c&255]^i[p++],f=m,h=y,c=g}m=(l[f>>>24]<<24|l[h>>>16&255]<<16|l[c>>>8&255]<<8|l[d&255])^i[p++];y=(l[h>>>24]<<24|l[c>>>16&255]<<16|l[d>>>8&255]<<8|l[f&255])^i[p++];g=(l[c>>>24]<<24|l[d>>>16&255]<<16|l[f>>>8&255]<<8|l[h&255])^i[p++];d=(l[d>>>24]<<24|l[f>>>16&255]<<16|l[h>>>8&255]<<8|l[c&255])^i[p++];t[n]=m;t[n+1]=y;t[n+2]=g;t[n+3]=d},keySize:8});e.AES=t._createHelper(n)})();t.default=_},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:true});var i=function(){function i(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||false;i.configurable=true;if("value"in i)i.writable=true;Object.defineProperty(e,i.key,i)}}return function(e,t,n){if(t)i(e.prototype,t);if(n)i(e,n);return e}}();function r(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var a=function(){var e=function(){function e(){r(this,e)}i(e,[{key:"createClientObject",value:function e(t,n,i,r){return{socket:t,id:n,playURL:i,deviceSerial:r.deviceSerial||"",verificationCode:r.verificationCode||"",resolve:null,reject:null}}},{key:"playCmd",value:function e(t){var n={sequence:0,cmd:"realplay",deviceSerial:t.deviceSerial,verificationCode:t.verificationCode,url:t.playURL};return JSON.stringify(n)}},{key:"playbackCmd",value:function e(t,n,i){var r={sequence:0,cmd:"playback",deviceSerial:t.deviceSerial,verificationCode:t.verificationCode,url:t.playURL,startTime:n,endTime:i};return JSON.stringify(r)}}]);return e}();return e}();t.LocalService=a},function(O,e,t){"use strict";Object.defineProperty(e,"__esModule",{value:true});e.JSPlayCtrl=undefined;var n=function(){function i(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||false;i.configurable=true;if("value"in i)i.writable=true;Object.defineProperty(e,i.key,i)}}return function(e,t,n){if(t)i(e.prototype,t);if(n)i(e,n);return e}}();var i=t(13);var r=a(i);function a(e){return e&&e.__esModule?e:{default:e}}function s(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var b=0;var P=1;var f=2;var F=3;var L=4;var U=5;var N=6;var q=7;var w=11;var _=16;var C=31;var D=35;var T=36;var M=48;var l=60;var o=61;var H=62;var u=63;var k=100;var x=101;var h=0;var c=1;var d=0;var p=1;var v=0;var R=1;var I=15;var m=8;var y=1;var z=25;var E=5;var W=2;var Y=5*1024*1024;var V=100;var g=1024*20;var j=200;var J=100;var G=20;var X=100;var S=200;var K={width:0,height:0,frameNum:0,yuvData:null};var $={width:0,height:0,frameNum:0,yuvData:null};var Z={sampleRate:0,channel:0,bitsPerSample:0,length:0,pcmData:null};var A=false;var B=false;var Q={id:null,cmd:null,data:null,errorCode:0,status:null};var ee=e.JSPlayCtrl=function(){function i(e,t,o,n){s(this,i);if(e!=null&&e!==undefined&&typeof e==="string"){this.szBasePath=e}else{return b}if(t&&typeof t==="function"){this.fnCallBack=t}else{return b}if(typeof n==="string"){this.staticPath=n}this.decodeWorker=null;this.streamOpenMode=null;this.bOpenStream=false;this.audioRenderer=null;this.aAudioBuffer=[];this.iAudioBufferSize=0;this.Volume=0;this.oSuperRender=null;this.aVideoFrameBuffer=[];this.YUVBufferSize=y;this.szOSDTime=null;this.bJSPrintLog=false;this.bWriteYUVData=false;this.iYUV10size=0;this.aVideoYUVBuffer=[];this.bWritePCMData=false;this.iAudioBuffer500Size=0;this.aAudioPCMBuffer=[];this.bWriteRawData=false;this.iRawDataSize=0;this.aRawDataBuffer=[];this.bWriteRTPData=true;this.iRTPDataSize=0;this.aRTPDataBuffer=[];this.downloadRTP=false;this.rtpNum=0;this.bPlaySound=false;this.bPlay=false;this.bPause=false;this.bOnebyOne=false;this.bPlayRateChange=false;this.audioNum=0;this.videoNum=0;this.FrameForwardLen=1;this.bOnlyPlaySound=false;this.dataCallBackFun=null;this.YUVBufSizeCBFun=null;this.DecCallBackFun=null;this.DisplayCallBackFun=null;this.PCMCallBackFun=null;this.DecInfoYUV=K;this.DisplayInfoYUV=$;this.DecInfoPCM=Z;this.nWidth=0;this.nHeight=0;this.nSPSCropLeft=0;this.nSPSCropRight=0;this.nSPSCropTop=0;this.nSPSCropBottom=0;this.sCanvasId=null;this.aDisplayBuf=null;this.bVisibility=true;this.nDecFrameType=v;this.iCanvasWidth=0;this.iCanvasHeight=0;this.iZoomNum=0;this.iRatio_x=1;this.iRatio_y=1;this.stDisplayRect={top:0,left:0,right:0,bottom:0};this.bDisRect=false;this.stYUVRect={top:0,left:0,right:0,bottom:0};this.aInputDataLens=[];this.aInputDataBuffer=[];this.bIsGetYUV=false;this.bIsFirstFrame=true;this.iInputMaxBufSize=Y;this.bIsInput=false;this.bIsInputBufOver=false;this.bIsInputBufWillOver=false;this.iInputDataLen=g;var S=this;this.errorCode=P;this.loopNum=0;this.setCallBack=function(e,t,n,i,r){var a=Q;a.id=o;a.cmd=t;a.data=n;a.errorCode=i;a.status=r;e.fnCallBack(a)};if(!A){A=true}if(!B){B=true}this.convertErrorCode=function(e){switch(e){case 1:return P;case 98:return b;default:return e}};this.arrayBufferCopy=function(e){var t=e.byteLength;var n=new Uint8Array(t);var i=new Uint8Array(e);var r=0;for(r=0;r<t;r++){n[r]=i[r]}return n};this.inputDataFun=function(){var e;var t;var n=0;S.bIsGetYUV=false;if(S.bIsInputBufOver||S.bIsInputBufWillOver){e=new Uint8Array(1);t=new Uint8Array(e);var i={command:"InputData",data:t.buffer,dataSize:0};S.decodeWorker.postMessage(i,[i.data])}else{if(S.bPlay&&(!S.bPause||S.bOnebyOne)||this.bOnlyPlaySound){while(S.aInputDataLens.length>0){n+=S.aInputDataLens.shift();if(n>S.iInputDataLen){break}}e=S.aInputDataBuffer.splice(0,n);t=new Uint8Array(e);var i={command:"InputData",data:t.buffer,dataSize:n};S.decodeWorker.postMessage(i,[i.data])}}e=null;t=null};this.getPic=function(e,t){if(this.decodeWorker==null||this.oSuperRender==null){return f}if(!this.bPlay){return f}if(e&&typeof e==="function"){this.dataCallBackFun=e}else{return b}if(0===this.iZoomNum){this.stYUVRect.left=0;this.stYUVRect.top=0;this.stYUVRect.right=0;this.stYUVRect.bottom=0}else{if(0===this.iCanvasWidth||0===this.iCanvasHeight){this.stYUVRect.left=0;this.stYUVRect.top=0;this.stYUVRect.right=0;this.stYUVRect.bottom=0}else{var n=this.nWidth/this.iCanvasWidth;var i=this.nHeight/this.iCanvasHeight;this.stYUVRect.left=Math.round(this.stDisplayRect.left*n);this.stYUVRect.top=Math.round(this.stDisplayRect.top*i);this.stYUVRect.right=Math.round(this.stDisplayRect.right*n);this.stYUVRect.bottom=Math.round(this.stDisplayRect.bottom*i)}if(this.stYUVRect.right-this.stYUVRect.left<32||this.stYUVRect.bottom-this.stYUVRect.top<32){return b}}if(this.aDisplayBuf==null){return f}var r=this.arrayBufferCopy(this.aDisplayBuf);var a={command:t,data:r.buffer,width:this.nWidth,height:this.nHeight,rect:this.stYUVRect};this.decodeWorker.postMessage(a,[a.data]);return P};this.createWorker=function(g){if(window.Worker){if(this.decodeWorker==null){console.log("staticPath",this.staticPath);var e=new Blob([(0,r.default)(this.staticPath)]);var t=URL.createObjectURL(e);this.decodeWorker=new Worker(t);if(g.bJSPrintLog){console.log(">>>JSPlayV1.1 createWorker success!")}if(this.decodeWorker==null){return l}}this.decodeWorker.onmessage=function(e){var t=null;var n=e.data;switch(n.function){case"printLog":console.log("print JSPlayerSDK log failed");break;case"loaded":t="loaded";g.setCallBack(g,"loaded",0,0,true);break;case"SetStreamOpenMode":t="SetStreamOpenMode";break;case"OpenStream":t="OpenStream";if(1===n.errorCode){S.bOpenStream=true;return}break;case"InputData":t="InputData";if(n.errorCode===w){S.bIsInputBufOver=true;console.log("yff inputBuffer over set key frame \n");if(S.nDecFrameType!=R){S.PlayM4_SetDecodeFrameType(R)}}if(n.errorCode===T){S.bIsInputBufWillOver=true;console.log("C buffer will over, C sourceRemain:"+n.sourceRemain)}if(n.errorCode===C){S.bIsInputBufOver=false;S.bIsInputBufWillOver=false;if(S.aInputDataLens.length>0&&S.bIsInput){S.inputDataFun();S.bIsInput=false}else{S.bIsGetYUV=true}}break;case"GetFrameData":t="GetFrameData";if(!S.bOnlyPlaySound){if(n.data!=null&&n.frameInfo!=null){var i=n.frameInfo.width;var r=n.frameInfo.height}if(!S.bPlay){return}S.errorCode=n.errorCode;if(!S.bIsFirstFrame&&(n.errorCode===C||n.errorCode===D)){if(n.errorCode===C){S.bIsInputBufOver=false;S.bIsInputBufWillOver=false}if(S.loopNum>5){S.bIsGetYUV=true;S.loopNum=0}else{S.inputDataFun();S.loopNum++}break}else if(S.bIsInputBufOver||S.bIsInputBufWillOver){S.inputDataFun()}else{if(n.type==="videoType"){if(S.aInputDataLens.length>0&&S.bIsInput){S.inputDataFun();S.bIsInput=false}else{S.bIsGetYUV=true}S.bIsFirstFrame=false}}}if(S.bVisibility){if(P===n.errorCode){switch(n.type){case"videoType":if(n.data==null||n.frameInfo==null){return b}if(S.DecCallBackFun!=null){S.DecInfoYUV.height=n.frameInfo.height;S.DecInfoYUV.width=n.frameInfo.width;S.DecInfoYUV.frameNum=n.frameInfo.frameNum;S.DecInfoYUV.yuvData=new Uint8Array(n.data);S.DecCallBackFun(S.DecInfoYUV)}S.bIsFirstFrame=false;g.nWidth=n.frameInfo.width;g.nHeight=n.frameInfo.height;g.nSPSCropLeft=n.frameInfo.cropLeft;g.nSPSCropRight=n.frameInfo.cropRight;g.nSPSCropTop=n.frameInfo.cropTop;g.nSPSCropBottom=n.frameInfo.cropBottom;var a=new Object;a.data=n.data;a.osdTime=n.osd;a.nWidth=n.frameInfo.width;a.nHeight=n.frameInfo.height;a.frameNum=n.frameInfo.frameNum;a.timeStamp=n.frameInfo.timeStamp;if(g.bWriteYUVData){var o=new Uint8Array(n.data);var s=g.aVideoYUVBuffer.length;for(var l=0,u=o.length;l<u;l++){g.aVideoYUVBuffer[s+l]=o[l]}g.iYUV10size++;o=null}if(g.bWriteYUVData&&g.iYUV10size>=G){var f=new Uint8Array(g.aVideoYUVBuffer);g.downloadFile(f,"videoYUV.data");g.aVideoYUVBuffer.splice(0,g.aVideoYUVBuffer.length);g.bWriteYUVData=false;g.iYUV10size=0;f=null}g.aVideoFrameBuffer.push(a);a=null;var h=g.aVideoFrameBuffer.length;if(h>E){if(!g.bOnebyOne){g.aVideoFrameBuffer.splice(0,W)}}if(g.bOnebyOne){if(g.aVideoFrameBuffer.length>=I){g.setCallBack(g,"OnebyOne",0,0,false);g.bIsFirstFrame=true;break}}break;case"audioType":if(g.bPlaySound&&!g.bPlayRateChange||S.bOnlyPlaySound){if(S.PCMCallBackFun!=null){S.DecInfoPCM.sampleRate=n.frameInfo.samplesPerSec;S.DecInfoPCM.channel=n.frameInfo.channels;S.DecInfoPCM.bitsPerSample=n.frameInfo.bitsPerSample;S.DecInfoPCM.pcmData=new Uint8Array(n.data);S.DecInfoPCM.length=S.DecInfoPCM.pcmData.length;S.PCMCallBackFun(S.DecInfoPCM)}var o=new Uint8Array(n.data);var s=g.aAudioBuffer.length;for(var l=0,u=o.length;l<u;l++){g.aAudioBuffer[s+l]=o[l]}g.iAudioBufferSize++;o=null;if(g.bWritePCMData){var o=new Uint8Array(n.data);var s=g.aAudioPCMBuffer.length;for(var l=0,u=o.length;l<u;l++){g.aAudioPCMBuffer[s+l]=o[l]}console.log("audio_type num:"+g.iAudioBuffer500Size+", len:"+o.length);g.iAudioBuffer500Size++;o=null}if(g.bWritePCMData&&g.iAudioBuffer500Size>=J){var c=new Uint8Array(g.aAudioPCMBuffer);g.downloadFile(c,"audioPCM.data");g.aAudioPCMBuffer.splice(0,g.aAudioPCMBuffer.length);g.bWritePCMData=false;g.iAudioBuffer500Size=0;c=null}if(g.iAudioBufferSize>=z){g.audioRenderer.Play(g.aAudioBuffer,g.aAudioBuffer.length,n.frameInfo);g.aAudioBuffer.splice(0,g.aAudioBuffer.length);g.aAudioBuffer.length=0;g.iAudioBufferSize=0}}break;case"privateType":break;default:break}}}break;case"GetRawData":t="GetRawData";if(g.bWriteRawData){var d=new Uint8Array(n.data);var p=g.aRawDataBuffer.length;for(var l=0,u=d.length;l<u;l++){g.aRawDataBuffer[p+l]=d[l]}g.iRawDataSize++;d=null}if(g.bWriteRawData&&g.iRawDataSize>=X){var v=new Uint8Array(g.aRawDataBuffer);g.downloadFile(v,"rawBuffer.data");g.aRawDataBuffer.splice(0,g.aRawDataBuffer.length);g.bWriteRawData=false;g.iRawDataSize=0;v=null}break;case"PlaySound":t="PlaySound";break;case"GetJPEG":t="GetJPEG";if(n.errorCode!==P){console.log("GetJPEG ErrorParam");return}var m=n.data;g.dataCallBackFun(m);break;case"GetBMP":t="GetBMP";if(n.errorCode!==P){console.log("GetBMP ErrorParam");return}var y=n.data;g.dataCallBackFun(y);break;default:break}if("GetFrameData"!==t){g.setCallBack(g,t,0,g.convertErrorCode(n.errorCode),true)}else{if(_===n.errorCode||x===n.errorCode||k===n.errorCode||M===n.errorCode){g.setCallBack(g,t,0,g.convertErrorCode(n.errorCode),true)}}}}};this.createWorker(S);this.draw=function(){if(S.bPlay){if(!S.bPause||S.bOnebyOne){requestAnimationFrame(S.draw);var e=S.aVideoFrameBuffer.length;if(S.YUVBufSizeCBFun!=null){S.YUVBufSizeCBFun(e)}if(S.bOnebyOne){if(e<=m){S.setCallBack(S,"OnebyOne",0,C,true)}if(e<=S.FrameForwardLen+1){S.setCallBack(S,"OnebyOne",0,C,true);return}else{var t=S.FrameForwardLen;while(t>1){var n=S.aVideoFrameBuffer.shift();t--}}S.bOnebyOne=false}if(e>0){var i=S.aVideoFrameBuffer.shift();S.aDisplayBuf=i.data;var r=new Uint8Array(S.aDisplayBuf);S.oSuperRender.SR_DisplayFrameData(i.nWidth,i.nHeight,r,i.nWidth-S.nSPSCropLeft-S.nSPSCropRight,i.nHeight-S.nSPSCropTop-S.nSPSCropBottom);if(S.DisplayCallBackFun!=null){S.DisplayInfoYUV.height=i.nHeight;S.DisplayInfoYUV.width=i.nWidth;S.DisplayInfoYUV.frameNum=i.frameNum;S.DisplayInfoYUV.yuvData=new Uint8Array(r);S.DisplayCallBackFun(S.DisplayInfoYUV)}r=null;S.szOSDTime=i.osdTime;i=null}else{S.setCallBack(S,"Play",0,C,true)}}}else{if(!S.bPlay){S.aVideoFrameBuffer.splice(0,S.aVideoFrameBuffer.length);S.aAudioBuffer.splice(0,S.aAudioBuffer.length)}}}}n(i,[{key:"PlayM4_SetCurrentFrameNum",value:function e(t,n){return _}},{key:"PlayM4_OpenPlayerSDKPrintLog",value:function e(t){if(t===true){this.bJSPrintLog=true;this.decodeWorker.postMessage({command:"printLog",data:t})}else{this.bJSPrintLog=false;this.decodeWorker.postMessage({command:"printLog",data:t})}return P}},{key:"PlayM4_DownloadYUVdata",value:function e(){this.bWriteYUVData=true;return P}},{key:"PlayM4_DownloadPCMdata",value:function e(){this.bWritePCMData=true;return P}},{key:"PlayM4_SetDecCallBack",value:function e(t){if(t&&typeof t==="function"){this.DecCallBackFun=t;return P}else{return b}}},{key:"PlayM4_SetDisplayCallBack",value:function e(t){if(t&&typeof t==="function"){this.DisplayCallBackFun=t;return P}else{return b}}},{key:"PlayM4_SetPCMCallBack",value:function e(t){if(t&&typeof t==="function"){this.PCMCallBackFun=t;return P}else{return b}}},{key:"PlayM4_SetStreamOpenMode",value:function e(t){if(t==null||t===undefined){return b}if(t!==d&&t!==p){return b}this.streamOpenMode=t;return P}},{key:"PlayM4_DownloadRTPData",value:function e(t){this.downloadRTP=t}},{key:"PlayM4_OpenStream",value:function e(t,n,i){if(this.bJSPrintLog){console.log(">>>JS PlayM4_OpenStream nSysTime:"+((new Date).getMonth()+1)+"-"+(new Date).getDate()+" "+(new Date).getHours()+":"+(new Date).getMinutes()+":"+(new Date).getSeconds()+"."+(new Date).getMilliseconds())}if(this.decodeWorker==null){return f}if(this.downloadRTP){var r=new Uint8Array(t.buffer);this.DownloadRTPData(r);console.log("write 40 hik head")}if(t==null||n<=0||i<=0){return b}this.bPlay=false;this.bPause=false;this.bOnebyOne=false;this.bIsFirstFrame=true;this.bIsGetYUV=false;this.bIsInput=false;this.decodeWorker.postMessage({command:"SetStreamOpenMode",data:this.streamOpenMode});this.decodeWorker.postMessage({command:"OpenStream",data:t,dataSize:n,bufPoolSize:i});this.bOpenStream=true;return P}},{key:"PlayM4_CloseStream",value:function e(){if(this.decodeWorker===null||this.bOpenStream===false){return f}this.bOnlyPlaySound=false;this.PlayM4_Stop();this.decodeWorker.postMessage({command:"CloseStream"});if(this.oSuperRender!==null){this.oSuperRender.SR_Destroy();this.oSuperRender=null}if(this.audioRenderer!==null){this.audioRenderer.Stop();this.audioRenderer=null}this.aAudioBuffer.splice(0,this.aAudioBuffer.length);this.aVideoFrameBuffer.splice(0,this.aVideoFrameBuffer.length);this.aInputDataBuffer.splice(0,this.aInputDataBuffer.length);this.aInputDataLens.splice(0,this.aInputDataLens.length);this.aVideoYUVBuffer.splice(0,this.aVideoYUVBuffer.length);this.aAudioPCMBuffer.splice(0,this.aAudioPCMBuffer.length);this.aRawDataBuffer.splice(0,this.aRawDataBuffer.length);this.bOpenStream=false;this.iAudioBufferSize=0;this.szOSDTime=null;return P}},{key:"PlayM4_Destroy",value:function e(){if(this.decodeWorker===null){return P}this.PlayM4_CloseStream();this.decodeWorker.terminate();this.decodeWorker=null;return P}},{key:"PlayM4_InputData",value:function e(t,n){var i=this.aInputDataBuffer.length;if(n===4){var r=new Uint8Array(t.buffer);if(r[0]===1&&r[1]===2&&r[2]===3&&r[3]===4){if(this.bIsFirstFrame){this.inputDataFun()}else{if(this.bIsGetYUV){this.inputDataFun()}else{this.bIsInput=true}}r=null;return P}}if(i+n>this.iInputMaxBufSize){console.log("input over");if(this.bIsGetYUV){this.inputDataFun()}else{this.bIsInput=true}return w}var a=null;var o=n;switch(this.streamOpenMode){case p:a=new Uint8Array(t.buffer);if(this.downloadRTP){this.DownloadRTPData(a);this.rtpNum++;console.log("STREAM_FILE psNUm:"+this.rtpNum)}this.aInputDataLens.push(n);break;case d:o=n+4;var s=new Uint32Array([n]);var l=new Uint8Array(s.buffer);a=new Uint8Array(o);a.set(l,0);a.set(t,4);if(this.downloadRTP){this.DownloadRTPData(a);this.rtpNum++;console.log("STREAM_REALTIME rtpNUm:"+this.rtpNum)}s=null;l=null;this.aInputDataLens.push(n+4);break;default:return _}for(var u=0;u<o;u++){this.aInputDataBuffer[i+u]=a[u]}if(!this.bPlay&&!this.bOnlyPlaySound||this.decodeWorker===null||this.bOpenStream===false){return P}a=null;if(this.bOnlyPlaySound){this.inputDataFun()}else{if(this.bIsFirstFrame){this.inputDataFun()}else{if(this.bIsGetYUV){this.inputDataFun()}else{this.bIsInput=true}}}return P}},{key:"DownloadRTPData",value:function e(t){if(this.bWriteRTPData){var n=new Uint8Array(t);var i=this.aRTPDataBuffer.length;for(var r=0,a=n.length;r<a;r++){this.aRTPDataBuffer[i+r]=n[r]}this.iRTPDataSize++;n=null}if(this.bWriteRTPData&&this.iRTPDataSize>=S){console.log("download"+S+"RTPdata");var o=new Uint8Array(this.aRTPDataBuffer);this.downloadFile(o,"RTP.data");this.aRTPDataBuffer.splice(0,this.aRTPDataBuffer.length);this.iRTPDataSize=0;this.rtpNum=0;this.downloadRTP=false;o=null}}},{key:"PlayM4_Play",value:function e(t){if(this.decodeWorker===null){return f}if(this.bJSPrintLog){console.log(">>>JS PlayM4_Play canvasID: "+t)}if(t===null){this.bOnlyPlaySound=true;this.decodeWorker.postMessage({command:"OnlyPlaySound"});this.sCanvasId=null}else{if(typeof t!=="string"){return b}if(this.bOnebyOne){this.bPlayRateChange=false;this.bOnebyOne=false;this.bPause=false;this.draw()}if(this.bPlay){return P}if(this.oSuperRender==null){this.oSuperRender=new SuperRender(t,this.szBasePath);if(this.oSuperRender==null){return o}}this.sCanvasId=t;this.bPlay=true;this.bPause=false;this.bOnebyOne=false;this.bPlayRateChange=false;this.bOnlyPlaySound=false;this.draw()}if(this.audioRenderer==null){this.audioRenderer=new AudioRenderer;if(this.audioRenderer==null){return o}}this.decodeWorker.postMessage({command:"Play"});return P}},{key:"PlayM4_Stop",value:function e(){if(this.decodeWorker==null||this.oSuperRender==null){return f}if(!this.bPlay){return f}if(this.bPlaySound){this.PlayM4_StopSound();this.bPlaySound=true}this.bPlay=false;this.bOnebyOne=false;this.bPause=false;this.oSuperRender.SR_SetDisplayRect(null);this.iZoomNum=0;this.bDisRect=false;return P}},{key:"PlayM4_PlayRate",value:function e(t){if(this.decodeWorker==null){return f}if(t===1){this.bPlayRateChange=false}else{this.bPlayRateChange=true}if(t<1){t=1}this.iInputDataLen=t*g;this.decodeWorker.postMessage({command:"PlayRate",playRate:t});return P}},{key:"PlayM4_Pause",value:function e(t){if(this.decodeWorker==null||this.oSuperRender==null){return f}if(!this.bPlay){return f}if(this.bOnebyOne){return f}if(this.bPause==t){return f}if(typeof t!=="boolean"){return b}this.bPause=t;this.bIsFirstFrame=true;if(t){if(this.bPlaySound){this.PlayM4_StopSound();this.bPlaySound=true}}else{if(this.bPlaySound){this.PlayM4_PlaySound()}this.draw()}return P}},{key:"PlayM4_OneByOne",value:function e(t){if(this.decodeWorker==null||this.oSuperRender==null){return f}if(!this.bPlay){return f}if(t>10||t<=0){return b}if(!t){t=1}this.iInputDataLen=g;this.FrameForwardLen=t;this.bPause=true;this.bOnebyOne=true;this.bIsFirstFrame=true;this.draw();return P}},{key:"PlayM4_PlaySound",value:function e(t){if(this.decodeWorker===null||this.bOpenStream===false){return f}if(t<0||t>16){return b}if(this.audioRenderer==null){this.audioRenderer=new AudioRenderer;if(this.audioRenderer==null){return o}}this.decodeWorker.postMessage({command:"PlaySound"});this.audioRenderer.SetWndNum(t);if(this.Volume!==0){this.audioRenderer.SetVolume(this.Volume)}this.audioRenderer.oAudioContext.resume();this.bPlaySound=true;return P}},{key:"PlayM4_StopSound",value:function e(){if(this.decodeWorker==null||this.audioRenderer==null){return f}if(!this.bPlaySound){return f}this.decodeWorker.postMessage({command:"StopSound"});this.bPlaySound=false;return P}},{key:"PlayM4_SetDisplayBuf",value:function e(t){if(this.decodeWorker==null){return f}if(t<=0){return b}this.YUVBufferSize=t;return P}},{key:"PlayM4_SetSecretKey",value:function e(t,n,i){if(this.decodeWorker==null||this.bOpenStream===false){return f}if(n==null){return b}if(c===t){if(128===i){if(n==null||n===undefined){return b}}else{return b}}else if(h===t){}else{return b}this.decodeWorker.postMessage({command:"SetSecretKey",data:n,nKeyType:t,nKeyLen:i});return P}},{key:"PlayM4_SetDecodeFrameType",value:function e(t){console.log("PlayM4_SetDecodeFrameType nFrameType:"+t);if(this.decodeWorker==null||this.oSuperRender==null){return f}if(t!==v&&t!==R){return b}if(this.bJSPrintLog){console.log(">>>JS PlayM4_SetDecodeFrameType :"+t)}this.nDecFrameType=t;this.decodeWorker.postMessage({command:"SetDecodeFrameType",data:t});return P}},{key:"PlayM4_SetIFrameDecInterval",value:function e(t){if(this.nDecFrameType!==R){return f}if(t<0){return b}this.decodeWorker.postMessage({command:"SetIFrameDecInterval",data:t});return P}},{key:"PlayM4_SetLostFrameMode",value:function e(t){if(t<0||t>1){return b}this.decodeWorker.postMessage({command:"SetLostFrameMode",data:t});return P}},{key:"PlayM4_SetDisplayRegion",value:function e(t,n){if(this.decodeWorker===null||this.bPlay===false||this.oSuperRender===null){return f}if(this.canvasId===null){return f}if(n===true){if(t===null||t===undefined){return b}if(typeof t.left==="number"&&typeof t.top==="number"&&typeof t.right==="number"&&typeof t.bottom==="number"){if(t.right<0||t.left<0||t.top<0||t.bottom<0){return b}var i=t.left;var r=t.right;var a=t.top;var o=t.bottom;var s=document.getElementById(this.sCanvasId).getBoundingClientRect();this.iCanvasWidth=s.width;this.iCanvasHeight=s.height;if(r-i<16||o-a<16||r-i>this.iCanvasWidth||o-a>this.iCanvasHeight){return b}if(this.iZoomNum!==0){i=Math.round(i/this.iRatio_x)+this.stDisplayRect.left;a=Math.round(a/this.iRatio_y)+this.stDisplayRect.top;r=Math.round(r/this.iRatio_x)+this.stDisplayRect.left;o=Math.round(o/this.iRatio_y)+this.stDisplayRect.top}this.stDisplayRect={top:a,left:i,right:r,bottom:o};this.oSuperRender.SR_SetDisplayRect(this.stDisplayRect);this.bDisRect=true;var l=r-i;var u=o-a;this.iRatio_x=this.iCanvasWidth/l;this.iRatio_y=this.iCanvasHeight/u}else{return b}}else{this.oSuperRender.SR_SetDisplayRect(null);this.iZoomNum=0;this.bDisRect=false}if(this.bPause||this.bOnebyOne||this.bPlayRateChange){this.oSuperRender.SR_DisplayFrameData(this.nWidth,this.nHeight,new Uint8Array(this.aDisplayBuf))}return P}},{key:"PlayM4_GetBMP",value:function e(t){return this.getPic(t,"GetBMP")}},{key:"PlayM4_GetJPEG",value:function e(t){return this.getPic(t,"GetJPEG")}},{key:"PlayM4_SetVolume",value:function e(t){if(this.decodeWorker==null){return f}if(this.audioRenderer==null){return f}if(t<0||t>100||t===""){return b}this.Volume=t;this.audioRenderer.SetVolume(t);return P}},{key:"PlayM4_GetVolume",value:function e(t){if(this.decodeWorker==null){return f}if(this.audioRenderer==null){return f}if(t&&typeof t==="function"){var n=this.audioRenderer.GetVolume();if(n===null){return u}else{t(n);return P}}else{return b}}},{key:"PlayM4_GetOSDTime",value:function e(t){if(this.decodeWorker==null){return f}if(!this.bPlay){return f}if(t&&typeof t==="function"){t(this.szOSDTime);return P}else{return b}}},{key:"PlayM4_IsVisible",value:function e(t){this.bVisibility=t;return P}},{key:"PlayM4_GetSdkVersion",value:function e(){return"07040001"}},{key:"PlayM4_GetBuildDate",value:function e(){return"20220624"}},{key:"PlayM4_GetInputBufSize",value:function e(){return this.aInputDataBuffer.length}},{key:"PlayM4_SetInputBufSize",value:function e(t){if(t>0){this.iInputMaxBufSize=t;console.log(">>JSBufferSize SetInputBufSize:"+this.iInputMaxBufSize);return P}else{return b}}},{key:"PlayM4_GetYUVBufSize",value:function e(){return this.aVideoFrameBuffer.length}},{key:"PlayM4_GetFrameResolution",value:function e(t){if(this.decodeWorker==null){return f}if(t&&typeof t==="function"){t(this.nWidth,this.nHeight);return P}else{return b}}},{key:"PlayM4_RegisterYUVBufSizeCB",value:function e(t){if(t&&typeof t==="function"){this.YUVBufSizeCBFun=t;return P}else{return b}}},{key:"PlayM4_UnRegisterYUVBufSizeCB",value:function e(){if(this.YUVBufSizeCBFun!=null){this.YUVBufSizeCBFun=null}return P}},{key:"PlayM4_ClearCanvas",value:function e(){if(this.oSuperRender==null){return f}this.oSuperRender.SR_DisplayFrameData(this.nWidth,this.nHeight,null);return P}},{key:"PlayM4_ReleaseInputBuffer",value:function e(){if(this.aInputDataBuffer===null){return f}this.aInputDataBuffer.splice(0,this.aInputDataBuffer.length);this.aInputDataLens.splice(0,this.aInputDataLens.length);return P}},{key:"PlayM4_GetDecodeFrameType",value:function e(){return this.nDecFrameType}},{key:"PlayM4_SetDemuxModel",value:function e(t,n){this.decodeWorker.postMessage({command:"SetDemuxModel",nIdemuxType:t,bTrue:n});return P}},{key:"PlayM4_SkipErrorData",value:function e(t){this.decodeWorker.postMessage({command:"SkipErrorData",bSkip:t});return P}},{key:"PlayM4_SetDecodeERC",value:function e(t){this.decodeWorker.postMessage({command:"SetDecodeERC",nLevel:t});return P}},{key:"PlayM4_SetANRParam",value:function e(t,n){this.decodeWorker.postMessage({command:"SetANRParam",nEnable:t,nANRLevel:n});return P}},{key:"PlayM4_SetResampleValue",value:function e(t,n){this.decodeWorker.postMessage({command:"SetResampleValue",nEnable:t,resampleValue:n});return P}},{key:"downloadFile",value:function e(t,n){var i=t;if(!(t instanceof Blob||t instanceof File)){i=new Blob([t])}var r=window.URL.createObjectURL(i);var a=window.document.createElement("a");a.href=r;a.download=n;var o=document.createEvent("MouseEvents");o.initEvent("click",true,true);a.dispatchEvent(o)}}]);return i}()},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:true});var i=function e(){var t=arguments.length>0&&arguments[0]!==undefined?arguments[0]:"https://open.ys7.com/assets/ezuikit_v3.6";return"\n(function (event) {\n    const AUDIO_TYPE = 0;\t// 音频\n    const VIDEO_TYPE = 1;   // 视频\n    const PRIVT_TYPE = 2;  // 私有帧\n\n    const PLAYM4_AUDIO_FRAME = 100; // 音频帧\n    const PLAYM4_VIDEO_FRAME = 101; // 视频帧\n\n    const PLAYM4_OK = 1;\n\tconst PLAYM4_ORDER_ERROR = 2;\n    const PLAYM4_DECODE_ERROR = 44 \t// 解码失败\n    const PLAYM4_NOT_KEYFRAME = 48; \t// 非关键帧\n    const PLAYM4_NEED_MORE_DATA = 31;   // 需要更多数据才能解析\n    const PLAYM4_NEED_NEET_LOOP = 35; //丢帧需要下个循环\n    const PLAYM4_SYS_NOT_SUPPORT = 16; \t// 不支持\n\n    importScripts('"+t+'/js/playctrl/Decoder.js\');\n    Module.addOnPostRun(function () {\n        postMessage({\'function\': "loaded"});\n    });\n\n    var iStreamMode = 0;  // 流模式\n\n    var bOpenMode = false;\n    var bOpenStream = false;\n    \n    var funGetFrameData = null;\n    var funGetAudFrameData = null;\n\t\n    var bWorkerPrintLog=false;//worker层log开关\n    \n    var g_nPort = -1;\n\n    onmessage = function (event)\n    {\n        var eventData = event.data;\n        var res = 0;\n        switch (eventData.command)\n        {\n\t\t\tcase "printLog":\n\t\t\t    let downloadFlag=eventData.data;\n\t\t\t    if(downloadFlag===true)\n                {\n                    bWorkerPrintLog=true;\n                    res = Module._SetPrintLogFlag(g_nPort,downloadFlag);\n                }\n\t\t\t    else\n                {\n                    bWorkerPrintLog=false;\n                    res = Module._SetPrintLogFlag(g_nPort,downloadFlag);\n                }\n\n\t\t\t\tif (res !== PLAYM4_OK)\n                {\n\t\t\t\t\tconsole.log("DecodeWorker.js: PlayerSDK print log failed,res"+res);\n                    postMessage({\'function\': "printLog", \'errorCode\': res});\n                }\n\t\t\t\tbreak;\n            case "SetPlayPosition":\n                let nFrameNumOrTime=eventData.data;\n                let enPosType=eventData.type;\n                // res = Module._SetPlayPosition(nFrameNumOrTime,enPosType);\n                // if (res !== PLAYM4_OK)\n                // {\n                //     postMessage({\'function\': "SetPlayPosition", \'errorCode\': res});\n                //     return;\n                // }\n                // //有没有buffer需要清除\n\n                break;\n            case "SetStreamOpenMode":\n                //获取端口号\n                g_nPort = Module._GetPort();\n                //设置流打开模式\n                iStreamMode = eventData.data;\n                res = Module._SetStreamOpenMode(g_nPort,iStreamMode);\n                if (res !== PLAYM4_OK)\n                {\n                    postMessage({\'function\': "SetStreamOpenMode", \'errorCode\': res});\n                    return;\n                }\n                bOpenMode = true;\n                break;\n\n            case "OpenStream":\n                // 接收到的数据\n                var iHeadLen = eventData.dataSize;\n                var pHead = Module._malloc(iHeadLen + 4);\n                if (pHead === null)\n                {\n                    return;\n                }\n                var aHead = Module.HEAPU8.subarray(pHead, pHead + iHeadLen);\n                aHead.set(eventData.data);\n                res = Module._OpenStream(g_nPort,pHead, iHeadLen, eventData.bufPoolSize);\n                postMessage({\'function\': "OpenStream", \'errorCode\': res});\n                if (res !== PLAYM4_OK)\n                {\n                    //释放内存\n                    Module._free(pHead);\n                    pHead = null;\n                    return;\n                }\n                bOpenStream = true;\n                break;\n            case "Play":\n                 let resP = Module._Play(g_nPort);\n                 if (resP !== PLAYM4_OK)\n                 {\n                     return;\n                 }\n                break;\n            case "InputData":\n                // 接收到的数据\n                var iLen = eventData.dataSize;\n                if (iLen > 0)\n                {\n                    var pInputData = Module._malloc(iLen);\n                    if (pInputData === null)\n                    {\n                        return;\n                    }\n                    var inputData = new Uint8Array(eventData.data);\n                    // var aInputData = Module.HEAPU8.subarray(pInputData, pInputData + iLen);\n                    // aInputData.set(inputData);\n                    Module.writeArrayToMemory(inputData, pInputData);\n                    inputData = null;\n                    res = Module._InputData(g_nPort,pInputData, iLen);\n                    if (res !== PLAYM4_OK)\n                    {\n                        let errorCode = Module._GetLastError(g_nPort);\n                        let sourceRemain =  Module._GetSourceBufferRemain(g_nPort);\n                        postMessage({\'function\': "InputData", \'errorCode\': errorCode,"sourceRemain":sourceRemain});\n                    }\n                    Module._free(pInputData);\n                    pInputData = null;  \n                }else{\n                    let sourceRemain =  Module._GetSourceBufferRemain(g_nPort);\n                    if(sourceRemain == 0)\n                    {\n                        // console.log("C buffer and JS buffer size is both 0");\n                        postMessage({\'function\': "InputData", \'errorCode\':PLAYM4_NEED_MORE_DATA});\n                        return;\n                    }\n                }\n\n                /////////////////////\n                if (funGetFrameData === null)\n                {\n                    funGetFrameData = Module.cwrap(\'GetFrameData\', \'number\');\n                }\n\n                while (bOpenMode && bOpenStream)\n                {\n                    \n                    var ret = getFrameData(funGetFrameData);\n                    // 直到获取视频帧或数据不足为止\n                    if (PLAYM4_VIDEO_FRAME === ret ||PLAYM4_NEED_MORE_DATA === ret || PLAYM4_ORDER_ERROR === ret || PLAYM4_NEED_NEET_LOOP ===ret)//PLAYM4_VIDEO_FRAME === ret ||\n                    {\n                        break;\n                    }\n                }\n                break;\n\n            case "SetSecretKey":\n                var keyLen = eventData.nKeyLen;\n                var pKeyData = Module._malloc(keyLen);\n                if (pKeyData === null) {\n                    return;\n                }\n                var nKeySize = eventData.data.length\n                var bufData = stringToBytes (eventData.data);\n                var aKeyData = Module.HEAPU8.subarray(pKeyData, pKeyData + keyLen);\n                aKeyData.set(new Uint8Array(bufData));\n\n                res = Module._SetSecretKey(g_nPort,eventData.nKeyType, pKeyData, keyLen);//, nKeySize\n                if (res !== PLAYM4_OK) {\n                    postMessage({\'function\': "SetSecretKey", \'errorCode\': res});\n                    Module._free(pKeyData);\n                    pKeyData = null;\n                    return;\n                }\n\n                Module._free(pKeyData);\n                pKeyData = null;\n                break;\n\n            case "GetBMP":\n                var nBMPWidth = eventData.width;\n                var nBMPHeight = eventData.height;\n                var pYUVData = eventData.data;\n                var nYUVSize = nBMPWidth * nBMPHeight * 3 / 2;\n                var oBMPCropRect = eventData.rect;\n\n                var pDataYUV = Module._malloc(nYUVSize);\n                if (pDataYUV === null) {\n                    return;\n                }\n\n                Module.writeArrayToMemory(new Uint8Array(pYUVData, 0, nYUVSize), pDataYUV);\n\n                // 分配BMP空间\n                var nBmpSize = nBMPWidth * nBMPHeight * 4 + 60;\n                var pBmpData = Module._malloc(nBmpSize);\n                var pBmpSize = Module._malloc(4);\n                if (pBmpData === null || pBmpSize === null) {\n                    Module._free(pDataYUV);\n                    pDataYUV = null;\n\n                    if (pBmpData != null) {\n                        Module._free(pBmpData);\n                        pBmpData = null;\n                    }\n\n                    if (pBmpSize != null) {\n                        Module._free(pBmpSize);\n                        pBmpSize = null;\n                    }\n                    return;\n                }\n\n               //Module._memset(pBmpSize, nBmpSize, 4); // 防止bmp截图出现输入数据过大的错误码\n                Module.setValue(pBmpSize, nBmpSize, "i32"); \n                res = Module._GetBMP(g_nPort,pDataYUV, nYUVSize, pBmpData, pBmpSize,\n                    oBMPCropRect.left, oBMPCropRect.top, oBMPCropRect.right, oBMPCropRect.bottom);\n                if (res !== PLAYM4_OK) {\n                    postMessage({\'function\': "GetBMP", \'errorCode\': res});\n                    Module._free(pDataYUV);\n                    pDataYUV = null;\n                    Module._free(pBmpData);\n                    pBmpData = null;\n                    Module._free(pBmpSize);\n                    pBmpSize = null;\n                    return;\n                }\n\n                // 获取BMP图片大小\n                var nBmpDataSize = Module.getValue(pBmpSize, "i32");\n\n                // 获取BMP图片数据\n                var aBmpData = new Uint8Array(nBmpDataSize);\n                aBmpData.set(Module.HEAPU8.subarray(pBmpData, pBmpData + nBmpDataSize));\n\n                postMessage({\'function\': "GetBMP", \'data\': aBmpData, \'errorCode\': res}, [aBmpData.buffer]);\n                aBmpData=null;\n                if (pDataYUV != null) {\n                    Module._free(pDataYUV);\n                    pDataYUV = null;\n                }\n                if (pBmpData != null) {\n                    Module._free(pBmpData);\n                    pBmpData = null;\n                }\n                if (pBmpSize != null) {\n                    Module._free(pBmpSize);\n                    pBmpSize = null;\n                }\n                break;\n\n            case "GetJPEG":\n                var nJpegWidth = eventData.width;\n                var nJpegHeight = eventData.height;\n                var pYUVData1 = eventData.data;\n                var nYUVSize1 = nJpegWidth * nJpegHeight * 3 / 2;\n                var oJpegCropRect = eventData.rect;\n\n                var pDataYUV1 = Module._malloc(nYUVSize1);\n                if (pDataYUV1 === null) {\n                    return;\n                }\n\n                Module.writeArrayToMemory(new Uint8Array(pYUVData1, 0, nYUVSize1), pDataYUV1);\n\n                // 分配JPEG空间\n                var pJpegData = Module._malloc(nYUVSize1);\n                var pJpegSize = Module._malloc(4);\n                if (pJpegData === null || pJpegSize === null) {\n                    if (pJpegData != null) {\n                        Module._free(pJpegData);\n                        pJpegData = null;\n                    }\n\n                    if (pJpegSize != null) {\n                        Module._free(pJpegSize);\n                        pJpegSize = null;\n                    }\n\n                    if (pDataYUV1 != null) {\n                        Module._free(pDataYUV1);\n                        pDataYUV1 = null;\n                    }\n                    return;\n                }\n\n                Module.setValue(pJpegSize, nJpegWidth * nJpegHeight * 2, "i32");    // JPEG抓图，输入缓冲长度不小于当前帧YUV大小\n\n                res = Module._GetJPEG(g_nPort,pDataYUV1, nYUVSize1, pJpegData, pJpegSize,\n                    oJpegCropRect.left, oJpegCropRect.top, oJpegCropRect.right, oJpegCropRect.bottom);\n                if (res !== PLAYM4_OK) {\n                    postMessage({\'function\': "GetJPEG", \'errorCode\': res});\n                    if (pJpegData != null) {\n                        Module._free(pJpegData);\n                        pJpegData = null;\n                    }\n\n                    if (pJpegSize != null) {\n                        Module._free(pJpegSize);\n                        pJpegSize = null;\n                    }\n\n                    if (pDataYUV1 != null) {\n                        Module._free(pDataYUV1);\n                        pDataYUV1 = null;\n                    }\n                    return;\n                }\n\n                // 获取JPEG图片大小\n                var nJpegSize = Module.getValue(pJpegSize, "i32");\n\n                // 获取JPEG图片数据\n                var aJpegData = new Uint8Array(nJpegSize);\n                aJpegData.set(Module.HEAPU8.subarray(pJpegData, pJpegData + nJpegSize));\n\n                postMessage({\'function\': "GetJPEG", \'data\': aJpegData, \'errorCode\': res}, [aJpegData.buffer]);\n\n                nJpegSize = null;\n                aJpegData = null;\n\n                if (pDataYUV1 != null) {\n                    Module._free(pDataYUV1);\n                    pDataYUV1 = null;\n                }\n                if (pJpegData != null) {\n                    Module._free(pJpegData);\n                    pJpegData = null;\n                }\n                if (pJpegSize != null) {\n                    Module._free(pJpegSize);\n                    pJpegSize = null;\n                }\n                break;\n\n            case "SetDecodeFrameType":\n                var nFrameType = eventData.data;\n                res = Module._SetDecodeFrameType(g_nPort,nFrameType);\n                if (res !== PLAYM4_OK) {\n                    postMessage({\'function\': "SetDecodeFrameType", \'errorCode\': res});\n                    return;\n                }\n                break;\n            case "CloseStream":\n                //stop\n                let resS = Module._Stop(g_nPort);\n                if (resS !== PLAYM4_OK) {\n                    postMessage({\'function\': "Stop", \'errorCode\': res});\n                    return;\n                }\n                //closeStream\n                res = Module._CloseStream(g_nPort);\n                if (res !== PLAYM4_OK) {\n                    postMessage({\'function\': "CloseStream", \'errorCode\': res});\n                    return;\n                }\n                //freePort\n                let resF = Module._FreePort(g_nPort);\n                if (resF !== PLAYM4_OK) {\n                    postMessage({\'function\': "FreePort", \'errorCode\': res});\n                    return;\n                }\n                break;\n            case "PlaySound":\n                let resPS = Module._PlaySound(g_nPort);\n                if (resPS !== PLAYM4_OK) {\n                    console.log("PlaySound failed");\n                    return;\n                }\n                break;\n            case "StopSound":\n                let resSS = Module._StopSound();\n                if (resSS !== PLAYM4_OK) {\n                    console.log("StopSound failed");\n                    return;\n                }\n                break;\n            case "SetVolume":\n                let resSV = Module._SetVolume(g_nPort,eventData.volume);\n                if (resSV !== PLAYM4_OK) {\n                    console.log("Audio SetVolume failed");\n                    return;\n                }\n                break;\n            case "GetVolume":\n                let volume = Module._GetVolume();\n                if(volume>0)\n                {\n                    postMessage({\'function\': "GetVolume", \'volume\': volume});\n                }\n                else{\n                    console.log("Audio GetVolume failed");\n                    return;\n                }\n                break;\n            case "OnlyPlaySound":\n                let resOPS = Module._OnlyPlaySound(g_nPort);\n                if (resOPS !== PLAYM4_OK) {\n                    console.log("OnlyPlaySound failed");\n                    return;\n                }\n                break;\n            case "Pause" :\n                let resPa = Module._Pause(g_nPort,eventData.bPlay);\n                if (resPa !== PLAYM4_OK) {\n                    console.log("Pause failed");\n                    return;\n                }\n            case "PlayRate":\n                Module._SetPlayRate(g_nPort,eventData.playRate);\n                break;\n            case "SetIFrameDecInterval":\n                Module._SetIFrameDecInterval(g_nPort,eventData.data);\n                break;\n\t\t\tcase "SetLostFrameMode":\n\t\t\t    Module._SetLostFrameMode(g_nPort,eventData.data);\n                break;\n            case "SetDemuxModel":\n                Module._SetDemuxModel(g_nPort,eventData.nIdemuxType,eventData.bTrue);\n                break;\n            case "SkipErrorData":\n                Module._SkipErrorData(g_nPort,eventData.bSkip);\n                break;\n            case "SetDecodeERC":\n                Module._SetDecodeERC(g_nPort,eventData.nLevel);\n                break;\n            case "SetANRParam":\n                Module._SetANRParam(g_nPort,eventData.nEnable,eventData.nANRLevel);\n                break;\n            case "SetResampleValue":\n                Module._SetResampleValue(g_nPort,eventData.nEnable,eventData.resampleValue);\n                break;\n            case "GetLastError":\n                let errorCode = Module._GetLastError(g_nPort);\n                postMessage({\'function\': "GetLastError", \'errorCode\': errorCode});\n                break;\n            default:\n                break;\n        }\n    };\n\n    function getOSDTime(oFrameInfo) {\n        var iYear = oFrameInfo.year;\n        var iMonth = oFrameInfo.month;\n        var iDay = oFrameInfo.day;\n        var iHour = oFrameInfo.hour;\n        var iMinute = oFrameInfo.minute;\n        var iSecond = oFrameInfo.second;\n\n        if (iMonth < 10) {\n            iMonth = "0" + iMonth;\n        }\n        if (iDay < 10) {\n            iDay = "0" + iDay;\n        }\n        if (iHour < 10) {\n            iHour = "0" + iHour;\n        }\n        if (iMinute < 10) {\n            iMinute = "0" + iMinute;\n        }\n        if (iSecond < 10) {\n            iSecond = "0" + iSecond;\n        }\n\n        return iYear + "-" + iMonth + "-" + iDay + " " + iHour + ":" + iMinute + ":" + iSecond;\n    }\n    // 获取帧数据\n    function getFrameData(fun)\n    {\n    // function getFrameData() {\n        // 获取帧数据\n        // var res = Module._GetFrameData();\n        var res = fun();\n        if (res === PLAYM4_OK)\n        {\n            var oFrameInfo = Module._GetFrameInfo();\n            switch (oFrameInfo.frameType)\n            {\n                case AUDIO_TYPE:\n                    var iSize = oFrameInfo.frameSize;\n                    if (0 === iSize)\n                    {\n                        return -1;\n                    }\n                    var pPCM = Module._GetFrameBuffer();\n                    // var audioBuf = new ArrayBuffer(iSize);\n                    var aPCMData = new Uint8Array(iSize);\n                    aPCMData.set(Module.HEAPU8.subarray(pPCM, pPCM + iSize));\n                    if(bWorkerPrintLog)\n                    {\n                        console.log("<<<Worker: audio media Info: nSise:"+ oFrameInfo.frameSize+",nSampleRate:"+oFrameInfo.samplesPerSec+\',channel:\'+oFrameInfo.channels+\',bitsPerSample:\'+oFrameInfo.bitsPerSample);\n                    }\n                    postMessage({\n                        \'function\': "GetFrameData", \'type\': "audioType", \'data\': aPCMData.buffer,\n                        \'frameInfo\': oFrameInfo, \'errorCode\': res\n                    }, [aPCMData.buffer]);\n\n                    oFrameInfo = null;\n                    pPCM = null;\n                    aPCMData = null;\n                    return PLAYM4_AUDIO_FRAME;\n\n                case VIDEO_TYPE:\n                    var szOSDTime = getOSDTime(oFrameInfo);\n\n                    var iWidth = oFrameInfo.width;\n                    var iHeight = oFrameInfo.height;\n\n                    var iYUVSize = iWidth * iHeight * 3 / 2;\n                    if (0 === iYUVSize)\n                    {\n                        return -1;\n                    }\n\n                    var pYUV = Module._GetFrameBuffer();\n\n                    // 图像数据渲染后压回，若从主码流切到子码流，存在数组大小与图像大小不匹配现象\n                    var aYUVData = new Uint8Array(iYUVSize);\n                    aYUVData.set(Module.HEAPU8.subarray(pYUV, pYUV + iYUVSize));\n                    if(bWorkerPrintLog)\n                    {\n                        console.log("<<<Worker: video media Info: Width:"+ oFrameInfo.width+",Height:"+oFrameInfo.height+",timeStamp:"+oFrameInfo.timeStamp);\n                    }\n\n                    postMessage({\n                      \'function\': "GetFrameData", \'type\': "videoType", \'data\': aYUVData.buffer,\n                   \'dataLen\': aYUVData.length,\'osd\': szOSDTime, \'frameInfo\': oFrameInfo, \'errorCode\': res\n                    }, [aYUVData.buffer]);\n\n                    oFrameInfo = null;\n                    pYUV = null;\n                    aYUVData = null;\n                    return PLAYM4_VIDEO_FRAME;\n\n                case PRIVT_TYPE:\n                    postMessage({\n                        \'function\': "GetFrameData", \'type\': "", \'data\': null,\n                        \'dataLen\': -1, \'osd\': 0, \'frameInfo\': null, \'errorCode\': PLAYM4_SYS_NOT_SUPPORT\n                    });\n                    return PLAYM4_SYS_NOT_SUPPORT;\n\n                default:\n                    postMessage({\n                        \'function\': "GetFrameData", \'type\': "", \'data\': null,\n                        \'dataLen\': -1, \'osd\': 0, \'frameInfo\': null, \'errorCode\': PLAYM4_SYS_NOT_SUPPORT\n                    });\n                    return PLAYM4_SYS_NOT_SUPPORT;\n            }\n        }\n        else {\n            let errorCode = Module._GetLastError(g_nPort);\n            //解码失败返回裸数据\n            if(PLAYM4_DECODE_ERROR===errorCode)\n            {\n                var rawInfo=Module._GetRawDataInfo();\n                var pRawData = Module._GetRawDataBuffer();\n                var aRawData = new Uint8Array(rawInfo.isize);\n                aRawData.set(Module.HEAPU8.subarray(pRawData, pRawData + rawInfo.isize));\n                postMessage({\n                    \'function\': "GetRawData", \'type\': "", \'data\':aRawData.buffer,\n                    \'rawDataLen\': rawInfo.isize, \'osd\': 0, \'frameInfo\': null, \'errorCode\': errorCode\n                });\n                rawInfo=null;\n                pRawData=null;\n                aRawData=null;\n            }\n            //需要更多数据\n            if (PLAYM4_NEED_MORE_DATA === errorCode || PLAYM4_SYS_NOT_SUPPORT === errorCode || PLAYM4_NEED_NEET_LOOP === errorCode){\n                postMessage({\n                    \'function\': "GetFrameData", \'type\': "", \'data\': null,\n                    \'dataLen\': -1, \'osd\': 0, \'frameInfo\': null, \'errorCode\': errorCode\n                });\n            }\n            return errorCode;\n        }\n    }\n\n    // 开始计算时间\n    function startTime() {\n        return new Date().getTime();\n    }\n\n    // 结束计算时间\n    function endTime() {\n        return new Date().getTime();\n    }\n\n    // 字母字符串转byte数组\n    function stringToBytes ( str ) {\n        var ch, st, re = [];\n        for (var i = 0; i < str.length; i++ ) {\n            ch = str.charCodeAt(i);  // get char\n            st = [];                 // set up "stack"\n            do {\n                st.push( ch & 0xFF );  // push byte to stack\n                ch = ch >> 8;          // shift value down by 1 byte\n            }\n            while ( ch );\n            // add stack contents to result\n            // done because chars have "wrong" endianness\n            re = re.concat( st.reverse() );\n        }\n        // return an array of bytes\n        return re;\n    }\n})();\n'};t.default=i},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:true});t.StorageManager=undefined;var a=function(){function i(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||false;i.configurable=true;if("value"in i)i.writable=true;Object.defineProperty(e,i.key,i)}}return function(e,t,n){if(t)i(e.prototype,t);if(n)i(e,n);return e}}();var c=n(2);var i=n(0);var r=n(15);var o=s(r);function s(e){return e&&e.__esModule?e:{default:e}}function u(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var l="Web/RecordFiles/";var f="Web/PlaybackFiles/";var h=1e3;var d=1;var p=3001;window.requestFileSystem=window.requestFileSystem||window.webkitRequestFileSystem;window.URL=window.URL||window.webkitURL;var v=function(){function r(e,t,n,i){u(this,r);this.szUUID=e;this.szFileName=t;this.iStreamType=n;this.szPath="";this.bStart=false;this.aStreamList=[];this.options=i}a(r,[{key:"init",value:function e(){var n=this;if(0===this.iStreamType){this.szPath=l}else if(1===this.iStreamType){this.szPath=f}this.szPath+=this.getDateDir();var i=n.szPath.split("/");var t=new Promise(function(t){window.requestFileSystem(window.TEMPORARY,n.options.iFileSize,function(e){n.createDir(e.root,i,function(){t()})},n.errorHandler)});return t}},{key:"getDateDir",value:function e(){return i.oTool.dateFormat(new Date,"yyyy-MM-dd")}},{key:"createDir",value:function e(t,n,i){var r=this;if(n.length){t.getDirectory(n[0],{create:true},function(e){r.createDir(e,n.slice(1),i)},r.errorHandler)}else{i()}}},{key:"errorHandler",value:function e(){}},{key:"writeFileHeader",value:function e(n){var i=this;window.requestFileSystem(window.TEMPORARY,i.options.iFileSize,function(e){e.root.getFile(i.szPath+"/"+i.szFileName,{create:true},function(e){e.createWriter(function(e){e.onwriteend=function(){i.bStart=true;i.writeFile(e)};e.onerror=function(){};e.seek(e.length);var t=new Blob([n]);e.write(t)},i.errorHandler)},i.errorHandler)},i.errorHandler)}},{key:"writeFileContent",value:function e(t){this.aStreamList.push(t)}},{key:"writeFile",value:function e(t){var n=this;if(this.bStart){if(this.aStreamList.length>0){var i=this.aStreamList.shift();t.seek(t.length);if(t.length>=this.options.iFileSize){if(this.options.cbEventHandler){this.options.cbEventHandler(p,this.szUUID)}return}var r=new Blob([i]);t.write(r)}else{setTimeout(function(){n.writeFile(t)},h)}}}},{key:"stopWriteFile",value:function e(){var n=this;this.bStart=false;this.aStreamList.length=0;var t=new Promise(function(t){window.requestFileSystem(window.TEMPORARY,n.options.iFileSize,function(e){e.root.getFile(n.szPath+"/"+n.szFileName,{create:false},function(e){e.file(function(e){t();i.oTool.downloadFile(e,e.name)})},n.errorHandler)},n.errorHandler)});return t}}]);return r}();var m=function(){function l(e,t,n,i,r,a,o,s){u(this,l);this.szBasePath=e;this.szUUID=t;this.szFileName=n;this.aHeadBuf=new Uint8Array(i);this.iPackType=r;this.iStreamType=a;this.oWorker=null;this.oFileSystem=null;this.options=o;this.bHead=true;this.staticPath=s}a(l,[{key:"init",value:function e(){var n=this;var t=new Promise(function(e,t){n.initFileSystem().then(function(){n.initWorker().then(function(){e(n.szUUID)},function(e){t(e)})},function(e){t(e)})});return t}},{key:"initFileSystem",value:function e(){var n=this;this.oFileSystem=new v(this.szUUID,this.szFileName,this.iStreamType,this.options);var t=new Promise(function(e,t){n.oFileSystem.init().then(function(){e()},function(e){t(e)})});return t}},{key:"initWorker",value:function e(){var a=this;var t=new Promise(function(r){var e=new Blob([(0,o.default)(a.staticPath)]);var t=URL.createObjectURL(e);a.oWorker=new Worker(t);a.oWorker.onmessage=function(e){var t=e.data;var n=a.iPackType;if(a.options.iPackage===1){n=12}if("loaded"===t.type){a.oWorker.postMessage({type:"create",buf:a.aHeadBuf.buffer,len:40,packType:n},[a.aHeadBuf.buffer])}else if("created"===t.type){r()}else if("outputData"===t.type){var i=new Uint8Array(t.buf);if(a.options.iPackage===1){if(a.bHead){a.oFileSystem.writeFileHeader(i);a.bHead=false}else{a.oFileSystem.writeFileContent(i)}}else{if(d===t.dType){a.oFileSystem.writeFileHeader(i)}else{a.oFileSystem.writeFileContent(i)}}}}});return t}},{key:"inputData",value:function e(t){if(this.oWorker){var n=new Uint8Array(t);this.oWorker.postMessage({type:"inputData",buf:n.buffer,len:n.length},[n.buffer])}}},{key:"stopRecord",value:function e(){var n=this;var t=new Promise(function(e,t){if(n.oWorker){n.oWorker.postMessage({type:"release"})}else{t()}if(n.oFileSystem){n.oFileSystem.stopWriteFile().then(function(){n.bHead=true;e()},function(){t()})}else{t()}});return t}}]);return l}();var y=function(){var h=Symbol("STORAGELIST");var e=function(){function i(e,t,n){u(this,i);this.szBasePath=e;this[h]={};this.options={iFileSize:1024*1024*1024};Object.assign(this.options,t);if(typeof t.staticPath==="string"){this.staticPath=t.staticPath}}a(i,[{key:"startRecord",value:function e(t,n,i,r,a){var o=this;var s=(0,c.v4)();var l=Object.assign({},this.options,a);var u=new m(this.szBasePath,s,t,n,i,r,l,this.staticPath);var f=new Promise(function(t,n){u.init().then(function(e){o[h][e]=u;t(e)},function(e){n(e)})});return f}},{key:"inputData",value:function e(t,n){var i=this[h][t];if(i){i.inputData(n)}}},{key:"stopRecord",value:function e(i){var r=this;var t=new Promise(function(e,t){var n=r[h][i];if(n){n.stopRecord().then(function(){delete r[h][i];e()},function(){t()})}else{t()}});return t}}]);return i}();return e}();t.StorageManager=y},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:true});var i=function e(){var t=arguments.length>0&&arguments[0]!==undefined?arguments[0]:"https://open.ys7.com/assets/ezuikit_v3.6";return"(function() {\n    const RECORDRTP = 0;  //录制一份未经过转封装的码流原始数据，用于定位问题\n    self.Module = { memoryInitializerRequest: loadMemInitFile(), TOTAL_MEMORY: 128*1024*1024 };\n    importScripts('"+t+'/js/transform/SystemTransform.js\');\n\n    Module.postRun.push(function() {\n        postMessage({type: "loaded"});\n    });\n\n    onmessage = function (e) {\n        var data = e.data;\n\n        if ("create" === data.type) {\n            var iHeadLen = data.len;\n            var pHead = Module._malloc(iHeadLen);\n\n            var aData = Module.HEAPU8.subarray(pHead, pHead + iHeadLen);\n            aData.set(new Uint8Array(data.buf));\n\n            var iTransType = data.packType;//目标格式 RTP->PS\n            if (RECORDRTP) {\n                postMessage({type: "created"});\n                postMessage({type: "outputData", buf: data.buf, dType: 1}, [data.buf]);\n            } else {\n                var iRet = Module._ST_Create(pHead, iHeadLen, iTransType);\n                if (iRet != 0) {\n                    console.log("_ST_Create failed!");\n                } else {\n                    Module._ST_Start();\n                    postMessage({type: "created"});\n                }\n            }\n\n        } else if ("inputData" === data.type) {\n            if (RECORDRTP) {\n                var aFileData = new Uint8Array(data.buf);  // 拷贝一份\n                var iBufferLen = aFileData.length;\n                var szBufferLen = iBufferLen.toString(16);\n                if (szBufferLen.length === 1) {\n                    szBufferLen = "000" + szBufferLen;\n                } else if (szBufferLen.length === 2) {\n                    szBufferLen = "00" + szBufferLen;\n                } else if (szBufferLen.length === 3) {\n                    szBufferLen = "0" + szBufferLen;\n                }\n                var aData = [0, 0, parseInt(szBufferLen.substring(0, 2), 16), parseInt(szBufferLen.substring(2, 4), 16)];\n                for(var iIndex = 0, iDataLength = aFileData.length; iIndex < iDataLength; iIndex++) {\n                    aData[iIndex + 4] = aFileData[iIndex]\n                }\n                var dataUint8 = new Uint8Array(aData);\n                postMessage({type: "outputData", buf: dataUint8.buffer, dType: 2}, [dataUint8.buffer]);\n            } else {\n                var iDataLen = data.len;\n                var pData = Module._malloc(iDataLen);\n\n                var aData = Module.HEAPU8.subarray(pData, pData + iDataLen);\n                aData.set(new Uint8Array(data.buf));\n\n                var iRet = Module._ST_InputData(0, pData, iDataLen);\n                if (iRet != 0) {\n                    //console.log("_ST_InputData failed!");// 一开始会有一些失败，但是不影响后面的文件存储\n                }\n\n                Module._free(pData);\n            }\n        } else if ("release" === data.type) {\n            Module._ST_Stop();\n            Module._ST_Release();\n\n            close();\n        }\n    };\n\n    function loadMemInitFile() {\n        var req = new XMLHttpRequest();\n        req.open(\'GET\', \''+t+"/js/transform/SystemTransform.js.mem');\n        req.responseType = 'arraybuffer';\n        req.send();\n\n        return req;\n    }\n})();"};t.default=i},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:true});t.ESCanvas=undefined;var a=function(){function i(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||false;i.configurable=true;if("value"in i)i.writable=true;Object.defineProperty(e,i.key,i)}}return function(e,t,n){if(t)i(e.prototype,t);if(n)i(e,n);return e}}();var i=n(1);var s=r(i);function r(e){return e&&e.__esModule?e:{default:e}}function u(e,t){if(!e){throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return t&&(typeof t==="object"||typeof t==="function")?t:e}function T(e,t){if(typeof t!=="function"&&t!==null){throw new TypeError("Super expression must either be null or a function, not "+typeof t)}e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:false,writable:true,configurable:true}});if(t)Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t}function M(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var o=function(){var g=null;var f=Symbol("CANVAS");var S=Symbol("CONTEXT");var h=Symbol("SHAPES");var c=Symbol("DRAWSTATUS");var d=Symbol("SHAPETYPE");var p=Symbol("MAXSHAPENUMSUPPORT");var r=Symbol("SHAPESTYLE");var v=Symbol("POLYGONDRAWING");var m=Symbol("CURRENTSHAPEINFO");var y=Symbol("DRAWSHAPEMULTIONETIME");var i=Symbol("EVENTCALLBACK");function b(){g[S].clearRect(0,0,g.m_iCanvasWidth,g.m_iCanvasHeight);for(var e=0,t=g[h].length;e<t;e++){g[h][e].draw()}}function P(e){var t=g[h].length;if(t<g[p]){g[h].push(e)}}function n(){var r=false;var a=0;var o=0;var s="draw";var l=null;function u(){var e=-1;for(var t=0,n=g[h].length;t<n;t++){if(g[h][t].m_bChoosed){e=t;break}}return e}g[f][0].oncontextmenu=function(){return false};g[f][0].onselectstart=function(){return false};g[f].unbind();g[f].bind("mousedown",function(e){if(e.button===2){if(g[v]&&l){if(l.m_aPoint.length>=l.m_iMinClosed){l.m_bClosed=true;g[v]=false;l.setPointInfo(l.m_aPoint);P(l);b();r=false;if(!g[y]){g[c]=false}window.onDrawShapeEvent&&window.onDrawShapeEvent(l.m_szType,"onDrawEnd",l.m_szId)}}}else if(e.button===0){a=e.offsetX;o=e.offsetY;s="draw";if(!g[v]){var t=u();if(t!==-1){if(g[h][t].inArc(e.offsetX,e.offsetY,5)){s="stretch"}}if(s!=="stretch"){for(var n=0,i=g[h].length;n<i;n++){if(g[h][n].inShape(e.offsetX,e.offsetY)&&g[h][n].m_iEditType!==2){g[h][n].m_bChoosed=true;g[h][n].getMouseDownPoints(e.offsetX,e.offsetY);s="drag";window.onDrawShapeEvent&&window.onDrawShapeEvent(g[h][n].m_szType,"onChoose",g[h][n].m_szId)}else{g[h][n].m_bChoosed=false}}}if(s==="drag"){g[f][0].style.cursor="move"}else{g[f][0].style.cursor="default"}if("draw"===s&&1===g[h].length&&1===g[h][0].m_iRedrawMode){g.deleteRepeatPolyonById(g[h][0].m_szId);g[c]=true}if(g[c]&&!g[y]){s="draw"}}if(s==="draw"){if(g[c]){if(g[p]<=g[h].length&&g[d]!=="Grid"&&g[d]!=="Point"){return}if(g[d]==="Rect"){l=new w;l.m_szTips=g[m].szTips||""}else if(g[d]==="Grid"){if(g[h].length===0){l=new _;P(l)}}else if(g[d]==="Polygon"){if(!g[v]){g[v]=true;l=new C;l.m_szId=g[m].szId||"";l.m_szTips=g[m].szTips||"";l.m_iMinClosed=g[m].iMinClosed||3;l.m_iMaxPointNum=g[m].iMaxPointNum||11;l.m_iPolygonType=g[m].iPolygonType;l.m_szDrawColor=g[m].szDrawColor;l.m_szFillColor=g[m].szFillColor;l.m_iTranslucent=g[m].iTranslucent;l.m_iRedrawMode=g[m].iRedrawMode}if(l.m_iPolygonType===1){l.addPoint(a,o);if(l.m_aPoint.length===l.m_iMaxPointNum){l.m_bClosed=true;g[v]=false;P(l);b();r=false;if(!g[y]){g[c]=false}window.onDrawShapeEvent&&window.onDrawShapeEvent(l.m_szType,"onDrawEnd",l.m_szId)}}}else if(g[d]==="Point"){g.clearShapeByType("Point");l=new D;l.m_szId=g[m].szId||"";l.m_szDrawColor=g[m].szDrawColor;l.setPointInfo([[a,o]]);P(l);b()}}}r=true}});g[f].bind("mousemove",function(e){if(!g[v]){var t=u();if(t>-1){if(r){if(s==="drag"){g[h][t].drag(e.offsetX,e.offsetY);window.onDrawShapeEvent&&window.onDrawShapeEvent(g[h][t].m_szType,"onDrag",g[h][t].m_szId)}else if(s==="stretch"){g[h][t].stretch(e.offsetX,e.offsetY);window.onDrawShapeEvent&&window.onDrawShapeEvent(g[h][t].m_szType,"onStretch",g[h][t].m_szId)}}}else{if(g[c]){if(r){if(g[d]==="Rect"){l.move([[a,o],[e.offsetX,e.offsetY]])}else if(g[d]==="Grid"){g[h][0].move(a,o,e.offsetX,e.offsetY)}}}}}else{if(g[c]){if(r){if(g[d]==="Polygon"&&l.m_iPolygonType===0){l.m_bClosed=true}b();l.move(e.offsetX,e.offsetY,a,o)}}}});g[f].bind("mouseup",function(e){g[f][0].style.cursor="default";if(l!==null&&typeof l!=="undefined"&&s==="draw"){if(g[d]==="Rect"){if(Math.abs(e.offsetX-a)>2&&Math.abs(e.offsetY-o)>2){P(l);if(!g[y]){g[c]=false}}if(g[i]){var t={startPos:[],endPos:[]};if(e.offsetX>a&&e.offsetY>o){t.startPos=l.m_aPoint[0]||[e.offsetX,e.offsetY];t.endPos=l.m_aPoint[2]||[e.offsetX,e.offsetY]}else{t.startPos=l.m_aPoint[2]||[e.offsetX,e.offsetY];t.endPos=l.m_aPoint[0]||[e.offsetX,e.offsetY]}g[i]&&g[i](t);g.clearAllShape()}l=null}else if(g[d]==="Polygon"&&l.m_iPolygonType===0&&g[v]){if(Math.abs(e.offsetX-a)>2&&Math.abs(e.offsetY-o)>2){P(l);g[v]=false;if(!g[y]){g[c]=false}window.onDrawShapeEvent&&window.onDrawShapeEvent(l.m_szType,"onDrawEnd",l.m_szId)}}}if(!g[v]){r=false}else{r=true}if(!g[v]){b()}});g[f].bind("dblclick",function(){if(g[c]){if(g[d]==="Grid"){g[h][0].m_szGridMap="fffffcfffffcfffffcfffffcfffffcfffffcfffffcfffffcfffffcfffffcfffffcfffffcfffffcfffffcfffffcfffffcfffffcfffffc";b()}}});g[f].bind("mouseout",function(){g[f][0].style.cursor="default";if(!g[v]){r=false}else{r=true}})}var e=function(){function e(){M(this,e);this.m_szId="";this.m_aPoint=[];this.m_bChoosed=false;this.m_szDrawColor=g[r].szDrawColor;this.m_szFillColor=g[r].szFillColor;this.m_iTranslucent=g[r].iTranslucent;this.m_iIndexChoosePoint=-1;this.m_iDriftStartX=0;this.m_iDriftStartY=0;this.m_oEdgePoints={top:{x:0,y:0},left:{x:0,y:0},right:{x:0,y:0},bottom:{x:0,y:0}};this.m_szTips="";this.m_iEditType=0;this.m_iMinClosed=3;this.m_iMaxPointNum=11;this.m_bClosed=false;this.m_iRedrawMode=0}a(e,[{key:"draw",value:function e(){}},{key:"drag",value:function e(t,n){if(this.m_iEditType!==2){var i=this.m_aPoint.length;var r=0;for(r=0;r<i;r++){if(this.m_aPoint[r][0]+t-this.m_iDriftStartX>g.m_iCanvasWidth||this.m_aPoint[r][1]+n-this.m_iDriftStartY>g.m_iCanvasHeight||this.m_aPoint[r][0]+t-this.m_iDriftStartX<0||this.m_aPoint[r][1]+n-this.m_iDriftStartY<0){this.m_iDriftStartX=t;this.m_iDriftStartY=n;return}}for(r=0;r<i;r++){this.m_aPoint[r][0]=this.m_aPoint[r][0]+t-this.m_iDriftStartX;this.m_aPoint[r][1]=this.m_aPoint[r][1]+n-this.m_iDriftStartY}this.m_iDriftStartX=t;this.m_iDriftStartY=n;this.setPointInfo(this.m_aPoint);b()}}},{key:"stretch",value:function e(t,n){if(this.m_iEditType===0){if(this.m_iIndexChoosePoint!==-1){this.m_aPoint[this.m_iIndexChoosePoint][0]=t;this.m_aPoint[this.m_iIndexChoosePoint][1]=n}this.setPointInfo(this.m_aPoint);b()}}},{key:"inShape",value:function e(t,n){var i=false;var r=this.m_aPoint.length;for(var a=0,o=r-1;a<r;o=a++){if(this.m_aPoint[a][1]>n!==this.m_aPoint[o][1]>n&&t<(this.m_aPoint[o][0]-this.m_aPoint[a][0])*(n-this.m_aPoint[a][1])/(this.m_aPoint[o][1]-this.m_aPoint[a][1])+this.m_aPoint[a][0]){i=!i}}return i}},{key:"inArc",value:function e(t,n,i){var r=false;for(var a=0,o=this.m_aPoint.length;a<o;a++){var s=Math.sqrt((t-this.m_aPoint[a][0])*(t-this.m_aPoint[a][0])+(n-this.m_aPoint[a][1])*(n-this.m_aPoint[a][1]));if(s<i){r=true;this.m_iIndexChoosePoint=a;break}}return r}},{key:"getMouseDownPoints",value:function e(t,n){this.m_iDriftStartX=t;this.m_iDriftStartY=n}},{key:"getPointInfo",value:function e(){return this.m_aPoint}},{key:"setPointInfo",value:function e(t){if(t!==null&&typeof t!=="undefined"&&t.length>0){this.m_aPoint=t;this.setEdgePoints(t)}}},{key:"addPoint",value:function e(t,n){if(this.m_aPoint.length<this.m_iMaxPointNum){this.m_aPoint.push([t,n])}if(this.m_aPoint.length===this.m_iMaxPointNum){this.setPointInfo(this.m_aPoint)}}},{key:"setEdgePoints",value:function e(t){for(var n=0,i=t.length;n<i;n++){if(n===0){this.m_oEdgePoints.top.x=t[n][0];this.m_oEdgePoints.top.y=t[n][1];this.m_oEdgePoints.left.x=t[n][0];this.m_oEdgePoints.left.y=t[n][1];this.m_oEdgePoints.right.x=t[n][0];this.m_oEdgePoints.right.y=t[n][1];this.m_oEdgePoints.bottom.x=t[n][0];this.m_oEdgePoints.bottom.y=t[n][1]}else{if(t[n][1]<this.m_oEdgePoints.top.y){this.m_oEdgePoints.top.x=t[n][0];this.m_oEdgePoints.top.y=t[n][1]}if(t[n][0]>this.m_oEdgePoints.right.x){this.m_oEdgePoints.right.x=t[n][0];this.m_oEdgePoints.right.y=t[n][1]}if(t[n][1]>this.m_oEdgePoints.bottom.y){this.m_oEdgePoints.bottom.x=t[n][0];this.m_oEdgePoints.bottom.y=t[n][1]}if(t[n][0]<this.m_oEdgePoints.left.x){this.m_oEdgePoints.left.x=t[n][0];this.m_oEdgePoints.left.y=t[n][1]}}}}}]);return e}();var w=function(e){T(t,e);function t(){M(this,t);var e=u(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));e.m_szType="Rect";return e}a(t,[{key:"setPointInfo",value:function e(t){if(t!==null&&typeof t!=="undefined"){var n=t[0][0];var i=t[0][1];var r=t[0][0];var a=t[0][1];for(var o=0,s=t.length;o<s;o++){if(n>t[o][0]){n=t[o][0]}if(i>t[o][1]){i=t[o][1]}if(r<t[o][0]){r=t[o][0]}if(a<t[o][1]){a=t[o][1]}}this.m_aPoint=[[n,i],[r,i],[r,a],[n,a]]}}},{key:"draw",value:function e(){g[S].fillStyle=this.m_szFillColor;g[S].strokeStyle=this.m_szDrawColor;var t=this.m_aPoint[0][0];var n=this.m_aPoint[0][1];var i=this.m_aPoint[2][0]-t;var r=this.m_aPoint[2][1]-n;g[S].globalAlpha=this.m_iTranslucent;g[S].fillRect(t,n,i,r);g[S].globalAlpha=1;g[S].fillText(this.m_szTips,(t+this.m_aPoint[2][0])/2,(n+this.m_aPoint[2][1])/2);if(this.m_bChoosed){var a=Math.round(i/2);var o=Math.round(r/2);if(this.m_iEditType===0){var s=[t,t+a,t+i,t,t+i,t,t+a,t+i];var l=[n,n,n,n+o,n+o,n+r,n+r,n+r];for(var u=0;u<8;u++){g[S].beginPath();g[S].arc(s[u],l[u],3,0,360,false);g[S].fillStyle=this.m_szDrawColor;g[S].closePath();g[S].fill()}}}g[S].strokeRect(t,n,i,r)}},{key:"stretch",value:function e(t,n){if(this.m_iEditType===0){if(this.m_iIndexChoosePoint===0){if(t<this.m_aPoint[2][0]&&n<this.m_aPoint[2][1]){this.m_aPoint[0][0]=t;this.m_aPoint[0][1]=n;this.m_aPoint[3][0]=t;this.m_aPoint[1][1]=n}}else if(this.m_iIndexChoosePoint===1){if(n<this.m_aPoint[2][1]){this.m_aPoint[0][1]=n;this.m_aPoint[1][1]=n}}else if(this.m_iIndexChoosePoint===2){if(t>this.m_aPoint[3][0]&&n<this.m_aPoint[3][1]){this.m_aPoint[1][0]=t;this.m_aPoint[1][1]=n;this.m_aPoint[2][0]=t;this.m_aPoint[0][1]=n}}else if(this.m_iIndexChoosePoint===3){if(t<this.m_aPoint[2][0]){this.m_aPoint[0][0]=t;this.m_aPoint[3][0]=t}}else if(this.m_iIndexChoosePoint===4){if(t>this.m_aPoint[0][0]){this.m_aPoint[1][0]=t;this.m_aPoint[2][0]=t}}else if(this.m_iIndexChoosePoint===5){if(t<this.m_aPoint[1][0]&&n>this.m_aPoint[1][1]){this.m_aPoint[3][0]=t;this.m_aPoint[3][1]=n;this.m_aPoint[0][0]=t;this.m_aPoint[2][1]=n}}else if(this.m_iIndexChoosePoint===6){if(n>this.m_aPoint[1][1]){this.m_aPoint[2][1]=n;this.m_aPoint[3][1]=n}}else if(this.m_iIndexChoosePoint===7){if(t>this.m_aPoint[0][0]&&n>this.m_aPoint[0][1]){this.m_aPoint[2][0]=t;this.m_aPoint[2][1]=n;this.m_aPoint[1][0]=t;this.m_aPoint[3][1]=n}}b()}}},{key:"move",value:function e(t){b();this.m_bChoosed=true;var n=t[0][0];var i=t[0][1];var r=t[1][0];var a=t[1][1];this.setPointInfo([[n,i],[r,i],[r,a],[n,a]]);this.draw()}},{key:"inArc",value:function e(t,n,i){var r=this.m_aPoint[0][0];var a=this.m_aPoint[0][1];var o=this.m_aPoint[2][0]-r;var s=this.m_aPoint[2][1]-a;var l=Math.round(o/2);var u=Math.round(s/2);var f=[r,r+l,r+o,r,r+o,r,r+l,r+o];var h=[a,a,a,a+u,a+u,a+s,a+s,a+s];for(var c=0;c<8;c++){var d=Math.sqrt((t-f[c])*(t-f[c])+(n-h[c])*(n-h[c]));if(d<i){this.m_iIndexChoosePoint=c;return true}}return false}}]);return t}(e);var l=function(e){T(i,e);function i(e,t){M(this,i);var n=u(this,(i.__proto__||Object.getPrototypeOf(i)).call(this));n.m_szType="RectOSD";n.m_szOSDType="overlay-date";n.m_szText=e||"";n.m_szEnabled=t||"";n.m_szDateStyle="";n.m_szClockType="";n.m_szDisplayWeek="";n.m_szId="";n.m_szAlignment="0";return n}a(i,[{key:"draw",value:function e(){if(this.m_szEnabled==="true"){var t=this.m_aPoint[0][0];var n=this.m_aPoint[0][1];var i=this.m_aPoint[2][0]-t;var r=this.m_aPoint[2][1]-n;g[S].beginPath();g[S].strokeStyle=this.m_szDrawColor;g[S].globalAlpha=1;g[S].rect(t,n,i,r);g[S].font="15px serif";g[S].strokeText(this.m_szText,t,n+15);g[S].stroke()}}},{key:"drag",value:function e(t,n){var i=this.m_aPoint.length;var r=0;if("0"===this.m_szAlignment){for(r=0;r<i;r++){if(this.m_aPoint[r][1]+n-this.m_iDriftStartY>g.m_iCanvasHeight||this.m_aPoint[r][0]+t-this.m_iDriftStartX<0||this.m_aPoint[r][1]+n-this.m_iDriftStartY<0){this.m_iDriftStartX=t;this.m_iDriftStartY=n;return}}for(r=0;r<i;r++){this.m_aPoint[r][0]=this.m_aPoint[r][0]+t-this.m_iDriftStartX;this.m_aPoint[r][1]=this.m_aPoint[r][1]+n-this.m_iDriftStartY}}else if("1"===this.m_szAlignment||"2"===this.m_szAlignment){for(r=0;r<i;r++){if(this.m_aPoint[r][1]+n-this.m_iDriftStartY>g.m_iCanvasHeight||this.m_aPoint[r][1]+n-this.m_iDriftStartY<0){this.m_iDriftStartX=t;this.m_iDriftStartY=n;return}}for(r=0;r<i;r++){this.m_aPoint[r][1]=this.m_aPoint[r][1]+n-this.m_iDriftStartY}}this.m_iDriftStartX=t;this.m_iDriftStartY=n;this.setEdgePoints(this.m_aPoint);b()}},{key:"stretch",value:function e(){}}]);return i}(e);var _=function(e){T(t,e);function t(){M(this,t);var e=u(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));e.m_szType="Grid";e.m_iGridColNum=22;e.m_iGridRowNum=18;e.m_szGridMap="";e.m_aAddGridMap=[];return e}a(t,[{key:"draw",value:function e(){var t=g.m_iCanvasWidth/this.m_iGridColNum;var n=g.m_iCanvasHeight/this.m_iGridRowNum;var i="";for(var r=0;r<this.m_iGridRowNum;r++){var a=this.m_szGridMap.substring(r*6,r*6+6);var o=parseInt("f"+a,16).toString(2).split("").slice(4);var s="";for(var l=0;l<this.m_iGridColNum;l++){var u="";if(o[l]==="1"){g[S].strokeStyle=this.m_szDrawColor;g[S].globalAlpha=1;g[S].strokeRect(t*l,n*r,t,n);u="1"}else{u="0"}if(this.m_aAddGridMap.length){if(this.m_aAddGridMap[r][l]===1){g[S].strokeStyle=this.m_szDrawColor;g[S].strokeRect(t*l,n*r,t,n);u="1"}}s+=u}i+=parseInt("1111"+s+"00",2).toString(16).substring(1)}this.m_szGridMap=i}},{key:"move",value:function e(t,n,i,r){var a=g.m_iCanvasWidth/this.m_iGridColNum;var o=g.m_iCanvasHeight/this.m_iGridRowNum;var s=Math.floor(t/a);var l=Math.floor(n/o);var u=Math.floor(Math.abs(i-t)/a);var f=Math.floor(Math.abs(r-n)/o);var h=1;var c=1;if(i-t>0){h=1}else{h=-1}if(r-n>0){c=1}else{c=-1}var d=[];for(var p=0;p<this.m_iGridRowNum;p++){d[p]=[];for(var v=0;v<this.m_iGridColNum;v++){if(h===1){if(c===1){if(p>=l&&p<=l+f&&v>=s&&v<=s+u){d[p][v]=1}else{d[p][v]=0}}else{if(p<=l&&p>=l-f&&v>=s&&v<=s+u){d[p][v]=1}else{d[p][v]=0}}}else{if(c===1){if(p>=l&&p<=l+f&&v<=s&&v>=s-u){d[p][v]=1}else{d[p][v]=0}}else{if(p<=l&&p>=l-f&&v<=s&&v>=s-u){d[p][v]=1}else{d[p][v]=0}}}}}this.m_aAddGridMap=d;this.draw()}}]);return t}(e);var o=function(e){T(t,e);function t(){M(this,t);var e=u(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));e.m_szType="Line";e.m_iLineType=0;e.m_iDirection=0;e.m_iArrowType=0;e.m_aCrossArrowPoint=[];return e}a(t,[{key:"draw",value:function e(){if(this.m_iLineType===0){this.drawNormalLine()}else if(this.m_iLineType===1){this.drawArrowLine()}else if(this.m_iLineType===3){this.drawCrossLine()}else if(this.m_iLineType===4){this.drawLineCount()}}},{key:"drawNormalLine",value:function e(){g[S].globalAlpha=1;if(this.m_aPoint.length>0){g[S].beginPath();g[S].strokeStyle=this.m_szDrawColor;g[S].lineWidth=2;g[S].moveTo(this.m_aPoint[0][0],this.m_aPoint[0][1]);for(var t=1,n=this.m_aPoint.length;t<n;t++){g[S].lineTo(this.m_aPoint[t][0],this.m_aPoint[t][1])}g[S].stroke();if(this.m_bChoosed){for(var i=0,r=this.m_aPoint.length;i<r;i++){g[S].beginPath();g[S].fillStyle=this.m_szDrawColor;g[S].arc(this.m_aPoint[i][0],this.m_aPoint[i][1],3,0,Math.PI*2,true);g[S].closePath();g[S].fill()}}if(this.m_szTips!==""){g[S].strokeStyle=this.m_szDrawColor;g[S].fillText(this.m_szTips,this.m_aPoint[0][0]+10,this.m_aPoint[0][1]+4)}}}},{key:"drawArrowLine",value:function e(t,n,i,r,a,o,s,l){o=typeof o!=="undefined"?o:30;s=typeof s!=="undefined"?s:10;l=typeof l!=="undefined"?l:1;var u=Math.atan2(i-a,n-r)*180/Math.PI;var f=(u+o)*Math.PI/180;var h=(u-o)*Math.PI/180;var c=s*Math.cos(f);var d=s*Math.sin(f);var p=s*Math.cos(h);var v=s*Math.sin(h);g[S].save();g[S].beginPath();var m=n-c;var y=i-d;g[S].moveTo(m,y);g[S].lineTo(n,i);m=n-p;y=i-v;g[S].lineTo(m,y);g[S].moveTo(n,i);g[S].lineTo(r,a);if(t===1){m=r+c;y=a+d;g[S].moveTo(m,y);g[S].lineTo(r,a);m=r+p;y=a+v;g[S].lineTo(m,y)}g[S].strokeStyle=this.m_szDrawColor;g[S].lineWidth=l;g[S].stroke();g[S].restore()}},{key:"drawCrossLine",value:function e(){this.drawNormalLine();var t=(this.m_aPoint[0][0]+this.m_aPoint[1][0])/2;var n=(this.m_aPoint[0][1]+this.m_aPoint[1][1])/2;var i=Math.atan2(n-this.m_aPoint[0][1],t-this.m_aPoint[0][0])*180/Math.PI;var r=(i+90)*Math.PI/180;var a=(i-90)*Math.PI/180;var o=25*Math.cos(r);var s=25*Math.sin(r);var l=25*Math.cos(a);var u=25*Math.sin(a);var f=0;var h=0;f=t-o;h=n-s;var c=0;var d=0;if(this.m_iDirection===0){c=-10;d=-15}else if(this.m_iDirection===1){c=10;d=10}else{c=10;d=-15}if(this.m_iDirection!==0){this.drawArrowLine(0,f,h,t,n)}g[S].strokeStyle=this.m_szDrawColor;g[S].font="8px";g[S].strokeText("A",f+c,h+4);f=t-l;h=n-u;if(this.m_iDirection!==1){this.drawArrowLine(0,f,h,t,n)}g[S].strokeStyle=this.m_szDrawColor;g[S].font="8px";g[S].strokeText("B",f+d,h+4)}},{key:"drawLineCount",value:function e(){this.drawNormalLine();var t=(this.m_aPoint[0][0]+this.m_aPoint[1][0])/2;var n=(this.m_aPoint[0][1]+this.m_aPoint[1][1])/2;var i=Math.atan2(n-this.m_aPoint[0][1],t-this.m_aPoint[0][0])*180/Math.PI;var r=(i+90)*Math.PI/180;var a=(i-90)*Math.PI/180;var o=25*Math.cos(r);var s=25*Math.sin(r);var l=25*Math.cos(a);var u=25*Math.sin(a);var f=0;var h=0;f=t-o;h=n-s;if(this.m_iArrowType===1){f=t-l;h=n-u;this.drawArrowLine(0,f,h,t,n)}else if(this.m_iArrowType===0){this.drawArrowLine(0,f,h,t,n)}this.m_aCrossArrowPoint=[[t,n],[f,h]]}},{key:"inShape",value:function e(t,n){var i=false;for(var r=0,a=this.m_aPoint.length-1;r<a;r++){var o=Math.sqrt((this.m_aPoint[r+1][0]-this.m_aPoint[r][0])*(this.m_aPoint[r+1][0]-this.m_aPoint[r][0])+(this.m_aPoint[r+1][1]-this.m_aPoint[r][1])*(this.m_aPoint[r+1][1]-this.m_aPoint[r][1]));var s=Math.sqrt((t-this.m_aPoint[r][0])*(t-this.m_aPoint[r][0])+(n-this.m_aPoint[r][1])*(n-this.m_aPoint[r][1]));var l=Math.sqrt((t-this.m_aPoint[r+1][0])*(t-this.m_aPoint[r+1][0])+(n-this.m_aPoint[r+1][1])*(n-this.m_aPoint[r+1][1]));if(s+l-o<1){i=true}}return i}}]);return t}(e);var C=function(e){T(t,e);function t(){M(this,t);var e=u(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));e.m_szType="Polygon";e.m_iPolygonType=1;return e}a(t,[{key:"setPointInfo",value:function e(t){if(t!==null&&typeof t!=="undefined"){if(this.m_iPolygonType===0){var n=t[0][0];var i=t[0][1];var r=t[0][0];var a=t[0][1];for(var o=0,s=t.length;o<s;o++){if(n>t[o][0]){n=t[o][0]}if(i>t[o][1]){i=t[o][1]}if(r<t[o][0]){r=t[o][0]}if(a<t[o][1]){a=t[o][1]}}this.m_aPoint=[[n,i],[r,i],[r,a],[n,a]]}else if(this.m_iPolygonType===1){this.m_aPoint=t}else{this.m_aPoint=t}this.setEdgePoints(t)}}},{key:"draw",value:function e(){if(this.m_aPoint.length>0){g[S].fillStyle=this.m_szFillColor;g[S].strokeStyle=this.m_szDrawColor;g[S].globalAlpha=1;var t=0;var n=0;if(this.m_bChoosed){for(t=0,n=this.m_aPoint.length;t<n;t++){g[S].beginPath();g[S].arc(this.m_aPoint[t][0],this.m_aPoint[t][1],3,0,360,false);g[S].fillStyle=this.m_szDrawColor;g[S].closePath();g[S].fill()}}g[S].beginPath();g[S].moveTo(this.m_aPoint[0][0],this.m_aPoint[0][1]);for(t=0,n=this.m_aPoint.length;t<n;t++){if(t!==0){g[S].lineTo(this.m_aPoint[t][0],this.m_aPoint[t][1])}}g[S].stroke();if(this.m_bClosed){g[S].fillText(this.m_szTips,(this.m_oEdgePoints.left.x+this.m_oEdgePoints.right.x)/2,(this.m_oEdgePoints.top.y+this.m_oEdgePoints.bottom.y)/2);g[S].closePath();g[S].stroke();g[S].globalAlpha=this.m_iTranslucent;g[S].fill()}}}},{key:"move",value:function e(t,n,i,r){if(this.m_iPolygonType===1){if(this.m_aPoint.length<this.m_iMaxPointNum&&this.m_aPoint.length>0){g[S].fillStyle=this.m_szFillColor;g[S].strokeStyle=this.m_szDrawColor;g[S].globalAlpha=1;var a=0;var o=0;for(a=0,o=this.m_aPoint.length;a<o;a++){g[S].beginPath();g[S].arc(this.m_aPoint[a][0],this.m_aPoint[a][1],3,0,360,false);g[S].fillStyle=this.m_szDrawColor;g[S].closePath();g[S].fill()}g[S].beginPath();g[S].moveTo(this.m_aPoint[0][0],this.m_aPoint[0][1]);for(a=0,o=this.m_aPoint.length;a<o;a++){if(a!==0){g[S].lineTo(this.m_aPoint[a][0],this.m_aPoint[a][1])}}g[S].lineTo(t,n);g[S].closePath();g[S].stroke()}}else if(this.m_iPolygonType===0){this.m_bChoosed=true;var s=i;var l=r;var u=t;var f=n;this.setPointInfo([[s,l],[u,l],[u,f],[s,f]]);this.draw()}}},{key:"stretch",value:function e(t,n){if(this.m_iEditType===0){if(this.m_iPolygonType===1){if(this.m_iIndexChoosePoint!==-1){this.m_aPoint[this.m_iIndexChoosePoint][0]=t;this.m_aPoint[this.m_iIndexChoosePoint][1]=n}}else{if(this.m_iIndexChoosePoint===0){if(t<this.m_aPoint[2][0]&&n<this.m_aPoint[2][1]){this.m_aPoint[0][0]=t;this.m_aPoint[0][1]=n;this.m_aPoint[3][0]=t;this.m_aPoint[1][1]=n}}else if(this.m_iIndexChoosePoint===1){if(t>this.m_aPoint[3][0]&&n<this.m_aPoint[3][1]){this.m_aPoint[1][0]=t;this.m_aPoint[1][1]=n;this.m_aPoint[2][0]=t;this.m_aPoint[0][1]=n}}else if(this.m_iIndexChoosePoint===2){if(t>this.m_aPoint[0][0]&&n>this.m_aPoint[0][1]){this.m_aPoint[2][0]=t;this.m_aPoint[2][1]=n;this.m_aPoint[1][0]=t;this.m_aPoint[3][1]=n}}else if(this.m_iIndexChoosePoint===3){if(t<this.m_aPoint[1][0]&&n>this.m_aPoint[1][1]){this.m_aPoint[3][0]=t;this.m_aPoint[3][1]=n;this.m_aPoint[0][0]=t;this.m_aPoint[2][1]=n}}}this.setPointInfo(this.m_aPoint);b()}}}]);return t}(e);var D=function(e){T(t,e);function t(){M(this,t);var e=u(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));e.m_szType="Point";e.m_szId="";return e}a(t,[{key:"draw",value:function e(){g[S].beginPath();g[S].fillStyle=this.m_szDrawColor;g[S].globalAlpha=1;g[S].arc(this.m_aPoint[0][0],this.m_aPoint[0][1],10,0,Math.PI*2,true);g[S].closePath();g[S].fill()}},{key:"drag",value:function e(){}},{key:"stretch",value:function e(){}}]);return t}(e);var t=function(){function t(e){M(this,t);g=this;this[f]=(0,s.default)("#"+e);this[S]=this[f][0].getContext("2d");this[h]=[];this[c]=false;this[d]="Rect";this[p]=10;this[y]=true;this[m]={};this[i]=null;this[r]={szDrawColor:"#ff0000",szFillColor:"#343434",iTranslucent:.7};this[v]=false;this.m_iCanvasWidth=this[f].width();this.m_iCanvasHeight=this[f].height();this.m_iHorizontalResolution=0;this.m_iVerticalResolution=0;this.m_szDisplayMode="";this.m_szVideoFormat="";n();this[h].length=0}a(t,[{key:"setDrawMutiShapeOneTime",value:function e(t){this[y]=t}},{key:"setMaxShapeSupport",value:function e(t){this[p]=t}},{key:"getMaxShapeSupport",value:function e(){return this[p]}},{key:"setDrawStatus",value:function e(t,n){this[c]=t;if(n&&t){this[i]=n}if(!t){this[i]=null}}},{key:"setShapeType",value:function e(t){this[d]=t;b()}},{key:"setCurrentShapeInfo",value:function e(t){this[m]=t||{szId:"",szTips:"",iMinClosed:3,iMaxPointNum:11,iPolygonType:1,iRedrawMode:0}}},{key:"getShapeType",value:function e(){return this[d]}},{key:"getAllShapesInfo",value:function e(){var t=[];for(var n=0,i=this[h].length;n<i;n++){if(this[h][n].m_szType==="Grid"){t.push({szType:this[h][n].m_szType,szGridMap:this[h][n].m_szGridMap,iGridColNum:this[h][n].m_iGridColNum,iGridRowNum:this[h][n].m_iGridRowNum})}else if(this[h][n].m_szType==="RectOSD"){t.push({szType:this[h][n].m_szType,szText:this[h][n].m_szText,szEnabled:this[h][n].m_szEnabled,szOSDType:this[h][n].m_szOSDType,iPositionX:this[h][n].m_aPoint[0][0],iPositionY:this[h][n].m_aPoint[0][1],szDateStyle:this[h][n].m_szDateStyle,szClockType:this[h][n].m_szClockType,szDisplayWeek:this[h][n].m_szDisplayWeek,szId:this[h][n].m_szId,szAlignment:this[h][n].m_szAlignment})}else{t.push({szType:this[h][n].m_szType,aPoint:this[h][n].m_aPoint,szId:this[h][n].m_szId,bChoosed:this[h][n].m_bChoosed})}}return t}},{key:"deleteRepeatPolyonById",value:function e(t){var n=this.getAllShapesInfo();var i=n.length;if(i>0){for(var r=0;r<i;r++){if(n[r].szType==="Polygon"){if(n[r].szId===t){this.deleteShape(r)}}}}}},{key:"getShapesInfoByType",value:function e(t){var n=[];for(var i=0,r=this[h].length;i<r;i++){if(this[h][i].m_szType===t){if(this[h][i].m_szType==="Grid"){n.push({szType:this[h][i].m_szType,szGridMap:this[h][i].m_szGridMap,iGridColNum:this[h][i].m_iGridColNum,iGridRowNum:this[h][i].m_iGridRowNum})}else if(this[h][i].m_szType==="RectOSD"){n.push({szType:this[h][i].m_szType,szText:this[h][i].m_szText,szEnabled:this[h][i].m_szEnabled,szOSDType:this[h][i].m_szOSDType,iPositionX:this[h][i].m_aPoint[0][0],iPositionY:this[h][i].m_aPoint[0][1],szDateStyle:this[h][i].m_szDateStyle,szClockType:this[h][i].m_szClockType,szDisplayWeek:this[h][i].m_szDisplayWeek,szId:this[h][i].m_szId,szAlignment:this[h][i].m_szAlignment})}else if(t==="Polygon"){n.push({szType:this[h][i].m_szType,szId:this[h][i].m_szId,iPolygonType:this[h][i].m_iPolygonType,iMinClosed:this[h][i].m_iMinClosed,iMaxPointNum:this[h][i].m_iMaxPointNum,iEditType:this[h][i].m_iEditType,aPoint:this[h][i].m_aPoint,bClosed:this[h][i].m_bClosed,szTips:this[h][i].m_szTips,szDrawColor:this[h][i].m_szDrawColor,szFillColor:this[h][i].m_szFillColor,iTranslucent:this[h][i].m_iTranslucent})}else if(t==="Line"){n.push({szType:this[h][i].m_szType,szId:this[h][i].m_szId,aPoint:this[h][i].m_aPoint,szTips:this[h][i].m_szTips,iLineType:this[h][i].m_iLineType,iDirection:this[h][i].m_iDirection,iArrowType:this[h][i].m_iArrowType,szDrawColor:this[h][i].m_szDrawColor,aCrossArrowPoint:this[h][i].m_aCrossArrowPoint})}else if(t==="Rect"){n.push({szType:this[h][i].m_szType,iEditType:this[h][i].m_iEditType,aPoint:this[h][i].m_aPoint,szTips:this[h][i].m_szTips,szDrawColor:this[h][i].m_szDrawColor,szFillColor:this[h][i].m_szFillColor,iTranslucent:this[h][i].m_iTranslucent})}else{n.push({szType:this[h][i].m_szType,aPoint:this[h][i].m_aPoint})}}}return n}},{key:"setShapesInfoByType",value:function e(t,n){if(!n){n=[]}var i=null;if(t==="Rect"||t==="Polygon"||t==="Line"||t==="Point"){for(var r=0,a=n.length;r<a;r++){if(t==="Rect"){i=new w;i.m_iEditType=n[r].iEditType;i.m_szTips=n[r].szTips||"";i.m_szDrawColor=n[r].szDrawColor;i.m_szFillColor=n[r].szFillColor;i.m_iTranslucent=n[r].iTranslucent;i.m_iRedrawMode=n[r].iRedrawMode}else if(t==="Polygon"){i=new C;if(n[r].iPolygonType===0){i.m_bClosed=true}else{i.m_bClosed=n[r].bClosed}i.m_szTips=n[r].szTips||"";i.m_szId=n[r].szId||"";i.m_iPolygonType=n[r].iPolygonType;i.m_iMinClosed=n[r].iMinClosed||3;i.m_iMaxPointNum=n[r].iMaxPointNum||11;i.m_iEditType=n[r].iEditType;i.m_szDrawColor=n[r].szDrawColor;i.m_szFillColor=n[r].szFillColor;i.m_iTranslucent=n[r].iTranslucent;i.m_iRedrawMode=n[r].iRedrawMode}else if(t==="Line"){i=new o;i.m_iLineType=n[r].iLineType;i.m_szTips=n[r].szTips||"";i.m_szId=n[r].szId;i.m_iDirection=n[r].iDirection;i.m_iArrowType=n[r].iArrowType;i.m_szDrawColor=n[r].szDrawColor;i.setPointInfo(n[r].aPoint)}else if(t==="Point"){i=new D;i.m_szId=n[r].szId;i.m_szDrawColor=n[r].szDrawColor;i.setPointInfo(n[r].aPoint)}i.setPointInfo(n[r].aPoint);if(r===0){i.m_bChoosed=true}P(i)}}else if(t==="Grid"){i=new _;i.m_szGridMap=n[0].szGridMap||"";i.m_iGridColNum=n[0].iGridColNum||22;i.m_iGridRowNum=n[0].iGridRowNum||18;P(i)}b()}},{key:"addOSDShape",value:function e(t,n,i,r,a){if(!i&&!r){i=0;r=0}if(!a){a={}}var o=new l(t,n);var s=t.replace(/[^x00-xff]/g,"rr").length*10;o.m_szOSDType=a.szOSDType||"";o.m_szDateStyle=a.szDateStyle||"";o.m_szClockType=a.szClockType||"";o.m_szDisplayWeek=a.szDisplayWeek||"";o.m_szId=a.szId||"";o.m_szAlignment=""+a.szAlignment||"0";if("0"===o.m_szAlignment){o.m_aPoint=[[i,r],[s+i,r],[s+i,r+20],[i,r+20]]}else if("1"===o.m_szAlignment){o.m_aPoint=[[0,r],[s,r],[s,r+20],[0,r+20]]}else if("2"===o.m_szAlignment){o.m_aPoint=[[this.m_iCanvasWidth-s,r],[this.m_iCanvasWidth,r],[this.m_iCanvasWidth,r+20],[this.m_iCanvasWidth-s,r+20]]}else{o.m_aPoint=[[i,r],[s+i,r],[s+i,r+20],[i,r+20]]}P(o);b()}},{key:"selectShapeById",value:function e(t,n){for(var i=0,r=g[h].length;i<r;i++){if(t===g[h][i].m_szType){if(n===g[h][i].m_szId){g[h][i].m_bChoosed=true}else{g[h][i].m_bChoosed=false}}}b()}},{key:"setCanvasSize",value:function e(t,n){if(t>0&&n>0){this.m_iCanvasWidth=t;this.m_iCanvasHeight=n;b()}}},{key:"setDrawStyle",value:function e(t,n,i){this[r]={szDrawColor:t,szFillColor:n,iTranslucent:i}}},{key:"clearAllShape",value:function e(){this[h].length=0;g[v]=false;b()}},{key:"clearShapeByType",value:function e(t){var n=this[h].length;for(var i=n;i>0;i--){if(this[h][i-1].m_szType===t){if(t==="Grid"){this[h][i-1].m_szGridMap="";this[h][i-1].m_aAddGridMap=[]}else{this[h].splice(i-1,1)}}}if("Polygon"===t){g[v]=false}b()}},{key:"deleteShape",value:function e(t){if(this[h].length>t){this[h].splice(t,1)}b()}},{key:"updateCanvas",value:function e(t){this[f]=(0,s.default)("#"+t);this[S]=this[f][0].getContext("2d");this.m_iCanvasWidth=this[f].width();this.m_iCanvasHeight=this[f].height();n()}},{key:"resizeCanvas",value:function e(){this.m_iCanvasWidth=this[f].width();this.m_iCanvasHeight=this[f].height()}},{key:"canvasRedraw",value:function e(){b()}}]);return t}();return t}();t.ESCanvas=o}])});