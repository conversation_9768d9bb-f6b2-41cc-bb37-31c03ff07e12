/* eslint-disable max-classes-per-file */
import Vue from 'vue';
import { VanComponent } from './component';
import { AddressEdit } from './address-edit';
import { Area } from './area';
import { Calendar } from './calendar';
import { Checkbox } from './checkbox';
import { CheckboxGroup } from './checkbox-group';
import { CollapseItem } from './collapse-item';
import { CountDown } from './count-down';
import { DatetimePicker } from './datetime-picker';
import { Dialog } from './dialog';
import { DropdownItem } from './dropdown-item';
import { Field } from './field';
import { Form } from './form';
import { ImagePreview } from './image-preview';
import { IndexBar } from './index-bar';
import { Lazyload } from './lazyload';
import { List } from './list';
import { Locale } from './locale';
import { Notify } from './notify';
import { Picker } from './picker';
import { Progress } from './progress';
import { Sku } from './sku';
import { Swipe } from './swipe';
import { SwipeCell } from './swipe-cell';
import { Tabs } from './tabs';
import { Toast } from './toast';
import { Uploader } from './uploader';

export const version: string;
export function install(vue: typeof Vue): void;

export class ActionSheet extends VanComponent {}
export class AddressList extends VanComponent {}
export class Badge extends VanComponent {}
export class Button extends VanComponent {}
export class Card extends VanComponent {}
export class Cascader extends VanComponent {}
export class Cell extends VanComponent {}
export class CellGroup extends VanComponent {}
export class Circle extends VanComponent {}
export class Col extends VanComponent {}
export class Collapse extends VanComponent {}
export class ContactCard extends VanComponent {}
export class ContactEdit extends VanComponent {}
export class ContactList extends VanComponent {}
export class CouponCell extends VanComponent {}
export class CouponList extends VanComponent {}
export class Divider extends VanComponent {}
export class DropdownMenu extends VanComponent {}
export class Empty extends VanComponent {}
export class Grid extends VanComponent {}
export class GridItem extends VanComponent {}
export class GoodsAction extends VanComponent {}
export class GoodsActionButton extends VanComponent {}
export class GoodsActionIcon extends VanComponent {}
export class Icon extends VanComponent {}
export class Image extends VanComponent {}
export class IndexAnchor extends VanComponent {}
export class Info extends VanComponent {}
export class Loading extends VanComponent {}
export class NavBar extends VanComponent {}
export class NoticeBar extends VanComponent {}
export class NumberKeyboard extends VanComponent {}
export class Overlay extends VanComponent {}
export class Pagination extends VanComponent {}
export class Panel extends VanComponent {}
export class PasswordInput extends VanComponent {}
export class Popover extends VanComponent {}
export class Popup extends VanComponent {}
export class PullRefresh extends VanComponent {}
export class Radio extends VanComponent {}
export class RadioGroup extends VanComponent {}
export class Rate extends VanComponent {}
export class Row extends VanComponent {}
export class Search extends VanComponent {}
export class ShareSheet extends VanComponent {}
export class Sidebar extends VanComponent {}
export class SidebarItem extends VanComponent {}
export class Skeleton extends VanComponent {}
export class Slider extends VanComponent {}
export class Step extends VanComponent {}
export class Stepper extends VanComponent {}
export class Steps extends VanComponent {}
export class Sticky extends VanComponent {}
export class SubmitBar extends VanComponent {}
export class SwipeItem extends VanComponent {}
export class Switch extends VanComponent {}
export class SwitchCell extends VanComponent {}
export class Tab extends VanComponent {}
export class Tabbar extends VanComponent {}
export class TabbarItem extends VanComponent {}
export class Tag extends VanComponent {}
export class TreeSelect extends VanComponent {}

export {
  AddressEdit,
  Area,
  Calendar,
  Checkbox,
  CheckboxGroup,
  CollapseItem,
  CountDown,
  DatetimePicker,
  Dialog,
  DropdownItem,
  Form,
  Field,
  ImagePreview,
  IndexBar,
  Lazyload,
  List,
  Locale,
  Notify,
  Picker,
  Progress,
  Sku,
  Swipe,
  SwipeCell,
  Tabs,
  Toast,
  Uploader,
};
