{"name": "native-request", "version": "1.1.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://zaral.fr"}, "bugs": {"url": "https://github.com/ZaralDev/native-request/issues"}, "description": "A simple package with no dependencies for native requests using callback", "main": "index.js", "keywords": ["node-request", "native-request", "request", "node-fetch", "fetch", "request", "http", "GET", "get request", "http.get", "follow redirects", "https", "http-https", "simple request", "simple get"], "license": "MIT", "devDependencies": {"mocha": "^6.2.3", "body-parser": "^1.19.0", "cookie-parser": "^1.4.5", "express": "^4.17.1"}, "repository": {"type": "git", "url": "git://github.com/ZaralDev/native-request.git"}, "scripts": {"test": "node ./node_modules/mocha/bin/mocha"}}