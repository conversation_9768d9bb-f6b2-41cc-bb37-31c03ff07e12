"""demo1_server URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/2.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.conf import settings
from django.urls import path,include,re_path
from django.views.static import serve
# import xadmin


urlpatterns = [
    # path('admin/', xadmin.site.urls),
    path('user/', include('user.urls')),
    path('report/', include('report.urls')),
    path('learning/', include('learning.urls')),
    re_path(r'static/(?P<path>.*)', serve, {'document_root':settings.STATIC_ROOT}),
]
