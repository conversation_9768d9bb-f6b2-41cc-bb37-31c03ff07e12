/**
 * 桌面端路由配置
 */

// 桌面端页面组件
const Login = () => import('@/views/Login2.vue')
const Home = () => import('@/views/Home.vue')
const Detail = () => import('@/views/Detail2.vue')
const Cultiver = () => import('@/views/Cultiver2.vue')
const historyData = () => import('@/views/data/history.vue')
const reportData = () => import('@/views/data/report.vue')
const deviceManage = () => import('@/views/device/manage.vue')
const deviceCamera = () => import('@/views/device/camera.vue')
const userAdd = () => import('@/views/user/add.vue')
const tousiplan = () => import('@/views/data/tousi.vue')

const desktopRoutes = [
  {
    path: '/desktop',
    redirect: '/desktop/login'
  },
  {
    path: '/desktop/login',
    name: 'DesktopLogin',
    component: Login,
    meta: { 
      withMenu: false,
      title: '登录',
      platform: 'desktop'
    }
  },
  {
    path: '/desktop/home',
    name: 'DesktopHome',
    component: Home,
    meta: { 
      withMenu: false,
      title: '首页',
      platform: 'desktop'
    }
  },
  {
    path: '/desktop/detail',
    name: 'DesktopDetail',
    component: Detail,
    meta: { 
      withMenu: false,
      title: '详情',
      platform: 'desktop'
    }
  },
  {
    path: '/desktop/cultiver',
    name: 'DesktopCultiver',
    component: Cultiver,
    meta: { 
      withMenu: false,
      title: '养殖',
      platform: 'desktop'
    }
  },
  {
    path: '/desktop/data/history',
    name: 'DesktopHistoryData',
    component: historyData,
    meta: { 
      withMenu: true, 
      pathName: '数据中心 / 历史数据',
      title: '历史数据',
      platform: 'desktop'
    }
  },
  {
    path: '/desktop/data/report',
    name: 'DesktopReportData',
    component: reportData,
    meta: { 
      withMenu: true, 
      pathName: '数据中心 / 统计报表',
      title: '统计报表',
      platform: 'desktop'
    }
  },
  {
    path: '/desktop/data/tousi',
    name: 'DesktopTousiplan',
    component: tousiplan,
    meta: { 
      withMenu: true, 
      pathName: '数据中心 / 投饲计划',
      title: '投饲计划',
      platform: 'desktop'
    }
  },
  {
    path: '/desktop/device/manage',
    name: 'DesktopDeviceManage',
    component: deviceManage,
    meta: { 
      withMenu: true, 
      pathName: '设备中心 / 设备管理',
      title: '设备管理',
      platform: 'desktop'
    }
  },
  {
    path: '/desktop/device/camera',
    name: 'DesktopDeviceCamera',
    component: deviceCamera,
    meta: { 
      withMenu: true, 
      pathName: '设备中心 / 摄像头管理',
      title: '摄像头管理',
      platform: 'desktop'
    }
  },
  {
    path: '/desktop/user/add',
    name: 'DesktopUserAdd',
    component: userAdd,
    meta: { 
      withMenu: true, 
      pathName: '用户管理 / 新增用户',
      title: '新增用户',
      platform: 'desktop'
    }
  },
  {
    path: '/desktop/register',
    name: 'DesktopRegister',
    component: userAdd,
    meta: { 
      withMenu: false,
      title: '注册',
      platform: 'desktop'
    }
  }
]

export default desktopRoutes
