{"_args": [["@babel/helper-annotate-as-pure@7.27.1", "F:\\桌面\\上海海洋大屏2期\\phone"]], "_development": true, "_from": "@babel/helper-annotate-as-pure@7.27.1", "_id": "@babel/helper-annotate-as-pure@7.27.1", "_inBundle": false, "_integrity": "sha512-WnuuDILl9oOBbKnb4L+DyODx7iC47XfzmNCpTttFsSp6hTG7XZxu60+4IO+2/hPfcGOoKbFiwoI/+zwARbNQow==", "_location": "/@babel/helper-annotate-as-pure", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@babel/helper-annotate-as-pure@7.27.1", "name": "@babel/helper-annotate-as-pure", "escapedName": "@babel%2fhelper-annotate-as-pure", "scope": "@babel", "rawSpec": "7.27.1", "saveSpec": null, "fetchSpec": "7.27.1"}, "_requiredBy": ["/@babel/helper-create-class-features-plugin", "/@babel/helper-create-regexp-features-plugin", "/@babel/helper-remap-async-to-generator", "/@babel/plugin-transform-classes", "/@babel/plugin-transform-private-property-in-object"], "_resolved": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.27.1.tgz", "_spec": "7.27.1", "_where": "F:\\桌面\\上海海洋大屏2期\\phone", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "dependencies": {"@babel/types": "^7.27.1"}, "description": "Helper function to annotate paths and nodes with #__PURE__ comment", "devDependencies": {"@babel/traverse": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-helper-annotate-as-pure", "license": "MIT", "main": "./lib/index.js", "name": "@babel/helper-annotate-as-pure", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-helper-annotate-as-pure"}, "type": "commonjs", "version": "7.27.1"}