<script>
import { shebeilistv2, getcamera, cultiverlistv2, add_cultiver } from '@/request/api';
import { mapActions, mapState } from 'vuex';
import LogList from '@/components/layout/LogList.vue'
import SectionTitle from './SectionTitle.vue'

export default {
    components: {
        LogList,
        SectionTitle
    },

    data() {
        return {
            title: '设备列表',  // 直接设置默认值
            activeTab: 'devices', // 默认显示设备列表
            shebeilist: {
                water: [],
                meteorological: [],
                monitoring: []
            },
            // 日志相关数据
            loglist: [],
            showAddLogDialog: false,
            showDatePicker: false,      // 日期选择器显示状态
            showActionPicker: false,    // 动作选择器显示状态
            showFacilityPicker: false,  // 设施选择器显示状态
            addLoading: false,
            addForm: {
                time: '',
                name: '',
                action: '',
                num: '',
                remark: '',
                zhonglei: '',
                sheshiid: '',
                base_id: '',
            },
            actionOptions: [
                { name: '投饲料', value: '投饲料' },
                { name: '投药物', value: '投药物' },
                { name: '捕捞水产品', value: '捕捞水产品' },
                { name: '投放水产品', value: '投放水产品' }
            ],
            addFromName: {
                sheshiid: '设施号',
                time: '时间',
                name: '操作人姓名',
                action: '动作',
                zhonglei: '种类',
                num: '数量',
                remark: '备注'
            },
            errMsg: '',
            errMsgNum: '', // 数量输入错误提示
            isdisabled: true,
            dialogWidth: '90%', // 默认宽度为屏幕宽度的90%
            shelist: [], // 添加这行初始化 shelist
            // alertlist: [],
            rizhishuru: 0,
            // 告警列表相关数据
            alertYuanList: [],
            scrollOffset: 0,
            scrollInterval: null,
            contentHeight: 0,
            containerHeight: 0,
            isPaused: false,
            // 添加水质参数数据
            waterParams: {
                nh3: {
                    value: 0.25,
                    unit: 'mg/L'
                },
                no2: {
                    value: 0.15,
                    unit: 'mg/L'
                }
            }
        }
    },

    computed: {
        ...mapState({
            base_id: state => state.base_id
        }),
        showShebeilist() {
            const s = this.shebeilist;
            if (
                (s.water && s.water.length) ||
                (s.meteorological && s.meteorological.length) ||
                (s.monitoring && s.monitoring.length)
            ) {
                return s;
            }
            return [];
        },

        // 合并水质和气象设备列表，用于设施选择器
        facilitiesList() {
            return this.shelist.map(item => ({
                text: (item.BACILITIES_NAME || '未知'),
                value: item.ID || ''
            }));
        },
        needExtraItem() {
            return this.alertYuanList.length % 2 !== 0 && this.alertYuanList.length > 3;
        },
        shouldScroll() {
            return this.contentHeight > this.containerHeight;
        },
        scrollStyle() {
            return this.shouldScroll
                ? { transform: `translateY(${-this.scrollOffset}px)` }
                : {};
        }
    },

    watch: {
        activeTab(newVal) {
            // 根据活动标签更新页面标题
            this.title = newVal === 'devices' ? '设备列表' : '养殖日志';

            // 如果切换到日志标签，加载日志数据
            if (newVal === 'logs') {
                this.getLogList();
            }
        },
        alertYuanList: {
            handler() {
                this.resetScroll();
            },
            deep: true
        }
    },

    methods: {
        ...mapActions('device', ['updateDeviceInfo', 'updateBaseId']),
        ...mapActions(['updateBaseId']),

        onClickLeft() {
            this.$router.back();
        },

        addAlarm(message, value, type, time) {
            // 生成唯一ID
            const ID = Date.now() + Math.floor(Math.random() * 1000);

            this.alertYuanList.push({
                // ID,
                message,
                value,
                type,
                time
            });

            console.log(`已添加报警: ${message}, 值: ${value}, 类型: ${type}`);
        },

        getlist() {
            // 使用 Vuex 中的 base_id
            shebeilistv2({
                base_id: this.base_id,
            }).then(res => {
                console.log('数据接口:', res);

                // 清空原有数据
                this.shebeilist.water = [];
                this.shebeilist.meteorological = [];
                this.alertYuanList = []; // 清空报警数据
                res.data.alertlist = []; // 清空alertlist数据

                if (res && res.data && res.data.shebeilist && Array.isArray(res.data.shebeilist)) {
                    // 处理设备列表
                    res.data.shebeilist.forEach(item => {
                        try {
                            if (item.UANDL && typeof item.UANDL === 'string') {
                                item.UANDL = JSON.parse(item.UANDL);
                            }
                            if (item.EQUIPMENT_TYPE == '水质检测') {
                                this.shebeilist.water.push(item);
                            } else if (item.EQUIPMENT_TYPE == '气象检测') {
                                this.shebeilist.meteorological.push(item);
                            }
                        } catch (error) {
                            console.error('解析设备数据出错:', error, item);
                        }
                    });

                    // 处理告警信息 - 先检查API返回的现有告警
                    if (res.data.alertlist && Array.isArray(res.data.alertlist)) {
                        this.alertYuanList = res.data.alertlist;
                    }

                    // 如果没有告警数据，根据设备状态生成告警
                    if (this.alertYuanList.length === 0) {
                        // 从第1个设备开始检查（如果需要从第0个开始，改为i=0）
                        for (let i = 0; i < res.data.shebeilist.length; i++) {
                            const device = res.data.shebeilist[i];

                            // 检查设备电压
                            const Online_Voltage = device.Online_Voltage || 0;

                            // 检查设备类型是否为水质设备
                            if (device.EQUIPMENT_TYPE === '水质检测') {
                                const oxygenLevel = device.O2;
                                const temperature = device.TEM;
                                const pHValue = device.PH;
                                const UANDL = device.UANDL;
                                const time = device.ACQUISITION_TIME
                                    // || device.UPDATE_TIME || new Date().toISOString().replace('T', ' ').substring(0, 19)
                                    ;

                                let parsedUANDL = UANDL;
                                if (typeof UANDL === 'string') {
                                    try {
                                        parsedUANDL = JSON.parse(UANDL);
                                    } catch (error) {
                                        console.error('解析 UANDL 失败:', error);
                                        continue; // 跳过此设备
                                    }
                                } else if (typeof UANDL !== 'object' || UANDL === null) {
                                    console.error('UANDL 不是有效的对象或 JSON 字符串');
                                    // continue; // 跳过此设备
                                    return; // 或者进行其他错误处理
                                }

                                if (oxygenLevel < parsedUANDL.o2.th[0] || oxygenLevel > parsedUANDL.o2.th[1]) {
                                    const alarmMessage = `${device.EQUIPMENT_NAME}的溶氧值异常`;
                                    const value = oxygenLevel
                                    this.addAlarm(alarmMessage, value, 1, time);
                                }
                                if (temperature < parsedUANDL.te.th[0] || temperature > parsedUANDL.te.th[1]) {
                                    const alarmMessage = `${device.EQUIPMENT_NAME}的温度异常`;
                                    const value = temperature
                                    this.addAlarm(alarmMessage, value, 1, time);
                                }

                                if (pHValue < parsedUANDL.ph.th[0] || pHValue > parsedUANDL.ph.th[1]) {
                                    const alarmMessage = `${device.EQUIPMENT_NAME}的PH值异常`;
                                    const value = pHValue
                                    this.addAlarm(alarmMessage, value, 1, time);
                                }
                                if (Online_Voltage == 0 || Online_Voltage < 10) {
                                    const alarmMessage = `${device.EQUIPMENT_NAME}的电压值异常`;
                                    const value = Online_Voltage
                                    this.addAlarm(alarmMessage, value, 2, time);
                                }
                            }
                        }
                    }
                }

                this.shelist = res.data.sheshilist || [];

                // 打印告警信息
                console.log("报警信息列表:", JSON.stringify(this.alertYuanList));
                console.log("设施名称:", this.shelist);

                // 确保报警信息列表显示更新
                this.$nextTick(() => {
                    this.resetScroll();
                });

            }).catch(error => {
                console.error('获取设备列表失败:', error);

                // 添加测试数据（仅在API调用失败时）
                if (this.alertYuanList.length === 0) {
                    this.alertYuanList = [
                        { ID: 1, message: "测试设备1的溶氧值异常", value: "3.2", type: 1, time: this.getCurrentTime() },
                        { ID: 2, message: "测试设备2的电压值异常", value: "8.5", type: 2, time: this.getCurrentTime() }
                    ];
                    this.resetScroll();
                }
            });
        },

        getCurrentTime() {
            const now = new Date();
            const year = now.getFullYear();
            const month = String(now.getMonth() + 1).padStart(2, '0');
            const day = String(now.getDate()).padStart(2, '0');
            const hours = String(now.getHours()).padStart(2, '0');
            const minutes = String(now.getMinutes()).padStart(2, '0');
            const seconds = String(now.getSeconds()).padStart(2, '0');
            return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
        },

        GetCamera() {
            getcamera({
                base_id: this.base_id,
            }).then(res => {
                this.shebeilist.monitoring = [];  // 清空原有数据
                if (res && res.item_list && Array.isArray(res.item_list)) {
                    this.shebeilist.monitoring = res.item_list;
                }
            }).catch(error => {
                console.error('获取监控设备失败:', error);
            });
        },

        emitLinkShebei(type, item, index) {
            if (!item) {
                console.warn('没有设备数据，无法跳转');
                return;
            }

            try {
                // 更新 Vuex store
                this.updateDeviceInfo({
                    id: item.ID || item.id || '',
                    name: type === 'monitoring' ? (item.camera_name || '未命名摄像头') : (item.EQUIPMENT_NAME || '未命名设备'),
                    type: type,
                    itemData: item
                });

                // 简单的路由跳转，不带参数
                const path = type === 'monitoring' ? '/section-title' : '/device-detail';
                this.$router.push(path);
            } catch (error) {
                console.error('设备跳转出错:', error);
            }
        },

        // 日志相关方法

        getLogList() {
            // 获取日志列表
            cultiverlistv2({
                base_id: this.base_id
            }).then(res => {
                if (res && res.data && res.data.loglist) {
                    this.loglist = res.data.loglist;
                }
            }).catch(error => {
                console.error('获取日志列表失败:', error);
            });
        },

        showAddLogForm() {
            this.showAddLogDialog = true;
            // 重置表单
            this.addForm = {
                time: '',
                name: '',
                action: '',
                num: '',
                remark: '',
                zhonglei: '',
                sheshiid: '',
                base_id: this.base_id
            };
            this.isdisabled = true;
            this.errMsg = '';
        },

        addLog() {
            this.addLoading = false;

            // 单独检查每个必填字段
            if (this.addForm.sheshiid == '') {
                this.$message.warning('请选择设施');
                return;
            }
            if (this.addForm.time == '') {
                this.$message.warning('请选择时间');
                return;
            }
            if (this.addForm.name == '') {
                this.$message.warning('请填写操作人姓名');
                return;
            }
            if (this.addForm.action == '') {
                this.$message.warning('请选择动作');
                return;
            }
            if (this.addForm.zhonglei == '') {
                this.$message.warning('请填写种类');
                return;
            }
            if (this.addForm.num == '') {
                this.$message.warning('请填写数量');
                return;
            }
            // 开始加载状态
            this.addLoading = true;
            // 直接从路由获取base_id而不是使用store
            this.addForm.base_id = this.$route.query.base_id || this.base_id;

            // 打印发送到后端的数据和base_id
            console.log('发送到后端的数据:', JSON.stringify(this.addForm));

            // 调用API发送数据到后端
            add_cultiver(
                // cultiverlistv2(
                this.addForm
            ).then(res => {
                this.addLoading = false;
                // 更新日志列表数据
                this.loglist = res.data.loglist;
                // 使用alert而不是toast
                alert('提交成功');
                this.showAddLogDialog = false;
            }).catch(error => {
                this.addLoading = false;
                alert('提交失败: ' + error);
                console.error('添加日志失败:', error);
            });

        },

        check_form() {
            this.errMsg = '';
            // 定义必填字段列表，不包括remark字段
            const requiredFields = ['sheshiid', 'time', 'name', 'action', 'zhonglei', 'num'];

            for (var key in this.addFromName) {
                // 仅检查必填字段
                if (requiredFields.includes(key) && !this.addForm[key]) {
                    this.isdisabled = true;
                    this.errMsg = this.addFromName[key] + '不能为空';
                    return;
                }
            }
            this.isdisabled = false;
            this.errMsg = '';
        },

        validateNum(val) {
            // 清除错误信息
            this.errMsgNum = '';

            // 判断是否为数字
            if (val && !/^\d+$/.test(val)) {
                this.errMsgNum = '数量必须为数字';
                this.addForm.num = ''; // 清空非法输入
                this.isdisabled = true;
                return false;
            }

            // 验证通过，继续检查其他表单项
            this.check_form();
            return true;
        },

        getImagePath(type) {
            return type == 1
                ? require('@/assets/ui/error1.png')
                : require('@/assets/ui/error2.png');
        },
        resetScroll() {
            this.stopScrolling();
            this.$nextTick(() => {
                const container = this.$refs.scrollContainer;
                if (container) {
                    // 获取容器高度
                    this.containerHeight = container.clientHeight;
                    // 计算单个列表项的高度（包括空白项）
                    const itemHeight = container.querySelector('.area-item')?.offsetHeight || 50;
                    // 计算需要滚动的总高度（第一组列表的高度）
                    this.contentHeight = itemHeight * (this.alertYuanList.length + (this.needExtraItem ? 1 : 0));
                    this.scrollOffset = 0;
                    
                    // 只有当内容高度大于容器高度时才启动滚动
                    if (this.shouldScroll) {
                        this.startScrolling();
                    }
                }
            });
        },
        startScrolling() {
            // 增加判断条件
            if (!this.shouldScroll || this.isPaused) return;
            
            const scrollStep = 0.3; // 可以调整这个值来改变滚动速度
            let lastTime = performance.now();
            
            const step = (currentTime) => {
                // 再次检查是否需要滚动
                if (!this.shouldScroll || this.isPaused) {
                    this.stopScrolling();
                    return;
                }
                
                const deltaTime = currentTime - lastTime;
                lastTime = currentTime;

                this.scrollOffset += scrollStep * (deltaTime / 16.67);
                
                // 当滚动到第一组列表的末尾时重置
                if (this.scrollOffset >= this.contentHeight) {
                    this.scrollOffset = 0;
                }
                
                this.scrollInterval = requestAnimationFrame(step);
            };

            this.scrollInterval = requestAnimationFrame(step);
        },
        stopScrolling() {
            if (this.scrollInterval) {
                cancelAnimationFrame(this.scrollInterval);
                this.scrollInterval = null;
            }
        },
        handleResize() {
            // 触发重新计算
            this.$nextTick(() => {
                this.resetScroll();
            });
        },
        toggleScroll() {
            this.isPaused = !this.isPaused;
            if (!this.isPaused) {
                this.startScrolling();
            }
        }
    },

    created() {
        // 如果需要，可以在这里初始化一些 Vuex 状态
        if (!this.base_id) {
            // 设置默认的 base_id，如果需要的话
            this.updateBaseId('1');
        }
    },

    mounted() {
        this.resetScroll();
        try {
            // 从路由获取 base_id
            if (this.$route.query.base_id) {
                // 更新 Vuex 中的 base_id
                this.updateBaseId(this.$route.query.base_id);
            }
            console.log('当前base_id:', this.base_id, '路由参数base_id:', this.$route.query.base_id);
            this.GetCamera();
            this.getlist();
        } catch (error) {
            console.error('初始化DeviceList出错:', error);
        }
        
        // 添加窗口大小变化监听
        window.addEventListener('resize', this.handleResize);
        this.handleResize(); // 初始化时调用一次
    },

    beforeDestroy() {
        this.stopScrolling();
        window.removeEventListener('resize', this.handleResize);
    }
}
</script>


<template>
    <div class="Device-List">
        <van-nav-bar :title="title" left-arrow @click-left="onClickLeft" class="device-nav" />

        <!-- 只在activeTab为设备时显示设备列表内容 -->
        <div v-if="activeTab === 'devices'" class="devices-container">
            <!-- 设备列表部分 -->
            <div class="devices-section">
                <el-tabs v-if="activeTab === 'devices'" type="border-card" class="h-100 w-100 mobile-tabs">
                    <el-tab-pane label="水质设备">
                        <ul class="area-list">
                            <li v-for="i in shebeilist.water" :key="i.ID || index" class="area-item cursor-pointer"
                                @click="emitLinkShebei('water', i)">
                                <div class="item-left">
                                    <img src="~@/assets/ui/base.png" alt="">
                                </div>
                                <div class="item-middle">{{ i.BACILITIES_NAME || '未知' }} -> {{ i.EQUIPMENT_NAME || '未知设备' }}
                                </div>
                                <div class="item-right">运行良好</div>
                            </li>
                        </ul>
                    </el-tab-pane>
                    <el-tab-pane label="气象设备">
                        <ul class="area-list">
                            <li v-for="i in shebeilist.meteorological" :key="i.ID || index" class="area-item cursor-pointer"
                                @click="emitLinkShebei('meteorological', i)">
                                <div class="item-left">
                                    <img src="~@/assets/ui/base.png" alt="">
                                </div>
                                <div class="item-middle">{{ i.BACILITIES_NAME || '未知' }} -> {{ i.EQUIPMENT_NAME || '未知设备' }}
                                </div>
                                <div class="item-right">运行良好</div>
                            </li>
                        </ul>
                    </el-tab-pane>
                    <el-tab-pane label="监控设备">
                        <ul class="area-list">
                            <li v-for="(i, index) in shebeilist.monitoring" :key="i.ID || index"
                                class="area-item cursor-pointer" @click="emitLinkShebei('monitoring', i, index)">
                                <div class="item-left">
                                    <img src="~@/assets/ui/base.png" alt="">
                                </div>
                                <div class="item-middle">{{ i.camera_name || '未命名摄像头' }}</div>
                                <div class="item-right" :style="{ color: i.state == 1 ? '#43C8FF' : '#F69E29' }">
                                    {{ i.state == 1 ? '运行良好' : '运行异常' }}
                                </div>
                            </li>
                        </ul>
                    </el-tab-pane>
                </el-tabs>
            </div>

            <!-- 新增的分隔区域 -->
            <div class="separator-section">
                <div class="separator-left">
                    <div class="water-param">
                        <img src="~@/assets/ui/waterquality-ph.png" alt="氨氮" class="param-icon">
                        <div class="param-info">
                            <div class="param-name">氨氮(NH₃)</div>
                            <div class="param-value">{{ waterParams.nh3.value }}<span class="param-unit">mg/L</span></div>
                        </div>
                    </div>
                </div>
                <div class="separator-right">
                    <div class="water-param">
                        <img src="~@/assets/ui/waterquality-o2.png" alt="亚硝酸盐" class="param-icon">
                        <div class="param-info">
                            <div class="param-name">亚硝酸盐(NO₂⁻)</div>
                            <div class="param-value">{{ waterParams.no2.value }}<span class="param-unit">mg/L</span></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 报警信息部分 -->
            <div class="alarm-section">
                <section-title title="报警信息" class="white-title" />
                <div class="w-100 h-100 position-relative alarm-list-container">
                    <div class="scroll-container" ref="scrollContainer" >
                        <div class="scroll-content" :style="scrollStyle">
                            <ul class="area-list">
                                <li v-for="(i, index) in alertYuanList" :key="i.ID"
                                    :class="['area-item flex-item cursor-pointer', { 'odd-item': index % 2 === 0 }]">
                                    <div class="d-flex align-items-center justify-content-center area-item-img">
                                        <img :src="getImagePath(i.type)" alt="">
                                    </div>
                                    <div class="area-item-content">
                                        <div class="flex-content information-title d-flex align-items-center">
                                            {{ i.message }}
                                        </div>
                                        <div class="info-row">
                                            <div class="info-left">
                                                <span>当前值为</span>
                                                <span :class="i.type == 1 ? 'error1' : 'error2'" class="value-text">{{ i.value }}</span>
                                            </div>
                                            <div class="info-right">
                                                <span>{{ i.time }}</span>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <li v-if="needExtraItem" :key="'extra-item'" class="area-item flex-item"></li>
                            </ul>
                            <!-- 只在需要滚动时显示重复列表 -->
                            <ul class="area-list" v-if="shouldScroll">
                                <li v-for="(i, index) in alertYuanList" :key="i.ID"
                                    :class="['area-item flex-item cursor-pointer', { 'odd-item': index % 2 === 0 }]">
                                    <div class="d-flex align-items-center justify-content-center area-item-img">
                                        <img :src="getImagePath(i.type)" alt="">
                                    </div>
                                    <div class="area-item-content">
                                        <div class="flex-content information-title d-flex align-items-center">
                                            {{ i.message }}
                                        </div>
                                        <div class="info-row">
                                            <div class="info-left">
                                                <span>当前值为</span>
                                                <span :class="i.type == 1 ? 'error1' : 'error2'" class="value-text">{{ i.value }}</span>
                                            </div>
                                            <div class="info-right">
                                                <span>{{ i.time }}</span>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <li v-if="needExtraItem" :key="'extra-item'" class="area-item flex-item"></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 养殖日志页面 -->
        <div v-if="activeTab === 'logs'" class="breeding-logs">
            <!-- 日志记录列表 -->
            <div class="log-header">
                <h3 class="log-title">日志记录</h3>
                <van-button type="primary" size="small" icon="plus" class="add-log-btn" @click="showAddLogForm">
                    添加日志
                </van-button>
            </div>
            <template>
                <LogList :baselist="loglist"></LogList>
            </template>
            <!-- </div> -->
        </div>

        <!-- 添加日志弹窗 -->
        <el-dialog :visible.sync="showAddLogDialog" title="添加养殖日志" :width="dialogWidth" custom-class="log-dialog">
            <div class="h-100 w-100 log-form-container">
                <el-form :model="addForm" label-position="top" class="log-form">
                    <el-form-item label="时间">
                        <el-date-picker v-model="addForm.time" value-format="yyyy-MM-dd%20HH:mm" placeholder="选择日期时间"
                            @change="check_form" class="form-control">
                        </el-date-picker>
                    </el-form-item>

                    <el-form-item label="设施名称">
                        <el-select v-model="addForm.sheshiid" placeholder="请选择设施" @change="check_form"
                            class="form-control">
                            <el-option v-for="item in facilitiesList" :key="item.value" :label="item.text"
                                :value="item.value">
                            </el-option>
                        </el-select>
                    </el-form-item>

                    <div class="form-row">
                        <el-form-item label="姓名" class="half-width">
                            <el-input v-model="addForm.name" @change="check_form"></el-input>
                        </el-form-item>
                        <el-form-item label="动作" class="half-width">
                            <el-select v-model="addForm.action" placeholder="请选择动作" @change="check_form">
                                <el-option v-for="item in actionOptions" :key="item.value" :label="item.name"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </div>

                    <div class="form-row">
                        <el-form-item label="种类" class="half-width">
                            <el-input v-model="addForm.zhonglei" @change="check_form"></el-input>
                        </el-form-item>
                        <el-form-item label="数量" class="half-width">
                            <el-input v-model="addForm.num" @input="validateNum" class="no-number-controls"></el-input>
                            <div class="num-error" v-if="errMsgNum">{{ errMsgNum }}</div>
                        </el-form-item>
                    </div>

                    <el-form-item label="备注">
                        <el-input v-model="addForm.remark" type="textarea" :rows="2" @change="check_form"></el-input>
                    </el-form-item>

                    <div class="form-error" v-if="errMsg">{{ errMsg }}</div>

                    <el-form-item>
                        <el-button type="primary" @click="addLog" class="submit-btn" :loading="addLoading"
                            :disabled="isdisabled">提交</el-button>
                    </el-form-item>
                </el-form>
            </div>
        </el-dialog>

        <!-- 底部导航栏 -->
        <van-tabbar v-model="activeTab" class="bottom-nav">
            <van-tabbar-item name="devices" icon="apps-o" class="nav-item">设备列表</van-tabbar-item>
            <van-tabbar-item name="logs" icon="notes-o" class="nav-item">养殖日志</van-tabbar-item>
        </van-tabbar>
    </div>
</template>


<style lang="less" scoped>
.Device-List {
    height: 100%;
    display: flex;
    flex-direction: column;
    background-color: #001528;
    background-image: url('./ui/subtitled-bg.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    overflow-y: auto;
    padding-bottom: 50px;
    /* 增加底部padding以适应底部导航栏 */
}

.device-nav {
    flex-shrink: 0;

    :deep(.van-nav-bar__content) {
        background-color: #001528;
    }

    :deep(.van-nav-bar__title) {
        color: #C3E4FF;
    }

    :deep(.van-icon) {
        color: #C3E4FF !important;
    }
}


.mobile-tabs {
    font-size: 14px;
}

.area-list {
    width: 100%;
    padding: 0;
    margin: 0;
    list-style-type: none;
    overflow: hidden; /* 改为hidden */
}

.area-item {
    height: 50px;
    display: flex;
    align-items: center;
    font-family: PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #BDDBF1;
    border-bottom: 1px solid rgba(85, 150, 223, 0.2);
    transition: background 0.2s;
}

.area-item:hover {
    background: rgba(67, 200, 255, 0.08);
}

.area-item:nth-child(odd) {
    background-color: rgba(1, 10, 20, .6);
}

.item-left {
    width: 40px;
    display: flex;
    align-items: center;
    justify-content: center;

    img {
        height: 20px;
        width: 20px;
        object-fit: contain;
    }
}

.item-middle {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    padding-right: 10px;
}

.item-right {
    width: 70px;
    text-align: right;
    padding-right: 15px;
    color: #43C8FF;
}

.el-tabs--border-card {
    border: 0px !important;
    background-color: rgba(255, 255, 255, 0) !important;
}

/deep/ .el-tab-pane {
    height: 300px;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    /* 增强iOS滚动体验 */
}

/deep/ .el-tabs__content {
    height: 300px !important;
    padding: 0 !important;
}

/deep/ .el-tabs__nav {
    width: 100%;
    background-color: rgba(255, 255, 255, 0);
    display: flex;
}

/deep/ .el-tabs__item {
    flex: 1;
    text-align: center;
    height: 42px !important;
    line-height: 42px !important;
    padding: 0 !important;
    background: linear-gradient(0deg, #2C5199 0%, rgba(44, 81, 153, 0) 100%);
    border-bottom: 1px solid transparent !important;
    border-top: 0px solid transparent !important;
    border-left: 1px solid transparent !important;
    border-right: 1px solid transparent !important;
    border-image-slice: 1 !important;
    opacity: 0.6;
    border-image: linear-gradient(0deg, #5596DF, transparent) 10 10 !important;
    font-family: PingFang SC;
    font-weight: 600;
    font-size: 14px;
    color: #6C8BBC !important;
}

/deep/ .el-tabs__item.is-active {
    background: linear-gradient(180deg, rgba(44, 81, 153, 1) 0%, rgba(44, 81, 153, 0) 100%) !important;
    border-top: 1px solid transparent !important;
    border-left: 1px solid transparent !important;
    border-right: 1px solid transparent !important;
    border-bottom: 0px solid transparent !important;
    border-image: linear-gradient(0deg, #5596DF, #5596DF) 10 10 !important;
    border-image-slice: 0 0 1 0 !important;
    opacity: 1;
    color: #C3E4FF !important;
}

/deep/ .el-tabs__header {
    background-color: rgba(255, 255, 255, 0) !important;
    border: 0px !important;
    margin: 0 !important;
}

/* 自定义滚动条 */
/deep/ .el-tab-pane::-webkit-scrollbar {
    width: 3px;
}

/deep/ .el-tab-pane::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
}

/deep/ .el-tab-pane::-webkit-scrollbar-thumb {
    background: rgba(85, 150, 223, 0.6);
    border-radius: 4px;
}

/deep/ .el-tab-pane::-webkit-scrollbar-thumb:hover {
    background: rgba(85, 150, 223, 0.8);
}

/* 适配小屏幕手机 */
@media screen and (max-width: 360px) {
    .area-item {
        font-size: 12px;
    }

    /deep/ .el-tabs__item {
        font-size: 12px;
    }

    .item-right {
        width: 60px;
    }
}

/* 养殖日志样式 */
.breeding-logs {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 15px;
}

.log-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid rgba(85, 150, 223, 0.3);
}

.log-title {
    color: #C3E4FF;
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.add-log-btn {
    background-color: rgba(85, 150, 223, 0.5);
    border-color: #0f2a36;
    color: #fff;
    border-radius: 15px;
    font-size: 12px;
    padding: 0 15px;
}


/* ElementUI 日志表单样式 */
.log-dialog {
    background: rgba(4, 12, 24, 0.9);
    border: 1px solid #5596DF;
    border-radius: 6px;
    max-width: 450px;
    /* 设置最大宽度 */
    margin: 0 auto;
    /* 居中显示 */
}

.log-form-container {
    display: flex;
    flex-direction: column;
}

.log-form {
    display: flex;
    flex-direction: column;
}

.form-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
}

.half-width {
    width: 48%;
}

.submit-btn {
    width: 100%;
    background: linear-gradient(0deg, #5074BC 0%, #2C5199 100%);
    border: 1px solid #5596DF;
}

/deep/ .el-dialog__header {
    padding: 15px 20px;
    border-bottom: 1px solid rgba(85, 150, 223, 0.3);
    background-color: rgba(4, 41, 87, 0.8);
}

/deep/ .el-dialog__title {
    color: #C3E4FF;
    font-size: 16px;
    font-weight: 600;
}

/deep/ .el-dialog__body {
    background-color: rgba(4, 41, 87, 0.8);
    padding: 20px;
}

/deep/ .el-form-item__label {
    color: #BDDBF1;
    font-size: 14px;
    padding: 0;
    line-height: 20px;
}

/deep/ .el-input__inner,
/deep/ .el-textarea__inner {
    background: rgba(1, 10, 20, 0.6);
    border: 1px solid #376293;
    color: #BDDBF1;
}

/deep/ .el-input__inner:focus,
/deep/ .el-textarea__inner:focus {
    border-color: #5596DF;
}

/deep/ .el-input__inner::placeholder,
/deep/ .el-textarea__inner::placeholder {
    color: #376293;
}

/deep/ .el-select .el-input.is-focus .el-input__inner {
    border-color: #5596DF;
}

/deep/ .el-textarea__inner {
    resize: none;
}

.form-error {
    color: #F56C6C;
    font-size: 12px;
    padding: 5px 0;
    text-align: right;
}

/* 底部导航栏样式 */
.bottom-nav {
    border-top: 1px solid rgba(85, 150, 223, 0.3);
    background-color: rgba(0, 21, 40, 1);
    height: 50px;

    :deep(.van-tabbar-item) {
        color: #6C8BBC;
        background: linear-gradient(0deg, #2C5199 0%, rgba(44, 81, 153, 0) 100%);
        font-family: PingFang SC;
        font-weight: 600;
        font-size: 14px;
        opacity: 0.6;
        border-image: linear-gradient(0deg, #5596DF, transparent) 10 10 !important;
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }

    :deep(.van-tabbar-item--active) {
        color: #C3E4FF;
        background: linear-gradient(180deg, rgba(44, 81, 153, 1) 0%, rgba(44, 81, 153, 0) 100%) !important;
        opacity: 1;
        border-image: linear-gradient(0deg, #5596DF, #5596DF) 10 10 !important;
        border-image-slice: 0 0 1 0 !important;
    }

    :deep(.van-icon) {
        font-size: 20px;
        margin-bottom: 4px;
    }

    :deep(.van-tabbar-item__text) {
        font-size: 12px;
    }
}

/* 深色模式适配 Van组件 */
:deep(.van-empty__description) {
    color: #BDDBF1;
}

:deep(.van-empty__image) {
    color: #43C8FF;
}

/* 适配iPhone X等带有安全区域的设备 */
@supports (padding-bottom: constant(safe-area-inset-bottom)) or (padding-bottom: env(safe-area-inset-bottom)) {
    .mobile-tabs {
        padding-bottom: constant(safe-area-inset-bottom);
        padding-bottom: env(safe-area-inset-bottom);
    }

    .bottom-nav {
        padding-bottom: constant(safe-area-inset-bottom);
        padding-bottom: env(safe-area-inset-bottom);
    }
}

/* 表单控件响应式调整 */
.form-control {
    width: 100% !important;
}

/* 移动端适配 */
@media screen and (max-width: 480px) {
    /deep/ .el-dialog__body {
        padding: 15px 10px;
    }

    /deep/ .el-form-item {
        margin-bottom: 12px;
    }

    /deep/ .el-date-editor.el-input {
        width: 100%;
    }

    /deep/ .el-select {
        width: 100%;
    }

    /deep/ .el-form-item__label {
        font-size: 13px;
        margin-bottom: 3px;
    }

    .form-row {
        flex-direction: column;
    }

    .half-width {
        width: 100%;
        margin-bottom: 8px;
    }
}

/* 添加特小屏幕适配 */
@media screen and (max-width: 320px) {
    /deep/ .el-dialog__header {
        padding: 10px;
    }

    /deep/ .el-dialog__body {
        padding: 10px 8px;
    }

    /deep/ .el-form-item__label {
        font-size: 12px;
    }
}

/* 移除数字输入框的上下箭头 */
/deep/ .no-number-controls::-webkit-outer-spin-button,
/deep/ .no-number-controls::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

/deep/ .no-number-controls {
    -moz-appearance: textfield;
}

.num-error {
    color: #F56C6C;
    font-size: 12px;
    margin-top: 3px;
}

/* 告警列表样式 */
.alarm-section {
    padding: 15px;
    margin-top: 10px;
}


.error1 {
    color: #f15a24;
}

.error2 {
    color: #eeac46;
}

.alarm-list-container {
    display: flex;
    flex-direction: column;
    height: 180px;
    overflow: hidden; /* 确保内容不会溢出 */
}

.scroll-container {
    flex: 1;
    overflow: hidden;
    position: relative;
}

.scroll-content {
    width: 100%;
    will-change: transform;
    position: absolute; /* 添加绝对定位 */
    top: 0;
    left: 0;
}

.area-item-img {
    height: 39px;
    width: 39px;
    flex-shrink: 0;

    img {
        height: 100%;
        width: 100%;
    }
}

.area-item-content {
    padding-left: 1rem;
    width: calc(100% - 39px - 1rem);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
    flex: 1;
}

.information-title {
    font-size: 14px;
    margin-top: 4px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    margin-bottom: 4px;
}

.info-left {
    display: flex;
    align-items: center;
    color: #BDDBF1;
    // font-size: 12px;
}

.info-right {
    color: #6C8BBC;
    // font-size: 12px;
}

.value-text {
    padding-left: 5px;
}

/* 移除原有的滚动条样式 */
.area-list::-webkit-scrollbar {
    display: none; /* 隐藏滚动条 */
}

/* 新增和修改的样式 */
.devices-container {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 46px - 50px); /* 减去导航栏和底部导航的高度 */
    overflow: hidden;
}

.devices-section {
    flex: 1;
    min-height: 0; /* 重要：防止flex子项溢出 */
    display: flex;
    flex-direction: column;
}

.alarm-section {
    flex: 1;
    min-height: 0; /* 重要：防止flex子项溢出 */
    display: flex;
    flex-direction: column;
    padding: 15px;
    margin-top: 0; /* 移除原有的上边距 */
}

/* 修改 el-tabs 相关样式 */
.mobile-tabs {
    height: 100%;
    display: flex;
    flex-direction: column;
}

:deep(.el-tabs) {
    display: flex;
    flex-direction: column;
    height: 100%;
}

:deep(.el-tabs__content) {
    flex: 1;
    overflow: hidden;
    height: 100% !important;
}

:deep(.el-tab-pane) {
    height: 100% !important;
    overflow-y: auto;
}

/* 修改报警列表容器样式 */
.alarm-list-container {
    flex: 1;
    min-height: 0; /* 重要：防止flex子项溢出 */
    display: flex;
    flex-direction: column;
}

/* 调整滚动容器样式 */
.scroll-container {
    flex: 1;
    min-height: 0; /* 重要：防止flex子项溢出 */
    position: relative;
    overflow: hidden;
}

/* 确保内容区域正确显示 */
.scroll-content {
    width: 100%;
    will-change: transform;
    position: absolute;
    top: 0;
    left: 0;
}

/* 调整列表项样式 */
.area-list {
    width: 100%;
    padding: 0;
    margin: 0;
    list-style-type: none;
}

.area-item {
    height: 50px; /* 保持原有高度 */
    // ... 其他样式保持不变 ...
}

/* 适配 iPhone 安全区域 */
@supports (padding-bottom: constant(safe-area-inset-bottom)) or (padding-bottom: env(safe-area-inset-bottom)) {
    .devices-container {
        height: calc(100vh - 46px - 50px - constant(safe-area-inset-bottom));
        height: calc(100vh - 46px - 50px - env(safe-area-inset-bottom));
    }
}

/* 移动端适配 */
@media screen and (max-width: 480px) {
    .devices-container {
        height: calc(100vh - 46px - 50px);
    }
    
    .alarm-section {
        padding: 10px;
    }
}

/* 新增分隔区域样式 */
.separator-section {
    height: 60px;
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 0 15px;
    box-sizing: border-box;
    margin: 10px 0;
}

.separator-left,
.separator-right {
    width: 48%;
    height: 100%;
    background: rgba(1, 10, 20, 0.6);
    border: 1px solid rgba(85, 150, 223, 0.2);
    border-radius: 4px;
    display: flex;
    align-items: center;
    padding: 0 15px;
}

.water-param {
    display: flex;
    align-items: center;
    width: 100%;
}

.param-icon {
    width: 32px;
    height: 32px;
    margin-right: 12px;
}

.param-info {
    flex: 1;
}

.param-name {
    color: #BDDBF1;
    font-size: 14px;
    margin-bottom: 4px;
}

.param-value {
    color: #43C8FF;
    font-size: 18px;
    font-weight: 600;
}

.param-unit {
    color: #6C8BBC;
    font-size: 12px;
    margin-left: 4px;
    font-weight: normal;
}

/* 移动端适配 */
@media screen and (max-width: 480px) {
    .separator-section {
        padding: 0 10px;
        margin: 8px 0;
    }
    
    .separator-left,
    .separator-right {
        padding: 0 10px;
    }
    
    .param-icon {
        width: 28px;
        height: 28px;
        margin-right: 8px;
    }
    
    .param-name {
        font-size: 12px;
    }
    
    .param-value {
        font-size: 16px;
    }
    
    .param-unit {
        font-size: 11px;
    }
}
</style>