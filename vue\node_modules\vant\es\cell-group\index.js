import _mergeJSXProps from "@vue/babel-helper-vue-jsx-merge-props";
// Utils
import { createNamespace } from '../utils';
import { inherit } from '../utils/functional';
import { BORDER_TOP_BOTTOM } from '../utils/constant'; // Types

var _createNamespace = createNamespace('cell-group'),
    createComponent = _createNamespace[0],
    bem = _createNamespace[1];

function CellGroup(h, props, slots, ctx) {
  var _ref;

  var Group = h("div", _mergeJSXProps([{
    "class": [bem({
      inset: props.inset
    }), (_ref = {}, _ref[BORDER_TOP_BOTTOM] = props.border, _ref)]
  }, inherit(ctx, true)]), [slots.default == null ? void 0 : slots.default()]);

  if (props.title || slots.title) {
    return h("div", {
      "key": ctx.data.key
    }, [h("div", {
      "class": bem('title', {
        inset: props.inset
      })
    }, [slots.title ? slots.title() : props.title]), Group]);
  }

  return Group;
}

CellGroup.props = {
  title: String,
  inset: Boolean,
  border: {
    type: Boolean,
    default: true
  }
};
export default createComponent(CellGroup);