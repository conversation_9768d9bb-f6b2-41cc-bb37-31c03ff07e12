from django.db import models
from django.contrib.auth.models import AbstractUser
from django.db import models
from demo1_server.libs import human_datetime,ModelMixin
from django.contrib.auth.hashers import make_password, check_password


# Create your models here.
# class User(AbstractUser):
#     phone = models.CharField(max_length=11)
#     level = models.SmallIntegerField(null=True)

import json


class User(models.Model, ModelMixin):
    username = models.Char<PERSON><PERSON>(max_length=100)
    nickname = models.Char<PERSON>ield(max_length=100)
    password_hash = models.Char<PERSON>ield(max_length=100)  # hashed password
    is_supper = models.<PERSON><PERSON>an<PERSON>ield(default=False)
    is_active = models.BooleanField(default=True)
    access_token = models.Char<PERSON>ield(max_length=32)
    token_expired = models.IntegerField(null=True)
    last_login = models.Char<PERSON><PERSON>(max_length=20)
    last_ip = models.Char<PERSON><PERSON>(max_length=50)
    # role = models.Foreign<PERSON>ey('Role', on_delete=models.PROTECT, null=True)

    created_at = models.Char<PERSON>ield(max_length=20, default=human_datetime)
    created_by = models.ForeignKey('User', models.PROTECT, related_name='+', null=True)
    deleted_at = models.CharField(max_length=20, null=True)
    deleted_by = models.ForeignKey('User', models.PROTECT, related_name='+', null=True)

    @staticmethod
    def make_password(plain_password: str) -> str:
        return make_password(plain_password, hasher='pbkdf2_sha256')

    def verify_password(self, plain_password: str) -> bool:
        return check_password(plain_password, self.password_hash)

    class Meta:
        db_table = 'user'
        ordering = ('-id',)


# class Role(models.Model, ModelMixin):
#     name = models.CharField(max_length=50)
#     desc = models.CharField(max_length=255, null=True)
#     page_perms = models.TextField(null=True)
#     deploy_perms = models.TextField(null=True)
#     host_perms = models.TextField(null=True)

#     created_at = models.CharField(max_length=20, default=human_datetime)
#     created_by = models.ForeignKey(User, on_delete=models.PROTECT, related_name='+')

#     def to_dict(self, *args, **kwargs):
#         tmp = super().to_dict(*args, **kwargs)
#         tmp['page_perms'] = json.loads(self.page_perms) if self.page_perms else None
#         tmp['deploy_perms'] = json.loads(self.deploy_perms) if self.deploy_perms else None
#         tmp['host_perms'] = json.loads(self.host_perms) if self.host_perms else None
#         tmp['used'] = self.user_set.count()
#         return tmp

#     def add_deploy_perm(self, target, value):
#         perms = {'apps': [], 'envs': []}
#         if self.deploy_perms:
#             perms.update(json.loads(self.deploy_perms))
#         perms[target].append(value)
#         self.deploy_perms = json.dumps(perms)
#         self.save()

#     def add_host_perm(self, value):
#         perms = json.loads(self.host_perms) if self.host_perms else []
#         perms.append(value)
#         self.host_perms = json.dumps(perms)
#         self.save()

#     class Meta:
#         db_table = 'roles'
#         ordering = ('-id',)
