{"name": "ezuikit-js", "maintainers": ["jzy"], "version": "0.7.2", "description": "ezuikit javascript for npm", "main": "ezuikit.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/Hikvision-Ezviz/EZUIKit-JavaScript-npm.git"}, "keywords": ["video", "ezu<PERSON>t"], "author": "yu<PERSON><PERSON><PERSON>", "license": "ISC", "bugs": {"url": "https://github.com/Hikvision-Ezviz/EZUIKit-JavaScript-npm/issues"}, "homepage": "https://github.com/Hikvision-Ezviz/EZUIKit-JavaScript-npm#readme"}