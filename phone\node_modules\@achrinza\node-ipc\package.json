{"_args": [["@achrinza/node-ipc@9.2.9", "F:\\桌面\\上海海洋大屏2期\\phone"]], "_development": true, "_from": "@achrinza/node-ipc@9.2.9", "_id": "@achrinza/node-ipc@9.2.9", "_inBundle": false, "_integrity": "sha512-7s0VcTwiK/0tNOVdSX9FWMeFdOEcsAOz9HesBldXxFMaGvIak7KC2z9tV9EgsQXn6KUsWsfIkViMNuIo0GoZDQ==", "_location": "/@achrinza/node-ipc", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@achrinza/node-ipc@9.2.9", "name": "@achrinza/node-ipc", "escapedName": "@achrinza%2fnode-ipc", "scope": "@achrinza", "rawSpec": "9.2.9", "saveSpec": null, "fetchSpec": "9.2.9"}, "_requiredBy": ["/@vue/cli-shared-utils"], "_resolved": "https://registry.npmjs.org/@achrinza/node-ipc/-/node-ipc-9.2.9.tgz", "_spec": "9.2.9", "_where": "F:\\桌面\\上海海洋大屏2期\\phone", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/achrinza/node-ipc/issues"}, "dependencies": {"@node-ipc/js-queue": "2.0.3", "event-pubsub": "4.3.0", "js-message": "1.0.7"}, "description": "A nodejs module for local and remote Inter Process Communication (IPC), Neural Networking, and able to facilitate machine learning.", "devDependencies": {"istanbul": "0.4.1", "jasmine": "2.4.1", "lockfile-lint": "^4.7.4", "node-cmd": "2.0.0"}, "directories": {"example": "example"}, "engines": {"node": "8 || 9 || 10 || 11 || 12 || 13 || 14 || 15 || 16 || 17 || 18 || 19 || 20 || 21 || 22"}, "files": ["dao", "entities", "local-node-ipc-certs", "services"], "homepage": "https://github.com/achrinza/node-ipc", "keywords": ["IPC", "Neural Networking", "Machine Learning", "inter", "process", "communication", "unix", "windows", "win", "socket", "TCP", "UDP", "domain", "sockets", "threaded", "communication", "multi", "process", "shared", "memory"], "license": "MIT", "main": "node-ipc.js", "name": "@achrinza/node-ipc", "repository": {"type": "git", "url": "git+https://github.com/achrinza/node-ipc.git"}, "scripts": {"istanbul": "istanbul cover -x ./spec/**", "test": "npm run-script istanbul -- jasmine", "test-windows": "npm run-script istanbul -- ./node_modules/jasmine/bin/jasmine.js"}, "version": "9.2.9"}