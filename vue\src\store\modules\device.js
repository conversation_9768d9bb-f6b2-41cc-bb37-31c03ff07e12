const state = {
  deviceId: null,
  deviceName: '',
  deviceType: '',
  fullDeviceData: null,
  baseId: '1',
  chartData: null,
  // 设备检测相关状态
  deviceInfo: {
    type: 'desktop',
    isMobile: false,
    isTablet: false,
    isDesktop: true,
    os: 'unknown',
    browser: 'unknown',
    screenWidth: 1920,
    windowWidth: 1920,
    userAgent: ''
  }
}

const mutations = {
  SET_DEVICE_INFO(state, payload) {
    state.deviceId = payload.id || null
    state.deviceName = payload.name || ''
    state.deviceType = payload.type || ''
    state.fullDeviceData = payload.itemData || null
  },
  SET_BASE_ID(state, id) {
    state.baseId = id
  },
  SET_CHART_DATA(state, data) {
    state.chartData = data
  },
  // 设备检测相关mutations
  UPDATE_DEVICE_INFO(state, deviceInfo) {
    state.deviceInfo = { ...state.deviceInfo, ...deviceInfo }
  }
}

const actions = {
  updateDeviceInfo({ commit }, deviceInfo) {
    if (deviceInfo.id || deviceInfo.name || deviceInfo.type) {
      // 设备信息更新
      commit('SET_DEVICE_INFO', deviceInfo)
    } else {
      // 设备检测信息更新
      commit('UPDATE_DEVICE_INFO', deviceInfo)
    }
  },
  updateBaseId({ commit }, id) {
    commit('SET_BASE_ID', id)
  },
  updateChartData({ commit }, data) {
    commit('SET_CHART_DATA', data)
  }
}

const getters = {
  deviceInfo: state => state.deviceInfo,
  isMobile: state => state.deviceInfo.isMobile,
  isTablet: state => state.deviceInfo.isTablet,
  isDesktop: state => state.deviceInfo.isDesktop,
  deviceType: state => state.deviceInfo.type,
  // 保持原有的设备信息getter
  deviceData: state => ({
    id: state.deviceId,
    name: state.deviceName,
    type: state.deviceType,
    fullDeviceData: state.fullDeviceData
  }),
  baseId: state => state.baseId,
  chartData: state => state.chartData
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
