{"_args": [["@babel/helpers@7.27.1", "F:\\桌面\\上海海洋大屏2期\\phone"]], "_development": true, "_from": "@babel/helpers@7.27.1", "_id": "@babel/helpers@7.27.1", "_inBundle": false, "_integrity": "sha512-FCvFTm0sWV8Fxhpp2McP5/W53GPllQ9QeQ7SiqGWjMf/LVG07lFa5+pgK05IRhVwtvafT22KF+ZSnM9I545CvQ==", "_location": "/@babel/helpers", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@babel/helpers@7.27.1", "name": "@babel/helpers", "escapedName": "@babel%2fhelpers", "scope": "@babel", "rawSpec": "7.27.1", "saveSpec": null, "fetchSpec": "7.27.1"}, "_requiredBy": ["/@babel/core"], "_resolved": "https://registry.npmjs.org/@babel/helpers/-/helpers-7.27.1.tgz", "_spec": "7.27.1", "_where": "F:\\桌面\\上海海洋大屏2期\\phone", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "dependencies": {"@babel/template": "^7.27.1", "@babel/types": "^7.27.1"}, "description": "Collection of helper functions used by Babel transforms.", "devDependencies": {"@babel/generator": "^7.27.1", "@babel/helper-plugin-test-runner": "^7.27.1", "@babel/parser": "^7.27.1", "regenerator-runtime": "^0.14.0"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-helpers", "license": "MIT", "main": "./lib/index.js", "name": "@babel/helpers", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-helpers"}, "type": "commonjs", "version": "7.27.1"}