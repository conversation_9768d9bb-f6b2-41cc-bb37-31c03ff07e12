@import '../style/var';

.van-circle {
  position: relative;
  display: inline-block;
  width: @circle-size;
  height: @circle-size;
  text-align: center;

  svg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }

  &__layer {
    stroke: @circle-layer-color;
  }

  &__hover {
    fill: none;
    stroke: @circle-color;
    stroke-linecap: round;
  }

  &__text {
    position: absolute;
    top: 50%;
    left: 0;
    box-sizing: border-box;
    width: 100%;
    padding: 0 @padding-base;
    color: @circle-text-color;
    font-weight: @circle-text-font-weight;
    font-size: @circle-text-font-size;
    line-height: @circle-text-line-height;
    transform: translateY(-50%);
  }
}
