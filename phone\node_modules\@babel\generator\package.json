{"_args": [["@babel/generator@7.27.1", "F:\\桌面\\上海海洋大屏2期\\phone"]], "_development": true, "_from": "@babel/generator@7.27.1", "_id": "@babel/generator@7.27.1", "_inBundle": false, "_integrity": "sha512-UnJfnIpc/+JO0/+KRVQNGU+y5taA5vCbwN8+azkX6beii/ZF+enZJSOKo11ZSzGJjlNfJHfQtmQT8H+9TXPG2w==", "_location": "/@babel/generator", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@babel/generator@7.27.1", "name": "@babel/generator", "escapedName": "@babel%2fgenerator", "scope": "@babel", "rawSpec": "7.27.1", "saveSpec": null, "fetchSpec": "7.27.1"}, "_requiredBy": ["/@babel/core", "/@babel/traverse"], "_resolved": "https://registry.npmjs.org/@babel/generator/-/generator-7.27.1.tgz", "_spec": "7.27.1", "_where": "F:\\桌面\\上海海洋大屏2期\\phone", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen"}, "dependencies": {"@babel/parser": "^7.27.1", "@babel/types": "^7.27.1", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25", "jsesc": "^3.0.2"}, "description": "Turns an AST into code.", "devDependencies": {"@babel/core": "^7.27.1", "@babel/helper-fixtures": "^7.27.1", "@babel/plugin-transform-typescript": "^7.27.1", "@jridgewell/sourcemap-codec": "^1.4.15", "@types/jsesc": "^2.5.0", "charcodes": "^0.2.0"}, "engines": {"node": ">=6.9.0"}, "files": ["lib"], "homepage": "https://babel.dev/docs/en/next/babel-generator", "license": "MIT", "main": "./lib/index.js", "name": "@babel/generator", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-generator"}, "type": "commonjs", "version": "7.27.1"}