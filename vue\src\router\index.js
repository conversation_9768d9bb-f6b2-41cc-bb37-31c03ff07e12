/**
 * 统一路由配置
 * 支持桌面端和移动端自动识别和重定向
 */
import Vue from 'vue'
import VueRouter from 'vue-router'
import deviceDetector from '@/utils/deviceDetect'
import desktopRoutes from './modules/desktop'
import mobileRoutes from './modules/mobile'

Vue.use(VueRouter)

// 合并桌面端和移动端路由
const routes = [
  {
    path: '/',
    beforeEnter: (to, from, next) => {
      // 根据设备类型自动重定向
      if (deviceDetector.isMobile()) {
        next('/mobile/login')
      } else {
        next('/desktop/login')
      }
    }
  },
  // 兼容原有路由，自动重定向到对应平台
  {
    path: '/login',
    beforeEnter: (to, from, next) => {
      if (deviceDetector.isMobile()) {
        next('/mobile/login')
      } else {
        next('/desktop/login')
      }
    }
  },
  {
    path: '/home',
    beforeEnter: (to, from, next) => {
      if (deviceDetector.isMobile()) {
        next('/mobile/home')
      } else {
        next('/desktop/home')
      }
    }
  },
  // 桌面端路由
  ...desktopRoutes,
  // 移动端路由
  ...mobileRoutes,
  // 404 页面
  {
    path: '*',
    beforeEnter: (to, from, next) => {
      if (deviceDetector.isMobile()) {
        next('/mobile/login')
      } else {
        next('/desktop/login')
      }
    }
  }
]

const router = new VueRouter({
  mode: 'history',
  routes
})

// 全局路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta && to.meta.title) {
    document.title = to.meta.title
  }

  // 检查平台兼容性
  const deviceType = deviceDetector.getDeviceType()
  const routePlatform = to.meta && to.meta.platform

  // 如果路由指定了平台，检查是否匹配
  if (routePlatform) {
    if (routePlatform === 'mobile' && deviceType !== 'mobile') {
      // 桌面设备访问移动端路由，重定向到对应的桌面端路由
      const desktopPath = to.path.replace('/mobile', '/desktop')
      const desktopRoute = router.resolve(desktopPath)
      if (desktopRoute.route.matched.length > 0) {
        next(desktopPath)
        return
      }
    } else if (routePlatform === 'desktop' && deviceType === 'mobile') {
      // 移动设备访问桌面端路由，重定向到对应的移动端路由
      const mobilePath = to.path.replace('/desktop', '/mobile')
      const mobileRoute = router.resolve(mobilePath)
      if (mobileRoute.route.matched.length > 0) {
        next(mobilePath)
        return
      }
    }
  }

  // 权限检查（如果需要）
  if (to.meta && to.meta.requiresAuth) {
    // 这里可以添加登录状态检查逻辑
    // const isLoggedIn = store.getters.isLoggedIn
    // if (!isLoggedIn) {
    //   next(deviceType === 'mobile' ? '/mobile/login' : '/desktop/login')
    //   return
    // }
  }

  next()
})

export default router
