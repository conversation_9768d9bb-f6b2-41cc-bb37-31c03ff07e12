/**
 * 统一路由配置
 * 使用命名视图支持桌面端和移动端
 */
import Vue from 'vue'
import VueRouter from 'vue-router'

// 桌面端页面组件
const DesktopLogin = () => import('@/views/Login2.vue')
const DesktopHome = () => import('@/views/Home.vue')
const DesktopDetail = () => import('@/views/Detail2.vue')
const DesktopCultiver = () => import('@/views/Cultiver2.vue')
const historyData = () => import('@/views/data/history.vue')
const reportData = () => import('@/views/data/report.vue')
const deviceManage = () => import('@/views/device/manage.vue')
const deviceCamera = () => import('@/views/device/camera.vue')
const userAdd = () => import('@/views/user/add.vue')
const tousiplan = () => import('@/views/data/tousi.vue')

// 移动端页面组件
const MobileHome = () => import('@/views/mobile/Home.vue')
const MobileLogin = () => import('@/views/mobile/Login.vue')
const MobileRegister = () => import('@/views/mobile/Register.vue')
const MobileBaseList = () => import('@/views/mobile/BaseList.vue')
const MobileSectionTitlePage = () => import('@/views/mobile/SectionTitlePage.vue')

Vue.use(VueRouter)

// 使用命名视图的路由配置
const routes = [
  {
    path: '/',
    redirect: '/login'
  },
  {
    path: '/login',
    components: {
      default: DesktopLogin,
      desktop: DesktopLogin,
      mobile: MobileLogin
    },
    meta: { withMenu: false, title: '登录' }
  },
  {
    path: '/home',
    components: {
      default: DesktopHome,
      desktop: DesktopHome,
      mobile: MobileHome
    },
    meta: { withMenu: false, title: '首页' }
  },
  {
    path: '/detail',
    components: {
      default: DesktopDetail,
      desktop: DesktopDetail,
      mobile: MobileBaseList
    },
    meta: { withMenu: false, title: '详情' }
  },
  {
    path: '/cultiver',
    components: {
      default: DesktopCultiver,
      desktop: DesktopCultiver,
      mobile: MobileHome
    },
    meta: { withMenu: false, title: '养殖' }
  },
  {
    path: '/data/history',
    components: {
      default: historyData,
      desktop: historyData,
      mobile: MobileHome
    },
    meta: { withMenu: true, pathName: '数据中心 / 历史数据', title: '历史数据' }
  },
  {
    path: '/data/report',
    components: {
      default: reportData,
      desktop: reportData,
      mobile: MobileHome
    },
    meta: { withMenu: true, pathName: '数据中心 / 统计报表', title: '统计报表' }
  },
  {
    path: '/data/tousi',
    components: {
      default: tousiplan,
      desktop: tousiplan,
      mobile: MobileHome
    },
    meta: { withMenu: true, pathName: '数据中心 / 投饲计划', title: '投饲计划' }
  },
  {
    path: '/device/manage',
    components: {
      default: deviceManage,
      desktop: deviceManage,
      mobile: MobileHome
    },
    meta: { withMenu: true, pathName: '设备中心 / 设备管理', title: '设备管理' }
  },
  {
    path: '/device/camera',
    components: {
      default: deviceCamera,
      desktop: deviceCamera,
      mobile: MobileHome
    },
    meta: { withMenu: true, pathName: '设备中心 / 摄像头管理', title: '摄像头管理' }
  },
  {
    path: '/user/add',
    components: {
      default: userAdd,
      desktop: userAdd,
      mobile: MobileHome
    },
    meta: { withMenu: true, pathName: '用户管理 / 新增用户', title: '新增用户' }
  },
  {
    path: '/register',
    components: {
      default: userAdd,
      desktop: userAdd,
      mobile: MobileRegister
    },
    meta: { withMenu: false, title: '注册' }
  },
  // 移动端专用路由
  {
    path: '/mobile/list',
    components: {
      default: MobileBaseList,
      desktop: DesktopHome,
      mobile: MobileBaseList
    },
    meta: { withMenu: false, title: '基地列表' }
  },
  {
    path: '/mobile/section-title',
    components: {
      default: MobileSectionTitlePage,
      desktop: DesktopHome,
      mobile: MobileSectionTitlePage
    },
    meta: { withMenu: false, title: '区域标题' }
  }
]

const router = new VueRouter({
  mode: 'history',
  routes
})

// 全局路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta && to.meta.title) {
    document.title = to.meta.title
  }

  next()
})

export default router
