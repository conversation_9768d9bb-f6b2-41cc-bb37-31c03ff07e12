from django.db import models
from demo1_server.apps.user.models import User
from demo1_server.libs import human_datetime, ModelMixin
# Create your models here.


class UserLog(models.Model):
    status_choices = {
        (0, '增加'),
        (1, '删除'),
        (2, '修改')
    }
    user = models.ForeignKey(to=User, on_delete=models.DO_NOTHING, null=True)
    content = models.TextField()
    type = models.SmallIntegerField(default=2, choices=status_choices, verbose_name="类型")
    created_at = models.CharField(max_length=20, default=human_datetime)

    def get_type(self):
        return self.get_type_display()

    class Meta:
        db_table = 'userlog'
        ordering = ('-id',)