from django.http import JsonResponse
from django.test import TestCase

# Create your tests here.

#!/usr/bin/env python
# 指定脚本运行环境
# #!coding=utf-8
import datetime
import time
import sys

from django.db import connection
sys.path.insert(0, '../')

# 使用django配置文件进行设置
import os
if not os.getenv("DJANGO_SETTINGS_MODULE"):
    os.environ["DJANGO_SETTINGS_MODULE"] = "demo1_server.settings.dev"

# 让django初始化
import django
django.setup()

# 引入mqtt包
import paho.mqtt.client as mqtt
# 使用独立线程运行
from threading import Thread

import time
import json


# 建立mqtt连接
def on_connect(client, userdata, flag, rc):
    print("Connect with the result code " + str(rc))
    client.subscribe('testtopic/#', qos=0)

# 接收、处理mqtt消息
def on_message(client, userdata, msg):

    out = str(msg.payload.decode('utf-8'))
    print(msg.topic)
    out = json.loads(out)

    # 收到消息后执行任务
    if msg.topic == 'testtopic':
        print(out)

    cursor = connection.cursor()
    try:
        sql = "INSERT into app1_sensing_data (TEM,PH,O2,SALT,VOLTAGE,ACQUISITION_TIME,EQUIPMENT_id,CREATE_TIME,UPDATE_TIME) VALUES " \
              "(\"{tem}\",{ph},\"{o2}\",\"{salt}\",\"{voltage}\",\"{acquisition_time}\",\"{equipment_id}\",\"{time}\",\"{time}\");".format(tem=out["tem"],ph=out["ph"],o2=out["o2"],salt=out["salt"],voltage=out["voltage"],acquisition_time=out["acquisition_time"],equipment_id=out["equipment_id"],time=datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        cursor.execute(sql)
    except:
        print ("Error:写入数据库时发生错误")
    else:
        print("存入成功")
    # {"tem": "18.2", "ph": "7.1", "o2": "6.3", "salt": "", "voltage": "220", "acquisition_time": "2022-03-26 21:22:22",
    #  "equipment_id": "1"}





# mqtt客户端启动函数
def mqttfunction():
    global client
    # 使用loop_start 可以避免阻塞Django进程，使用loop_forever()可能会阻塞系统进程
    # client.loop_start()
    # client.loop_forever() 有掉线重连功能
    client.loop_forever(retry_first_connection=True)


client = mqtt.Client(client_id="test", clean_session=False)

# 启动函数
def mqtt_run():
    client.on_connect = on_connect
    client.on_message = on_message
    # 绑定 MQTT 服务器地址
    broker = '**************'
    # MQTT服务器的端口号
    client.connect(broker, 1883)
    client.username_pw_set('user', 'user')
    client.reconnect_delay_set(min_delay=1, max_delay=2000)
    # 启动
    mqttthread = Thread(target=mqttfunction)
    mqttthread.start()


# 启动 MQTT
# mqtt_run()


if __name__ == "__main__":
    mqtt_run()
