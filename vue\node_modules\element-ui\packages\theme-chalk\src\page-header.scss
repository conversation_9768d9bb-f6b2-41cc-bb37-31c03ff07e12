@import "mixins/mixins";
@import "common/var";

@include b(page-header) {
  display: flex;
  line-height: 24px;

  @include e(left) {
    display: flex;
    cursor: pointer;
    margin-right: 40px;
    position: relative;

    &::after {
      content: "";
      position: absolute;
      width: 1px;
      height: 16px;
      right: -20px;
      top: 50%;
      transform: translateY(-50%);
      background-color: $--border-color-base;
    }

    .el-icon-back {
      font-size: 18px;
      margin-right: 6px;
      align-self: center;
    }

    @include e(title) {
      font-size: 14px;
      font-weight: 500;
    }
  }

  @include e(content) {
    font-size: 18px;
    color: $--color-text-primary;
  }
}
