
from learning.models import BreedingBase
from learning import models
from django.utils.decorators import method_decorator
from django.views.decorators.cache import cache_page
from apscheduler.schedulers.background import BackgroundScheduler, BlockingScheduler
import time
import logging
import requests

from demo1_server.apps.log.models import UserLog
from demo1_server.apps.user.models import User
from django.views.generic import View
import json
from django.http import JsonResponse, HttpResponse
from django.db import connection


def queryDict(sql, params=None):
    '''
    查询结果返回字典类型数据
    :param sql:
    :param params:
    :return:
    '''
    st = time.time()
    with connection.cursor() as cursor:
        cursor.execute(sql, params=params)

        col_names = [desc[0] for desc in cursor.description]
        row = cursor.fetchall()
        rowList = []
        for list in row:
            tMap = dict(zip(col_names, list))
            rowList.append(tMap)

    logging.debug(f'{time.time() - st},{sql}')
    return rowList





class GetBreading_base(View):
    #@method_decorator(cache_page(60 * 8))
    def get(self, request):
        print(request.body)
        # rstdict = {}
        # cursor = connection.cursor()
        sql = "SELECT * from app1_breeding_base;"
        # cursor.execute(sql)
        ip_fw = queryDict(sql)
        baselist = []
        locationlist = []

        for item in ip_fw:
            list1 = []
            for a in str(item["BASE_LOCATION"]).split(","):
                list1.append(float(a))
            baselist.append(item)
            locationlist.append(list1)
        sql = "select app1_alarm_info.ID,app1_alarm_info.CREATE_TIME,app1_breeding_base.BASE_NAME,app1_equipment.EQUIPMENT_NAME,app1_alarm_info.Level,app1_alarm_info.TEM,app1_alarm_info.PH,app1_alarm_info.O2 from app1_alarm_info,app1_equipment,app1_breeding_base where app1_alarm_info.EQUIPMENT_id=app1_equipment.ID and app1_equipment.BACILITIES_id=app1_breeding_base.ID;"
        ip_fw = queryDict(sql)
        #print('CCCCCC')
        alertlist = []
        for item in ip_fw:
            # print(item["TEM"])
            dict1 = {}
            describe = str(item["BASE_NAME"]) + str(item["EQUIPMENT_NAME"]) + "TEM:" + str(
                item["TEM"]) + " " + "PH:" + str(item["PH"]) + " O2:" + str(item["O2"])
            dict1["describe"] = describe
            # print('DDDDDD')

            # 上海海洋大学养殖基地水质检测设备1TEM:36.60 PH:7.30 O2:6.20
            dict1["ID"] = item["ID"]
            dict1["Level"] = item["Level"]
            dict1["CREATE_TIME"] = item["CREATE_TIME"].strftime(
                "%Y-%m-%d %H:%M:%S")
            alertlist.append(dict1)
        # cursor = connection.cursor()

        #cursor = connection.cursor()
        sql = "SELECT TEM, HUMIDITY, ATM, RADIATION, LIGHT, RAINFALL, WIND_SPEED, VOLTAGE, WIND_DIRECTION FROM yuan_weather_data WHERE EQUIPMENT_id = '2006' ORDER BY update_time DESC LIMIT 1;"
        #cursor.execute(sql)
        #results = cursor.fetchall()
        results = queryDict(sql)
        tianqi = {}
        for row in results:
            tianqi["shidu"] = row['HUMIDITY']
            tianqi["wendu"] = row['TEM']
            tianqi["fengxiang"] = row['WIND_DIRECTION']
            tianqi["fengsu"] = row['WIND_SPEED']
            tianqi["qiya"] = row['ATM']
            tianqi["guangzhao"] = row['LIGHT']
            tianqi["jiangyuliang"] = row['RAINFALL']
        dict1 = {}
        dict1["baselist"] = baselist#基地信息表中的数据
        dict1["locationlist"] = locationlist#所有基地的位置
        dict1["alertlist"] = alertlist#报警信息表中的数据
        dict1["tianqi"] = tianqi#天气信息
        #print('GGGGGG')


        import datetime
        atime = ((datetime.datetime.now() -
                  datetime.timedelta(days=0)).strftime("%Y-%m-%d"))
        #cursor = connection.cursor()
        sql = "SELECT count(1) as aa from app1_alarm_info where CREATE_TIME like \"%{time}%\"".format(
            time=atime)
        #cursor.execute(sql)
        cc = queryDict(sql)
        #cursor = connection.cursor()
        sql = "SELECT count(1) as aa from app1_alarm_info ;"
        #cursor.execute(sql)
        dd = queryDict(sql)
        dict1["alert"] = {}
        dict1["alert"]["zongshu"] = dd[0]["aa"]
        dict1["alert"]["jintian"] = cc[0]["aa"]

        return JsonResponse({"codo": 200, "data": dict1})


# 获取设备号
class SelectEquipment(View):
    def post(self, request):
        rstdict = {}
        info2 = json.loads(request.body)
        equipmentID = info2["equipmentID"]
        v = request.GET.get('v', None)
        # 版本逻辑判断
        if v == '2.0':

            base_id = info2.get('base_id', None)
            if not base_id:
                error_json.update({'info': '基地号不能为空'})
                return JsonResponse(error_json)
            base = BreedingBase.objects.filter(ID=base_id).values()[0]
            if not base:
                error_json.update({'info': '基地不存在'})
                return JsonResponse(error_json)
            abbreviation = base['abbreviation']
        else:
            abbreviation = 'app1'

        # print("设备号")
        # print(equipmentID)
        # cursor = connection.cursor()
        sql = "select * from app1_equipment where ID=%s"
        sql = sql.replace('app1', abbreviation)
        equipment = queryDict(sql, equipmentID)
        if equipment and equipment[0]['EQUIPMENT_TYPE'] == '水质检测':
            sql = f"select * from app1_sensing_data where EQUIPMENT_id =%s ORDER BY ID desc limit 15"
            sql = sql.replace('app1', abbreviation)
            e_type = 1
        elif equipment and equipment[0]['EQUIPMENT_TYPE'] == '气象检测':
            sql = f"select * from app1_weather_data where EQUIPMENT_id =%s ORDER BY ID desc limit 15"
            sql = sql.replace('app1', abbreviation)
            e_type = 2
        else:
            return JsonResponse({{'state': 'error'}})

        # cursor.execute(sql)
        equipment_sensing_data = queryDict(sql, equipmentID)
        # print(equipment_sensing_data)
        dict3 = {}
        TEM = []
        PH = []
        O2 = []
        SALT = []
        CREATE_TIME = []
        HUMIDITY = []
        RAINFALL = []
        ATM = []

        for data in equipment_sensing_data:

            HUMIDITY.append(data.get("HUMIDITY", None))
            RAINFALL.append(data.get("RAINFALL", None))
            ATM.append(data.get("ATM", None))
            TEM.append(data.get("TEM", None))
            PH.append(data.get("PH", None))
            O2.append(data.get("O2", None))
            SALT.append(data.get("SALT", None))
            CREATE_TIME.append(data["CREATE_TIME"].strftime("%H:%M:%S"))

        dict3["TEM"] = TEM
        dict3["PH"] = PH
        dict3["O2"] = O2
        dict3["SALT"] = SALT
        dict3['HUMIDITY'] = HUMIDITY
        dict3['RAINFALL'] = RAINFALL
        dict3['ATM'] = ATM

        dict3["CREATE_TIME"] = CREATE_TIME
        #rstdict['data']["chuangan"] = dict3
        titlelist = dict3["CREATE_TIME"]
        shuiwenlist = dict3["TEM"]
        phlist = dict3["PH"]
        hanyanglist = dict3["O2"]

        HUMIDITY_list = dict3['HUMIDITY']
        ATM_list = dict3['ATM']
        RAINFALL_list = dict3['RAINFALL']

        # print("222222222222222222222")
        # print(phlist)

        sql = "SELECT * from app1_equipment where ID ={id}".format(
            id=equipmentID)
        sql = sql.replace('app1', abbreviation)
        # id=shebeilist[0]["ID"])
        # cursor.execute(sql)
        bbb = queryDict(sql)
        bbb = json.loads(bbb[0]["UANDL"])

        # 制作温度数据
        yuzhiyi = []
        yuzhier = []
        for i in range(0, 15):
            yuzhiyi.append(bbb["te"]["th"][0])
            yuzhier.append(bbb["te"]["th"][1])
        wendudict = {
            "x": titlelist[::-1],
            "min": bbb["te"]["co"][0],
            "max": bbb["te"]["co"][1],
            "y": [
                {
                    "name": "温度",
                    "type": "solid",
                    # 加颜色
                    "color": '#3c78d8',
                    "data": shuiwenlist[::-1]
                },
                {
                    "name": "温度上限值",
                    "type": "dotted",
                    # 加颜色
                    "color": '#eb0e2e',
                    # 此处将yuzhiyi改为yuzhier
                    "data": yuzhier
                },
                {
                    "name": "温度下限值",
                    "type": "dotted",
                    # 加颜色
                    "color": '#0bf00b',
                    # 此处将yuzhier改为yuzhiyi
                    "data": yuzhiyi
                },
            ],
        }

        hanyangdict = {}
        phdict = {}
        HUMIDITY_dict = {}
        ATM_dict = {}
        RAINFALL_dict = {}

        if e_type == 1:  # 水质设备
            # 制作溶氧数据
            yuzhiyi = []
            yuzhier = []
            for i in range(0, 15):
                u = l = 0
                yuzhiyi.append(bbb["o2"]["th"][0])
                yuzhier.append(bbb["o2"]["th"][1])
            hanyangdict = {
                "x": titlelist[::-1],
                "min": bbb["o2"]["co"][0],
                "max": bbb["o2"]["co"][1],
                "y": [
                    {
                        "name": "溶氧",
                        "type": "solid",
                        "color": '#3c78d8',
                        "data": hanyanglist[::-1]
                    },
                    {
                        "name": "溶氧上限值",
                        "type": "dotted",
                        "color": '#eb0e2e',
                        "data": yuzhier
                    },
                    {
                        "name": "溶氧下限值",
                        "type": "dotted",
                        "color": '#0bf00b',
                        "data": yuzhiyi
                    },
                ],
            }
            # 制作ph数据
            yuzhiyi = []
            yuzhier = []
            for i in range(0, 15):
                yuzhiyi.append(bbb["ph"]["th"][0])
                yuzhier.append(bbb["ph"]["th"][1])
            phdict = {
                "x": titlelist[::-1],
                "min": bbb["ph"]["co"][0],
                "max": bbb["ph"]["co"][1],
                "y": [
                    {
                        "name": "ph",
                        "type": "solid",
                        "color": '#3c78d8',
                        "data": phlist[::-1]
                    },
                    {
                        "name": "ph上限值",
                        "type": "dotted",
                        "color": '#eb0e2e',
                        "data": yuzhier
                    },
                    {
                        "name": "ph下限值",
                        "type": "dotted",
                        "color": '#0bf00b',
                        "data": yuzhiyi
                    },
                ],
            }

        elif e_type == 2:  # 气象设备
            # {"te": {"co": [-5,50], "th": [0,35]}, "humidity": {"co": [20,100], "th": [60,80]},"atm":{"co":[80,120],"th":[80,110]}, "rainfall": {"co": [0,50], "th": [0,200]}}

            yuzhiyi = []
            yuzhier = []
            for i in range(0, 15):
                yuzhiyi.append(bbb["humidity"]["th"][0])
                yuzhier.append(bbb["humidity"]["th"][1])

            HUMIDITY_dict = {
                "x": titlelist[::-1],
                "min": bbb["humidity"]["co"][0],
                "max": bbb["humidity"]["co"][1],
                "y": [
                    {
                        "name": "ph",
                        "type": "solid",
                        "color": '#3c78d8',
                        "data": HUMIDITY_list[::-1]
                    },
                    {
                        "name": "ph上限值",
                        "type": "dotted",
                        "color": '#eb0e2e',
                        "data": yuzhier
                    },
                    {
                        "name": "ph下限值",
                        "type": "dotted",
                        "color": '#0bf00b',
                        "data": yuzhiyi
                    },
                ],
            }
            yuzhiyi = []
            yuzhier = []
            for i in range(0, 15):
                yuzhiyi.append(bbb["atm"]["th"][0])
                yuzhier.append(bbb["atm"]["th"][1])
            ATM_dict = {
                "x": titlelist[::-1],
                "min": bbb["atm"]["co"][0],
                "max": bbb["atm"]["co"][1],
                "y": [
                    {
                        "name": "ph",
                        "type": "solid",
                        "color": '#3c78d8',
                        "data": ATM_list[::-1]
                    },
                    {
                        "name": "ph上限值",
                        "type": "dotted",
                        "color": '#eb0e2e',
                        "data": yuzhier
                    },
                    {
                        "name": "ph下限值",
                        "type": "dotted",
                        "color": '#0bf00b',
                        "data": yuzhiyi
                    },
                ],
            }
            yuzhiyi = []
            yuzhier = []
            for i in range(0, 15):
                yuzhiyi.append(bbb["rainfall"]["th"][0])
                yuzhier.append(bbb["rainfall"]["th"][1])
            RAINFALL_dict = {
                "x": titlelist[::-1],
                "min": bbb["rainfall"]["co"][0],
                "max": bbb["rainfall"]["co"][1],
                "y": [
                    {
                        "name": "ph",
                        "type": "solid",
                        "color": '#3c78d8',
                        "data": RAINFALL_list[::-1]
                    },
                    {
                        "name": "ph上限值",
                        "type": "dotted",
                        "color": '#eb0e2e',
                        "data": yuzhier
                    },
                    {
                        "name": "ph下限值",
                        "type": "dotted",
                        "color": '#0bf00b',
                        "data": yuzhiyi
                    },
                ],
            }

        dict1 = {}
        dict1["templist"] = []
        dict1["titlelist"] = titlelist[::-1]
        dict1["templist"].append(wendudict)
        dict1["templist"].append(hanyangdict)
        dict1["templist"].append(phdict)

        dict1["templist"].append(HUMIDITY_dict)
        dict1["templist"].append(ATM_dict)
        dict1["templist"].append(RAINFALL_dict)
        rstdict["data"] = {}
        rstdict["data"]["shuju"] = dict1
        return JsonResponse(rstdict)
        # return JsonResponse( rstdict)


error_json = {'code': 200, 'state': 'error'}


class equipment(View):
    #@method_decorator(cache_page(60 * 10))
    def get(self, request):
        print('home页面的数据自动加载结束1111111111')
        rstdict = {}
        itemlist = []
        # 获取url参数
        # info = request.GET#json.loads(request.body)
        # print("ssssssssssssssssssssssssss")
        # print(request.body)
        #info = json.loads(request.body.decode('utf-8'))
        base_id = request.GET.get("base_id")
        if not base_id:
            return JsonResponse({'erro':'bunenghw'})
        base = BreedingBase.objects.filter(ID=base_id).values()[0]
        if not base:
            error_json.update({'info': '基地不存在'})
            return JsonResponse(error_json)
        v = request.GET.get('v', None)
        if v == '2.0':
            abbreviation = base['abbreviation']
        else:
            abbreviation = 'app1'
        sql = "select * from app1_breeding_bacilities where BASE_id={id};".format(
            id=base_id)
        sql = sql.replace('app1', abbreviation)
        # cursor.execute(sql)
        sheshilist = queryDict(sql)

        # cursor = connection.cursor()
        print(abbreviation)
        print(base_id)
        sql = "select * from {abbreviation}_breeding_bacilities where BASE_id={id};".format(
            abbreviation=abbreviation, id=base_id)
        print(sql)
        shebeilist = queryDict(sql)
        print(shebeilist)
        bacilities_ids = []
        for bacilities_id in shebeilist:
            bacilities_ids.append(bacilities_id["ID"])
        bacilities_ids = tuple(bacilities_ids)
        # print(bacilities_ids)
        sql = "select * from {abbreviation}_equipment where BACILITIES_id in {bacilities_ids};".format(
            abbreviation=abbreviation, bacilities_ids=bacilities_ids)
        # cursor.execute(sql)
        shebeilist = queryDict(sql)
        # print(shebeilist)
        for shebei in shebeilist:
            sql = "select BACILITIES_NAME from {abbreviation}_breeding_bacilities where ID={bacilities_id};".format(
                abbreviation=abbreviation, bacilities_id=shebei["BACILITIES_id"])
            # cursor.execute(sql)
            shebei_jidiname = queryDict(sql)[0]["BACILITIES_NAME"]
            shebei["BACILITIES_NAME"] = shebei_jidiname
            if shebei['EQUIPMENT_TYPE'] == '水质检测':
                sql = 'select * from {abbreviation}_sensing_data where EQUIPMENT_id=%s order by UPDATE_TIME desc limit 0,1'.format(
                    abbreviation=abbreviation)
                sql1 = 'select * from {abbreviation}_sensing_data where EQUIPMENT_id=%s ORDER BY ID DESC LIMIT 1 OFFSET 1'.format(
                    abbreviation=abbreviation)

            elif shebei['EQUIPMENT_TYPE'] == '气象检测':
                sql = 'select * from {abbreviation}_weather_data where EQUIPMENT_id=%s order by UPDATE_TIME desc limit 0,1'.format(
                    abbreviation=abbreviation)
            else:
                shebei['Online_Voltage'] = '设备类型错误'
                continue  # 未知类型
            Voltage = queryDict(sql, shebei['ID'])
            Voltage1 = queryDict(sql1, shebei['ID'])
            #print("vovovovo")
            #print("Voltage:", Voltage)
            if Voltage:
                shebei['Online_Voltage'] = Voltage[0]['VOLTAGE']
                shebei['TEM'] = Voltage[0].get('TEM', '')
                shebei['PH'] = Voltage[0].get('PH', '')
                shebei['O2'] = Voltage[0].get('O2', '')
                shebei['SALT'] = Voltage[0].get('SALT', '')  # 盐度
                shebei['ORP'] = Voltage[0].get('ORP', '') if Voltage[0].get('ORP', '') else ''
                shebei['CON'] = Voltage[0].get('CONDUCTANCE', '') if Voltage[0].get('CONDUCTANCE', '') else ''  # 电导
                shebei['HUMIDITY'] = Voltage[0].get('HUMIDITY', '')  # 湿度
                shebei['RAINFALL'] = Voltage[0].get('RAINFALL', '')  # 降雨量
                shebei['ATM'] = Voltage[0].get('ATM', '')  # 大气压
                shebei['WIND_SPEED'] = Voltage[0].get('WIND_SPEED', '')  # 风速度
                shebei['WIND_DIRECTION'] = Voltage[0].get('WIND_DIRECTION', '')  # 风向
                shebei['RADIATION'] = Voltage[0].get('RADIATION', '')  # 辐射量
                shebei['LIGHT'] = Voltage[0].get('LIGHT', '')  # 光照
                shebei['ACQUISITION_TIME'] = Voltage[0].get('ACQUISITION_TIME', '')  # 采集时间

            else:
                shebei['Online_Voltage'] = '暂无实时电压'
            if Voltage1:
                shebei['O2_PREDICTION'] = Voltage1[0].get('O2_PREDICTION', '')
            else:
                shebei['Online_Voltage'] = '暂无实时电压'

        ip_fw = shebeilist
        # print(shebeilist)

        locationlist = []

        for item in ip_fw:
            list1 = []
            for a in str(item["EQUIPMENT_LOCATION"]).split(","):
                list1.append(float(a))
            locationlist.append(list1)

        # cursor = connection.cursor()
        #sql = "select app1_alarm_info.ID,app1_alarm_info.CREATE_TIME,app1_breeding_base.BASE_NAME,app1_equipment.EQUIPMENT_NAME,app1_alarm_info.Level,app1_alarm_info.TEM,app1_alarm_info.PH,app1_alarm_info.O2 from app1_alarm_info,app1_equipment,app1_breeding_base where app1_alarm_info.EQUIPMENT_id=app1_equipment.ID and app1_equipment.BACILITIES_id={id};".format(id=base_id)
        sql = "select app1_alarm_info.ID,app1_alarm_info.CREATE_TIME,app1_equipment.EQUIPMENT_NAME,app1_alarm_info.Level,app1_alarm_info.TEM,app1_alarm_info.PH,app1_alarm_info.O2 from app1_alarm_info,app1_equipment where app1_alarm_info.EQUIPMENT_id=app1_equipment.ID and app1_equipment.BACILITIES_id={id};".format(
            id=base_id)
        sql = sql.replace('app1', abbreviation)
        # cursor.execute(sql)
        ip_fw = queryDict(sql)
        alertlist = []

        for item in ip_fw:
            # print(item["TEM"])
            dict1 = {}
            describe = str(base['BASE_NAME']) + str(item["EQUIPMENT_NAME"]) + "TEM:" + str(
                item["TEM"]) + " " + "PH:" + str(item["PH"]) + " O2:" + str(item["O2"])
            dict1["describe"] = describe
            dict1["ID"] = item["ID"]
            dict1["Level"] = item["Level"]
            dict1["CREATE_TIME"] = item["CREATE_TIME"].strftime(
                "%Y-%m-%d %H:%M:%S")
            alertlist.append(dict1)
        # 设备说明信息
        # cursor = connection.cursor()
        sql = "select app1_equipment.ID,app1_equipment.MOBILITY,app1_equipment.VOLTAGE,app1_equipment.INTRODUCE from app1_equipment;".format(
            id=base_id)
        sql = sql.replace('app1', abbreviation)
        # cursor.execute(sql)
        shebei_in = queryDict(sql)
        inforlist = []

        for item in shebei_in:
            # print(item["TEM"])
            dicdic = {}
            de = "可移动性:" + str(item["MOBILITY"]) + " " + "电压:" + \
                str(item["VOLTAGE"]) + " 说明:" + str(item["INTRODUCE"])
            dicdic["de"] = de
            dicdic["ID"] = item["ID"]
            # dicdic["Level"] = item["Level"]
            # dicdic["CREATE_TIME"] = item["CREATE_TIME"].strftime("%Y-%m-%d %H:%M:%S")
            inforlist.append(dicdic)

 # 处理传感器信息表

        # 设置初始上下限
        sql1 = "SELECT * from app1_equipment where ID= {id}".format(id=1)
        sql = sql.replace('app1', abbreviation)
        # cursor.execute(sql1)
        bbb = queryDict(sql1)
        bbb = json.loads(bbb[0]["UANDL"])
        yuzhiyi = []
        yuzhier = []
        for i in range(0, 15):
            yuzhiyi.append(bbb["te"]["th"][0])
            yuzhier.append(bbb["te"]["th"][1])
        shangxian = yuzhier
        xiaxian = yuzhiyi

        sql = "select ID from app1_breeding_bacilities where BASE_id={id} limit 1".format(
            id=base_id)
        sql = sql.replace('app1', abbreviation)
        # cursor.execute(sql)
        first_bacilities_id = queryDict(sql)
        # print(first_bacilities_id[0]["ID"])
        sql = "select ID from app1_equipment where BACILITIES_id={id} limit 1".format(
            id=first_bacilities_id[0]["ID"])
        sql = sql.replace('app1', abbreviation)
        # cursor.execute(sql)
        first_equipment_id = queryDict(sql)

        sql = "select * from app1_sensing_data where EQUIPMENT_id ={id} ORDER BY ID desc limit 15 OFFSET 1".format(
            id=first_equipment_id[0]["ID"]) #第一个设备水质参数15条信息
        sql = sql.replace('app1', abbreviation)
        # cursor.execute(sql)
        first_equipment_sensing_data = queryDict(sql)
        # print(first_equipment_sensing_data)
        shebeiid = shebeilist[0]["ID"]
        # sql = "SELECT * from app1_sensing_data where EQUIPMENT_id ={id} ORDER BY ID desc limit 1;".format(id=shebeiid)
        # cursor.execute(sql)
        # chuangan = queryDict(sql)
        dict2 = {}
        TEM = []
        PH = []
        O2 = []
        SALT = []
        CON=[]
        CREATE_TIME = []
        for data in first_equipment_sensing_data:
            TEM.append(data["TEM"])
            PH.append(data["PH"])
            O2.append(data["O2"])
            SALT.append(data["SALT"])
            CON.append(data["SALT"])
            CREATE_TIME.append(data["CREATE_TIME"].strftime("%H:%M:%S"))
        # dict2["UP"] = shangxian
        # dict2["DOWN"] = xiaxian
        dict2["TEM"] = TEM
        dict2["PH"] = PH
        dict2["O2"] = O2
        dict2["SALT"] = SALT
        dict2["CON"] = CON
        dict2["CREATE_TIME"] = CREATE_TIME
        # print("000000000000000000000000000000")
        # print(dict2)

        # 设备参数信息改动
        sql = "SELECT any_value(ID),MAX(ID) AS new_id, GROUP_CONCAT(ID) from app1_sensing_data group by EQUIPMENT_id;"
        sql = sql.replace('app1', abbreviation)
        # any_value(ID)
        # cursor.execute(sql)
        chuangan1 = queryDict(sql)
        new_ids = []
        for new_id in chuangan1:
            new_ids.append(new_id["new_id"])
        new_ids = tuple(new_ids)
        # print(new_ids)
        sql = "select * from app1_sensing_data where ID in {new_id};".format(
            new_id=new_ids)
        sql = sql.replace('app1', abbreviation)
        # cursor.execute(sql)
        chuangan2 = queryDict(sql)
        # print(chuangan2)
        # 获取天气数据
        # cursor = connection.cursor()
        # sql = "select city from app1_breeding_base where ID={id};".format(
        #     id=base_id)
        # sql=sql.replace('app1',abbreviation)
        # cursor.execute(sql)
        city = base['city']  # queryDict(sql)
        cursor = connection.cursor()

        sql = "SELECT TEM, HUMIDITY, ATM, RADIATION, LIGHT, RAINFALL, WIND_SPEED, VOLTAGE, WIND_DIRECTION FROM yuan_weather_data WHERE EQUIPMENT_id = '2006' ORDER BY ID DESC LIMIT 1;"
        cursor.execute(sql)
        results = cursor.fetchall()
        tianqi = {}
        for row in results:
            tianqi["shidu"] = row[1]
            tianqi["wendu"] = row[0]
            tianqi["fengxiang"] = row[8]
            tianqi["fengsu"] = row[6]
            tianqi["qiya"] = row[2]
            tianqi["guangzhao"] = row[4]
            tianqi["jiangyuliang"] = row[5]

        # rstdict = {}
        rstdict['code'] = 200
        rstdict['data'] = {}
        rstdict['data']["shebeilist"] = shebeilist
        rstdict['data']["zuobiaoilist"] = locationlist
        rstdict['data']["alertlist"] = alertlist
        rstdict['data']["inforlist"] = inforlist
        rstdict['data']["chuangan"] = dict2
        rstdict['data']["chuangan2"] = chuangan2
        rstdict['data']["tianqi"] = tianqi

        # 用设备一初始化传感器参数
        wendudict2 = {
            "x": dict2["CREATE_TIME"][::-1],
            "min": bbb["te"]["co"][0],
            "max": bbb["te"]["co"][1],
            "y": [
                {
                    "name": "温度",
                    "type": "solid",
                    # 加颜色
                    "color": '#3c78d8',
                    "data": dict2["TEM"][::-1]
                },
                {
                    "name": "温度上限值",
                    "type": "dotted",
                    # 加颜色
                    "color": '#eb0e2e',
                    # 此处将yuzhiyi改为yuzhier
                    "data": yuzhier
                },
                {
                    "name": "温度下限值",
                    "type": "dotted",
                    # 加颜色
                    "color": '#0bf00b',
                    # 此处将yuzhier改为yuzhiyi
                    "data": yuzhiyi
                },
            ],
        }
        # 制作溶氧数据
        yuzhiyi = []
        yuzhier = []
        for i in range(0, 15):
            yuzhiyi.append(bbb["o2"]["th"][0])
            yuzhier.append(bbb["o2"]["th"][1])
        hanyangdict2 = {
            "x": dict2["CREATE_TIME"][::-1],
            "min": bbb["o2"]["co"][0],
            "max": bbb["o2"]["co"][1],
            "y": [
                {
                    "name": "溶氧",
                    "type": "solid",
                    "color": '#3c78d8',
                    "data": dict2["O2"][::-1]
                },
                {
                    "name": "溶氧上限值",
                    "type": "dotted",
                    "color": '#eb0e2e',
                    "data": yuzhier
                },
                {
                    "name": "溶氧下限值",
                    "type": "dotted",
                    "color": '#0bf00b',
                    "data": yuzhiyi
                },
            ],
        }
        # 制作ph数据
        yuzhiyi = []
        yuzhier = []
        for i in range(0, 15):
            yuzhiyi.append(bbb["ph"]["th"][0])
            yuzhier.append(bbb["ph"]["th"][1])
        phdict2 = {
            "x": dict2["CREATE_TIME"][::-1],
            "min": bbb["ph"]["co"][0],
            "max": bbb["ph"]["co"][1],
            "y": [
                {
                    "name": "ph",
                    "type": "solid",
                    "color": '#3c78d8',
                    "data": dict2["PH"][::-1]
                },
                {
                    "name": "ph上限值",
                    "type": "dotted",
                    "color": '#eb0e2e',
                    "data": yuzhier
                },
                {
                    "name": "ph下限值",
                    "type": "dotted",
                    "color": '#0bf00b',
                    "data": yuzhiyi
                },
            ],
        }
        dict5 = {}
        dict5["templist"] = []
        # dict5["titlelist"] = titlelist[::-1]
        dict5["templist"].append(wendudict2)
        dict5["templist"].append(hanyangdict2)
        dict5["templist"].append(phdict2)
        rstdict["data"]["shuju2"] = dict5
        rstdict["data"]['sheshilist'] = sheshilist
        return JsonResponse(rstdict)


# class doctor_information_management(View):
#     def post(self, request):
#         itemlist = []
#         # 获取url参数
#         data = json.loads(request.body)
#         limit = data.get('limit')
#         page = data.get('page')
#         queryset_fw = models.doctor_information_management.objects.order_by(
#             '-id')[page:][:limit]
#         queryset_fw_count = models.doctor_information_management.objects.order_by(
#             '-id').count()
#         for i in queryset_fw:
#             itemdict = {}
#             itemdict['id'] = i.id
#             itemdict['number'] = i.number
#             itemdict['gender'] = i.gender
#             itemdict['title'] = i.title
#             itemdict['post'] = i.post
#             itemdict['category'] = i.category
#             itemdict['date_birth'] = i.date_birth
#             itemdict['working_date'] = i.working_date
#             itemlist.append(itemdict)
#         rstdict = {}
#         rstdict['code'] = 200
#         rstdict['data'] = {}
#         rstdict['data']['total'] = queryset_fw_count
#         rstdict['data']['items'] = itemlist
#         # print(rstdict)
#         return JsonResponse(rstdict)

#     def put(self, request):
#         nickname = User.objects.values(
#             'nickname').filter(id=request.user.id).first()
#         nickname = nickname["nickname"]
#         data = json.loads(request.body)
#         number = data.get('number')
#         gender = data.get('gender')
#         title = data.get('title')
#         post = data.get('post')
#         category = data.get('category')
#         date_birth = data.get('date_birth')
#         working_date = data.get('working_date')
#         try:
#             obj = models.doctor_information_management(number=number, gender=gender, title=title,
#                                                        post=post, category=category, date_birth=date_birth,
#                                                        working_date=working_date,)
#             obj.save()

#         except Exception as e:
#             # logger.error(e)
#             print(e)
#             return JsonResponse({
#                 "code": 200,
#                 'status': '1',
#                 "result": "添加失败"
#             })
#         # print(data)

#         return JsonResponse({
#             "code": 200,
#             'status': '0',
#             "result": "添加成功",
#         })

#     def delete(self, request, *args, **kwargs):
#         nickname = User.objects.values(
#             'nickname').filter(id=request.user.id).first()
#         nickname = nickname["nickname"]
#         cid = request.GET.get("id")
#         obj = models.doctor_information_management.objects.filter(id=cid)
#         if obj:
#             obj.delete()
#             return JsonResponse({
#                 "code": 200,
#                 'status': '0',
#                 "result": "删除成功"
#             })
#         else:
#             return JsonResponse({
#                 "code": 200,
#                 'status': '1',
#                 "result": "删除失败"
#             })

#     def patch(self, request):
#         nickname = User.objects.values(
#             'nickname').filter(id=request.user.id).first()
#         nickname = nickname["nickname"]
#         data = json.loads(request.body)
#         category = data.get('category')
#         id = data.get('id')
#         data = json.loads(request.body)
#         number = data.get('number')
#         gender = data.get('gender')
#         title = data.get('title')
#         post = data.get('post')
#         category = data.get('category')
#         date_birth = data.get('date_birth')
#         working_date = data.get('working_date')
#         obj = models.doctor_information_management.objects.filter(id=id)
#         if obj:
#             obj.update(number=number, gender=gender, title=title,
#                        post=post, category=category, date_birth=date_birth,
#                        working_date=working_date),

#             return JsonResponse({"code": 200, "data": "success"})
#         else:
#             return JsonResponse({"code": 200, "data": "None type object"})


# class charge_information_management(View):
#     def post(self, request):
#         itemlist = []
#         # 获取url参数
#         data = json.loads(request.body)
#         limit = data.get('limit')
#         page = data.get('page')
#         queryset_fw = models.charge_information_management.objects.order_by(
#             '-id')[page:][:limit]
#         queryset_fw_count = models.charge_information_management.objects.order_by(
#             '-id').count()
#         for i in queryset_fw:
#             itemdict = {}
#             itemdict['id'] = i.id
#             itemdict['category'] = i.category
#             itemdict['name'] = i.name
#             itemdict['bed_number'] = i.bed_number
#             itemdict['unit_price'] = i.unit_price
#             itemdict['number'] = i.number
#             itemdict['amount_money'] = i.amount_money
#             itemdict['date'] = i.date
#             itemlist.append(itemdict)
#         rstdict = {}
#         rstdict['code'] = 200
#         rstdict['data'] = {}
#         rstdict['data']['total'] = queryset_fw_count
#         rstdict['data']['items'] = itemlist
#         # print(rstdict)
#         return JsonResponse(rstdict)

#     def put(self, request):
#         nickname = User.objects.values(
#             'nickname').filter(id=request.user.id).first()
#         nickname = nickname["nickname"]
#         data = json.loads(request.body)
#         category = data.get('category')
#         name = data.get('name')
#         bed_number = data.get('bed_number')
#         unit_price = data.get('unit_price')
#         number = data.get('number')
#         amount_money = data.get('amount_money')
#         date = data.get('date')
#         try:
#             obj = models.charge_information_management(category=category, name=name, bed_number=bed_number,
#                                                        unit_price=unit_price, number=number, amount_money=amount_money,
#                                                        date=date,)
#             obj.save()

#         except Exception as e:
#             # logger.error(e)
#             print(e)
#             return JsonResponse({
#                 "code": 200,
#                 'status': '1',
#                 "result": "添加失败"
#             })
#         # print(data)

#         return JsonResponse({
#             "code": 200,
#             'status': '0',
#             "result": "添加成功",
#         })

#     def delete(self, request, *args, **kwargs):
#         nickname = User.objects.values(
#             'nickname').filter(id=request.user.id).first()
#         nickname = nickname["nickname"]
#         cid = request.GET.get("id")
#         obj = models.charge_information_management.objects.filter(id=cid)
#         if obj:
#             obj.delete()
#             return JsonResponse({
#                 "code": 200,
#                 'status': '0',
#                 "result": "删除成功"
#             })
#         else:
#             return JsonResponse({
#                 "code": 200,
#                 'status': '1',
#                 "result": "删除失败"
#             })

#     def patch(self, request):
#         nickname = User.objects.values(
#             'nickname').filter(id=request.user.id).first()
#         nickname = nickname["nickname"]
#         data = json.loads(request.body)
#         category = data.get('category')
#         id = data.get('id')
#         category = data.get('category')
#         name = data.get('name')
#         bed_number = data.get('bed_number')
#         unit_price = data.get('unit_price')
#         number = data.get('number')
#         amount_money = data.get('amount_money')
#         date = data.get('date')
#         obj = models.charge_information_management.objects.filter(id=id)
#         if obj:
#             obj.update(category=category, name=name, bed_number=bed_number,
#                        unit_price=unit_price, number=number, amount_money=amount_money,
#                        date=date, ),

#             return JsonResponse({"code": 200, "data": "success"})
#         else:
#             return JsonResponse({"code": 200, "data": "None type object"})

from datetime import datetime
class cultiver(View):
    def post(self, request):
        itemlist = []
        # 获取url参数
        info = json.loads(request.body)

        base_id = info["base_id"]
        v = request.GET.get('v', None)
        # 版本逻辑判断
        if v == '2.0':

            # base_id=info.get('base_id',None)
            if not base_id:
                error_json.update({'info': '基地号不能为空'})
                return JsonResponse(error_json)
            base = BreedingBase.objects.filter(ID=base_id).values()[0]
            if not base:
                error_json.update({'info': '基地不存在'})
                return JsonResponse(error_json)
            abbreviation = base['abbreviation']
        else:
            abbreviation = 'app1'

        cursor = connection.cursor()
        print(abbreviation)
        sql = "select * from app1_equipment where BACILITIES_id={id};".format(
            id=base_id)
        sql = sql.replace('app1', abbreviation)
        cursor.execute(sql)
        ip_fw = queryDict(sql)
        # print(ip_fw)
        shebeilist = queryDict(sql)
        locationlist = []

        for item in ip_fw:
            list1 = []
            for a in str(item["EQUIPMENT_LOCATION"]).split(","):
                list1.append(float(a))
            locationlist.append(list1)

        # 处理传感器信息表
        # shebeiid=shebeilist[0]["ID"]
        # sql = "SELECT * from app1_sensing_data where EQUIPMENT_id ={id} ORDER BY ID desc limit 1;".format(id=shebeiid)
        # cursor.execute(sql)
        # chuangan = queryDict(sql)

        cursor = connection.cursor()
        sql = "SELECT CONCAT_WS('',TYPEI,WORKER,QUANTITY) AS content,CREATE_TIME as time  FROM app1_fishing ORDER BY ID desc LIMIT 10;"
        sql = sql.replace('app1', abbreviation)
        cursor.execute(sql)
        loglist = queryDict(sql)


        sql = "select city from app1_breeding_base where ID={id};".format(
            id=base_id)
        # sql = sql.replace('app1', abbreviation)
        # cursor.execute(sql)
        city = queryDict(sql)
        cursor = connection.cursor()
        sql = "SELECT TEM, HUMIDITY, ATM, RADIATION, LIGHT, RAINFALL, WIND_SPEED, VOLTAGE, WIND_DIRECTION FROM yuan_weather_data WHERE EQUIPMENT_id = '2006'"
        cursor.execute(sql)
        results = cursor.fetchall()
        tianqi = {}
        for row in results:
            tianqi["shidu"] = row[1]
            tianqi["wendu"] = row[0]
            tianqi["fengxiang"] = row[8]
            tianqi["fengsu"] = row[6]
            tianqi["qiya"] = row[2]
            tianqi["guangzhao"] = row[4]
            tianqi["jiangyuliang"] = row[5]

        rstdict = {}
        rstdict['code'] = 200
        rstdict['data'] = {}
        rstdict['data']["shebeilist"] = shebeilist
        rstdict['data']["zuobiaoilist"] = locationlist
        rstdict['data']["loglist"] = loglist
        rstdict['data']["tianqi"] = tianqi
        return JsonResponse(rstdict)

    def put(self, request):#直接升级为了V2

        nickname = User.objects.values('nickname').filter(id=request.user.id).first()
        nickname = nickname["nickname"]
        data = json.loads(request.body)
        time = str(data["time"]).replace("%20", "  ")
        base_id=data.get('base_id')
        if not base_id:
            error_json.update({'info': '基地号不能为空'})
            return JsonResponse(error_json)
        base = BreedingBase.objects.filter(ID=base_id).values()[0]
        if not base:
            error_json.update({'info': '基地不存在'})
            return JsonResponse(error_json)
        abbreviation = base['abbreviation']

        cursor = connection.cursor()
        sql = "INSERT into app1_fishing (`TYPE`,QUANTITY,ACQUISITION_TIME,BACILITIES_id,TYPEI,WORKER,NOTE,CREATE_TIME,UPDATE_TIME) VALUES " \
              "(\"{zhonglei}\",{shuliang},\"{time}\",\"{sheshiid}\",\"{action}\",\"{name}\",\"{remark}\",\"{time}\",\"{time}\");".format(
                  shuliang=data["num"], time=time, zhonglei=data["zhonglei"], sheshiid=data["sheshiid"], remark=data["remark"], name=data["name"], action=data["action"])
        sql=sql.replace('app1',abbreviation)
        cursor.execute(sql)
        # cursor = connection.cursor()
        sql = "SELECT CONCAT_WS('',TYPEI,WORKER,QUANTITY) AS content,CREATE_TIME  as time FROM app1_fishing ORDER BY ID desc LIMIT 10;"
        sql=sql.replace('app1',abbreviation)
        # cursor.execute(sql)
        ip_fw = queryDict(sql)

        return JsonResponse({
            "code": 200,
            'status': '0',
            "result": "添加成功",
            "data": {"loglist": ip_fw}
        })


class caicon(View):
    def post(self, request):
        itemlist = []
        # 获取url参数
        info = json.loads(request.body)
        # cursor = connection.cursor()
        base_id=info.get("base_id",None)
        if not base_id:
            result = "基地id不能为空"
            error_json.update({'info': 'result'})
            return JsonResponse(error_json)
        else:
            base = BreedingBase.objects.filter(ID=base_id).values()[0]
            if not base:
                error_json.update({'info': '基地不存在'})
                return JsonResponse(error_json)

            abbreviation = base['abbreviation']
        sql = "SELECT * from app1_equipment where EQUIPMENT_LOCATION like \"%{aa}%\"".format(
            aa=info["zuobiao"][0])
        sql= sql.replace('app1',abbreviation)
        # cursor.execute(sql)
        ip_fw = queryDict(sql)
        rstdict = {}
        rstdict['code'] = 200
        rstdict['data'] = ip_fw[0]["EQUIPMENT_TYPE"]
        # rstdict['data'] = ip_fw
        return JsonResponse(rstdict)
        # base_id=info["base_id"]
        # cursor = connection.cursor()
        # sql = "select * from app1_equipment where BACILITIES_id={id};".format(id=base_id)
        # cursor.execute(sql)
        # ip_fw = queryDict(sql)
        # shebeilist = queryDict(sql)
        # locationlist = []
        #
        # for item in ip_fw:
        #     list1 = []
        #     for a in str(item["EQUIPMENT_LOCATION"]).split(","):
        #         list1.append(float(a))
        #     locationlist.append(list1)
        #
        #
        #
        # ##处理传感器信息表
        # # shebeiid=shebeilist[0]["ID"]
        # # sql = "SELECT * from app1_sensing_data where EQUIPMENT_id ={id} ORDER BY ID desc limit 1;".format(id=shebeiid)
        # # cursor.execute(sql)
        # # chuangan = queryDict(sql)
        #
        # cursor = connection.cursor()
        # sql = "SELECT CONCAT_WS('',TYPEI,WORKER,QUANTITY) AS content,CREATE_TIME as time  FROM app1_fishing ORDER BY ID desc LIMIT 10;"
        # cursor.execute(sql)
        # loglist = queryDict(sql)
        #
        #
        # rstdict = {}
        # rstdict['code'] = 200
        # rstdict['data'] = {}
        # rstdict['data']["shebeilist"]=shebeilist
        # rstdict['data']["zuobiaoilist"]=locationlist
        # rstdict['data']["loglist"]=loglist
        #
        #
        #
        # # print(rstdict)

        # return JsonResponse(rstdict)

class GetCamera(View):
    def get(self, request):
        base_id = request.GET.get("base_id")  # 基地id
        print(base_id)
        if not base_id:
            return JsonResponse({'erro': 'bunenghw'})
        base = BreedingBase.objects.filter(ID=base_id).values()[0]

        if not base:
            error_json.update({'info': '基地不存在'})
            return JsonResponse(error_json)

        abbreviation = base['abbreviation']
        # 构造查询 SQL 语句
        with connection.cursor() as cursor:
            cursor.execute(
                """
                SELECT VIDEO_ADDRESS,Serial_Number,STATE,CAMERA_NAME,CAMERA_LOCATION
                FROM `{abbreviation}_camera`
                """.format(abbreviation=abbreviation)
            )
            item_data = cursor.fetchall() #读取数据

        item_list = []
        for item in item_data:
            item_dict = {
                'video_address': item[0],
                'serial_number': item[1],
                'state': item[2],
                'camera_name': item[3],
                'camera_location': item[4],
            }
            item_list.append(item_dict)
        print("location")
        print(item_list)
        return JsonResponse({'item_list':item_list},safe=False)


class GetFeeder_sensing(View):
    def get(self, request):
        cursor = connection.cursor()
        sql1 = "SELECT * from yuan_feeder_sensing;"
        cursor.execute(sql1)
        ip_fw1 = queryDict(sql1)

        sql2 = "SELECT * from yuan_feeder_control_data;"
        cursor.execute(sql2)
        ip_fw2 = queryDict(sql2)
        sql3 = "SELECT * from yuan_record_data;"
        cursor.execute(sql3)
        ip_fw3 = queryDict(sql3)

        return JsonResponse({'res1':ip_fw1,'res2': ip_fw2,'res3': ip_fw3})

    def post(self, request):


        if request.method == 'POST':

            try:
                data = json.loads(request.body)
            except json.JSONDecodeError:
                return JsonResponse({'message': 'Invalid JSON data'}, status=400)
            self.save_to_control(data)
            self.save_to_record(data)
            equ_id = data.get('equ_id')
            print(equ_id)
            if equ_id == 1:
                mqtt_topic = "yuan/feeder/1/setctrl"
            elif equ_id == 2:
                mqtt_topic = "yuan/feeder/2/setctrl"
            elif equ_id == 3:
                mqtt_topic = "yuan/feeder/3/setctrl"

            self.mqtt_ws_client.publish_mqtt(mqtt_topic, data)

            return JsonResponse({'setting': data, 'code': 200})

    def save_to_control(self, data):
        cursor = connection.cursor()


        sql = """
                UPDATE yuan_feeder_control_data
                SET TOULIAOKOU1_WEIGHT = %(S1F)s,
                    TOULIAOKOU2_WEIGHT = %(S2F)s,
                    TOULIAOKOU3_WEIGHT = %(S3F)s,
                    TOULIAOKOU4_WEIGHT = %(S4F)s,
                    FEED_TIME1 = %(S1TM)s,
                    FEED_TIME2 = %(S2TM)s,
                    FEED_TIME3 = %(S3TM)s,
                    FEED_TIME4 = %(S4TM)s,
                    FEED_TIME5 = %(S5TM)s,
                    FEED_TIME6 = %(S6TM)s,
                    FEED_TIME1_STAT = %(S1TE)s,
                    FEED_TIME2_STAT = %(S2TE)s,
                    FEED_TIME3_STAT = %(S3TE)s,
                    FEED_TIME4_STAT = %(S4TE)s,
                    FEED_TIME5_STAT = %(S5TE)s,
                    FEED_TIME6_STAT = %(S6TE)s,
                    Acquisition_time = %(acq_time)s
                WHERE EQUIPMENT_id = %(equ_id)s;
            """

        cursor.execute(sql, data)
        connection.commit()
        return JsonResponse({'code': 200, 'message': '写入成功'})

    def save_to_record(self, data):
        cursor = connection.cursor()
        sql = """
            INSERT INTO yuan_record_data (EQUIPMENT_id, TOUERJI1_FEED, TOUERJI2_FEED, TOUERJI3_FEED, TOUERJI4_FEED,
                                          TIME1, TIME2, TIME3, TIME4, TIME5, TIME6, TIME1_LIAOKOU_STAT, TIME2_LIAOKOU_STAT, TIME3_LIAOKOU_STAT,
                                          TIME4_LIAOKOU_STAT, TIME5_LIAOKOU_STAT, TIME6_LIAOKOU_STAT, CHULIAOKOU1_FEED_DAY, CHULIAOKOU2_FEED_DAY,
                                          CHULIAOKOU3_FEED_DAY, CHULIAOKOU4_FEED_DAY, acq_time)
            VALUES (%(equ_id)s, %(S1F)s, %(S2F)s, %(S3F)s, %(S4F)s, %(S1TM)s, %(S2TM)s, %(S3TM)s, %(S4TM)s, %(S5TM)s,
                    %(S6TM)s, %(S1TE)s, %(S2TE)s, %(S3TE)s, %(S4TE)s, %(S5TE)s, %(S6TE)s, %(S1F)s, %(S2F)s, %(S3F)s, %(S4F)s, %(acq_time)s);
        """
        cursor.execute(sql, data)
        connection.commit()
        return JsonResponse({'code': 200, 'message': '写入成功'})

class Getpeoplefeed(View):
    def get(self, request):
        base_id = request.GET.get("base_id")  # 基地id
        print(base_id)
        if not base_id:
            return JsonResponse({'erro': 'bunenghw'})
        base = BreedingBase.objects.filter(ID=base_id).values()[0]

        if not base:
            error_json.update({'info': '基地不存在'})
            return JsonResponse(error_json)

        abbreviation = base['abbreviation']
        # 构造查询 SQL 语句
        with connection.cursor() as cursor:
            cursor.execute(
                """
                SELECT QUANTITY
                      FROM {abbreviation}_fishing 
                      WHERE TYPEI = '投饲料' 
                      AND CREATE_TIME >= CURDATE() 
                      AND CREATE_TIME < CURDATE() + INTERVAL 1 DAY 
                      ORDER BY ID DESC 
                      LIMIT 14;
                """.format(abbreviation=abbreviation)
            )
            item_data = cursor.fetchall() #读取数据
            print(item_data)
        # total_quantity = sum(item['QUANTITY'] for item in item_data) if item_data  else 0
        total_quantity = sum(item[0] for item in item_data)
        print(total_quantity)
        return JsonResponse({'total_quantity':total_quantity},safe=False)


class WeatherView(View):
    def get(self, request):
        city_code = '310115'  # 示例：上海市浦东新区的城市编码
        key = '817eca6e2f2d8e6543a9d6c2a80db99e'  # 替换为你的高德API Key
        extensions = 'base'  # 获取实况天气
        output = 'JSON'  # 返回 JSON 格式数据

        # 构造请求 URL
        url = f'https://restapi.amap.com/v3/weather/weatherInfo?key={key}&city={city_code}&extensions={extensions}&output={output}'

        # 风向转化为角度值的字典
        wind_direction_map = {
            '北': 0,
            '东北': 45,
            '东': 90,
            '东南': 135,
            '南': 180,
            '西南': 225,
            '西': 270,
            '西北': 315
        }

        try:
            # 发送 GET 请求
            logging.debug(f"Sending request to: {url}")
            response = requests.get(url)
            # 判断响应状态
            if response.status_code == 200:
                data = response.json()
                # 检查数据的 status 是否为 '1'（表示请求成功）
                if data.get('status') == '1':
                    # 获取实时天气数据
                    weather_info = data.get('lives', [])[0]  # 获取第一个城市的天气信息

                    # 处理风向转换为角度
                    wind_direction = weather_info.get('winddirection', '')
                    wind_direction_angle = wind_direction_map.get(wind_direction, None)

                    # 构建返回的天气数据
                    result = {
                        "temperature": float(weather_info.get('temperature', 0)),  # 温度
                        "humidity": float(weather_info.get('humidity', 0)),  # 湿度
                        "wind_direction": wind_direction_angle,  # 风向角度
                        "wind_power": weather_info.get('windpower', ''),  # 风力
                        "guangzhao": float(weather_info.get('guangzhao', 0)) if weather_info.get('guangzhao', 0) != 0 else '-',  # 光照
                        "precipitation": float(weather_info.get('precipitation', 0)) if weather_info.get('precipitation', 0) != 0 else '-',  # 降水量
                        "pressure": float(weather_info.get('pressure', 0)) if weather_info.get('pressure', 0) != 0 else '-',  # 气压
                    }
                    print(result)
                    # 返回处理后的天气数据
                    return JsonResponse(result)
                else:
                    # 如果状态不为 '1'，返回错误信息
                    return JsonResponse({'error': data.get('info')}, status=500)
            else:
                # 请求失败，返回 HTTP 错误码
                return JsonResponse({'error': '请求失败'}, status=response.status_code)

        except requests.exceptions.RequestException as e:
            return JsonResponse({'error': f'请求异常: {str(e)}'}, status=500)

