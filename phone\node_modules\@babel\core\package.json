{"_args": [["@babel/core@7.27.1", "F:\\桌面\\上海海洋大屏2期\\phone"]], "_development": true, "_from": "@babel/core@7.27.1", "_id": "@babel/core@7.27.1", "_inBundle": false, "_integrity": "sha512-IaaGWsQqfsQWVLqMn9OB92MNN7zukfVA4s7KKAI0KfrrDsZ0yhi5uV4baBuLuN7n3vsZpwP8asPPcVwApxvjBQ==", "_location": "/@babel/core", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@babel/core@7.27.1", "name": "@babel/core", "escapedName": "@babel%2fcore", "scope": "@babel", "rawSpec": "7.27.1", "saveSpec": null, "fetchSpec": "7.27.1"}, "_requiredBy": ["/@vue/babel-preset-app", "/@vue/cli-plugin-babel"], "_resolved": "https://registry.npmjs.org/@babel/core/-/core-7.27.1.tgz", "_spec": "7.27.1", "_where": "F:\\桌面\\上海海洋大屏2期\\phone", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "browser": {"./lib/config/files/index.js": "./lib/config/files/index-browser.js", "./lib/config/resolve-targets.js": "./lib/config/resolve-targets-browser.js", "./lib/transform-file.js": "./lib/transform-file-browser.js", "./src/config/files/index.ts": "./src/config/files/index-browser.ts", "./src/config/resolve-targets.ts": "./src/config/resolve-targets-browser.ts", "./src/transform-file.ts": "./src/transform-file-browser.ts"}, "bugs": {"url": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20core%22+is%3Aopen"}, "dependencies": {"@ampproject/remapping": "^2.2.0", "@babel/code-frame": "^7.27.1", "@babel/generator": "^7.27.1", "@babel/helper-compilation-targets": "^7.27.1", "@babel/helper-module-transforms": "^7.27.1", "@babel/helpers": "^7.27.1", "@babel/parser": "^7.27.1", "@babel/template": "^7.27.1", "@babel/traverse": "^7.27.1", "@babel/types": "^7.27.1", "convert-source-map": "^2.0.0", "debug": "^4.1.0", "gensync": "^1.0.0-beta.2", "json5": "^2.2.3", "semver": "^6.3.1"}, "description": "Babel compiler core.", "devDependencies": {"@babel/helper-transform-fixture-test-runner": "^7.27.1", "@babel/plugin-syntax-flow": "^7.27.1", "@babel/plugin-transform-flow-strip-types": "^7.27.1", "@babel/plugin-transform-modules-commonjs": "^7.27.1", "@babel/preset-env": "^7.27.1", "@babel/preset-typescript": "^7.27.1", "@jridgewell/trace-mapping": "^0.3.25", "@types/convert-source-map": "^2.0.0", "@types/debug": "^4.1.0", "@types/resolve": "^1.3.2", "@types/semver": "^5.4.0", "rimraf": "^3.0.0", "ts-node": "^11.0.0-beta.1"}, "engines": {"node": ">=6.9.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/babel"}, "homepage": "https://babel.dev/docs/en/next/babel-core", "keywords": ["6to5", "babel", "classes", "const", "es6", "harmony", "let", "modules", "transpile", "transpiler", "var", "babel-core", "compiler"], "license": "MIT", "main": "./lib/index.js", "name": "@babel/core", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-core"}, "type": "commonjs", "version": "7.27.1"}