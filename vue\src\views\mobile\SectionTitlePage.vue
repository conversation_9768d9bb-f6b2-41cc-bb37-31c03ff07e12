<template>
  <div class="section-title-page">
    <van-nav-bar :title="deviceName" left-arrow @click-left="onClickLeft" class="device-nav" />
    <div class="monitor-content">
      <div class="monitor-placeholder" id="video-container" ref="videoContainer">
        <p v-if="!player">{{ loadingText }}</p>
      </div>
    </div>
  </div>
</template>

<script>
// import SectionTitle from '@/components/SectionTitle.vue' // SectionTitle 未在 template 中使用，若确实不用可移除
import axios from 'axios'
import EZUIKit from 'ezuikit-js';
import { mapState, mapGetters } from 'vuex';

export default {
  name: 'SectionTitlePage',
  // components: { SectionTitle }, // SectionTitle 未在 template 中使用
  data() {
    return {
      jktokens: {
        value: ''
      },
      player: null,
      currentVideoSerial: '', // 添加字段记录当前播放的视频序列号
      loadingText: '准备加载监控...'
    }
  },

  computed: {
    ...mapGetters('device', ['deviceInfo']),
    deviceName() {
      return this.deviceInfo.name || '监控详情'
    },
    cameraId() {
      return this.deviceInfo.id || ''
    }
  },

  watch: {
    deviceInfo: {
      handler(newDeviceInfo) {
        // 仅当 newDeviceInfo 存在且有效时处理
        if (newDeviceInfo && newDeviceInfo.fullDeviceData) {
          console.log("设备信息变化，准备更新播放器:", newDeviceInfo);
          
          // 在处理新的视频前，确保销毁旧的播放器
          this.destroyCurrentPlayer();
          
          // 使用 nextTick 确保 DOM 已更新
          this.$nextTick(() => {
            this.processItemDataAndInitPlayer(encodeURIComponent(JSON.stringify(newDeviceInfo.fullDeviceData)));
          });
        }
      },
      immediate: true,
      deep: true
    }
  },

  mounted() {
    if (!this.jktokens.value) {
      this.getToken();
    }
  },

  beforeDestroy() { // Vue 2 生命周期钩子
    console.log("组件即将卸载，销毁播放器...");
    this.destroyCurrentPlayer();
  },

  methods: {
    // 新增方法专门用于销毁播放器
    destroyCurrentPlayer() {
      if (this.player) {
        console.log("销毁播放器实例:", this.player);
        try {
          this.player.stop();
          this.player.destroy();
          console.log("播放器实例销毁成功");
        } catch (e) {
          console.error("销毁播放器实例时发生错误:", e);
        } finally {
          this.player = null;
          this.currentVideoSerial = '';
        }
        
        // 更新加载文本
        this.loadingText = '准备加载监控...';
      }
    },
    
    async getToken() {
      try {
        const appKey = '3d0be5dc16b846e58ba2e4efb80d6d7f'; // 替换为你的appKey  
        const appSecret = '1d040ec6b1a4d12061fa97ef21987942'; // 替换为你的appSecret  

        const response = await axios.post('https://open.ys7.com/api/lapp/token/get',
          `appKey=${appKey}&appSecret=${appSecret}`,
          {
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded'
            }
          }
        );
        this.jktokens.value = response.data.data.accessToken;
        console.log('Token获取成功:', this.jktokens.value);
        
        // Token 获取成功后，检查当前是否有设备信息并尝试初始化播放器
        if (this.deviceInfo && this.deviceInfo.fullDeviceData) {
          console.log("Token获取成功，处理设备信息:", this.deviceInfo);
          this.$nextTick(() => {
            this.processItemDataAndInitPlayer(encodeURIComponent(JSON.stringify(this.deviceInfo.fullDeviceData)));
          });
        } else {
          console.log("Token获取成功，但当前无设备信息。");
          this.loadingText = '等待设备信息...';
        }
      } catch (error) {
        console.error('获取Token失败:', error);
        this.loadingText = '获取播放授权失败，请稍后重试。';
      }
    },

    processItemDataAndInitPlayer(itemDataString) {
      if (!itemDataString) {
        console.warn("processItemDataAndInitPlayer 调用时 itemDataString 为空。");
        this.loadingText = '无法获取摄像头信息，请返回重试';
        return;
      }

      try {
        const fullDeviceData = JSON.parse(decodeURIComponent(itemDataString));
        console.log("解析设备数据成功:", fullDeviceData);
        
        if (fullDeviceData && fullDeviceData.video_address && fullDeviceData.serial_number) {
          // 如果当前已经在播放相同序列号的视频，不需要重新创建播放器
          if (this.currentVideoSerial === fullDeviceData.serial_number && this.player) {
            console.log("已经在播放该序列号的视频，无需重新创建播放器");
            return;
          }
          
          // 记录即将播放的视频序列号
          this.currentVideoSerial = fullDeviceData.serial_number;
          
          // 确保先销毁旧的播放器
          this.destroyCurrentPlayer();
          
          // 初始化新的播放器
          this.$nextTick(() => {
            this.initPlayer(fullDeviceData.video_address, fullDeviceData.serial_number);
          });
        } else {
          console.error('设备数据中缺少 video_address 或 serial_number。无法初始化播放器。');
          this.loadingText = '设备参数不完整，无法加载监控。';
        }
      } catch (error) {
        console.error('解析设备数据或初始化播放器时发生错误:', error);
        this.loadingText = '加载监控信息失败。';
      }
    },

    initPlayer(url, serialNumber) {
      // 检查 DOM 元素是否存在
      const videoContainer = this.$refs.videoContainer;
      if (!videoContainer) {
        console.error('播放器容器 #video-container 未找到! 无法初始化播放器。');
        this.loadingText = '播放器容器未就绪，请刷新页面重试。';
        return;
      }

      // 检查初始化播放器所需的参数
      console.log(`准备初始化播放器: URL=${url}, SerialNumber=${serialNumber}, Token已获取=${!!this.jktokens.value}`);
      if (!serialNumber || !this.jktokens.value || !url) {
        console.error('初始化播放器参数不足: 序列号、AccessToken或URL缺失。');
        this.loadingText = '播放器初始化失败：缺少必要信息。';
        return;
      }

      // 创建新的播放器实例
      console.log("尝试创建新的EZUIKitPlayer实例...");
      try {
        this.player = new EZUIKit.EZUIKitPlayer({
          id: 'video-container',
          accessToken: this.jktokens.value,
          url: url,
          height: 220,
          width: 450,
          template: 'simple',
          autoplay: true,
          handleError: (e) => {
            console.error('EZUIKitPlayer 运行时错误:', e);
            this.loadingText = `播放失败: ${e.msg || e.message || '未知错误'}`;
            // 错误发生时清理播放器引用
            this.player = null;
            this.currentVideoSerial = '';
          },
        });
        
        // 对创建的实例进行基本验证
        if (!this.player || typeof this.player.play !== 'function' || typeof this.player.destroy !== 'function') {
          console.error("EZUIKit.EZUIKitPlayer(...) 返回的不是一个有效的播放器实例，或者实例上缺少关键方法。", this.player);
          this.player = null;
          this.currentVideoSerial = '';
          this.loadingText = '播放器创建失败 (返回无效实例)。';
        } else {
          console.log("新的EZUIKitPlayer实例已成功创建:", this.player);
          this.loadingText = '';
        }
      } catch (error) {
        console.error("创建EZUIKitPlayer实例期间发生错误:", error);
        this.player = null;
        this.currentVideoSerial = '';
        this.loadingText = `创建播放器时出错: ${error.message || '未知错误'}`;
      }
    },

    onClickLeft() {
      this.$router.back();
    }
  }
}
</script>

<style scoped>
.section-title-page {
  min-height: 100vh;
  background: #001528;
  display: flex; /* 使用flex布局使navbar和内容区域垂直排列 */
  flex-direction: column; /*子元素垂直排列*/
}

.device-nav { /* NavBar通常是固定高度，不需要flex-shrink */
  /* flex-shrink: 0; */ /* MavBar高度固定，不需要收缩 */
  background-color: #001528!important;
  color: #C3E4FF !important;
}

.device-nav :deep(.van-icon) { /* :deep() 用法在 <style scoped> 中 */
  color: #C3E4FF !important;
}

.device-nav :deep(.van-nav-bar__content) {
  color: #C3E4FF !important;
}

.device-nav :deep(.van-nav-bar__title) {
  color: #C3E4FF !important;
}

.monitor-content {
  flex-grow: 1; /* 让监控内容区域占据剩余空间 */
  display: flex; /* 使用flex来居中播放器容器 */
  align-items: center; /* 垂直居中 */
  justify-content: center; /* 水平居中 */
  /* margin-top: 24px; /* 如果NavBar不是fixed定位，这个margin可能是需要的 */
  background: #222c3c;
  /* border-radius: 8px; */ /* 如果希望内容区有圆角 */
  /* min-height: 300px; */ /* 高度由播放器或父容器决定 */
  padding: 10px; /* 给内容区一些内边距 */
  overflow: hidden; /* 如果播放器可能溢出 */
}

.monitor-placeholder { /* 这是 #video-container */
  /* width: 450px; */ /* 宽度由播放器参数决定或CSS设置 */
  /* height: 220px; */ /* 高度由播放器参数决定或CSS设置 */
  background-color: #000; /* 播放器加载前的背景色 */
  display: flex; /* 用于内部P标签居中 */
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 16px; /* 调整占位符文字大小 */
}

/* 确保播放器容器（.monitor-placeholder）有明确尺寸，如果EZUIKitPlayer的width/height是百分比 */
/* 如果EZUIKitPlayer的width/height是像素值，则容器会自动适应 */
</style>
