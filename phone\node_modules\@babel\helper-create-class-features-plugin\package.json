{"_args": [["@babel/helper-create-class-features-plugin@7.27.1", "F:\\桌面\\上海海洋大屏2期\\phone"]], "_development": true, "_from": "@babel/helper-create-class-features-plugin@7.27.1", "_id": "@babel/helper-create-class-features-plugin@7.27.1", "_inBundle": false, "_integrity": "sha512-QwGAmuvM17btKU5VqXfb+Giw4JcN0hjuufz3DYnpeVDvZLAObloM77bhMXiqry3Iio+Ai4phVRDwl6WU10+r5A==", "_location": "/@babel/helper-create-class-features-plugin", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@babel/helper-create-class-features-plugin@7.27.1", "name": "@babel/helper-create-class-features-plugin", "escapedName": "@babel%2fhelper-create-class-features-plugin", "scope": "@babel", "rawSpec": "7.27.1", "saveSpec": null, "fetchSpec": "7.27.1"}, "_requiredBy": ["/@babel/plugin-proposal-class-properties", "/@babel/plugin-proposal-decorators", "/@babel/plugin-transform-class-properties", "/@babel/plugin-transform-class-static-block", "/@babel/plugin-transform-private-methods", "/@babel/plugin-transform-private-property-in-object"], "_resolved": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.27.1.tgz", "_spec": "7.27.1", "_where": "F:\\桌面\\上海海洋大屏2期\\phone", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "dependencies": {"@babel/helper-annotate-as-pure": "^7.27.1", "@babel/helper-member-expression-to-functions": "^7.27.1", "@babel/helper-optimise-call-expression": "^7.27.1", "@babel/helper-replace-supers": "^7.27.1", "@babel/helper-skip-transparent-expression-wrappers": "^7.27.1", "@babel/traverse": "^7.27.1", "semver": "^6.3.1"}, "description": "Compile class public and private fields, private methods and decorators to ES6", "devDependencies": {"@babel/core": "^7.27.1", "@babel/helper-plugin-test-runner": "^7.27.1", "@babel/preset-env": "^7.27.1", "@types/charcodes": "^0.2.0", "charcodes": "^0.2.0"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://github.com/babel/babel#readme", "keywords": ["babel", "babel-plugin"], "license": "MIT", "main": "./lib/index.js", "name": "@babel/helper-create-class-features-plugin", "peerDependencies": {"@babel/core": "^7.0.0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-helper-create-class-features-plugin"}, "type": "commonjs", "version": "7.27.1"}