# Generated by Django 2.1.8 on 2020-09-24 14:41

import demo1_server.libs.utils
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('user', '__first__'),
    ]

    operations = [
        migrations.CreateModel(
            name='UserLog',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('content', models.TextField()),
                ('type', models.SmallIntegerField(choices=[(0, '增加'), (1, '删除'), (2, '修改')], default=2, verbose_name='类型')),
                ('created_at', models.CharField(default=demo1_server.libs.utils.human_datetime, max_length=20)),
                ('user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.DO_NOTHING, to='user.User')),
            ],
            options={
                'db_table': 'userlog',
                'ordering': ('-id',),
            },
        ),
    ]
