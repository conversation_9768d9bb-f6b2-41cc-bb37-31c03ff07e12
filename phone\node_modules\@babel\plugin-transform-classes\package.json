{"_args": [["@babel/plugin-transform-classes@7.27.1", "F:\\桌面\\上海海洋大屏2期\\phone"]], "_development": true, "_from": "@babel/plugin-transform-classes@7.27.1", "_id": "@babel/plugin-transform-classes@7.27.1", "_inBundle": false, "_integrity": "sha512-7iLhfFAubmpeJe/Wo2TVuDrykh/zlWXLzPNdL0Jqn/Xu8R3QQ8h9ff8FQoISZOsw74/HFqFI7NX63HN7QFIHKA==", "_location": "/@babel/plugin-transform-classes", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@babel/plugin-transform-classes@7.27.1", "name": "@babel/plugin-transform-classes", "escapedName": "@babel%2fplugin-transform-classes", "scope": "@babel", "rawSpec": "7.27.1", "saveSpec": null, "fetchSpec": "7.27.1"}, "_requiredBy": ["/@babel/preset-env"], "_resolved": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.27.1.tgz", "_spec": "7.27.1", "_where": "F:\\桌面\\上海海洋大屏2期\\phone", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "dependencies": {"@babel/helper-annotate-as-pure": "^7.27.1", "@babel/helper-compilation-targets": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-replace-supers": "^7.27.1", "@babel/traverse": "^7.27.1", "globals": "^11.1.0"}, "description": "Compile ES2015 classes to ES5", "devDependencies": {"@babel/core": "^7.27.1", "@babel/helper-plugin-test-runner": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-classes", "keywords": ["babel-plugin"], "license": "MIT", "main": "./lib/index.js", "name": "@babel/plugin-transform-classes", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-classes"}, "type": "commonjs", "version": "7.27.1"}