'use strict';

// eslint-disable-next-line no-extend-native
Date.prototype.Format = function (fmt) {
  var o = {
    "M+": this.getMonth() + 1,
    "d+": this.getDate(),
    "h+": this.getHours(),
    "m+": this.getMinutes(),
    "s+": this.getSeconds(),
    "q+": Math.floor((this.getMonth() + 3) / 3),
    "S": this.getMilliseconds()
  };
  if (/(y+)/.test(fmt)) {
    fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
  }
  for (var k in o) {
    if (new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, RegExp.$1.length == 1 ? o[k] : ("00" + o[k]).substr(("" + o[k]).length));
  }
  return fmt;
};
const loadJSByBlobToHeader = (url, callback, isReady) => {
  if (isReady) {
    return;
  }
  fetch(url).then(response => response.text()).then(res => {
    var workBlob = new Blob([res]);
    const url = URL.createObjectURL(workBlob);
    var oJs = document.createElement("script");
    oJs.setAttribute("src", url);
    oJs.onload = () => callback();
    document.getElementsByTagName("head")[0].appendChild(oJs);
  });
};

(function webpackUniversalModuleDefinition(root, factory) {
  if (typeof exports === 'object' && typeof module === 'object') module.exports = factory();else if (typeof define === 'function' && define.amd) define([], factory);else {
    var a = factory();
    for (var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];
  }
})(window, function () {
  return (/******/function (modules) {
      // webpackBootstrap
      /******/ // The module cache
      /******/
      var installedModules = {};
      /******/
      /******/ // The require function
      /******/
      function __webpack_require__(moduleId) {
        /******/
        /******/ // Check if module is in cache
        /******/if (installedModules[moduleId]) {
          /******/return installedModules[moduleId].exports;
          /******/
        }
        /******/ // Create a new module (and put it into the cache)
        /******/
        var module = installedModules[moduleId] = {
          /******/i: moduleId,
          /******/l: false,
          /******/exports: {}
          /******/
        };
        /******/
        /******/ // Execute the module function
        /******/
        modules[moduleId].call(module.exports, module, module.exports, __webpack_require__);
        /******/
        /******/ // Flag the module as loaded
        /******/
        module.l = true;
        /******/
        /******/ // Return the exports of the module
        /******/
        return module.exports;
        /******/
      }
      /******/
      /******/
      /******/ // expose the modules object (__webpack_modules__)
      /******/
      __webpack_require__.m = modules;
      /******/
      /******/ // expose the module cache
      /******/
      __webpack_require__.c = installedModules;
      /******/
      /******/ // define getter function for harmony exports
      /******/
      __webpack_require__.d = function (exports, name, getter) {
        /******/if (!__webpack_require__.o(exports, name)) {
          /******/Object.defineProperty(exports, name, {
            enumerable: true,
            get: getter
          });
          /******/
        }
        /******/
      };
      /******/
      /******/ // define __esModule on exports
      /******/
      __webpack_require__.r = function (exports) {
        /******/if (typeof Symbol !== 'undefined' && Symbol.toStringTag) {
          /******/Object.defineProperty(exports, Symbol.toStringTag, {
            value: 'Module'
          });
          /******/
        }
        /******/
        Object.defineProperty(exports, '__esModule', {
          value: true
        });
        /******/
      };
      /******/
      /******/ // create a fake namespace object
      /******/ // mode & 1: value is a module id, require it
      /******/ // mode & 2: merge all properties of value into the ns
      /******/ // mode & 4: return value when already ns object
      /******/ // mode & 8|1: behave like require
      /******/
      __webpack_require__.t = function (value, mode) {
        /******/if (mode & 1) value = __webpack_require__(value);
        /******/
        if (mode & 8) return value;
        /******/
        if (mode & 4 && typeof value === 'object' && value && value.__esModule) return value;
        /******/
        var ns = Object.create(null);
        /******/
        __webpack_require__.r(ns);
        /******/
        Object.defineProperty(ns, 'default', {
          enumerable: true,
          value: value
        });
        /******/
        if (mode & 2 && typeof value != 'string') for (var key in value) __webpack_require__.d(ns, key, function (key) {
          return value[key];
        }.bind(null, key));
        /******/
        return ns;
        /******/
      };
      /******/
      /******/ // getDefaultExport function for compatibility with non-harmony modules
      /******/
      __webpack_require__.n = function (module) {
        /******/var getter = module && module.__esModule ? /******/function getDefault() {
          return module['default'];
        } : /******/function getModuleExports() {
          return module;
        };
        /******/
        __webpack_require__.d(getter, 'a', getter);
        /******/
        return getter;
        /******/
      };
      /******/
      /******/ // Object.prototype.hasOwnProperty.call
      /******/
      __webpack_require__.o = function (object, property) {
        return Object.prototype.hasOwnProperty.call(object, property);
      };
      /******/
      /******/ // __webpack_public_path__
      /******/
      __webpack_require__.p = "";
      /******/
      /******/
      /******/ // Load entry module and return exports
      /******/
      return __webpack_require__(__webpack_require__.s = 1);
      /******/
    }
    /************************************************************************/
    /******/([/* 0 */
    /***/function (module, exports) {
      var dbits;
      function BigInteger(a, b, c) {
        a != null && ("number" == typeof a ? this.fromNumber(a, b, c) : b == null && "string" != typeof a ? this.fromString(a, 256) : this.fromString(a, b));
      }
      function nbi() {
        return new BigInteger(null);
      }
      function am3(a, b, c, d, e, g) {
        var h = b & 16383;
        for (b >>= 14; --g >= 0;) {
          var f = this[a] & 16383,
            o = this[a++] >> 14,
            p = b * f + o * h,
            f = h * f + ((p & 16383) << 14) + c[d] + e,
            e = (f >> 28) + (p >> 14) + b * o;
          c[d++] = f & 268435455;
        }
        return e;
      }
      (BigInteger.prototype.am = am3, dbits = 28);
      BigInteger.prototype.DB = dbits;
      BigInteger.prototype.DM = (1 << dbits) - 1;
      BigInteger.prototype.DV = 1 << dbits;
      var BI_FP = 52;
      BigInteger.prototype.FV = Math.pow(2, BI_FP);
      BigInteger.prototype.F1 = BI_FP - dbits;
      BigInteger.prototype.F2 = 2 * dbits - BI_FP;
      var BI_RM = "0123456789abcdefghijklmnopqrstuvwxyz",
        BI_RC = [],
        rr,
        vv;
      rr = "0".charCodeAt(0);
      for (vv = 0; vv <= 9; ++vv) BI_RC[rr++] = vv;
      rr = "a".charCodeAt(0);
      for (vv = 10; vv < 36; ++vv) BI_RC[rr++] = vv;
      rr = "A".charCodeAt(0);
      for (vv = 10; vv < 36; ++vv) BI_RC[rr++] = vv;
      function int2char(a) {
        return BI_RM.charAt(a);
      }
      function intAt(a, b) {
        var c = BI_RC[a.charCodeAt(b)];
        return c == null ? -1 : c;
      }
      function bnpCopyTo(a) {
        for (var b = this.t - 1; b >= 0; --b) a[b] = this[b];
        a.t = this.t;
        a.s = this.s;
      }
      function bnpFromInt(a) {
        this.t = 1;
        this.s = a < 0 ? -1 : 0;
        a > 0 ? this[0] = a : a < -1 ? this[0] = a + DV : this.t = 0;
      }
      function nbv(a) {
        var b = nbi();
        b.fromInt(a);
        return b;
      }
      function bnpFromString(a, b) {
        var c;
        if (b == 16) c = 4;else if (b == 8) c = 3;else if (b == 256) c = 8;else if (b == 2) c = 1;else if (b == 32) c = 5;else if (b == 4) c = 2;else {
          this.fromRadix(a, b);
          return;
        }
        this.s = this.t = 0;
        for (var d = a.length, e = !1, g = 0; --d >= 0;) {
          var h = c == 8 ? a[d] & 255 : intAt(a, d);
          h < 0 ? a.charAt(d) == "-" && (e = !0) : (e = !1, g == 0 ? this[this.t++] = h : g + c > this.DB ? (this[this.t - 1] |= (h & (1 << this.DB - g) - 1) << g, this[this.t++] = h >> this.DB - g) : this[this.t - 1] |= h << g, g += c, g >= this.DB && (g -= this.DB));
        }
        if (c == 8 && (a[0] & 128) != 0) this.s = -1, g > 0 && (this[this.t - 1] |= (1 << this.DB - g) - 1 << g);
        this.clamp();
        e && BigInteger.ZERO.subTo(this, this);
      }
      function bnpClamp() {
        for (var a = this.s & this.DM; this.t > 0 && this[this.t - 1] == a;) --this.t;
      }
      function bnToString(a) {
        if (this.s < 0) return "-" + this.negate().toString(a);
        if (a == 16) a = 4;else if (a == 8) a = 3;else if (a == 2) a = 1;else if (a == 32) a = 5;else if (a == 64) a = 6;else if (a == 4) a = 2;else return this.toRadix(a);
        var b = (1 << a) - 1,
          c,
          d = !1,
          e = "",
          g = this.t,
          h = this.DB - g * this.DB % a;
        if (g-- > 0) {
          if (h < this.DB && (c = this[g] >> h) > 0) d = !0, e = int2char(c);
          for (; g >= 0;) h < a ? (c = (this[g] & (1 << h) - 1) << a - h, c |= this[--g] >> (h += this.DB - a)) : (c = this[g] >> (h -= a) & b, h <= 0 && (h += this.DB, --g)), c > 0 && (d = !0), d && (e += int2char(c));
        }
        return d ? e : "0";
      }
      function bnNegate() {
        var a = nbi();
        BigInteger.ZERO.subTo(this, a);
        return a;
      }
      function bnAbs() {
        return this.s < 0 ? this.negate() : this;
      }
      function bnCompareTo(a) {
        var b = this.s - a.s;
        if (b != 0) return b;
        var c = this.t,
          b = c - a.t;
        if (b != 0) return b;
        for (; --c >= 0;) if ((b = this[c] - a[c]) != 0) return b;
        return 0;
      }
      function nbits(a) {
        var b = 1,
          c;
        if ((c = a >>> 16) != 0) a = c, b += 16;
        if ((c = a >> 8) != 0) a = c, b += 8;
        if ((c = a >> 4) != 0) a = c, b += 4;
        if ((c = a >> 2) != 0) a = c, b += 2;
        a >> 1 != 0 && (b += 1);
        return b;
      }
      function bnBitLength() {
        return this.t <= 0 ? 0 : this.DB * (this.t - 1) + nbits(this[this.t - 1] ^ this.s & this.DM);
      }
      function bnpDLShiftTo(a, b) {
        var c;
        for (c = this.t - 1; c >= 0; --c) b[c + a] = this[c];
        for (c = a - 1; c >= 0; --c) b[c] = 0;
        b.t = this.t + a;
        b.s = this.s;
      }
      function bnpDRShiftTo(a, b) {
        for (var c = a; c < this.t; ++c) b[c - a] = this[c];
        b.t = Math.max(this.t - a, 0);
        b.s = this.s;
      }
      function bnpLShiftTo(a, b) {
        var c = a % this.DB,
          d = this.DB - c,
          e = (1 << d) - 1,
          g = Math.floor(a / this.DB),
          h = this.s << c & this.DM,
          f;
        for (f = this.t - 1; f >= 0; --f) b[f + g + 1] = this[f] >> d | h, h = (this[f] & e) << c;
        for (f = g - 1; f >= 0; --f) b[f] = 0;
        b[g] = h;
        b.t = this.t + g + 1;
        b.s = this.s;
        b.clamp();
      }
      function bnpRShiftTo(a, b) {
        b.s = this.s;
        var c = Math.floor(a / this.DB);
        if (c >= this.t) b.t = 0;else {
          var d = a % this.DB,
            e = this.DB - d,
            g = (1 << d) - 1;
          b[0] = this[c] >> d;
          for (var h = c + 1; h < this.t; ++h) b[h - c - 1] |= (this[h] & g) << e, b[h - c] = this[h] >> d;
          d > 0 && (b[this.t - c - 1] |= (this.s & g) << e);
          b.t = this.t - c;
          b.clamp();
        }
      }
      function bnpSubTo(a, b) {
        for (var c = 0, d = 0, e = Math.min(a.t, this.t); c < e;) d += this[c] - a[c], b[c++] = d & this.DM, d >>= this.DB;
        if (a.t < this.t) {
          for (d -= a.s; c < this.t;) d += this[c], b[c++] = d & this.DM, d >>= this.DB;
          d += this.s;
        } else {
          for (d += this.s; c < a.t;) d -= a[c], b[c++] = d & this.DM, d >>= this.DB;
          d -= a.s;
        }
        b.s = d < 0 ? -1 : 0;
        d < -1 ? b[c++] = this.DV + d : d > 0 && (b[c++] = d);
        b.t = c;
        b.clamp();
      }
      function bnpMultiplyTo(a, b) {
        var c = this.abs(),
          d = a.abs(),
          e = c.t;
        for (b.t = e + d.t; --e >= 0;) b[e] = 0;
        for (e = 0; e < d.t; ++e) b[e + c.t] = c.am(0, d[e], b, e, 0, c.t);
        b.s = 0;
        b.clamp();
        this.s != a.s && BigInteger.ZERO.subTo(b, b);
      }
      function bnpSquareTo(a) {
        for (var b = this.abs(), c = a.t = 2 * b.t; --c >= 0;) a[c] = 0;
        for (c = 0; c < b.t - 1; ++c) {
          var d = b.am(c, b[c], a, 2 * c, 0, 1);
          if ((a[c + b.t] += b.am(c + 1, 2 * b[c], a, 2 * c + 1, d, b.t - c - 1)) >= b.DV) a[c + b.t] -= b.DV, a[c + b.t + 1] = 1;
        }
        a.t > 0 && (a[a.t - 1] += b.am(c, b[c], a, 2 * c, 0, 1));
        a.s = 0;
        a.clamp();
      }
      function bnpDivRemTo(a, b, c) {
        var d = a.abs();
        if (!(d.t <= 0)) {
          var e = this.abs();
          if (e.t < d.t) b != null && b.fromInt(0), c != null && this.copyTo(c);else {
            c == null && (c = nbi());
            var g = nbi(),
              h = this.s,
              a = a.s,
              f = this.DB - nbits(d[d.t - 1]);
            f > 0 ? (d.lShiftTo(f, g), e.lShiftTo(f, c)) : (d.copyTo(g), e.copyTo(c));
            d = g.t;
            e = g[d - 1];
            if (e != 0) {
              var o = e * (1 << this.F1) + (d > 1 ? g[d - 2] >> this.F2 : 0),
                p = this.FV / o,
                o = (1 << this.F1) / o,
                q = 1 << this.F2,
                n = c.t,
                k = n - d,
                j = b == null ? nbi() : b;
              g.dlShiftTo(k, j);
              c.compareTo(j) >= 0 && (c[c.t++] = 1, c.subTo(j, c));
              BigInteger.ONE.dlShiftTo(d, j);
              for (j.subTo(g, g); g.t < d;) g[g.t++] = 0;
              for (; --k >= 0;) {
                var l = c[--n] == e ? this.DM : Math.floor(c[n] * p + (c[n - 1] + q) * o);
                if ((c[n] += g.am(0, l, c, k, 0, d)) < l) {
                  g.dlShiftTo(k, j);
                  for (c.subTo(j, c); c[n] < --l;) c.subTo(j, c);
                }
              }
              b != null && (c.drShiftTo(d, b), h != a && BigInteger.ZERO.subTo(b, b));
              c.t = d;
              c.clamp();
              f > 0 && c.rShiftTo(f, c);
              h < 0 && BigInteger.ZERO.subTo(c, c);
            }
          }
        }
      }
      function bnMod(a) {
        var b = nbi();
        this.abs().divRemTo(a, null, b);
        this.s < 0 && b.compareTo(BigInteger.ZERO) > 0 && a.subTo(b, b);
        return b;
      }
      function Classic(a) {
        this.m = a;
      }
      function cConvert(a) {
        return a.s < 0 || a.compareTo(this.m) >= 0 ? a.mod(this.m) : a;
      }
      function cRevert(a) {
        return a;
      }
      function cReduce(a) {
        a.divRemTo(this.m, null, a);
      }
      function cMulTo(a, b, c) {
        a.multiplyTo(b, c);
        this.reduce(c);
      }
      function cSqrTo(a, b) {
        a.squareTo(b);
        this.reduce(b);
      }
      Classic.prototype.convert = cConvert;
      Classic.prototype.revert = cRevert;
      Classic.prototype.reduce = cReduce;
      Classic.prototype.mulTo = cMulTo;
      Classic.prototype.sqrTo = cSqrTo;
      function bnpInvDigit() {
        if (this.t < 1) return 0;
        var a = this[0];
        if ((a & 1) == 0) return 0;
        var b = a & 3,
          b = b * (2 - (a & 15) * b) & 15,
          b = b * (2 - (a & 255) * b) & 255,
          b = b * (2 - ((a & 65535) * b & 65535)) & 65535,
          b = b * (2 - a * b % this.DV) % this.DV;
        return b > 0 ? this.DV - b : -b;
      }
      function Montgomery(a) {
        this.m = a;
        this.mp = a.invDigit();
        this.mpl = this.mp & 32767;
        this.mph = this.mp >> 15;
        this.um = (1 << a.DB - 15) - 1;
        this.mt2 = 2 * a.t;
      }
      function montConvert(a) {
        var b = nbi();
        a.abs().dlShiftTo(this.m.t, b);
        b.divRemTo(this.m, null, b);
        a.s < 0 && b.compareTo(BigInteger.ZERO) > 0 && this.m.subTo(b, b);
        return b;
      }
      function montRevert(a) {
        var b = nbi();
        a.copyTo(b);
        this.reduce(b);
        return b;
      }
      function montReduce(a) {
        for (; a.t <= this.mt2;) a[a.t++] = 0;
        for (var b = 0; b < this.m.t; ++b) {
          var c = a[b] & 32767,
            d = c * this.mpl + ((c * this.mph + (a[b] >> 15) * this.mpl & this.um) << 15) & a.DM,
            c = b + this.m.t;
          for (a[c] += this.m.am(0, d, a, b, 0, this.m.t); a[c] >= a.DV;) a[c] -= a.DV, a[++c]++;
        }
        a.clamp();
        a.drShiftTo(this.m.t, a);
        a.compareTo(this.m) >= 0 && a.subTo(this.m, a);
      }
      function montSqrTo(a, b) {
        a.squareTo(b);
        this.reduce(b);
      }
      function montMulTo(a, b, c) {
        a.multiplyTo(b, c);
        this.reduce(c);
      }
      Montgomery.prototype.convert = montConvert;
      Montgomery.prototype.revert = montRevert;
      Montgomery.prototype.reduce = montReduce;
      Montgomery.prototype.mulTo = montMulTo;
      Montgomery.prototype.sqrTo = montSqrTo;
      function bnpIsEven() {
        return (this.t > 0 ? this[0] & 1 : this.s) == 0;
      }
      function bnpExp(a, b) {
        if (a > ********** || a < 1) return BigInteger.ONE;
        var c = nbi(),
          d = nbi(),
          e = b.convert(this),
          g = nbits(a) - 1;
        for (e.copyTo(c); --g >= 0;) if (b.sqrTo(c, d), (a & 1 << g) > 0) b.mulTo(d, e, c);else var h = c,
          c = d,
          d = h;
        return b.revert(c);
      }
      function bnModPowInt(a, b) {
        var c;
        c = a < 256 || b.isEven() ? new Classic(b) : new Montgomery(b);
        return this.exp(a, c);
      }
      BigInteger.prototype.copyTo = bnpCopyTo;
      BigInteger.prototype.fromInt = bnpFromInt;
      BigInteger.prototype.fromString = bnpFromString;
      BigInteger.prototype.clamp = bnpClamp;
      BigInteger.prototype.dlShiftTo = bnpDLShiftTo;
      BigInteger.prototype.drShiftTo = bnpDRShiftTo;
      BigInteger.prototype.lShiftTo = bnpLShiftTo;
      BigInteger.prototype.rShiftTo = bnpRShiftTo;
      BigInteger.prototype.subTo = bnpSubTo;
      BigInteger.prototype.multiplyTo = bnpMultiplyTo;
      BigInteger.prototype.squareTo = bnpSquareTo;
      BigInteger.prototype.divRemTo = bnpDivRemTo;
      BigInteger.prototype.invDigit = bnpInvDigit;
      BigInteger.prototype.isEven = bnpIsEven;
      BigInteger.prototype.exp = bnpExp;
      BigInteger.prototype.toString = bnToString;
      BigInteger.prototype.negate = bnNegate;
      BigInteger.prototype.abs = bnAbs;
      BigInteger.prototype.compareTo = bnCompareTo;
      BigInteger.prototype.bitLength = bnBitLength;
      BigInteger.prototype.mod = bnMod;
      BigInteger.prototype.modPowInt = bnModPowInt;
      BigInteger.ZERO = nbv(0);
      BigInteger.ONE = nbv(1);
      function bnClone() {
        var a = nbi();
        this.copyTo(a);
        return a;
      }
      function bnIntValue() {
        if (this.s < 0) {
          if (this.t == 1) return this[0] - this.DV;else {
            if (this.t == 0) return -1;
          }
        } else if (this.t == 1) return this[0];else if (this.t == 0) return 0;
        return (this[1] & (1 << 32 - this.DB) - 1) << this.DB | this[0];
      }
      function bnByteValue() {
        return this.t == 0 ? this.s : this[0] << 24 >> 24;
      }
      function bnShortValue() {
        return this.t == 0 ? this.s : this[0] << 16 >> 16;
      }
      function bnpChunkSize(a) {
        return Math.floor(Math.LN2 * this.DB / Math.log(a));
      }
      function bnSigNum() {
        return this.s < 0 ? -1 : this.t <= 0 || this.t == 1 && this[0] <= 0 ? 0 : 1;
      }
      function bnpToRadix(a) {
        a == null && (a = 10);
        if (this.signum() == 0 || a < 2 || a > 36) return "0";
        var b = this.chunkSize(a),
          b = Math.pow(a, b),
          c = nbv(b),
          d = nbi(),
          e = nbi(),
          g = "";
        for (this.divRemTo(c, d, e); d.signum() > 0;) g = (b + e.intValue()).toString(a).substr(1) + g, d.divRemTo(c, d, e);
        return e.intValue().toString(a) + g;
      }
      function bnpFromRadix(a, b) {
        this.fromInt(0);
        b == null && (b = 10);
        for (var c = this.chunkSize(b), d = Math.pow(b, c), e = !1, g = 0, h = 0, f = 0; f < a.length; ++f) {
          var o = intAt(a, f);
          o < 0 ? a.charAt(f) == "-" && this.signum() == 0 && (e = !0) : (h = b * h + o, ++g >= c && (this.dMultiply(d), this.dAddOffset(h, 0), h = g = 0));
        }
        g > 0 && (this.dMultiply(Math.pow(b, g)), this.dAddOffset(h, 0));
        e && BigInteger.ZERO.subTo(this, this);
      }
      function bnpFromNumber(a, b, c) {
        if ("number" == typeof b) {
          if (a < 2) this.fromInt(1);else {
            this.fromNumber(a, c);
            this.testBit(a - 1) || this.bitwiseTo(BigInteger.ONE.shiftLeft(a - 1), op_or, this);
            for (this.isEven() && this.dAddOffset(1, 0); !this.isProbablePrime(b);) this.dAddOffset(2, 0), this.bitLength() > a && this.subTo(BigInteger.ONE.shiftLeft(a - 1), this);
          }
        } else {
          var c = [],
            d = a & 7;
          c.length = (a >> 3) + 1;
          b.nextBytes(c);
          d > 0 ? c[0] &= (1 << d) - 1 : c[0] = 0;
          this.fromString(c, 256);
        }
      }
      function bnToByteArray() {
        var a = this.t,
          b = [];
        b[0] = this.s;
        var c = this.DB - a * this.DB % 8,
          d,
          e = 0;
        if (a-- > 0) {
          if (c < this.DB && (d = this[a] >> c) != (this.s & this.DM) >> c) b[e++] = d | this.s << this.DB - c;
          for (; a >= 0;) if (c < 8 ? (d = (this[a] & (1 << c) - 1) << 8 - c, d |= this[--a] >> (c += this.DB - 8)) : (d = this[a] >> (c -= 8) & 255, c <= 0 && (c += this.DB, --a)), (d & 128) != 0 && (d |= -256), e == 0 && (this.s & 128) != (d & 128) && ++e, e > 0 || d != this.s) b[e++] = d;
        }
        return b;
      }
      function bnEquals(a) {
        return this.compareTo(a) == 0;
      }
      function bnMin(a) {
        return this.compareTo(a) < 0 ? this : a;
      }
      function bnMax(a) {
        return this.compareTo(a) > 0 ? this : a;
      }
      function bnpBitwiseTo(a, b, c) {
        var d,
          e,
          g = Math.min(a.t, this.t);
        for (d = 0; d < g; ++d) c[d] = b(this[d], a[d]);
        if (a.t < this.t) {
          e = a.s & this.DM;
          for (d = g; d < this.t; ++d) c[d] = b(this[d], e);
          c.t = this.t;
        } else {
          e = this.s & this.DM;
          for (d = g; d < a.t; ++d) c[d] = b(e, a[d]);
          c.t = a.t;
        }
        c.s = b(this.s, a.s);
        c.clamp();
      }
      function op_and(a, b) {
        return a & b;
      }
      function bnAnd(a) {
        var b = nbi();
        this.bitwiseTo(a, op_and, b);
        return b;
      }
      function op_or(a, b) {
        return a | b;
      }
      function bnOr(a) {
        var b = nbi();
        this.bitwiseTo(a, op_or, b);
        return b;
      }
      function op_xor(a, b) {
        return a ^ b;
      }
      function bnXor(a) {
        var b = nbi();
        this.bitwiseTo(a, op_xor, b);
        return b;
      }
      function op_andnot(a, b) {
        return a & ~b;
      }
      function bnAndNot(a) {
        var b = nbi();
        this.bitwiseTo(a, op_andnot, b);
        return b;
      }
      function bnNot() {
        for (var a = nbi(), b = 0; b < this.t; ++b) a[b] = this.DM & ~this[b];
        a.t = this.t;
        a.s = ~this.s;
        return a;
      }
      function bnShiftLeft(a) {
        var b = nbi();
        a < 0 ? this.rShiftTo(-a, b) : this.lShiftTo(a, b);
        return b;
      }
      function bnShiftRight(a) {
        var b = nbi();
        a < 0 ? this.lShiftTo(-a, b) : this.rShiftTo(a, b);
        return b;
      }
      function lbit(a) {
        if (a == 0) return -1;
        var b = 0;
        (a & 65535) == 0 && (a >>= 16, b += 16);
        (a & 255) == 0 && (a >>= 8, b += 8);
        (a & 15) == 0 && (a >>= 4, b += 4);
        (a & 3) == 0 && (a >>= 2, b += 2);
        (a & 1) == 0 && ++b;
        return b;
      }
      function bnGetLowestSetBit() {
        for (var a = 0; a < this.t; ++a) if (this[a] != 0) return a * this.DB + lbit(this[a]);
        return this.s < 0 ? this.t * this.DB : -1;
      }
      function cbit(a) {
        for (var b = 0; a != 0;) a &= a - 1, ++b;
        return b;
      }
      function bnBitCount() {
        for (var a = 0, b = this.s & this.DM, c = 0; c < this.t; ++c) a += cbit(this[c] ^ b);
        return a;
      }
      function bnTestBit(a) {
        var b = Math.floor(a / this.DB);
        return b >= this.t ? this.s != 0 : (this[b] & 1 << a % this.DB) != 0;
      }
      function bnpChangeBit(a, b) {
        var c = BigInteger.ONE.shiftLeft(a);
        this.bitwiseTo(c, b, c);
        return c;
      }
      function bnSetBit(a) {
        return this.changeBit(a, op_or);
      }
      function bnClearBit(a) {
        return this.changeBit(a, op_andnot);
      }
      function bnFlipBit(a) {
        return this.changeBit(a, op_xor);
      }
      function bnpAddTo(a, b) {
        for (var c = 0, d = 0, e = Math.min(a.t, this.t); c < e;) d += this[c] + a[c], b[c++] = d & this.DM, d >>= this.DB;
        if (a.t < this.t) {
          for (d += a.s; c < this.t;) d += this[c], b[c++] = d & this.DM, d >>= this.DB;
          d += this.s;
        } else {
          for (d += this.s; c < a.t;) d += a[c], b[c++] = d & this.DM, d >>= this.DB;
          d += a.s;
        }
        b.s = d < 0 ? -1 : 0;
        d > 0 ? b[c++] = d : d < -1 && (b[c++] = this.DV + d);
        b.t = c;
        b.clamp();
      }
      function bnAdd(a) {
        var b = nbi();
        this.addTo(a, b);
        return b;
      }
      function bnSubtract(a) {
        var b = nbi();
        this.subTo(a, b);
        return b;
      }
      function bnMultiply(a) {
        var b = nbi();
        this.multiplyTo(a, b);
        return b;
      }
      function bnSquare() {
        var a = nbi();
        this.squareTo(a);
        return a;
      }
      function bnDivide(a) {
        var b = nbi();
        this.divRemTo(a, b, null);
        return b;
      }
      function bnRemainder(a) {
        var b = nbi();
        this.divRemTo(a, null, b);
        return b;
      }
      function bnDivideAndRemainder(a) {
        var b = nbi(),
          c = nbi();
        this.divRemTo(a, b, c);
        return [b, c];
      }
      function bnpDMultiply(a) {
        this[this.t] = this.am(0, a - 1, this, 0, 0, this.t);
        ++this.t;
        this.clamp();
      }
      function bnpDAddOffset(a, b) {
        if (a != 0) {
          for (; this.t <= b;) this[this.t++] = 0;
          for (this[b] += a; this[b] >= this.DV;) this[b] -= this.DV, ++b >= this.t && (this[this.t++] = 0), ++this[b];
        }
      }
      function NullExp() {}
      function nNop(a) {
        return a;
      }
      function nMulTo(a, b, c) {
        a.multiplyTo(b, c);
      }
      function nSqrTo(a, b) {
        a.squareTo(b);
      }
      NullExp.prototype.convert = nNop;
      NullExp.prototype.revert = nNop;
      NullExp.prototype.mulTo = nMulTo;
      NullExp.prototype.sqrTo = nSqrTo;
      function bnPow(a) {
        return this.exp(a, new NullExp());
      }
      function bnpMultiplyLowerTo(a, b, c) {
        var d = Math.min(this.t + a.t, b);
        c.s = 0;
        for (c.t = d; d > 0;) c[--d] = 0;
        var e;
        for (e = c.t - this.t; d < e; ++d) c[d + this.t] = this.am(0, a[d], c, d, 0, this.t);
        for (e = Math.min(a.t, b); d < e; ++d) this.am(0, a[d], c, d, 0, b - d);
        c.clamp();
      }
      function bnpMultiplyUpperTo(a, b, c) {
        --b;
        var d = c.t = this.t + a.t - b;
        for (c.s = 0; --d >= 0;) c[d] = 0;
        for (d = Math.max(b - this.t, 0); d < a.t; ++d) c[this.t + d - b] = this.am(b - d, a[d], c, 0, 0, this.t + d - b);
        c.clamp();
        c.drShiftTo(1, c);
      }
      function Barrett(a) {
        this.r2 = nbi();
        this.q3 = nbi();
        BigInteger.ONE.dlShiftTo(2 * a.t, this.r2);
        this.mu = this.r2.divide(a);
        this.m = a;
      }
      function barrettConvert(a) {
        if (a.s < 0 || a.t > 2 * this.m.t) return a.mod(this.m);else if (a.compareTo(this.m) < 0) return a;else {
          var b = nbi();
          a.copyTo(b);
          this.reduce(b);
          return b;
        }
      }
      function barrettRevert(a) {
        return a;
      }
      function barrettReduce(a) {
        a.drShiftTo(this.m.t - 1, this.r2);
        if (a.t > this.m.t + 1) a.t = this.m.t + 1, a.clamp();
        this.mu.multiplyUpperTo(this.r2, this.m.t + 1, this.q3);
        for (this.m.multiplyLowerTo(this.q3, this.m.t + 1, this.r2); a.compareTo(this.r2) < 0;) a.dAddOffset(1, this.m.t + 1);
        for (a.subTo(this.r2, a); a.compareTo(this.m) >= 0;) a.subTo(this.m, a);
      }
      function barrettSqrTo(a, b) {
        a.squareTo(b);
        this.reduce(b);
      }
      function barrettMulTo(a, b, c) {
        a.multiplyTo(b, c);
        this.reduce(c);
      }
      Barrett.prototype.convert = barrettConvert;
      Barrett.prototype.revert = barrettRevert;
      Barrett.prototype.reduce = barrettReduce;
      Barrett.prototype.mulTo = barrettMulTo;
      Barrett.prototype.sqrTo = barrettSqrTo;
      function bnModPow(a, b) {
        var c = a.bitLength(),
          d,
          e = nbv(1),
          g;
        if (c <= 0) return e;else d = c < 18 ? 1 : c < 48 ? 3 : c < 144 ? 4 : c < 768 ? 5 : 6;
        g = c < 8 ? new Classic(b) : b.isEven() ? new Barrett(b) : new Montgomery(b);
        var h = [],
          f = 3,
          o = d - 1,
          p = (1 << d) - 1;
        h[1] = g.convert(this);
        if (d > 1) {
          c = nbi();
          for (g.sqrTo(h[1], c); f <= p;) h[f] = nbi(), g.mulTo(c, h[f - 2], h[f]), f += 2;
        }
        for (var q = a.t - 1, n, k = !0, j = nbi(), c = nbits(a[q]) - 1; q >= 0;) {
          c >= o ? n = a[q] >> c - o & p : (n = (a[q] & (1 << c + 1) - 1) << o - c, q > 0 && (n |= a[q - 1] >> this.DB + c - o));
          for (f = d; (n & 1) == 0;) n >>= 1, --f;
          if ((c -= f) < 0) c += this.DB, --q;
          if (k) h[n].copyTo(e), k = !1;else {
            for (; f > 1;) g.sqrTo(e, j), g.sqrTo(j, e), f -= 2;
            f > 0 ? g.sqrTo(e, j) : (f = e, e = j, j = f);
            g.mulTo(j, h[n], e);
          }
          for (; q >= 0 && (a[q] & 1 << c) == 0;) g.sqrTo(e, j), f = e, e = j, j = f, --c < 0 && (c = this.DB - 1, --q);
        }
        return g.revert(e);
      }
      function bnGCD(a) {
        var b = this.s < 0 ? this.negate() : this.clone(),
          a = a.s < 0 ? a.negate() : a.clone();
        if (b.compareTo(a) < 0) var c = b,
          b = a,
          a = c;
        var c = b.getLowestSetBit(),
          d = a.getLowestSetBit();
        if (d < 0) return b;
        c < d && (d = c);
        d > 0 && (b.rShiftTo(d, b), a.rShiftTo(d, a));
        for (; b.signum() > 0;) (c = b.getLowestSetBit()) > 0 && b.rShiftTo(c, b), (c = a.getLowestSetBit()) > 0 && a.rShiftTo(c, a), b.compareTo(a) >= 0 ? (b.subTo(a, b), b.rShiftTo(1, b)) : (a.subTo(b, a), a.rShiftTo(1, a));
        d > 0 && a.lShiftTo(d, a);
        return a;
      }
      function bnpModInt(a) {
        if (a <= 0) return 0;
        var b = this.DV % a,
          c = this.s < 0 ? a - 1 : 0;
        if (this.t > 0) if (b == 0) c = this[0] % a;else for (var d = this.t - 1; d >= 0; --d) c = (b * c + this[d]) % a;
        return c;
      }
      function bnModInverse(a) {
        var b = a.isEven();
        if (this.isEven() && b || a.signum() == 0) return BigInteger.ZERO;
        for (var c = a.clone(), d = this.clone(), e = nbv(1), g = nbv(0), h = nbv(0), f = nbv(1); c.signum() != 0;) {
          for (; c.isEven();) {
            c.rShiftTo(1, c);
            if (b) {
              if (!e.isEven() || !g.isEven()) e.addTo(this, e), g.subTo(a, g);
              e.rShiftTo(1, e);
            } else g.isEven() || g.subTo(a, g);
            g.rShiftTo(1, g);
          }
          for (; d.isEven();) {
            d.rShiftTo(1, d);
            if (b) {
              if (!h.isEven() || !f.isEven()) h.addTo(this, h), f.subTo(a, f);
              h.rShiftTo(1, h);
            } else f.isEven() || f.subTo(a, f);
            f.rShiftTo(1, f);
          }
          c.compareTo(d) >= 0 ? (c.subTo(d, c), b && e.subTo(h, e), g.subTo(f, g)) : (d.subTo(c, d), b && h.subTo(e, h), f.subTo(g, f));
        }
        if (d.compareTo(BigInteger.ONE) != 0) return BigInteger.ZERO;
        if (f.compareTo(a) >= 0) return f.subtract(a);
        if (f.signum() < 0) f.addTo(a, f);else return f;
        return f.signum() < 0 ? f.add(a) : f;
      }
      var lowprimes = [2, 3, 5, 7, 11, 13, 17, 19, 23, 29, 31, 37, 41, 43, 47, 53, 59, 61, 67, 71, 73, 79, 83, 89, 97, 101, 103, 107, 109, 113, 127, 131, 137, 139, 149, 151, 157, 163, 167, 173, 179, 181, 191, 193, 197, 199, 211, 223, 227, 229, 233, 239, 241, 251, 257, 263, 269, 271, 277, 281, 283, 293, 307, 311, 313, 317, 331, 337, 347, 349, 353, 359, 367, 373, 379, 383, 389, 397, 401, 409, 419, 421, 431, 433, 439, 443, 449, 457, 461, 463, 467, 479, 487, 491, 499, 503, 509, 521, 523, 541, 547, 557, 563, 569, 571, 577, 587, 593, 599, 601, 607, 613, 617, 619, 631, 641, 643, 647, 653, 659, 661, 673, 677, 683, 691, 701, 709, 719, 727, 733, 739, 743, 751, 757, 761, 769, 773, 787, 797, 809, 811, 821, 823, 827, 829, 839, 853, 857, 859, 863, 877, 881, 883, 887, 907, 911, 919, 929, 937, 941, 947, 953, 967, 971, 977, 983, 991, 997],
        lplim = 67108864 / lowprimes[lowprimes.length - 1];
      function bnIsProbablePrime(a) {
        var b,
          c = this.abs();
        if (c.t == 1 && c[0] <= lowprimes[lowprimes.length - 1]) {
          for (b = 0; b < lowprimes.length; ++b) if (c[0] == lowprimes[b]) return !0;
          return !1;
        }
        if (c.isEven()) return !1;
        for (b = 1; b < lowprimes.length;) {
          for (var d = lowprimes[b], e = b + 1; e < lowprimes.length && d < lplim;) d *= lowprimes[e++];
          for (d = c.modInt(d); b < e;) if (d % lowprimes[b++] == 0) return !1;
        }
        return c.millerRabin(a);
      }
      function bnpMillerRabin(a) {
        var b = this.subtract(BigInteger.ONE),
          c = b.getLowestSetBit();
        if (c <= 0) return !1;
        var d = b.shiftRight(c),
          a = a + 1 >> 1;
        if (a > lowprimes.length) a = lowprimes.length;
        for (var e = nbi(), g = 0; g < a; ++g) {
          e.fromInt(lowprimes[Math.floor(Math.random() * lowprimes.length)]);
          var h = e.modPow(d, this);
          if (h.compareTo(BigInteger.ONE) != 0 && h.compareTo(b) != 0) {
            for (var f = 1; f++ < c && h.compareTo(b) != 0;) if (h = h.modPowInt(2, this), h.compareTo(BigInteger.ONE) == 0) return !1;
            if (h.compareTo(b) != 0) return !1;
          }
        }
        return !0;
      }
      BigInteger.prototype.chunkSize = bnpChunkSize;
      BigInteger.prototype.toRadix = bnpToRadix;
      BigInteger.prototype.fromRadix = bnpFromRadix;
      BigInteger.prototype.fromNumber = bnpFromNumber;
      BigInteger.prototype.bitwiseTo = bnpBitwiseTo;
      BigInteger.prototype.changeBit = bnpChangeBit;
      BigInteger.prototype.addTo = bnpAddTo;
      BigInteger.prototype.dMultiply = bnpDMultiply;
      BigInteger.prototype.dAddOffset = bnpDAddOffset;
      BigInteger.prototype.multiplyLowerTo = bnpMultiplyLowerTo;
      BigInteger.prototype.multiplyUpperTo = bnpMultiplyUpperTo;
      BigInteger.prototype.modInt = bnpModInt;
      BigInteger.prototype.millerRabin = bnpMillerRabin;
      BigInteger.prototype.clone = bnClone;
      BigInteger.prototype.intValue = bnIntValue;
      BigInteger.prototype.byteValue = bnByteValue;
      BigInteger.prototype.shortValue = bnShortValue;
      BigInteger.prototype.signum = bnSigNum;
      BigInteger.prototype.toByteArray = bnToByteArray;
      BigInteger.prototype.equals = bnEquals;
      BigInteger.prototype.min = bnMin;
      BigInteger.prototype.max = bnMax;
      BigInteger.prototype.and = bnAnd;
      BigInteger.prototype.or = bnOr;
      BigInteger.prototype.xor = bnXor;
      BigInteger.prototype.andNot = bnAndNot;
      BigInteger.prototype.not = bnNot;
      BigInteger.prototype.shiftLeft = bnShiftLeft;
      BigInteger.prototype.shiftRight = bnShiftRight;
      BigInteger.prototype.getLowestSetBit = bnGetLowestSetBit;
      BigInteger.prototype.bitCount = bnBitCount;
      BigInteger.prototype.testBit = bnTestBit;
      BigInteger.prototype.setBit = bnSetBit;
      BigInteger.prototype.clearBit = bnClearBit;
      BigInteger.prototype.flipBit = bnFlipBit;
      BigInteger.prototype.add = bnAdd;
      BigInteger.prototype.subtract = bnSubtract;
      BigInteger.prototype.multiply = bnMultiply;
      BigInteger.prototype.divide = bnDivide;
      BigInteger.prototype.remainder = bnRemainder;
      BigInteger.prototype.divideAndRemainder = bnDivideAndRemainder;
      BigInteger.prototype.modPow = bnModPow;
      BigInteger.prototype.modInverse = bnModInverse;
      BigInteger.prototype.pow = bnPow;
      BigInteger.prototype.gcd = bnGCD;
      BigInteger.prototype.isProbablePrime = bnIsProbablePrime;
      BigInteger.prototype.square = bnSquare;
      (function (a, b, c, d, e, g, h) {
        function f(a) {
          var b,
            d,
            e = this,
            g = a.length,
            f = 0,
            h = e.i = e.j = e.m = 0;
          e.S = [];
          e.c = [];
          for (g || (a = [g++]); f < c;) e.S[f] = f++;
          for (f = 0; f < c; f++) b = e.S[f], h = h + b + a[f % g] & c - 1, d = e.S[h], e.S[f] = d, e.S[h] = b;
          e.g = function (a) {
            var b = e.S,
              d = e.i + 1 & c - 1,
              g = b[d],
              f = e.j + g & c - 1,
              h = b[f];
            b[d] = h;
            b[f] = g;
            for (var k = b[g + h & c - 1]; --a;) d = d + 1 & c - 1, g = b[d], f = f + g & c - 1, h = b[f], b[d] = h, b[f] = g, k = k * c + b[g + h & c - 1];
            e.i = d;
            e.j = f;
            return k;
          };
          e.g(c);
        }
        function o(a, b, c, d, e) {
          c = [];
          e = typeof a;
          if (b && e == "object") for (d in a) if (d.indexOf("S") < 5) try {
            c.push(o(a[d], b - 1));
          } catch (g) {}
          return c.length ? c : a + (e != "string" ? "\x00" : "");
        }
        function p(a, b, d, e) {
          a += "";
          for (e = d = 0; e < a.length; e++) {
            var g = b,
              f = e & c - 1,
              h = (d ^= b[e & c - 1] * 19) + a.charCodeAt(e);
            g[f] = h & c - 1;
          }
          a = "";
          for (e in b) a += String.fromCharCode(b[e]);
          return a;
        }
        b.seedrandom = function (q, n) {
          var k = [],
            j,
            q = p(o(n ? [q, a] : arguments.length ? q : [new Date().getTime(), a, window], 3), k);
          j = new f(k);
          p(j.S, a);
          b.random = function () {
            for (var a = j.g(d), b = h, f = 0; a < e;) a = (a + f) * c, b *= c, f = j.g(1);
            for (; a >= g;) a /= 2, b /= 2, f >>>= 1;
            return (a + f) / b;
          };
          return q;
        };
        h = b.pow(c, d);
        e = b.pow(2, e);
        g = e * 2;
        p(b.random(), a);
      })([], Math, 256, 6, 52);
      function SeededRandom() {}
      function SRnextBytes(a) {
        var b;
        for (b = 0; b < a.length; b++) a[b] = Math.floor(Math.random() * 256);
      }
      SeededRandom.prototype.nextBytes = SRnextBytes;
      function Arcfour() {
        this.j = this.i = 0;
        this.S = [];
      }
      function ARC4init(a) {
        var b, c, d;
        for (b = 0; b < 256; ++b) this.S[b] = b;
        for (b = c = 0; b < 256; ++b) c = c + this.S[b] + a[b % a.length] & 255, d = this.S[b], this.S[b] = this.S[c], this.S[c] = d;
        this.j = this.i = 0;
      }
      function ARC4next() {
        var a;
        this.i = this.i + 1 & 255;
        this.j = this.j + this.S[this.i] & 255;
        a = this.S[this.i];
        this.S[this.i] = this.S[this.j];
        this.S[this.j] = a;
        return this.S[a + this.S[this.i] & 255];
      }
      Arcfour.prototype.init = ARC4init;
      Arcfour.prototype.next = ARC4next;
      function prng_newstate() {
        return new Arcfour();
      }
      var rng_psize = 256,
        rng_state,
        rng_pool,
        rng_pptr;
      function rng_seed_int(a) {
        rng_pool[rng_pptr++] ^= a & 255;
        rng_pool[rng_pptr++] ^= a >> 8 & 255;
        rng_pool[rng_pptr++] ^= a >> 16 & 255;
        rng_pool[rng_pptr++] ^= a >> 24 & 255;
        rng_pptr >= rng_psize && (rng_pptr -= rng_psize);
      }
      function rng_seed_time() {
        rng_seed_int(new Date().getTime());
      }
      if (rng_pool == null) {
        rng_pool = [];
        rng_pptr = 0;
        var t;
        for (; rng_pptr < rng_psize;) t = Math.floor(65536 * Math.random()), rng_pool[rng_pptr++] = t >>> 8, rng_pool[rng_pptr++] = t & 255;
        rng_pptr = 0;
        rng_seed_time();
      }
      function rng_get_byte() {
        if (rng_state == null) {
          rng_seed_time();
          rng_state = prng_newstate();
          rng_state.init(rng_pool);
          for (rng_pptr = 0; rng_pptr < rng_pool.length; ++rng_pptr) rng_pool[rng_pptr] = 0;
          rng_pptr = 0;
        }
        return rng_state.next();
      }
      function rng_get_bytes(a) {
        var b;
        for (b = 0; b < a.length; ++b) a[b] = rng_get_byte();
      }
      function SecureRandom() {}
      SecureRandom.prototype.nextBytes = rng_get_bytes;
      function SHA256(a) {
        function b(a, b) {
          var c = (a & 65535) + (b & 65535);
          return (a >> 16) + (b >> 16) + (c >> 16) << 16 | c & 65535;
        }
        function c(a, b) {
          return a >>> b | a << 32 - b;
        }
        a = function (a) {
          for (var a = a.replace(/\r\n/g, "\n"), b = "", c = 0; c < a.length; c++) {
            var h = a.charCodeAt(c);
            h < 128 ? b += String.fromCharCode(h) : (h > 127 && h < 2048 ? b += String.fromCharCode(h >> 6 | 192) : (b += String.fromCharCode(h >> 12 | 224), b += String.fromCharCode(h >> 6 & 63 | 128)), b += String.fromCharCode(h & 63 | 128));
          }
          return b;
        }(a);
        return function (a) {
          for (var b = "", c = 0; c < a.length * 4; c++) b += "0123456789abcdef".charAt(a[c >> 2] >> (3 - c % 4) * 8 + 4 & 15) + "0123456789abcdef".charAt(a[c >> 2] >> (3 - c % 4) * 8 & 15);
          return b;
        }(function (a, e) {
          var g = [1116352408, 1899447441, 3049323471, 3921009573, 961987163, 1508970993, 2453635748, 2870763221, 3624381080, 310598401, 607225278, 1426881987, 1925078388, 2162078206, 2614888103, 3248222580, 3835390401, 4022224774, 264347078, 604807628, 770255983, 1249150122, 1555081692, 1996064986, 2554220882, 2821834349, 2952996808, 3210313671, 3336571891, 3584528711, 113926993, 338241895, 666307205, 773529912, 1294757372, 1396182291, 1695183700, 1986661051, 2177026350, 2456956037, 2730485921, 2820302411, 3259730800, 3345764771, 3516065817, 3600352804, 4094571909, 275423344, 430227734, 506948616, 659060556, 883997877, 958139571, 1322822218, 1537002063, 1747873779, 1955562222, 2024104815, 2227730452, 2361852424, 2428436474, 2756734187, 3204031479, 3329325298],
            h = [1779033703, 3144134277, 1013904242, 2773480762, 1359893119, 2600822924, 528734635, 1541459225],
            f = Array(64),
            o,
            p,
            q,
            n,
            k,
            j,
            l,
            m,
            s,
            r,
            u,
            w;
          a[e >> 5] |= 128 << 24 - e % 32;
          a[(e + 64 >> 9 << 4) + 15] = e;
          for (s = 0; s < a.length; s += 16) {
            o = h[0];
            p = h[1];
            q = h[2];
            n = h[3];
            k = h[4];
            j = h[5];
            l = h[6];
            m = h[7];
            for (r = 0; r < 64; r++) f[r] = r < 16 ? a[r + s] : b(b(b(c(f[r - 2], 17) ^ c(f[r - 2], 19) ^ f[r - 2] >>> 10, f[r - 7]), c(f[r - 15], 7) ^ c(f[r - 15], 18) ^ f[r - 15] >>> 3), f[r - 16]), u = b(b(b(b(m, c(k, 6) ^ c(k, 11) ^ c(k, 25)), k & j ^ ~k & l), g[r]), f[r]), w = b(c(o, 2) ^ c(o, 13) ^ c(o, 22), o & p ^ o & q ^ p & q), m = l, l = j, j = k, k = b(n, u), n = q, q = p, p = o, o = b(u, w);
            h[0] = b(o, h[0]);
            h[1] = b(p, h[1]);
            h[2] = b(q, h[2]);
            h[3] = b(n, h[3]);
            h[4] = b(k, h[4]);
            h[5] = b(j, h[5]);
            h[6] = b(l, h[6]);
            h[7] = b(m, h[7]);
          }
          return h;
        }(function (a) {
          for (var b = [], c = 0; c < a.length * 8; c += 8) b[c >> 5] |= (a.charCodeAt(c / 8) & 255) << 24 - c % 32;
          return b;
        }(a), a.length * 8));
      }
      var sha256 = {
        hex: function (a) {
          return SHA256(a);
        }
      };
      function SHA1(a) {
        function b(a, b) {
          return a << b | a >>> 32 - b;
        }
        function c(a) {
          var b = "",
            c,
            d;
          for (c = 7; c >= 0; c--) d = a >>> c * 4 & 15, b += d.toString(16);
          return b;
        }
        var d,
          e,
          g = Array(80),
          h = 1732584193,
          f = 4023233417,
          o = 2562383102,
          p = 271733878,
          q = 3285377520,
          n,
          k,
          j,
          l,
          m,
          a = function (a) {
            for (var a = a.replace(/\r\n/g, "\n"), b = "", c = 0; c < a.length; c++) {
              var d = a.charCodeAt(c);
              d < 128 ? b += String.fromCharCode(d) : (d > 127 && d < 2048 ? b += String.fromCharCode(d >> 6 | 192) : (b += String.fromCharCode(d >> 12 | 224), b += String.fromCharCode(d >> 6 & 63 | 128)), b += String.fromCharCode(d & 63 | 128));
            }
            return b;
          }(a);
        n = a.length;
        var s = [];
        for (d = 0; d < n - 3; d += 4) e = a.charCodeAt(d) << 24 | a.charCodeAt(d + 1) << 16 | a.charCodeAt(d + 2) << 8 | a.charCodeAt(d + 3), s.push(e);
        switch (n % 4) {
          case 0:
            d = 2147483648;
            break;
          case 1:
            d = a.charCodeAt(n - 1) << 24 | 8388608;
            break;
          case 2:
            d = a.charCodeAt(n - 2) << 24 | a.charCodeAt(n - 1) << 16 | 32768;
            break;
          case 3:
            d = a.charCodeAt(n - 3) << 24 | a.charCodeAt(n - 2) << 16 | a.charCodeAt(n - 1) << 8 | 128;
        }
        for (s.push(d); s.length % 16 != 14;) s.push(0);
        s.push(n >>> 29);
        s.push(n << 3 & **********);
        for (a = 0; a < s.length; a += 16) {
          for (d = 0; d < 16; d++) g[d] = s[a + d];
          for (d = 16; d <= 79; d++) g[d] = b(g[d - 3] ^ g[d - 8] ^ g[d - 14] ^ g[d - 16], 1);
          e = h;
          n = f;
          k = o;
          j = p;
          l = q;
          for (d = 0; d <= 19; d++) m = b(e, 5) + (n & k | ~n & j) + l + g[d] + 1518500249 & **********, l = j, j = k, k = b(n, 30), n = e, e = m;
          for (d = 20; d <= 39; d++) m = b(e, 5) + (n ^ k ^ j) + l + g[d] + 1859775393 & **********, l = j, j = k, k = b(n, 30), n = e, e = m;
          for (d = 40; d <= 59; d++) m = b(e, 5) + (n & k | n & j | k & j) + l + g[d] + 2400959708 & **********, l = j, j = k, k = b(n, 30), n = e, e = m;
          for (d = 60; d <= 79; d++) m = b(e, 5) + (n ^ k ^ j) + l + g[d] + 3395469782 & **********, l = j, j = k, k = b(n, 30), n = e, e = m;
          h = h + e & **********;
          f = f + n & **********;
          o = o + k & **********;
          p = p + j & **********;
          q = q + l & **********;
        }
        m = c(h) + c(f) + c(o) + c(p) + c(q);
        return m.toLowerCase();
      }
      var sha1 = {
          hex: function (a) {
            return SHA1(a);
          }
        },
        MD5 = function (a) {
          function b(a, b) {
            var c, d, e, f, g;
            e = a & 2147483648;
            f = b & 2147483648;
            c = a & 1073741824;
            d = b & 1073741824;
            g = (a & 1073741823) + (b & 1073741823);
            return c & d ? g ^ 2147483648 ^ e ^ f : c | d ? g & 1073741824 ? g ^ 3221225472 ^ e ^ f : g ^ 1073741824 ^ e ^ f : g ^ e ^ f;
          }
          function c(a, c, d, e, f, g, h) {
            a = b(a, b(b(c & d | ~c & e, f), h));
            return b(a << g | a >>> 32 - g, c);
          }
          function d(a, c, d, e, f, g, h) {
            a = b(a, b(b(c & e | d & ~e, f), h));
            return b(a << g | a >>> 32 - g, c);
          }
          function e(a, c, d, e, f, g, h) {
            a = b(a, b(b(c ^ d ^ e, f), h));
            return b(a << g | a >>> 32 - g, c);
          }
          function g(a, c, d, e, f, g, h) {
            a = b(a, b(b(d ^ (c | ~e), f), h));
            return b(a << g | a >>> 32 - g, c);
          }
          function h(a) {
            var b = "",
              c = "",
              d;
            for (d = 0; d <= 3; d++) c = a >>> d * 8 & 255, c = "0" + c.toString(16), b += c.substr(c.length - 2, 2);
            return b;
          }
          var f = [],
            o,
            p,
            q,
            n,
            k,
            j,
            l,
            m,
            a = function (a) {
              for (var a = a.replace(/\r\n/g, "\n"), b = "", c = 0; c < a.length; c++) {
                var d = a.charCodeAt(c);
                d < 128 ? b += String.fromCharCode(d) : (d > 127 && d < 2048 ? b += String.fromCharCode(d >> 6 | 192) : (b += String.fromCharCode(d >> 12 | 224), b += String.fromCharCode(d >> 6 & 63 | 128)), b += String.fromCharCode(d & 63 | 128));
              }
              return b;
            }(a),
            f = function (a) {
              var b,
                c = a.length;
              b = c + 8;
              for (var d = ((b - b % 64) / 64 + 1) * 16, e = Array(d - 1), f = 0, g = 0; g < c;) b = (g - g % 4) / 4, f = g % 4 * 8, e[b] |= a.charCodeAt(g) << f, g++;
              e[(g - g % 4) / 4] |= 128 << g % 4 * 8;
              e[d - 2] = c << 3;
              e[d - 1] = c >>> 29;
              return e;
            }(a);
          k = 1732584193;
          j = 4023233417;
          l = 2562383102;
          m = 271733878;
          for (a = 0; a < f.length; a += 16) o = k, p = j, q = l, n = m, k = c(k, j, l, m, f[a + 0], 7, 3614090360), m = c(m, k, j, l, f[a + 1], 12, 3905402710), l = c(l, m, k, j, f[a + 2], 17, 606105819), j = c(j, l, m, k, f[a + 3], 22, 3250441966), k = c(k, j, l, m, f[a + 4], 7, 4118548399), m = c(m, k, j, l, f[a + 5], 12, 1200080426), l = c(l, m, k, j, f[a + 6], 17, 2821735955), j = c(j, l, m, k, f[a + 7], 22, 4249261313), k = c(k, j, l, m, f[a + 8], 7, 1770035416), m = c(m, k, j, l, f[a + 9], 12, 2336552879), l = c(l, m, k, j, f[a + 10], 17, 4294925233), j = c(j, l, m, k, f[a + 11], 22, 2304563134), k = c(k, j, l, m, f[a + 12], 7, 1804603682), m = c(m, k, j, l, f[a + 13], 12, 4254626195), l = c(l, m, k, j, f[a + 14], 17, 2792965006), j = c(j, l, m, k, f[a + 15], 22, 1236535329), k = d(k, j, l, m, f[a + 1], 5, 4129170786), m = d(m, k, j, l, f[a + 6], 9, 3225465664), l = d(l, m, k, j, f[a + 11], 14, 643717713), j = d(j, l, m, k, f[a + 0], 20, 3921069994), k = d(k, j, l, m, f[a + 5], 5, 3593408605), m = d(m, k, j, l, f[a + 10], 9, 38016083), l = d(l, m, k, j, f[a + 15], 14, 3634488961), j = d(j, l, m, k, f[a + 4], 20, 3889429448), k = d(k, j, l, m, f[a + 9], 5, 568446438), m = d(m, k, j, l, f[a + 14], 9, 3275163606), l = d(l, m, k, j, f[a + 3], 14, 4107603335), j = d(j, l, m, k, f[a + 8], 20, 1163531501), k = d(k, j, l, m, f[a + 13], 5, 2850285829), m = d(m, k, j, l, f[a + 2], 9, 4243563512), l = d(l, m, k, j, f[a + 7], 14, 1735328473), j = d(j, l, m, k, f[a + 12], 20, 2368359562), k = e(k, j, l, m, f[a + 5], 4, 4294588738), m = e(m, k, j, l, f[a + 8], 11, 2272392833), l = e(l, m, k, j, f[a + 11], 16, 1839030562), j = e(j, l, m, k, f[a + 14], 23, 4259657740), k = e(k, j, l, m, f[a + 1], 4, 2763975236), m = e(m, k, j, l, f[a + 4], 11, 1272893353), l = e(l, m, k, j, f[a + 7], 16, 4139469664), j = e(j, l, m, k, f[a + 10], 23, 3200236656), k = e(k, j, l, m, f[a + 13], 4, 681279174), m = e(m, k, j, l, f[a + 0], 11, 3936430074), l = e(l, m, k, j, f[a + 3], 16, 3572445317), j = e(j, l, m, k, f[a + 6], 23, 76029189), k = e(k, j, l, m, f[a + 9], 4, 3654602809), m = e(m, k, j, l, f[a + 12], 11, 3873151461), l = e(l, m, k, j, f[a + 15], 16, 530742520), j = e(j, l, m, k, f[a + 2], 23, 3299628645), k = g(k, j, l, m, f[a + 0], 6, 4096336452), m = g(m, k, j, l, f[a + 7], 10, 1126891415), l = g(l, m, k, j, f[a + 14], 15, 2878612391), j = g(j, l, m, k, f[a + 5], 21, 4237533241), k = g(k, j, l, m, f[a + 12], 6, 1700485571), m = g(m, k, j, l, f[a + 3], 10, 2399980690), l = g(l, m, k, j, f[a + 10], 15, 4293915773), j = g(j, l, m, k, f[a + 1], 21, 2240044497), k = g(k, j, l, m, f[a + 8], 6, 1873313359), m = g(m, k, j, l, f[a + 15], 10, 4264355552), l = g(l, m, k, j, f[a + 6], 15, 2734768916), j = g(j, l, m, k, f[a + 13], 21, 1309151649), k = g(k, j, l, m, f[a + 4], 6, 4149444226), m = g(m, k, j, l, f[a + 11], 10, 3174756917), l = g(l, m, k, j, f[a + 2], 15, 718787259), j = g(j, l, m, k, f[a + 9], 21, 3951481745), k = b(k, o), j = b(j, p), l = b(l, q), m = b(m, n);
          return (h(k) + h(j) + h(l) + h(m)).toLowerCase();
        };
      function parseBigInt(a, b) {
        return new BigInteger(a, b);
      }
      function pkcs1pad2(a, b) {
        if (b < a.length + 11) throw "Message too long for RSA (n=" + b + ", l=" + a.length + ")";
        for (var c = [], d = a.length - 1; d >= 0 && b > 0;) {
          var e = a.charCodeAt(d--);
          e < 128 ? c[--b] = e : e > 127 && e < 2048 ? (c[--b] = e & 63 | 128, c[--b] = e >> 6 | 192) : (c[--b] = e & 63 | 128, c[--b] = e >> 6 & 63 | 128, c[--b] = e >> 12 | 224);
        }
        c[--b] = 0;
        d = new SecureRandom();
        for (e = []; b > 2;) {
          for (e[0] = 0; e[0] == 0;) d.nextBytes(e);
          c[--b] = e[0];
        }
        c[--b] = 2;
        c[--b] = 0;
        return new BigInteger(c);
      }
      function RSAKey() {
        this.n = null;
        this.e = 0;
        this.coeff = this.dmq1 = this.dmp1 = this.q = this.p = this.d = null;
      }
      function RSASetPublic(a, b) {
        a != null && b != null && a.length > 0 && b.length > 0 ? (this.n = parseBigInt(a, 16), this.e = parseInt(b, 16)) : alert("Invalid RSA public key");
      }
      function RSADoPublic(a) {
        return a.modPowInt(this.e, this.n);
      }
      function RSAEncrypt(a) {
        a = pkcs1pad2(a, this.n.bitLength() + 7 >> 3);
        if (a == null) return null;
        a = this.doPublic(a);
        if (a == null) return null;
        a = a.toString(16);
        return (a.length & 1) == 0 ? a : "0" + a;
      }
      RSAKey.prototype.doPublic = RSADoPublic;
      RSAKey.prototype.setPublic = RSASetPublic;
      RSAKey.prototype.encrypt = RSAEncrypt;
      function pkcs1unpad2(a, b) {
        for (var c = a.toByteArray(), d = 0; d < c.length && c[d] == 0;) ++d;
        if (c.length - d != b - 1 || c[d] != 2) return null;
        for (++d; c[d] != 0;) if (++d >= c.length) return null;
        for (var e = ""; ++d < c.length;) {
          var g = c[d] & 255;
          g < 128 ? e += String.fromCharCode(g) : g > 191 && g < 224 ? (e += String.fromCharCode((g & 31) << 6 | c[d + 1] & 63), ++d) : (e += String.fromCharCode((g & 15) << 12 | (c[d + 1] & 63) << 6 | c[d + 2] & 63), d += 2);
        }
        return e;
      }
      function RSASetPrivate(a, b, c) {
        a != null && b != null && a.length > 0 && b.length > 0 ? (this.n = parseBigInt(a, 16), this.e = parseInt(b, 16), this.d = parseBigInt(c, 16)) : alert("Invalid RSA private key");
      }
      function RSASetPrivateEx(a, b, c, d, e, g, h, f) {
        a != null && b != null && a.length > 0 && b.length > 0 ? (this.n = parseBigInt(a, 16), this.e = parseInt(b, 16), this.d = parseBigInt(c, 16), this.p = parseBigInt(d, 16), this.q = parseBigInt(e, 16), this.dmp1 = parseBigInt(g, 16), this.dmq1 = parseBigInt(h, 16), this.coeff = parseBigInt(f, 16)) : alert("Invalid RSA private key");
      }
      function RSAGenerate(a, b) {
        var c = new SeededRandom(),
          d = a >> 1;
        this.e = parseInt(b, 16);
        for (var e = new BigInteger(b, 16);;) {
          for (;;) if (this.p = new BigInteger(a - d, 1, c), this.p.subtract(BigInteger.ONE).gcd(e).compareTo(BigInteger.ONE) == 0 && this.p.isProbablePrime(10)) break;
          for (;;) if (this.q = new BigInteger(d, 1, c), this.q.subtract(BigInteger.ONE).gcd(e).compareTo(BigInteger.ONE) == 0 && this.q.isProbablePrime(10)) break;
          if (this.p.compareTo(this.q) <= 0) {
            var g = this.p;
            this.p = this.q;
            this.q = g;
          }
          var g = this.p.subtract(BigInteger.ONE),
            h = this.q.subtract(BigInteger.ONE),
            f = g.multiply(h);
          if (f.gcd(e).compareTo(BigInteger.ONE) == 0) {
            this.n = this.p.multiply(this.q);
            this.d = e.modInverse(f);
            this.dmp1 = this.d.mod(g);
            this.dmq1 = this.d.mod(h);
            this.coeff = this.q.modInverse(this.p);
            break;
          }
        }
      }
      function RSADoPrivate(a) {
        if (this.p == null || this.q == null) return a.modPow(this.d, this.n);
        for (var b = a.mod(this.p).modPow(this.dmp1, this.p), a = a.mod(this.q).modPow(this.dmq1, this.q); b.compareTo(a) < 0;) b = b.add(this.p);
        return b.subtract(a).multiply(this.coeff).mod(this.p).multiply(this.q).add(a);
      }
      function RSADecrypt(a) {
        a = this.doPrivate(parseBigInt(a, 16));
        return a == null ? null : pkcs1unpad2(a, this.n.bitLength() + 7 >> 3);
      }
      RSAKey.prototype.doPrivate = RSADoPrivate;
      RSAKey.prototype.setPrivate = RSASetPrivate;
      RSAKey.prototype.setPrivateEx = RSASetPrivateEx;
      RSAKey.prototype.generate = RSAGenerate;
      RSAKey.prototype.decrypt = RSADecrypt;
      var _RSASIGN_DIHEAD = [];
      _RSASIGN_DIHEAD.sha1 = "3021300906052b0e03021a05000414";
      _RSASIGN_DIHEAD.sha256 = "3031300d060960864801650304020105000420";
      var _RSASIGN_HASHHEXFUNC = [];
      _RSASIGN_HASHHEXFUNC.sha1 = sha1.hex;
      _RSASIGN_HASHHEXFUNC.sha256 = sha256.hex;
      function _rsasign_getHexPaddedDigestInfoForString(a, b, c) {
        b /= 4;
        for (var a = (0, _RSASIGN_HASHHEXFUNC[c])(a), c = "00" + _RSASIGN_DIHEAD[c] + a, a = "", b = b - 4 - c.length, d = 0; d < b; d += 2) a += "ff";
        return sPaddedMessageHex = "0001" + a + c;
      }
      function _rsasign_signString(a, b) {
        var c = _rsasign_getHexPaddedDigestInfoForString(a, this.n.bitLength(), b);
        return this.doPrivate(parseBigInt(c, 16)).toString(16);
      }
      function _rsasign_signStringWithSHA1(a) {
        a = _rsasign_getHexPaddedDigestInfoForString(a, this.n.bitLength(), "sha1");
        return this.doPrivate(parseBigInt(a, 16)).toString(16);
      }
      function _rsasign_signStringWithSHA256(a) {
        a = _rsasign_getHexPaddedDigestInfoForString(a, this.n.bitLength(), "sha256");
        return this.doPrivate(parseBigInt(a, 16)).toString(16);
      }
      function _rsasign_getDecryptSignatureBI(a, b, c) {
        var d = new RSAKey();
        d.setPublic(b, c);
        return d.doPublic(a);
      }
      function _rsasign_getHexDigestInfoFromSig(a, b, c) {
        return _rsasign_getDecryptSignatureBI(a, b, c).toString(16).replace(/^1f+00/, "");
      }
      function _rsasign_getAlgNameAndHashFromHexDisgestInfo(a) {
        for (var b in _RSASIGN_DIHEAD) {
          var c = _RSASIGN_DIHEAD[b],
            d = c.length;
          if (a.substring(0, d) == c) return [b, a.substring(d)];
        }
        return [];
      }
      function _rsasign_verifySignatureWithArgs(a, b, c, d) {
        b = _rsasign_getHexDigestInfoFromSig(b, c, d);
        c = _rsasign_getAlgNameAndHashFromHexDisgestInfo(b);
        if (c.length == 0) return !1;
        b = c[1];
        a = (0, _RSASIGN_HASHHEXFUNC[c[0]])(a);
        return b == a;
      }
      function _rsasign_verifyHexSignatureForMessage(a, b) {
        var c = parseBigInt(a, 16);
        return _rsasign_verifySignatureWithArgs(b, c, this.n.toString(16), this.e.toString(16));
      }
      function _rsasign_verifyString(a, b) {
        var b = b.replace(/[ \n]+/g, ""),
          c = this.doPublic(parseBigInt(b, 16)).toString(16).replace(/^1f+00/, ""),
          d = _rsasign_getAlgNameAndHashFromHexDisgestInfo(c);
        if (d.length == 0) return !1;
        c = d[1];
        d = (0, _RSASIGN_HASHHEXFUNC[d[0]])(a);
        return c == d;
      }
      RSAKey.prototype.signString = _rsasign_signString;
      RSAKey.prototype.signStringWithSHA1 = _rsasign_signStringWithSHA1;
      RSAKey.prototype.signStringWithSHA256 = _rsasign_signStringWithSHA256;
      RSAKey.prototype.verifyString = _rsasign_verifyString;
      RSAKey.prototype.verifyHexSignatureForMessage = _rsasign_verifyHexSignatureForMessage;
      var aes = function () {
          var a = {
            Sbox: [99, 124, 119, 123, 242, 107, 111, 197, 48, 1, 103, 43, 254, 215, 171, 118, 202, 130, 201, 125, 250, 89, 71, 240, 173, 212, 162, 175, 156, 164, 114, 192, 183, 253, 147, 38, 54, 63, 247, 204, 52, 165, 229, 241, 113, 216, 49, 21, 4, 199, 35, 195, 24, 150, 5, 154, 7, 18, 128, 226, 235, 39, 178, 117, 9, 131, 44, 26, 27, 110, 90, 160, 82, 59, 214, 179, 41, 227, 47, 132, 83, 209, 0, 237, 32, 252, 177, 91, 106, 203, 190, 57, 74, 76, 88, 207, 208, 239, 170, 251, 67, 77, 51, 133, 69, 249, 2, 127, 80, 60, 159, 168, 81, 163, 64, 143, 146, 157, 56, 245, 188, 182, 218, 33, 16, 255, 243, 210, 205, 12, 19, 236, 95, 151, 68, 23, 196, 167, 126, 61, 100, 93, 25, 115, 96, 129, 79, 220, 34, 42, 144, 136, 70, 238, 184, 20, 222, 94, 11, 219, 224, 50, 58, 10, 73, 6, 36, 92, 194, 211, 172, 98, 145, 149, 228, 121, 231, 200, 55, 109, 141, 213, 78, 169, 108, 86, 244, 234, 101, 122, 174, 8, 186, 120, 37, 46, 28, 166, 180, 198, 232, 221, 116, 31, 75, 189, 139, 138, 112, 62, 181, 102, 72, 3, 246, 14, 97, 53, 87, 185, 134, 193, 29, 158, 225, 248, 152, 17, 105, 217, 142, 148, 155, 30, 135, 233, 206, 85, 40, 223, 140, 161, 137, 13, 191, 230, 66, 104, 65, 153, 45, 15, 176, 84, 187, 22],
            ShiftRowTab: [0, 5, 10, 15, 4, 9, 14, 3, 8, 13, 2, 7, 12, 1, 6, 11]
          };
          a.Init = function () {
            a.Sbox_Inv = Array(256);
            for (var b = 0; b < 256; b++) a.Sbox_Inv[a.Sbox[b]] = b;
            a.ShiftRowTab_Inv = Array(16);
            for (b = 0; b < 16; b++) a.ShiftRowTab_Inv[a.ShiftRowTab[b]] = b;
            a.xtime = Array(256);
            for (b = 0; b < 128; b++) a.xtime[b] = b << 1, a.xtime[128 + b] = b << 1 ^ 27;
          };
          a.Done = function () {
            delete a.Sbox_Inv;
            delete a.ShiftRowTab_Inv;
            delete a.xtime;
          };
          a.ExpandKey = function (b) {
            var c = b.length,
              d,
              e = 1;
            switch (c) {
              case 16:
                d = 176;
                break;
              case 24:
                d = 208;
                break;
              case 32:
                d = 240;
                break;
              default:
                alert("my.ExpandKey: Only key lengths of 16, 24 or 32 bytes allowed!");
            }
            for (var g = c; g < d; g += 4) {
              var h = b.slice(g - 4, g);
              if (g % c == 0) {
                if (h = [a.Sbox[h[1]] ^ e, a.Sbox[h[2]], a.Sbox[h[3]], a.Sbox[h[0]]], (e <<= 1) >= 256) e ^= 283;
              } else c > 24 && g % c == 16 && (h = [a.Sbox[h[0]], a.Sbox[h[1]], a.Sbox[h[2]], a.Sbox[h[3]]]);
              for (var f = 0; f < 4; f++) b[g + f] = b[g + f - c] ^ h[f];
            }
          };
          a.Encrypt = function (b, c) {
            var d = c.length;
            a.AddRoundKey(b, c.slice(0, 16));
            for (var e = 16; e < d - 16; e += 16) a.SubBytes(b, a.Sbox), a.ShiftRows(b, a.ShiftRowTab), a.MixColumns(b), a.AddRoundKey(b, c.slice(e, e + 16));
            a.SubBytes(b, a.Sbox);
            a.ShiftRows(b, a.ShiftRowTab);
            a.AddRoundKey(b, c.slice(e, d));
          };
          a.Decrypt = function (b, c) {
            var d = c.length;
            a.AddRoundKey(b, c.slice(d - 16, d));
            a.ShiftRows(b, a.ShiftRowTab_Inv);
            a.SubBytes(b, a.Sbox_Inv);
            for (d -= 32; d >= 16; d -= 16) a.AddRoundKey(b, c.slice(d, d + 16)), a.MixColumns_Inv(b), a.ShiftRows(b, a.ShiftRowTab_Inv), a.SubBytes(b, a.Sbox_Inv);
            a.AddRoundKey(b, c.slice(0, 16));
          };
          a.SubBytes = function (a, c) {
            for (var d = 0; d < 16; d++) a[d] = c[a[d]];
          };
          a.AddRoundKey = function (a, c) {
            for (var d = 0; d < 16; d++) a[d] ^= c[d];
          };
          a.ShiftRows = function (a, c) {
            for (var d = [].concat(a), e = 0; e < 16; e++) a[e] = d[c[e]];
          };
          a.MixColumns = function (b) {
            for (var c = 0; c < 16; c += 4) {
              var d = b[c + 0],
                e = b[c + 1],
                g = b[c + 2],
                h = b[c + 3],
                f = d ^ e ^ g ^ h;
              b[c + 0] ^= f ^ a.xtime[d ^ e];
              b[c + 1] ^= f ^ a.xtime[e ^ g];
              b[c + 2] ^= f ^ a.xtime[g ^ h];
              b[c + 3] ^= f ^ a.xtime[h ^ d];
            }
          };
          a.MixColumns_Inv = function (b) {
            for (var c = 0; c < 16; c += 4) {
              var d = b[c + 0],
                e = b[c + 1],
                g = b[c + 2],
                h = b[c + 3],
                f = d ^ e ^ g ^ h,
                o = a.xtime[f],
                p = a.xtime[a.xtime[o ^ d ^ g]] ^ f;
              f ^= a.xtime[a.xtime[o ^ e ^ h]];
              b[c + 0] ^= p ^ a.xtime[d ^ e];
              b[c + 1] ^= f ^ a.xtime[e ^ g];
              b[c + 2] ^= p ^ a.xtime[g ^ h];
              b[c + 3] ^= f ^ a.xtime[h ^ d];
            }
          };
          return a;
        }(),
        cryptico = function () {
          var a = {};
          aes.Init();
          a.b256to64 = function (a) {
            var c,
              d,
              e,
              g = "",
              f = 0,
              o = a.length;
            for (e = 0; e < o; e++) d = a.charCodeAt(e), f == 0 ? (g += "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(d >> 2 & 63), c = (d & 3) << 4) : f == 1 ? (g += "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(c | d >> 4 & 15), c = (d & 15) << 2) : f == 2 && (g += "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(c | d >> 6 & 3), g += "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(d & 63)), f += 1, f == 3 && (f = 0);
            f > 0 && (g += "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(c), g += "=");
            f == 1 && (g += "=");
            return g;
          };
          a.b64to256 = function (a) {
            var c,
              d,
              e = "",
              g = 0,
              h = 0,
              f = a.length;
            for (d = 0; d < f; d++) c = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".indexOf(a.charAt(d)), c >= 0 && (g && (e += String.fromCharCode(h | c >> 6 - g & 255)), g = g + 2 & 7, h = c << g & 255);
            return e;
          };
          a.b16to64 = function (a) {
            var c,
              d,
              e = "";
            a.length % 2 == 1 && (a = "0" + a);
            for (c = 0; c + 3 <= a.length; c += 3) d = parseInt(a.substring(c, c + 3), 16), e += "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(d >> 6) + "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(d & 63);
            c + 1 == a.length ? (d = parseInt(a.substring(c, c + 1), 16), e += "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(d << 2)) : c + 2 == a.length && (d = parseInt(a.substring(c, c + 2), 16), e += "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(d >> 2) + "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt((d & 3) << 4));
            for (; (e.length & 3) > 0;) e += "=";
            return e;
          };
          a.b64to16 = function (a) {
            var c = "",
              d,
              e = 0,
              g;
            for (d = 0; d < a.length; ++d) {
              if (a.charAt(d) == "=") break;
              v = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".indexOf(a.charAt(d));
              v < 0 || (e == 0 ? (c += int2char(v >> 2), g = v & 3, e = 1) : e == 1 ? (c += int2char(g << 2 | v >> 4), g = v & 15, e = 2) : e == 2 ? (c += int2char(g), c += int2char(v >> 2), g = v & 3, e = 3) : (c += int2char(g << 2 | v >> 4), c += int2char(v & 15), e = 0));
            }
            e == 1 && (c += int2char(g << 2));
            return c;
          };
          a.string2bytes = function (a) {
            for (var c = [], d = 0; d < a.length; d++) c.push(a.charCodeAt(d));
            return c;
          };
          a.bytes2string = function (a) {
            for (var c = "", d = 0; d < a.length; d++) c += String.fromCharCode(a[d]);
            return c;
          };
          a.blockXOR = function (a, c) {
            for (var d = Array(16), e = 0; e < 16; e++) d[e] = a[e] ^ c[e];
            return d;
          };
          a.blockIV = function () {
            var a = new SecureRandom(),
              c = Array(16);
            a.nextBytes(c);
            return c;
          };
          a.pad16 = function (a) {
            var c = a.slice(0),
              d = (16 - a.length % 16) % 16;
            for (i = a.length; i < a.length + d; i++) c.push(0);
            return c;
          };
          a.depad = function (a) {
            for (a = a.slice(0); a[a.length - 1] == 0;) a = a.slice(0, a.length - 1);
            return a;
          };
          a.encryptAESCBC = function (b, c) {
            var d = c.slice(0);
            aes.ExpandKey(d);
            for (var e = a.string2bytes(b), e = a.pad16(e), g = a.blockIV(), h = 0; h < e.length / 16; h++) {
              var f = e.slice(h * 16, h * 16 + 16),
                o = g.slice(h * 16, h * 16 + 16),
                f = a.blockXOR(o, f);
              aes.Encrypt(f, d);
              g = g.concat(f);
            }
            d = a.bytes2string(g);
            return a.b256to64(d);
          };
          a.decryptAESCBC = function (b, c) {
            var d = c.slice(0);
            aes.ExpandKey(d);
            for (var b = a.b64to256(b), e = a.string2bytes(b), g = [], h = 1; h < e.length / 16; h++) {
              var f = e.slice(h * 16, h * 16 + 16),
                o = e.slice((h - 1) * 16, (h - 1) * 16 + 16);
              aes.Decrypt(f, d);
              f = a.blockXOR(o, f);
              g = g.concat(f);
            }
            g = a.depad(g);
            return a.bytes2string(g);
          };
          a.wrap60 = function (a) {
            for (var c = "", d = 0; d < a.length; d++) d % 60 == 0 && d != 0 && (c += "\n"), c += a[d];
            return c;
          };
          a.generateAESKey = function () {
            var a = Array(16);
            new SecureRandom().nextBytes(a);
            return a;
          };
          a.generateRSAKey = function (a, c) {
            Math.seedrandom(sha256.hex(a));
            var d = new RSAKey();
            d.generate(c, "10001");
            return d;
          };
          a.publicKeyString = function (b) {
            return pubkey = b.n.toString(16);
          };
          a.publicKeyID = function (a) {
            return MD5(a);
          };
          a.publicKeyFromString = function (b) {
            var b = b.split("|")[0],
              c = new RSAKey();
            c.setPublic(b, "10001");
            return c;
          };
          a.encrypt = function (b, c, d) {
            var e = "";
            try {
              var h = a.publicKeyFromString(c);
              e += h.encrypt(b) + "?";
            } catch (f) {
              return {
                status: "Invalid public key"
              };
            }
            return {
              status: "success",
              cipher: e
            };
          };
          a.decrypt = function (b, c) {
            var d = b.split("?"),
              e = c.decrypt(d[0]);
            return {
              status: "success",
              plaintext: e,
              signature: "unsigned"
            };
          };
          return a;
        }();
      module.exports = cryptico;

      /***/
    }, /* 1 */
    /***/function (module, exports, __webpack_require__) {
      module.exports = __webpack_require__(2);

      /***/
    }, /* 2 */
    /***/function (module, __webpack_exports__, __webpack_require__) {

      // ESM COMPAT FLAG
      __webpack_require__.r(__webpack_exports__);

      // EXPORTS
      __webpack_require__.d(__webpack_exports__, "StreamClient", function () {
        return (/* binding */StreamClient
        );
      });

      // CONCATENATED MODULE: ./node_modules/uuid/dist/esm-browser/rng.js
      // Unique ID creation requires a high quality random # generator. In the browser we therefore
      // require the crypto API and do not support built-in fallback to lower quality random number
      // generators (like Math.random()).
      var getRandomValues;
      var rnds8 = new Uint8Array(16);
      function rng() {
        // lazy load so that environments that need to polyfill have a chance to do so
        if (!getRandomValues) {
          // getRandomValues needs to be invoked in a context where "this" is a Crypto implementation. Also,
          // find the complete implementation of crypto (msCrypto) on IE11.
          getRandomValues = typeof crypto !== 'undefined' && crypto.getRandomValues && crypto.getRandomValues.bind(crypto) || typeof msCrypto !== 'undefined' && typeof msCrypto.getRandomValues === 'function' && msCrypto.getRandomValues.bind(msCrypto);
          if (!getRandomValues) {
            throw new Error('crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported');
          }
        }
        return getRandomValues(rnds8);
      }
      // CONCATENATED MODULE: ./node_modules/uuid/dist/esm-browser/regex.js
      /* harmony default export */
      var regex = /^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i;
      // CONCATENATED MODULE: ./node_modules/uuid/dist/esm-browser/validate.js

      function validate(uuid) {
        return typeof uuid === 'string' && regex.test(uuid);
      }

      /* harmony default export */
      var esm_browser_validate = validate;
      // CONCATENATED MODULE: ./node_modules/uuid/dist/esm-browser/stringify.js

      /**
       * Convert array of 16 byte values to UUID string format of the form:
       * XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX
       */

      var byteToHex = [];
      for (var stringify_i = 0; stringify_i < 256; ++stringify_i) {
        byteToHex.push((stringify_i + 0x100).toString(16).substr(1));
      }
      function stringify(arr) {
        var offset = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;
        // Note: Be careful editing this code!  It's been tuned for performance
        // and works in ways you may not expect. See https://github.com/uuidjs/uuid/pull/434
        var uuid = (byteToHex[arr[offset + 0]] + byteToHex[arr[offset + 1]] + byteToHex[arr[offset + 2]] + byteToHex[arr[offset + 3]] + '-' + byteToHex[arr[offset + 4]] + byteToHex[arr[offset + 5]] + '-' + byteToHex[arr[offset + 6]] + byteToHex[arr[offset + 7]] + '-' + byteToHex[arr[offset + 8]] + byteToHex[arr[offset + 9]] + '-' + byteToHex[arr[offset + 10]] + byteToHex[arr[offset + 11]] + byteToHex[arr[offset + 12]] + byteToHex[arr[offset + 13]] + byteToHex[arr[offset + 14]] + byteToHex[arr[offset + 15]]).toLowerCase(); // Consistency check for valid UUID.  If this throws, it's likely due to one
        // of the following:
        // - One or more input array values don't map to a hex octet (leading to
        // "undefined" in the uuid)
        // - Invalid input values for the RFC `version` or `variant` fields

        if (!esm_browser_validate(uuid)) {
          throw TypeError('Stringified UUID is invalid');
        }
        return uuid;
      }

      /* harmony default export */
      var esm_browser_stringify = stringify;
      // CONCATENATED MODULE: ./node_modules/uuid/dist/esm-browser/v4.js

      function v4(options, buf, offset) {
        options = options || {};
        var rnds = options.random || (options.rng || rng)(); // Per 4.4, set bits for version and `clock_seq_hi_and_reserved`

        rnds[6] = rnds[6] & 0x0f | 0x40;
        rnds[8] = rnds[8] & 0x3f | 0x80; // Copy bytes to buffer, if provided

        if (buf) {
          offset = offset || 0;
          for (var i = 0; i < 16; ++i) {
            buf[offset + i] = rnds[i];
          }
          return buf;
        }
        return esm_browser_stringify(rnds);
      }

      /* harmony default export */
      var esm_browser_v4 = v4;
      // CONCATENATED MODULE: ./directDeviceCustom.js
      /**
       * @synopsis 定制设备取流
       *
       * @note [ADD][2017-07-28]新建 by fengzhongjian
       *
       */
      let DirectDeviceCustom = function () {
        class DeviceDirect {
          constructor() {}
          //to do

          //创建取流客户端对象
          createClientObject(oWebsocket, szId, iCurChannel, iCurStream) {
            return {
              socket: oWebsocket,
              id: szId,
              iCurChannel: iCurChannel,
              iCurStream: iCurStream,
              resolve: null,
              reject: null
            };
          }
          //零通道预览
          zeroPlayCmd(iCurChannel, iCurStream) {
            let aCmd = [0x00, 0x00, 0x00, 0x2c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x13, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, iCurChannel + 1, 0x00, 0x00, 0x00, iCurStream, 0x00, 0x00, 0x04, 0x00];
            return new Uint8Array(aCmd);
          }
          //普通通道预览
          playCmd(iCurChannel, iCurStream) {
            let aCmd = [0x00, 0x00, 0x00, 0x2c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, iCurChannel, 0x00, 0x00, 0x00, iCurStream, 0x00, 0x00, 0x04, 0x00];
            return new Uint8Array(aCmd);
          }
          //回放
          playbackCmd(szStartTime, szStopTime, iCurChannel, iCurStream) {
            let szStartDayMonthYear = szStartTime.split("T")[0];
            let szStartHourMinSec = szStartTime.split("T")[1];
            let szStartYear = "0" + parseInt(szStartDayMonthYear.substring(0, 4), 10).toString(16);
            let iStartMonth = parseInt(szStartDayMonthYear.substring(4, 6), 10);
            let iStartDay = parseInt(szStartDayMonthYear.substring(6), 10);
            let iStartHour = parseInt(szStartHourMinSec.substring(0, 2), 10);
            let iStartMin = parseInt(szStartHourMinSec.substring(2, 4), 10);
            let iStartSec = parseInt(szStartHourMinSec.substring(4, 6), 10);
            let szStopDayMonthYear = szStopTime.split("T")[0];
            let szStopHourMinSec = szStopTime.split("T")[1];
            let szStopYear = "0" + parseInt(szStopDayMonthYear.substring(0, 4), 10).toString(16);
            let iStopMonth = parseInt(szStopDayMonthYear.substring(4, 6), 10);
            //let iStopDay = parseInt(szStopDayMonthYear.substring(6), 10);
            let iStopHour = parseInt(szStopHourMinSec.substring(0, 2), 10);
            let iStopMin = parseInt(szStopHourMinSec.substring(2, 4), 10);
            let iStopSec = parseInt(szStopHourMinSec.substring(4, 6), 10);
            let aCmd = [/*header*/0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x01, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /*channel*/0x00, 0x00, 0x00, iCurChannel, /*start time*/0x00, 0x00, parseInt(szStartYear.substring(0, 2), 16), parseInt(szStartYear.substring(2, 4), 16), 0x00, 0x00, 0x00, iStartMonth, 0x00, 0x00, 0x00, iStartDay, 0x00, 0x00, 0x00, iStartHour, 0x00, 0x00, 0x00, iStartMin, 0x00, 0x00, 0x00, iStartSec, /*end time*/0x00, 0x00, parseInt(szStopYear.substring(0, 2), 16), parseInt(szStopYear.substring(2, 4), 16), 0x00, 0x00, 0x00, iStopMonth, 0x00, 0x00, 0x00, iStartDay, 0x00, 0x00, 0x00, iStopHour, 0x00, 0x00, 0x00, iStopMin, 0x00, 0x00, 0x00, iStopSec, /*是否抽帧*/0x00, /*是否下载*/0x00, /*录像卷类型 0普通卷，1存档卷*/0x00, /*存档卷号*/0x00, /*存档卷上的录像文件索引*/0x00, 0x00, 0x00, 0x00, /*码流类型 0主码流，1子码流，2三码流*/iCurStream, /*保留字*/0x00, 0x00, 0x00];
            return new Uint8Array(aCmd);
          }
          //回放速率
          playRateCmd(iRate) {
            let szHex = (parseInt(iRate, 10) >>> 0).toString(16).toLocaleUpperCase().toString(16);
            for (let j = szHex.length; j < 8; j++) {
              //对字符串进行补0，筹齐8位
              szHex = "0" + szHex;
            }
            let aRate = [0, 0, 0, 0]; //4字节16机制表示
            for (let j = 0, iLenRate = szHex.length; j < iLenRate; j = j + 2) {
              aRate[Math.floor(j / 2)] = parseInt(szHex.substring(j, j + 2), 16);
            }
            let aCmd = [/*header*/0x00, 0x00, 0x00, 0x24, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x01, 0x2f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, aRate[0], aRate[1], aRate[2], aRate[3]];
            return new Uint8Array(aCmd);
          }
          //回放暂停
          pauseCmd() {
            let aCmd = [/*header*/0x00, 0x00, 0x00, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x01, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00];
            return new Uint8Array(aCmd);
          }
          //恢复命令
          resumeCmd() {
            let aCmd = [/*header*/0x00, 0x00, 0x00, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x01, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00];
            return new Uint8Array(aCmd);
          }
        }
        return DeviceDirect;
      }();

      // CONCATENATED MODULE: ./directDevice.js
      /**
       * @synopsis 直连设备取流
       *
       * @note [ADD][2017-07-28]新建 by fengzhongjian
       *
       */
      const ERROR_STREAM_UNKNOWN = 3001; //未知取流错误
      const ERROR_STREAM_LIMIT = 3002; //资源上限
      const ERROR_BAD_AUTH = 3003; //认证失败

      let DirectDevice = function () {
        class DeviceDirect {
          constructor() {}
          //to do

          //创建取流客户端对象
          createClientObject(oWebsocket, szId, szPlayURL) {
            return {
              socket: oWebsocket,
              id: szId,
              playURL: szPlayURL,
              resolve: null,
              reject: null
            };
          }
          //从sdp信息获取媒体头
          getMediaFromSdp(szSdp) {
            let iMediaIndex = szSdp.indexOf("MEDIAINFO=") + 10;
            let szMediaInfo = szSdp.slice(iMediaIndex, iMediaIndex + 80);
            let aMediaInfo = [];
            for (let i = 0, iLen = szMediaInfo.length / 2; i < iLen; i++) {
              aMediaInfo[i] = parseInt(szMediaInfo.slice(i * 2, i * 2 + 2), 16);
            }
            return new Uint8Array(aMediaInfo);
          }
          //普通通道预览
          playCmd(szPlayURL) {
            let oCmd = {
              sequence: 0,
              cmd: 'realplay',
              url: szPlayURL
            };
            return JSON.stringify(oCmd);
          }
          //回放
          playbackCmd(szStartTime, szStopTime, szPlayURL) {
            let oCmd = {
              sequence: 0,
              cmd: 'playback',
              url: szPlayURL,
              startTime: szStartTime,
              endTime: szStopTime
            };
            return JSON.stringify(oCmd);
          }
          //回放速率
          playRateCmd(iRate) {
            let oCmd = {
              sequence: 0,
              cmd: "changespeed",
              speed: iRate
            };
            return JSON.stringify(oCmd);
          }
          //回放暂停
          pauseCmd() {
            let oCmd = {
              sequence: 0,
              cmd: "pause"
            };
            return JSON.stringify(oCmd);
          }
          //恢复命令
          resumeCmd() {
            let oCmd = {
              sequence: 0,
              cmd: "resume"
            };
            return JSON.stringify(oCmd);
          }
          //获取取流错误
          getError(oError) {
            let iErrorNum = ERROR_STREAM_UNKNOWN;
            if (oError) {
              if (parseInt(oError.statusCode, 10) === 6 && oError.subStatusCode === "streamLimit") {
                iErrorNum = ERROR_STREAM_LIMIT;
              } else if (parseInt(oError.statusCode, 10) === 4 && oError.subStatusCode === "badAuthorization") {
                iErrorNum = ERROR_BAD_AUTH;
              }
            }
            return {
              iErrorNum: iErrorNum,
              oError: oError
            };
          }
        }
        return DeviceDirect;
      }();

      // EXTERNAL MODULE: ./tool/cryptico.js
      var cryptico = __webpack_require__(0);
      var cryptico_default = /*#__PURE__*/__webpack_require__.n(cryptico);

      // CONCATENATED MODULE: ./tool/aes.js
      /*
      CryptoJS v3.1.2
      code.google.com/p/crypto-js
      (c) 2009-2013 by Jeff Mott. All rights reserved.
      code.google.com/p/crypto-js/wiki/License
      */
      var CryptoJS = CryptoJS || function (u, p) {
        var d = {},
          l = d.lib = {},
          s = function () {},
          t = l.Base = {
            extend: function (a) {
              s.prototype = this;
              var c = new s();
              a && c.mixIn(a);
              c.hasOwnProperty("init") || (c.init = function () {
                c.$super.init.apply(this, arguments);
              });
              c.init.prototype = c;
              c.$super = this;
              return c;
            },
            create: function () {
              var a = this.extend();
              a.init.apply(a, arguments);
              return a;
            },
            init: function () {},
            mixIn: function (a) {
              for (var c in a) a.hasOwnProperty(c) && (this[c] = a[c]);
              a.hasOwnProperty("toString") && (this.toString = a.toString);
            },
            clone: function () {
              return this.init.prototype.extend(this);
            }
          },
          r = l.WordArray = t.extend({
            init: function (a, c) {
              a = this.words = a || [];
              this.sigBytes = c != p ? c : 4 * a.length;
            },
            toString: function (a) {
              return (a || v).stringify(this);
            },
            concat: function (a) {
              var c = this.words,
                e = a.words,
                j = this.sigBytes;
              a = a.sigBytes;
              this.clamp();
              if (j % 4) for (var k = 0; k < a; k++) c[j + k >>> 2] |= (e[k >>> 2] >>> 24 - 8 * (k % 4) & 255) << 24 - 8 * ((j + k) % 4);else if (65535 < e.length) for (k = 0; k < a; k += 4) c[j + k >>> 2] = e[k >>> 2];else c.push.apply(c, e);
              this.sigBytes += a;
              return this;
            },
            clamp: function () {
              var a = this.words,
                c = this.sigBytes;
              a[c >>> 2] &= ********** << 32 - 8 * (c % 4);
              a.length = u.ceil(c / 4);
            },
            clone: function () {
              var a = t.clone.call(this);
              a.words = this.words.slice(0);
              return a;
            },
            random: function (a) {
              for (var c = [], e = 0; e < a; e += 4) c.push(4294967296 * u.random() | 0);
              return new r.init(c, a);
            }
          }),
          w = d.enc = {},
          v = w.Hex = {
            stringify: function (a) {
              var c = a.words;
              a = a.sigBytes;
              for (var e = [], j = 0; j < a; j++) {
                var k = c[j >>> 2] >>> 24 - 8 * (j % 4) & 255;
                e.push((k >>> 4).toString(16));
                e.push((k & 15).toString(16));
              }
              return e.join("");
            },
            parse: function (a) {
              for (var c = a.length, e = [], j = 0; j < c; j += 2) e[j >>> 3] |= parseInt(a.substr(j, 2), 16) << 24 - 4 * (j % 8);
              return new r.init(e, c / 2);
            }
          },
          b = w.Latin1 = {
            stringify: function (a) {
              var c = a.words;
              a = a.sigBytes;
              for (var e = [], j = 0; j < a; j++) e.push(String.fromCharCode(c[j >>> 2] >>> 24 - 8 * (j % 4) & 255));
              return e.join("");
            },
            parse: function (a) {
              for (var c = a.length, e = [], j = 0; j < c; j++) e[j >>> 2] |= (a.charCodeAt(j) & 255) << 24 - 8 * (j % 4);
              return new r.init(e, c);
            }
          },
          x = w.Utf8 = {
            stringify: function (a) {
              try {
                return decodeURIComponent(escape(b.stringify(a)));
              } catch (c) {
                throw Error("Malformed UTF-8 data");
              }
            },
            parse: function (a) {
              return b.parse(unescape(encodeURIComponent(a)));
            }
          },
          q = l.BufferedBlockAlgorithm = t.extend({
            reset: function () {
              this._data = new r.init();
              this._nDataBytes = 0;
            },
            _append: function (a) {
              "string" == typeof a && (a = x.parse(a));
              this._data.concat(a);
              this._nDataBytes += a.sigBytes;
            },
            _process: function (a) {
              var c = this._data,
                e = c.words,
                j = c.sigBytes,
                k = this.blockSize,
                b = j / (4 * k),
                b = a ? u.ceil(b) : u.max((b | 0) - this._minBufferSize, 0);
              a = b * k;
              j = u.min(4 * a, j);
              if (a) {
                for (var q = 0; q < a; q += k) this._doProcessBlock(e, q);
                q = e.splice(0, a);
                c.sigBytes -= j;
              }
              return new r.init(q, j);
            },
            clone: function () {
              var a = t.clone.call(this);
              a._data = this._data.clone();
              return a;
            },
            _minBufferSize: 0
          });
        l.Hasher = q.extend({
          cfg: t.extend(),
          init: function (a) {
            this.cfg = this.cfg.extend(a);
            this.reset();
          },
          reset: function () {
            q.reset.call(this);
            this._doReset();
          },
          update: function (a) {
            this._append(a);
            this._process();
            return this;
          },
          finalize: function (a) {
            a && this._append(a);
            return this._doFinalize();
          },
          blockSize: 16,
          _createHelper: function (a) {
            return function (b, e) {
              return new a.init(e).finalize(b);
            };
          },
          _createHmacHelper: function (a) {
            return function (b, e) {
              return new n.HMAC.init(a, e).finalize(b);
            };
          }
        });
        var n = d.algo = {};
        return d;
      }(Math);
      (function () {
        var u = CryptoJS,
          p = u.lib.WordArray;
        u.enc.Base64 = {
          stringify: function (d) {
            var l = d.words,
              p = d.sigBytes,
              t = this._map;
            d.clamp();
            d = [];
            for (var r = 0; r < p; r += 3) for (var w = (l[r >>> 2] >>> 24 - 8 * (r % 4) & 255) << 16 | (l[r + 1 >>> 2] >>> 24 - 8 * ((r + 1) % 4) & 255) << 8 | l[r + 2 >>> 2] >>> 24 - 8 * ((r + 2) % 4) & 255, v = 0; 4 > v && r + 0.75 * v < p; v++) d.push(t.charAt(w >>> 6 * (3 - v) & 63));
            if (l = t.charAt(64)) for (; d.length % 4;) d.push(l);
            return d.join("");
          },
          parse: function (d) {
            var l = d.length,
              s = this._map,
              t = s.charAt(64);
            t && (t = d.indexOf(t), -1 != t && (l = t));
            for (var t = [], r = 0, w = 0; w < l; w++) if (w % 4) {
              var v = s.indexOf(d.charAt(w - 1)) << 2 * (w % 4),
                b = s.indexOf(d.charAt(w)) >>> 6 - 2 * (w % 4);
              t[r >>> 2] |= (v | b) << 24 - 8 * (r % 4);
              r++;
            }
            return p.create(t, r);
          },
          _map: "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="
        };
      })();
      (function (u) {
        function p(b, n, a, c, e, j, k) {
          b = b + (n & a | ~n & c) + e + k;
          return (b << j | b >>> 32 - j) + n;
        }
        function d(b, n, a, c, e, j, k) {
          b = b + (n & c | a & ~c) + e + k;
          return (b << j | b >>> 32 - j) + n;
        }
        function l(b, n, a, c, e, j, k) {
          b = b + (n ^ a ^ c) + e + k;
          return (b << j | b >>> 32 - j) + n;
        }
        function s(b, n, a, c, e, j, k) {
          b = b + (a ^ (n | ~c)) + e + k;
          return (b << j | b >>> 32 - j) + n;
        }
        for (var t = CryptoJS, r = t.lib, w = r.WordArray, v = r.Hasher, r = t.algo, b = [], x = 0; 64 > x; x++) b[x] = 4294967296 * u.abs(u.sin(x + 1)) | 0;
        r = r.MD5 = v.extend({
          _doReset: function () {
            this._hash = new w.init([1732584193, 4023233417, 2562383102, 271733878]);
          },
          _doProcessBlock: function (q, n) {
            for (var a = 0; 16 > a; a++) {
              var c = n + a,
                e = q[c];
              q[c] = (e << 8 | e >>> 24) & 16711935 | (e << 24 | e >>> 8) & 4278255360;
            }
            var a = this._hash.words,
              c = q[n + 0],
              e = q[n + 1],
              j = q[n + 2],
              k = q[n + 3],
              z = q[n + 4],
              r = q[n + 5],
              t = q[n + 6],
              w = q[n + 7],
              v = q[n + 8],
              A = q[n + 9],
              B = q[n + 10],
              C = q[n + 11],
              u = q[n + 12],
              D = q[n + 13],
              E = q[n + 14],
              x = q[n + 15],
              f = a[0],
              m = a[1],
              g = a[2],
              h = a[3],
              f = p(f, m, g, h, c, 7, b[0]),
              h = p(h, f, m, g, e, 12, b[1]),
              g = p(g, h, f, m, j, 17, b[2]),
              m = p(m, g, h, f, k, 22, b[3]),
              f = p(f, m, g, h, z, 7, b[4]),
              h = p(h, f, m, g, r, 12, b[5]),
              g = p(g, h, f, m, t, 17, b[6]),
              m = p(m, g, h, f, w, 22, b[7]),
              f = p(f, m, g, h, v, 7, b[8]),
              h = p(h, f, m, g, A, 12, b[9]),
              g = p(g, h, f, m, B, 17, b[10]),
              m = p(m, g, h, f, C, 22, b[11]),
              f = p(f, m, g, h, u, 7, b[12]),
              h = p(h, f, m, g, D, 12, b[13]),
              g = p(g, h, f, m, E, 17, b[14]),
              m = p(m, g, h, f, x, 22, b[15]),
              f = d(f, m, g, h, e, 5, b[16]),
              h = d(h, f, m, g, t, 9, b[17]),
              g = d(g, h, f, m, C, 14, b[18]),
              m = d(m, g, h, f, c, 20, b[19]),
              f = d(f, m, g, h, r, 5, b[20]),
              h = d(h, f, m, g, B, 9, b[21]),
              g = d(g, h, f, m, x, 14, b[22]),
              m = d(m, g, h, f, z, 20, b[23]),
              f = d(f, m, g, h, A, 5, b[24]),
              h = d(h, f, m, g, E, 9, b[25]),
              g = d(g, h, f, m, k, 14, b[26]),
              m = d(m, g, h, f, v, 20, b[27]),
              f = d(f, m, g, h, D, 5, b[28]),
              h = d(h, f, m, g, j, 9, b[29]),
              g = d(g, h, f, m, w, 14, b[30]),
              m = d(m, g, h, f, u, 20, b[31]),
              f = l(f, m, g, h, r, 4, b[32]),
              h = l(h, f, m, g, v, 11, b[33]),
              g = l(g, h, f, m, C, 16, b[34]),
              m = l(m, g, h, f, E, 23, b[35]),
              f = l(f, m, g, h, e, 4, b[36]),
              h = l(h, f, m, g, z, 11, b[37]),
              g = l(g, h, f, m, w, 16, b[38]),
              m = l(m, g, h, f, B, 23, b[39]),
              f = l(f, m, g, h, D, 4, b[40]),
              h = l(h, f, m, g, c, 11, b[41]),
              g = l(g, h, f, m, k, 16, b[42]),
              m = l(m, g, h, f, t, 23, b[43]),
              f = l(f, m, g, h, A, 4, b[44]),
              h = l(h, f, m, g, u, 11, b[45]),
              g = l(g, h, f, m, x, 16, b[46]),
              m = l(m, g, h, f, j, 23, b[47]),
              f = s(f, m, g, h, c, 6, b[48]),
              h = s(h, f, m, g, w, 10, b[49]),
              g = s(g, h, f, m, E, 15, b[50]),
              m = s(m, g, h, f, r, 21, b[51]),
              f = s(f, m, g, h, u, 6, b[52]),
              h = s(h, f, m, g, k, 10, b[53]),
              g = s(g, h, f, m, B, 15, b[54]),
              m = s(m, g, h, f, e, 21, b[55]),
              f = s(f, m, g, h, v, 6, b[56]),
              h = s(h, f, m, g, x, 10, b[57]),
              g = s(g, h, f, m, t, 15, b[58]),
              m = s(m, g, h, f, D, 21, b[59]),
              f = s(f, m, g, h, z, 6, b[60]),
              h = s(h, f, m, g, C, 10, b[61]),
              g = s(g, h, f, m, j, 15, b[62]),
              m = s(m, g, h, f, A, 21, b[63]);
            a[0] = a[0] + f | 0;
            a[1] = a[1] + m | 0;
            a[2] = a[2] + g | 0;
            a[3] = a[3] + h | 0;
          },
          _doFinalize: function () {
            var b = this._data,
              n = b.words,
              a = 8 * this._nDataBytes,
              c = 8 * b.sigBytes;
            n[c >>> 5] |= 128 << 24 - c % 32;
            var e = u.floor(a / 4294967296);
            n[(c + 64 >>> 9 << 4) + 15] = (e << 8 | e >>> 24) & 16711935 | (e << 24 | e >>> 8) & 4278255360;
            n[(c + 64 >>> 9 << 4) + 14] = (a << 8 | a >>> 24) & 16711935 | (a << 24 | a >>> 8) & 4278255360;
            b.sigBytes = 4 * (n.length + 1);
            this._process();
            b = this._hash;
            n = b.words;
            for (a = 0; 4 > a; a++) c = n[a], n[a] = (c << 8 | c >>> 24) & 16711935 | (c << 24 | c >>> 8) & 4278255360;
            return b;
          },
          clone: function () {
            var b = v.clone.call(this);
            b._hash = this._hash.clone();
            return b;
          }
        });
        t.MD5 = v._createHelper(r);
        t.HmacMD5 = v._createHmacHelper(r);
      })(Math);
      (function () {
        var u = CryptoJS,
          p = u.lib,
          d = p.Base,
          l = p.WordArray,
          p = u.algo,
          s = p.EvpKDF = d.extend({
            cfg: d.extend({
              keySize: 4,
              hasher: p.MD5,
              iterations: 1
            }),
            init: function (d) {
              this.cfg = this.cfg.extend(d);
            },
            compute: function (d, r) {
              for (var p = this.cfg, s = p.hasher.create(), b = l.create(), u = b.words, q = p.keySize, p = p.iterations; u.length < q;) {
                n && s.update(n);
                var n = s.update(d).finalize(r);
                s.reset();
                for (var a = 1; a < p; a++) n = s.finalize(n), s.reset();
                b.concat(n);
              }
              b.sigBytes = 4 * q;
              return b;
            }
          });
        u.EvpKDF = function (d, l, p) {
          return s.create(p).compute(d, l);
        };
      })();
      CryptoJS.lib.Cipher || function (u) {
        var p = CryptoJS,
          d = p.lib,
          l = d.Base,
          s = d.WordArray,
          t = d.BufferedBlockAlgorithm,
          r = p.enc.Base64,
          w = p.algo.EvpKDF,
          v = d.Cipher = t.extend({
            cfg: l.extend(),
            createEncryptor: function (e, a) {
              return this.create(this._ENC_XFORM_MODE, e, a);
            },
            createDecryptor: function (e, a) {
              return this.create(this._DEC_XFORM_MODE, e, a);
            },
            init: function (e, a, b) {
              this.cfg = this.cfg.extend(b);
              this._xformMode = e;
              this._key = a;
              this.reset();
            },
            reset: function () {
              t.reset.call(this);
              this._doReset();
            },
            process: function (e) {
              this._append(e);
              return this._process();
            },
            finalize: function (e) {
              e && this._append(e);
              return this._doFinalize();
            },
            keySize: 4,
            ivSize: 4,
            _ENC_XFORM_MODE: 1,
            _DEC_XFORM_MODE: 2,
            _createHelper: function (e) {
              return {
                encrypt: function (b, k, d) {
                  return ("string" == typeof k ? c : a).encrypt(e, b, k, d);
                },
                decrypt: function (b, k, d) {
                  return ("string" == typeof k ? c : a).decrypt(e, b, k, d);
                }
              };
            }
          });
        d.StreamCipher = v.extend({
          _doFinalize: function () {
            return this._process(!0);
          },
          blockSize: 1
        });
        var b = p.mode = {},
          x = function (e, a, b) {
            var c = this._iv;
            c ? this._iv = u : c = this._prevBlock;
            for (var d = 0; d < b; d++) e[a + d] ^= c[d];
          },
          q = (d.BlockCipherMode = l.extend({
            createEncryptor: function (e, a) {
              return this.Encryptor.create(e, a);
            },
            createDecryptor: function (e, a) {
              return this.Decryptor.create(e, a);
            },
            init: function (e, a) {
              this._cipher = e;
              this._iv = a;
            }
          })).extend();
        q.Encryptor = q.extend({
          processBlock: function (e, a) {
            var b = this._cipher,
              c = b.blockSize;
            x.call(this, e, a, c);
            b.encryptBlock(e, a);
            this._prevBlock = e.slice(a, a + c);
          }
        });
        q.Decryptor = q.extend({
          processBlock: function (e, a) {
            var b = this._cipher,
              c = b.blockSize,
              d = e.slice(a, a + c);
            b.decryptBlock(e, a);
            x.call(this, e, a, c);
            this._prevBlock = d;
          }
        });
        b = b.CBC = q;
        q = (p.pad = {}).Pkcs7 = {
          pad: function (a, b) {
            for (var c = 4 * b, c = c - a.sigBytes % c, d = c << 24 | c << 16 | c << 8 | c, l = [], n = 0; n < c; n += 4) l.push(d);
            c = s.create(l, c);
            a.concat(c);
          },
          unpad: function (a) {
            a.sigBytes -= a.words[a.sigBytes - 1 >>> 2] & 255;
          }
        };
        d.BlockCipher = v.extend({
          cfg: v.cfg.extend({
            mode: b,
            padding: q
          }),
          reset: function () {
            v.reset.call(this);
            var a = this.cfg,
              b = a.iv,
              a = a.mode;
            if (this._xformMode == this._ENC_XFORM_MODE) var c = a.createEncryptor;else c = a.createDecryptor, this._minBufferSize = 1;
            this._mode = c.call(a, this, b && b.words);
          },
          _doProcessBlock: function (a, b) {
            this._mode.processBlock(a, b);
          },
          _doFinalize: function () {
            var a = this.cfg.padding;
            if (this._xformMode == this._ENC_XFORM_MODE) {
              a.pad(this._data, this.blockSize);
              var b = this._process(!0);
            } else b = this._process(!0), a.unpad(b);
            return b;
          },
          blockSize: 4
        });
        var n = d.CipherParams = l.extend({
            init: function (a) {
              this.mixIn(a);
            },
            toString: function (a) {
              return (a || this.formatter).stringify(this);
            }
          }),
          b = (p.format = {}).OpenSSL = {
            stringify: function (a) {
              var b = a.ciphertext;
              a = a.salt;
              return (a ? s.create([1398893684, 1701076831]).concat(a).concat(b) : b).toString(r);
            },
            parse: function (a) {
              a = r.parse(a);
              var b = a.words;
              if (1398893684 == b[0] && 1701076831 == b[1]) {
                var c = s.create(b.slice(2, 4));
                b.splice(0, 4);
                a.sigBytes -= 16;
              }
              return n.create({
                ciphertext: a,
                salt: c
              });
            }
          },
          a = d.SerializableCipher = l.extend({
            cfg: l.extend({
              format: b
            }),
            encrypt: function (a, b, c, d) {
              d = this.cfg.extend(d);
              var l = a.createEncryptor(c, d);
              b = l.finalize(b);
              l = l.cfg;
              return n.create({
                ciphertext: b,
                key: c,
                iv: l.iv,
                algorithm: a,
                mode: l.mode,
                padding: l.padding,
                blockSize: a.blockSize,
                formatter: d.format
              });
            },
            decrypt: function (a, b, c, d) {
              d = this.cfg.extend(d);
              b = this._parse(b, d.format);
              return a.createDecryptor(c, d).finalize(b.ciphertext);
            },
            _parse: function (a, b) {
              return "string" == typeof a ? b.parse(a, this) : a;
            }
          }),
          p = (p.kdf = {}).OpenSSL = {
            execute: function (a, b, c, d) {
              d || (d = s.random(8));
              a = w.create({
                keySize: b + c
              }).compute(a, d);
              c = s.create(a.words.slice(b), 4 * c);
              a.sigBytes = 4 * b;
              return n.create({
                key: a,
                iv: c,
                salt: d
              });
            }
          },
          c = d.PasswordBasedCipher = a.extend({
            cfg: a.cfg.extend({
              kdf: p
            }),
            encrypt: function (b, c, d, l) {
              l = this.cfg.extend(l);
              d = l.kdf.execute(d, b.keySize, b.ivSize);
              l.iv = d.iv;
              b = a.encrypt.call(this, b, c, d.key, l);
              b.mixIn(d);
              return b;
            },
            decrypt: function (b, c, d, l) {
              l = this.cfg.extend(l);
              c = this._parse(c, l.format);
              d = l.kdf.execute(d, b.keySize, b.ivSize, c.salt);
              l.iv = d.iv;
              return a.decrypt.call(this, b, c, d.key, l);
            }
          });
      }();
      (function () {
        for (var u = CryptoJS, p = u.lib.BlockCipher, d = u.algo, l = [], s = [], t = [], r = [], w = [], v = [], b = [], x = [], q = [], n = [], a = [], c = 0; 256 > c; c++) a[c] = 128 > c ? c << 1 : c << 1 ^ 283;
        for (var e = 0, j = 0, c = 0; 256 > c; c++) {
          var k = j ^ j << 1 ^ j << 2 ^ j << 3 ^ j << 4,
            k = k >>> 8 ^ k & 255 ^ 99;
          l[e] = k;
          s[k] = e;
          var z = a[e],
            F = a[z],
            G = a[F],
            y = 257 * a[k] ^ 16843008 * k;
          t[e] = y << 24 | y >>> 8;
          r[e] = y << 16 | y >>> 16;
          w[e] = y << 8 | y >>> 24;
          v[e] = y;
          y = 16843009 * G ^ 65537 * F ^ 257 * z ^ 16843008 * e;
          b[k] = y << 24 | y >>> 8;
          x[k] = y << 16 | y >>> 16;
          q[k] = y << 8 | y >>> 24;
          n[k] = y;
          e ? (e = z ^ a[a[a[G ^ z]]], j ^= a[a[j]]) : e = j = 1;
        }
        var H = [0, 1, 2, 4, 8, 16, 32, 64, 128, 27, 54],
          d = d.AES = p.extend({
            _doReset: function () {
              for (var a = this._key, c = a.words, d = a.sigBytes / 4, a = 4 * ((this._nRounds = d + 6) + 1), e = this._keySchedule = [], j = 0; j < a; j++) if (j < d) e[j] = c[j];else {
                var k = e[j - 1];
                j % d ? 6 < d && 4 == j % d && (k = l[k >>> 24] << 24 | l[k >>> 16 & 255] << 16 | l[k >>> 8 & 255] << 8 | l[k & 255]) : (k = k << 8 | k >>> 24, k = l[k >>> 24] << 24 | l[k >>> 16 & 255] << 16 | l[k >>> 8 & 255] << 8 | l[k & 255], k ^= H[j / d | 0] << 24);
                e[j] = e[j - d] ^ k;
              }
              c = this._invKeySchedule = [];
              for (d = 0; d < a; d++) j = a - d, k = d % 4 ? e[j] : e[j - 4], c[d] = 4 > d || 4 >= j ? k : b[l[k >>> 24]] ^ x[l[k >>> 16 & 255]] ^ q[l[k >>> 8 & 255]] ^ n[l[k & 255]];
            },
            encryptBlock: function (a, b) {
              this._doCryptBlock(a, b, this._keySchedule, t, r, w, v, l);
            },
            decryptBlock: function (a, c) {
              var d = a[c + 1];
              a[c + 1] = a[c + 3];
              a[c + 3] = d;
              this._doCryptBlock(a, c, this._invKeySchedule, b, x, q, n, s);
              d = a[c + 1];
              a[c + 1] = a[c + 3];
              a[c + 3] = d;
            },
            _doCryptBlock: function (a, b, c, d, e, j, l, f) {
              for (var m = this._nRounds, g = a[b] ^ c[0], h = a[b + 1] ^ c[1], k = a[b + 2] ^ c[2], n = a[b + 3] ^ c[3], p = 4, r = 1; r < m; r++) var q = d[g >>> 24] ^ e[h >>> 16 & 255] ^ j[k >>> 8 & 255] ^ l[n & 255] ^ c[p++], s = d[h >>> 24] ^ e[k >>> 16 & 255] ^ j[n >>> 8 & 255] ^ l[g & 255] ^ c[p++], t = d[k >>> 24] ^ e[n >>> 16 & 255] ^ j[g >>> 8 & 255] ^ l[h & 255] ^ c[p++], n = d[n >>> 24] ^ e[g >>> 16 & 255] ^ j[h >>> 8 & 255] ^ l[k & 255] ^ c[p++], g = q, h = s, k = t;
              q = (f[g >>> 24] << 24 | f[h >>> 16 & 255] << 16 | f[k >>> 8 & 255] << 8 | f[n & 255]) ^ c[p++];
              s = (f[h >>> 24] << 24 | f[k >>> 16 & 255] << 16 | f[n >>> 8 & 255] << 8 | f[g & 255]) ^ c[p++];
              t = (f[k >>> 24] << 24 | f[n >>> 16 & 255] << 16 | f[g >>> 8 & 255] << 8 | f[h & 255]) ^ c[p++];
              n = (f[n >>> 24] << 24 | f[g >>> 16 & 255] << 16 | f[h >>> 8 & 255] << 8 | f[k & 255]) ^ c[p++];
              a[b] = q;
              a[b + 1] = s;
              a[b + 2] = t;
              a[b + 3] = n;
            },
            keySize: 8
          });
        u.AES = p._createHelper(d);
      })();
      /* harmony default export */
      var aes = CryptoJS;
      // CONCATENATED MODULE: ./liveMedia.js
      /**
       * @synopsis 流媒体取流
       *
       * @note [ADD][2017-07-28]新建 by fengzhongjian
       *
       */

      let LiveMedia = function () {
        class MediaLive {
          constructor() {}
          //to do

          //创建取流客户端对象
          createClientObject(oWebsocket, szId, szPKD, szRand, oParams) {
            let key = aes.AES.encrypt(new Date().getTime().toString(), aes.enc.Hex.parse("****************************************************************"), {
              mode: aes.mode.CBC,
              iv: aes.enc.Hex.parse("********************************"),
              padding: aes.pad.Pkcs7
            }).ciphertext.toString();
            if (key.length < 64) {
              key = key + key;
            }
            let iv = aes.AES.encrypt(new Date().getTime().toString(), aes.enc.Hex.parse("********************************"), {
              mode: aes.mode.CBC,
              iv: aes.enc.Hex.parse("********************************"),
              padding: aes.pad.Pkcs7
            }).ciphertext.toString();
            return {
              socket: oWebsocket,
              id: szId,
              PKD: szPKD,
              rand: szRand,
              playURL: oParams.playURL || "",
              auth: oParams.auth || "",
              token: oParams.token || "",
              key: key,
              iv: iv,
              resolve: null,
              reject: null
            };
          }
          //预览命令
          playCmd(oWebsocket) {
            let oCmd = {
              sequence: 0,
              cmd: 'realplay',
              url: oWebsocket.playURL,
              key: cryptico_default.a.encrypt(oWebsocket.iv + ":" + oWebsocket.key, oWebsocket.PKD).cipher.split("?")[0],
              authorization: aes.AES.encrypt(oWebsocket.rand + ":" + oWebsocket.auth, aes.enc.Hex.parse(oWebsocket.key), {
                mode: aes.mode.CBC,
                iv: aes.enc.Hex.parse(oWebsocket.iv),
                padding: aes.pad.Pkcs7
              }).ciphertext.toString(),
              token: aes.AES.encrypt(oWebsocket.token, aes.enc.Hex.parse(oWebsocket.key), {
                mode: aes.mode.CBC,
                iv: aes.enc.Hex.parse(oWebsocket.iv),
                padding: aes.pad.Pkcs7
              }).ciphertext.toString()
            };
            return JSON.stringify(oCmd);
          }
          //回放
          playbackCmd(oWebsocket, szStartTime, szStopTime) {
            let oCmd = {
              sequence: 0,
              cmd: 'playback',
              url: oWebsocket.playURL,
              key: cryptico_default.a.encrypt(oWebsocket.iv + ":" + oWebsocket.key, oWebsocket.PKD).cipher.split("?")[0],
              authorization: aes.AES.encrypt(oWebsocket.rand + ":" + oWebsocket.auth, aes.enc.Hex.parse(oWebsocket.key), {
                mode: aes.mode.CBC,
                iv: aes.enc.Hex.parse(oWebsocket.iv),
                padding: aes.pad.Pkcs7
              }).ciphertext.toString(),
              token: aes.AES.encrypt(oWebsocket.token, aes.enc.Hex.parse(oWebsocket.key), {
                mode: aes.mode.CBC,
                iv: aes.enc.Hex.parse(oWebsocket.iv),
                padding: aes.pad.Pkcs7
              }).ciphertext.toString(),
              startTime: szStartTime,
              endTime: szStopTime
            };
            return JSON.stringify(oCmd);
          }
          //定位回放
          seekCmd(szStartTime, szStopTime) {
            let oCmd = {
              sequence: 0,
              cmd: "seek",
              startTime: szStartTime,
              endTime: szStopTime
            };
            return JSON.stringify(oCmd);
          }
        }
        return MediaLive;
      }();

      // CONCATENATED MODULE: ./localService.js
      /**
       * @synopsis 本地服务取流
       *
       * @note [ADD][2017-07-28]新建 by fengzhongjian
       *
       */
      let LocalService = function () {
        class LocalServer {
          constructor() {}
          //to do

          //创建取流客户端对象
          createClientObject(oWebsocket, szId, szPlayURL, oParams) {
            return {
              socket: oWebsocket,
              id: szId,
              playURL: szPlayURL,
              deviceSerial: oParams.deviceSerial || "",
              verificationCode: oParams.verificationCode || "",
              resolve: null,
              reject: null
            };
          }
          //普通通道预览
          playCmd(oWebsocket) {
            let oCmd = {
              sequence: 0,
              cmd: 'realplay',
              deviceSerial: oWebsocket.deviceSerial,
              verificationCode: oWebsocket.verificationCode,
              url: oWebsocket.playURL
            };
            return JSON.stringify(oCmd);
          }
          //回放
          playbackCmd(oWebsocket, szStartTime, szStopTime) {
            let oCmd = {
              sequence: 0,
              cmd: 'playback',
              deviceSerial: oWebsocket.deviceSerial,
              verificationCode: oWebsocket.verificationCode,
              url: oWebsocket.playURL,
              startTime: szStartTime,
              endTime: szStopTime
            };
            return JSON.stringify(oCmd);
          }
        }
        return LocalServer;
      }();

      // CONCATENATED MODULE: ./streamClient.js

      //定制设备直连取流
      //设备直连取流
      //流媒体取流
      //本地服务取流

      /**
       * @synopsis 取流类
       *
       * @note [ADD][2017-01-03]新建 by fengzhongjian
       *
       */
      let StreamClient = function () {
        const WEBSOCKET = Symbol("WEBSOCKET");
        const GETINDEX = Symbol("GETINDEX");
        const PROTOCOLVERSION = Symbol("PROTOCOLVERSION");
        const CIPHERSUITES = Symbol("CIPHERSUITES");
        let oDirectDeviceCustom = new DirectDeviceCustom(); //定制设备直连取流
        let oDirectDevice = new DirectDevice(); //设备直连取流
        let oLiveMedia = new LiveMedia(); //流媒体取流
        let oLocalService = new LocalService(); //本地服务取流

        class WebsocketClient {
          constructor() {
            this[PROTOCOLVERSION] = "0.1"; //协议版本
            this[CIPHERSUITES] = 0; //秘钥套件
            this[WEBSOCKET] = []; //websocket对象列表
            this.ERRORS = {//错误码
            };
            //openstream后保存当前通道号和当前码流类型
            this[GETINDEX] = function (id) {
              let iIndex = -1;
              for (let i = 0, iLen = this[WEBSOCKET].length; i < iLen; i++) {
                if (this[WEBSOCKET][i].id === id) {
                  iIndex = i;
                  break;
                }
              }
              return iIndex;
            };
          }
          /**
           * @synopsis 开流, 此时设备的流还没有发出来
             * @param {string} szUrl 取流路径，如ws://hostname:port/channel
             * @param {object} oParams 取流需要涉及的相关参数
             * @param {function} cbMessage 消息回调函数
             * @param {function} cbClose 失败回调
             * @returns {objcet} 返回Promise对象
           */
          openStream(szUrl, oParams, cbMessage, cbClose) {
            let bIpv6 = false;
            if (szUrl.indexOf("[") > -1) {
              bIpv6 = true;
            }
            let that = this;
            let aUrl = szUrl.split("://"); //szUrl格式为ws://ip:port/channel/stream或wss://ip:port/channel/stream
            let szProtocol = aUrl[0];
            let szHostname = "";
            let iPort = 7681;
            let iCurChannel = 1;
            let iCurStream = 0;
            let bWebSocketVideoCtrlProxy = false; //判断是否是开发包代理模式
            if (bIpv6) {
              szHostname = aUrl[1].split("]:")[0] + "]";
              iPort = Math.floor(aUrl[1].split("]:")[1].split("/")[0]);
              iCurChannel = Math.floor(aUrl[1].split("]:")[1].split("/")[1] / 100); //通道号
              iCurStream = Math.floor(aUrl[1].split("]:")[1].split("/")[1] % 100) - 1; //码流类型
            } else {
              szHostname = aUrl[1].split(":")[0];
              iPort = Math.floor(aUrl[1].split(":")[1].split("/")[0]);
              iCurChannel = Math.floor(aUrl[1].split(":")[1].split("/")[1] / 100); //通道号
              iCurStream = Math.floor(aUrl[1].split(":")[1].split("/")[1] % 100) - 1; //码流类型
              let szProxyFlag = aUrl[1].split(":")[1].split("/")[2];
              if (szProxyFlag !== "" && szProxyFlag === "webSocketVideoCtrlProxy") {
                bWebSocketVideoCtrlProxy = true;
              }
            }
            if (iCurChannel === 0) {
              //表示零通道
              iCurStream = 0;
            }
            oParams = oParams || {};
            let szAuthType = "&sessionID=";
            if (oParams.token && !oParams.playURL) {
              //流媒体的token和配套token属性名一样，需要区分
              szAuthType = "&token=";
            }
            let szSessionID = oParams.sessionID || oParams.session || (oParams.playURL ? "" : oParams.token) || "";
            let oWebsocket = new window.WebSocket(szProtocol + "://" + szHostname + ":" + iPort + (oParams.mode ? "/" + oParams.mode : "") + (bWebSocketVideoCtrlProxy ? "/webSocketVideoCtrlProxy" : "") + "?version=" + that[PROTOCOLVERSION] + "&cipherSuites=" + that[CIPHERSUITES] + szAuthType + szSessionID + (oParams.proxy ? "&proxy=" + oParams.proxy : ""));
            oWebsocket.binaryType = "arraybuffer";
            let szId = esm_browser_v4(); //取流uuid，由于区分每条取流连接
            let iDataIndex = -1;
            let promise = new Promise(function (resolve, reject) {
              oWebsocket.onopen = function () {
                if (!oParams.playURL && !oParams.sessionID && !oParams.deviceSerial && !oParams.token) {
                  //定制设备取流，open就表示成功，因为没有命令码返回
                  that[WEBSOCKET].push(oDirectDeviceCustom.createClientObject(oWebsocket, szId, iCurChannel, iCurStream));
                  resolve(szId);
                }
                /*let aTest = [73, 77, 75, 72, 1, 2, 0, 0, 4, 0, 0, 1, 16, 113, 1, 16, 64, 31, 0, 0, 0, 250, 0, 0, 129, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];
                cbMessage({
                    bHead: true,
                    buf: aTest
                });*/ //写死head，本地架websokcet服务时使用
              };

              oWebsocket.onmessage = function (e) {
                if (typeof e.data === "string") {
                  let oJSON = JSON.parse(e.data);
                  let iWebsocketIndex = that[GETINDEX](szId);
                  if (oJSON && oJSON.version && oJSON.cipherSuite) {
                    //建立websocket连接成功返回
                    that[PROTOCOLVERSION] = oJSON.version;
                    that[CIPHERSUITES] = parseInt(oJSON.cipherSuite, 10);
                    if (oJSON && oJSON.PKD && oJSON.rand) {
                      that[WEBSOCKET].push(oLiveMedia.createClientObject(oWebsocket, szId, oJSON.PKD, oJSON.rand, oParams)); //流媒体
                    } else {
                      let szPlayURL = "live://" + szHostname + ":" + iPort + "/" + iCurChannel + "/" + iCurStream;
                      if (that[CIPHERSUITES] === -1) {
                        that[WEBSOCKET].push(oLocalService.createClientObject(oWebsocket, szId, szPlayURL, oParams)); //本地服务
                      } else {
                        that[WEBSOCKET].push(oDirectDevice.createClientObject(oWebsocket, szId, szPlayURL)); //基线设备
                      }
                    }

                    resolve(szId);
                    return;
                  }
                  if (oJSON && oJSON.sdp) {
                    let aSadpHeadBuf = oDirectDevice.getMediaFromSdp(oJSON.sdp); //获取媒体头
                    cbMessage({
                      bHead: true,
                      buf: aSadpHeadBuf
                    });
                  }
                  if (oJSON && oJSON.cmd) {
                    if (oJSON.cmd === "end") {
                      cbMessage({
                        type: "exception",
                        cmd: oJSON.cmd
                      }); //回放结束
                    }
                  }

                  if (oJSON && oJSON.statusString) {
                    if (oJSON.statusString.toLowerCase() === "ok") {
                      if (that[WEBSOCKET][iWebsocketIndex].resolve) {
                        that[WEBSOCKET][iWebsocketIndex].resolve(oJSON);
                      }
                    }
                    if (oJSON.statusString.toLowerCase() !== "ok") {
                      let oError = oDirectDevice.getError(oJSON);
                      if (iWebsocketIndex > -1) {
                        //建立连接即返回错误，此时iWebsocketIndex为-1
                        if (that[WEBSOCKET][iWebsocketIndex].reject) {
                          that[WEBSOCKET][iWebsocketIndex].reject(oError);
                        }
                      } else {
                        reject(oError);
                      }
                    }
                  }
                  if (oJSON && oJSON.errorCode) {
                    //推流异常时返回异常信息
                    cbMessage(oJSON);
                  }
                } else {
                  let dataObj = {};
                  let dataBuf = new Uint8Array(e.data);
                  iDataIndex++;
                  if (dataBuf.byteLength === 64 || dataBuf.byteLength === 40) {
                    //媒体头包含在第一个64字节的数据包中
                    let iMediaHeadIndex = -1; //回放和预览媒体头位置不一致，需要动态查询媒体头位置
                    let iLen = dataBuf.byteLength;
                    for (let i = 0; i < iLen; i++) {
                      if (dataBuf[i] === 73 && dataBuf[i + 1] === 77 && dataBuf[i + 2] === 75 && dataBuf[i + 3] === 72) {
                        iMediaHeadIndex = i;
                        break;
                      }
                    }
                    if (iMediaHeadIndex !== -1) {
                      let aHeadBuf = dataBuf.slice(iMediaHeadIndex, iMediaHeadIndex + 40);
                      dataObj = {
                        bHead: true,
                        buf: aHeadBuf
                      };
                    } else {
                      dataObj = {
                        bHead: false,
                        buf: dataBuf
                      };
                    }
                    cbMessage(dataObj);
                  } else {
                    if (iDataIndex > 0) {
                      dataObj = {
                        bHead: false,
                        buf: dataBuf
                      };
                      cbMessage(dataObj);
                    }
                  }
                  dataBuf = null;
                  dataObj = null;
                  e = null;
                }
              };
              oWebsocket.onclose = function () {
                for (let i = 0, iLen = that[WEBSOCKET].length; i < iLen; i++) {
                  if (that[WEBSOCKET][i].id === szId) {
                    that[WEBSOCKET][i].resolve(); //关闭连接后触发该事件表示关闭成功
                    that[WEBSOCKET].splice(i, 1);
                    setTimeout(function () {
                      cbClose();
                    }, 200); //延时触发停止成功回调，保证停止成功

                    break;
                  }
                }
                reject(); //建立websocket连接时就触发onclose
              };
            });

            return promise;
          }
          /**
           * @synopsis 开始取流
           *
           * @param {string} id websocket id，在openStream的时候生成
             @param {string} szStartTime 开始时间
             @param {string} szStopTime 结束时间
           * @param {function} cbMessage 码流回调函数
           *
           * @returns {object} 返回Promise对象
           */
          startPlay(id, szStartTime, szStopTime) {
            let that = this;
            let iWebsocketIndex = this[GETINDEX](id);
            if (szStartTime && szStopTime && that[PROTOCOLVERSION] === "0.1") {
              szStartTime = szStartTime.replace(/-/g, "").replace(/:/g, "");
              szStopTime = szStopTime.replace(/-/g, "").replace(/:/g, "");
            }
            let promise = new Promise(function (resolve, reject) {
              if (iWebsocketIndex > -1) {
                that[WEBSOCKET][iWebsocketIndex].resolve = resolve;
                that[WEBSOCKET][iWebsocketIndex].reject = reject;
                let uIntCmd = null;
                if (!szStartTime || !szStopTime) {
                  //预览
                  if (that[WEBSOCKET][iWebsocketIndex].iCurChannel === 0 && that[PROTOCOLVERSION] === "0.1") {
                    //零通道预览
                    uIntCmd = oDirectDeviceCustom.zeroPlayCmd(that[WEBSOCKET][iWebsocketIndex].iCurChannel, that[WEBSOCKET][iWebsocketIndex].iCurStream);
                  } else {
                    //普通预览
                    if (that[PROTOCOLVERSION] !== "0.1") {
                      if (that[CIPHERSUITES] === 0) {
                        uIntCmd = oLiveMedia.playCmd(that[WEBSOCKET][iWebsocketIndex]); //流媒体预览
                      } else if (that[CIPHERSUITES] === 1) {
                        uIntCmd = oDirectDevice.playCmd(that[WEBSOCKET][iWebsocketIndex].playURL); //基线普通预览
                      } else if (that[CIPHERSUITES] === -1) {
                        uIntCmd = oLocalService.playCmd(that[WEBSOCKET][iWebsocketIndex]); //本地服务预览
                      }
                    } else {
                      uIntCmd = oDirectDeviceCustom.playCmd(that[WEBSOCKET][iWebsocketIndex].iCurChannel, that[WEBSOCKET][iWebsocketIndex].iCurStream); //定制普通预览
                    }
                  }
                } else {
                  //回放
                  if (that[PROTOCOLVERSION] !== "0.1") {
                    if (that[CIPHERSUITES] === 0) {
                      uIntCmd = oLiveMedia.playbackCmd(that[WEBSOCKET][iWebsocketIndex], szStartTime, szStopTime); //流媒体回放
                    } else if (that[CIPHERSUITES] === 1) {
                      uIntCmd = oDirectDevice.playbackCmd(szStartTime, szStopTime, that[WEBSOCKET][iWebsocketIndex].playURL); //基线设备回放
                    } else if (that[CIPHERSUITES] === -1) {
                      uIntCmd = oLocalService.playbackCmd(that[WEBSOCKET][iWebsocketIndex], szStartTime, szStopTime); //本地服务回放
                    }
                  } else {
                    //定制设备回放
                    uIntCmd = oDirectDeviceCustom.playbackCmd(szStartTime, szStopTime, that[WEBSOCKET][iWebsocketIndex].iCurChannel, that[WEBSOCKET][iWebsocketIndex].iCurStream);
                  }
                }
                that[WEBSOCKET][iWebsocketIndex].socket.send(uIntCmd);
                if (that[PROTOCOLVERSION] === "0.1") {
                  //定制协议没有返回码，发送后直接认为发送成功
                  resolve();
                }
              } else {
                if (that[PROTOCOLVERSION] === "0.1") {
                  //定制协议没有返回码，发送后直接认为发送成功
                  reject();
                }
              }
            });
            return promise;
          }
          singleFrame() {}
          //do something

          /**
           * @synopsis 设置倍率
           *
           * @param {string} id websocket id在openStream的时候生成
             @param {number} iRate 播放倍率
           *
           * @returns {object} Promise
           */
          setPlayRate(id, iRate) {
            let that = this;
            let promise = new Promise(function (resolve, reject) {
              for (let i = 0, iLen = that[WEBSOCKET].length; i < iLen; i++) {
                if (that[WEBSOCKET][i].id === id) {
                  if (that[PROTOCOLVERSION] === "0.1") {
                    let uIntCmd = oDirectDeviceCustom.playRateCmd(iRate); //定制协议快慢放
                    that[WEBSOCKET][i].socket.send(uIntCmd);
                    resolve(); //定制协议没有返回码，发送后直接认为发送成功
                    break;
                  } else {
                    that[WEBSOCKET][i].resolve = resolve;
                    that[WEBSOCKET][i].reject = reject;
                    let szCmd = oDirectDevice.playRateCmd(iRate); //标准协议快慢放
                    that[WEBSOCKET][i].socket.send(szCmd);
                  }
                }
              }
            });
            return promise;
          }
          /**
           * @synopsis 定位回放
           *
           * @param {string} id websocket id在openStream的时候生成
             @param {string} szStartTime 开始时间
             @param {string} szStopTime 结束时间
           *
           * @returns {object} Promise
           */
          seek(id, szStartTime, szStopTime) {
            let that = this;
            let promise = new Promise(function (resolve, reject) {
              for (let i = 0, iLen = that[WEBSOCKET].length; i < iLen; i++) {
                if (that[WEBSOCKET][i].id === id) {
                  that[WEBSOCKET][i].resolve = resolve;
                  that[WEBSOCKET][i].reject = reject;
                  let szCmd = oLiveMedia.seekCmd(szStartTime, szStopTime);
                  that[WEBSOCKET][i].socket.send(szCmd);
                }
              }
            });
            return promise;
          }
          /**
           * @synopsis 暂停取流
           *
           * @param {string} id websocket id，在openStream的时候生成
           *
           * @returns {object} 返回Promise对象
           */
          pause(id) {
            let that = this;
            let promise = new Promise(function (resolve, reject) {
              for (let i = 0, iLen = that[WEBSOCKET].length; i < iLen; i++) {
                if (that[WEBSOCKET][i].id === id) {
                  if (that[PROTOCOLVERSION] === "0.1") {
                    let uIntCmd = oDirectDeviceCustom.pauseCmd(); //定制协议暂停
                    that[WEBSOCKET][i].socket.send(uIntCmd);
                    resolve(); //定制协议没有返回码，发送后直接认为发送成功
                    break;
                  } else {
                    that[WEBSOCKET][i].resolve = resolve;
                    that[WEBSOCKET][i].reject = reject;
                    let szCmd = oDirectDevice.pauseCmd(); //标准协议暂停
                    that[WEBSOCKET][i].socket.send(szCmd);
                  }
                }
              }
            });
            return promise;
          }
          /**
           * @synopsis 透传协议
           *
           * @param {string} id websocket id，在openStream的时候生成
           * @param {string} szCmd, 透传的命令码
           *
           * @returns {object} 返回Promise对象
           */
          transmission(id, szCmd) {
            let that = this;
            let promise = new Promise(function (resolve, reject) {
              for (let i = 0, iLen = that[WEBSOCKET].length; i < iLen; i++) {
                if (that[WEBSOCKET][i].id === id) {
                  that[WEBSOCKET][i].resolve = resolve;
                  that[WEBSOCKET][i].reject = reject;
                  that[WEBSOCKET][i].socket.send(szCmd);
                }
              }
            });
            return promise;
          }
          /**
           * @synopsis 恢复取流
           *
           * @param {string} id websocket id，在openStream的时候生成
           *
           * @returns {object} 返回Promise对象
           */
          resume(id) {
            let that = this;
            let promise = new Promise(function (resolve, reject) {
              for (let i = 0, iLen = that[WEBSOCKET].length; i < iLen; i++) {
                if (that[WEBSOCKET][i].id === id) {
                  if (that[PROTOCOLVERSION] === "0.1") {
                    let uIntCmd = oDirectDeviceCustom.resumeCmd(); //定制协议恢复
                    that[WEBSOCKET][i].socket.send(uIntCmd);
                    resolve(); //定制协议没有返回码，发送后直接认为发送成功
                    break;
                  } else {
                    that[WEBSOCKET][i].resolve = resolve;
                    that[WEBSOCKET][i].reject = reject;
                    let szCmd = oDirectDevice.resumeCmd(); //定制协议恢复
                    that[WEBSOCKET][i].socket.send(szCmd);
                  }
                }
              }
            });
            return promise;
          }
          /**
           * @synopsis 停止取流
           *
           * @param {string} id websocket id，在openStream的时候生成
           *
           * @returns {object} 返回Promise对象
           */
          stop(id) {
            let that = this;
            let promise = new Promise(function (resolve, reject) {
              if (!id) {
                reject();
              } else {
                let iIndex = -1;
                for (let i = 0, iLen = that[WEBSOCKET].length; i < iLen; i++) {
                  if (that[WEBSOCKET][i].id === id) {
                    iIndex = i;
                    that[WEBSOCKET][i].resolve = resolve; //onclose中处理，判断是否停止成功
                    that[WEBSOCKET][i].socket.close(1000, "CLOSE");
                    break;
                  }
                }
                if (iIndex === -1) {
                  reject();
                }
              }
            });
            return promise;
          }
          /**
           * @synopsis 停止所有通道取流
           *
           * @returns {object} 返回Promise对象
           */
          stopAll() {
            let that = this;
            let promise = new Promise(function (resolve, reject) {
              for (let i = 0, iLen = that[WEBSOCKET].length; i < iLen; i++) {
                that[WEBSOCKET][i].socket.close(1000, "CLOSE");
              }
              console.log("停止所有流");
              resolve();
            });
            return promise;
          }
        }
        return WebsocketClient;
      }();

      /***/
    }
    /******/])
  );
});

!function (e, t) {
  if ("object" == typeof exports && "object" == typeof module) module.exports = t();else if ("function" == typeof define && define.amd) define([], t);else {
    var n = t();
    for (var r in n) ("object" == typeof exports ? exports : e)[r] = n[r];
  }
}(window, function () {
  return function (e) {
    var t = {};
    function n(r) {
      if (t[r]) return t[r].exports;
      var a = t[r] = {
        i: r,
        l: !1,
        exports: {}
      };
      return e[r].call(a.exports, a, a.exports, n), a.l = !0, a.exports;
    }
    return n.m = e, n.c = t, n.d = function (e, t, r) {
      n.o(e, t) || Object.defineProperty(e, t, {
        enumerable: !0,
        get: r
      });
    }, n.r = function (e) {
      "undefined" != typeof Symbol && Symbol.toStringTag && Object.defineProperty(e, Symbol.toStringTag, {
        value: "Module"
      }), Object.defineProperty(e, "__esModule", {
        value: !0
      });
    }, n.t = function (e, t) {
      if (1 & t && (e = n(e)), 8 & t) return e;
      if (4 & t && "object" == typeof e && e && e.__esModule) return e;
      var r = Object.create(null);
      if (n.r(r), Object.defineProperty(r, "default", {
        enumerable: !0,
        value: e
      }), 2 & t && "string" != typeof e) for (var a in e) n.d(r, a, function (t) {
        return e[t];
      }.bind(null, a));
      return r;
    }, n.n = function (e) {
      var t = e && e.__esModule ? function () {
        return e.default;
      } : function () {
        return e;
      };
      return n.d(t, "a", t), t;
    }, n.o = function (e, t) {
      return Object.prototype.hasOwnProperty.call(e, t);
    }, n.p = "", n(n.s = 0);
  }([function (e, t, n) {

    var r;
    n.r(t), n.d(t, "StorageManager", function () {
      return y;
    });
    var a = new Uint8Array(16);
    function i() {
      if (!r && !(r = "undefined" != typeof crypto && crypto.getRandomValues && crypto.getRandomValues.bind(crypto) || "undefined" != typeof msCrypto && "function" == typeof msCrypto.getRandomValues && msCrypto.getRandomValues.bind(msCrypto))) throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");
      return r(a);
    }
    var o = /^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i;
    for (var s = function (e) {
        return "string" == typeof e && o.test(e);
      }, u = [], f = 0; f < 256; ++f) u.push((f + 256).toString(16).substr(1));
    var l = function (e) {
      var t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : 0,
        n = (u[e[t + 0]] + u[e[t + 1]] + u[e[t + 2]] + u[e[t + 3]] + "-" + u[e[t + 4]] + u[e[t + 5]] + "-" + u[e[t + 6]] + u[e[t + 7]] + "-" + u[e[t + 8]] + u[e[t + 9]] + "-" + u[e[t + 10]] + u[e[t + 11]] + u[e[t + 12]] + u[e[t + 13]] + u[e[t + 14]] + u[e[t + 15]]).toLowerCase();
      if (!s(n)) throw TypeError("Stringified UUID is invalid");
      return n;
    };
    var d = function (e, t, n) {
      var r = (e = e || {}).random || (e.rng || i)();
      if (r[6] = 15 & r[6] | 64, r[8] = 63 & r[8] | 128, t) {
        n = n || 0;
        for (var a = 0; a < 16; ++a) t[n + a] = r[a];
        return t;
      }
      return l(r);
    };
    let c = new class {
      constructor() {
        this._keyStr = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";
      }
      $(e) {
        var t = /^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]*))$/,
          n = /^(?:\s*(<[\w\W]+>)[^>]*|.([\w-]*))$/;
        if (t.test(e)) {
          var r = t.exec(e);
          return document.getElementById(r[2]);
        }
        if (n.test(e)) {
          for (var a = n.exec(e), i = document.getElementsByTagName("*"), o = [], s = 0, u = i.length; s < u; s++) i[s].className.match(new RegExp("(\\s|^)" + a[2] + "(\\s|$)")) && o.push(i[s]);
          return o;
        }
      }
      dateFormat(e, t) {
        var n = {
          "M+": e.getMonth() + 1,
          "d+": e.getDate(),
          "h+": e.getHours(),
          "m+": e.getMinutes(),
          "s+": e.getSeconds(),
          "q+": Math.floor((e.getMonth() + 3) / 3),
          S: e.getMilliseconds()
        };
        for (var r in /(y+)/.test(t) && (t = t.replace(RegExp.$1, (e.getFullYear() + "").substr(4 - RegExp.$1.length))), n) new RegExp("(" + r + ")").test(t) && (t = t.replace(RegExp.$1, 1 === RegExp.$1.length ? n[r] : ("00" + n[r]).substr(("" + n[r]).length)));
        return t;
      }
      downloadFile(e, t) {
        let n = e;
        e instanceof Blob || e instanceof File || (n = new Blob([e]));
        var r = window.URL.createObjectURL(n),
          a = window.document.createElement("a");
        a.href = r, a.download = t;
        var i = document.createEvent("MouseEvents");
        i.initEvent("click", !0, !0), a.dispatchEvent(i);
      }
      createxmlDoc() {
        for (var e, t = ["MSXML2.DOMDocument", "MSXML2.DOMDocument.5.0", "MSXML2.DOMDocument.4.0", "MSXML2.DOMDocument.3.0", "Microsoft.XmlDom"], n = 0, r = t.length; n < r; n++) try {
          e = new ActiveXObject(t[n]);
          break;
        } catch (t) {
          e = document.implementation.createDocument("", "", null);
          break;
        }
        return e.async = "false", e;
      }
      parseXmlFromStr(e) {
        if (null === e || "" === e) return null;
        var t = this.createxmlDoc();
        "Netscape" === navigator.appName || "Opera" === navigator.appName ? t = new DOMParser().parseFromString(e, "text/xml") : t.loadXML(e);
        return t;
      }
      encode(e) {
        var t,
          n,
          r,
          a,
          i,
          o,
          s,
          u = "",
          f = 0;
        for (e = this._utf8_encode(e); f < e.length;) a = (t = e.charCodeAt(f++)) >> 2, i = (3 & t) << 4 | (n = e.charCodeAt(f++)) >> 4, o = (15 & n) << 2 | (r = e.charCodeAt(f++)) >> 6, s = 63 & r, isNaN(n) ? o = s = 64 : isNaN(r) && (s = 64), u = u + this._keyStr.charAt(a) + this._keyStr.charAt(i) + this._keyStr.charAt(o) + this._keyStr.charAt(s);
        return u;
      }
      decode(e) {
        var t,
          n,
          r,
          a,
          i,
          o,
          s = "",
          u = 0;
        for (e = e.replace(/[^A-Za-z0-9+/=]/g, ""); u < e.length;) t = this._keyStr.indexOf(e.charAt(u++)) << 2 | (a = this._keyStr.indexOf(e.charAt(u++))) >> 4, n = (15 & a) << 4 | (i = this._keyStr.indexOf(e.charAt(u++))) >> 2, r = (3 & i) << 6 | (o = this._keyStr.indexOf(e.charAt(u++))), s += String.fromCharCode(t), 64 !== i && (s += String.fromCharCode(n)), 64 !== o && (s += String.fromCharCode(r));
        return s = this._utf8_decode(s);
      }
      _utf8_encode(e) {
        e = e.replace(/\r\n/g, "\n");
        for (var t = "", n = 0; n < e.length; n++) {
          var r = e.charCodeAt(n);
          r < 128 ? t += String.fromCharCode(r) : r > 127 && r < 2048 ? (t += String.fromCharCode(r >> 6 | 192), t += String.fromCharCode(63 & r | 128)) : (t += String.fromCharCode(r >> 12 | 224), t += String.fromCharCode(r >> 6 & 63 | 128), t += String.fromCharCode(63 & r | 128));
        }
        return t;
      }
      _utf8_decode(e) {
        for (var t = "", n = 0, r = 0, a = 0; n < e.length;) if ((r = e.charCodeAt(n)) < 128) t += String.fromCharCode(r), n++;else if (r > 191 && r < 224) a = e.charCodeAt(n + 1), t += String.fromCharCode((31 & r) << 6 | 63 & a), n += 2;else {
          a = e.charCodeAt(n + 1);
          var i = e.charCodeAt(n + 2);
          t += String.fromCharCode((15 & r) << 12 | (63 & a) << 6 | 63 & i), n += 3;
        }
        return t;
      }
      isFirefox() {
        var e = !1,
          t = navigator.userAgent.toLowerCase(),
          n = "";
        return t.match(/firefox\/([\d.]+)/) && (n = t.match(/firefox\/([\d.]+)/)[1], parseInt(n.split(".")[0], 10) > -1 && (e = !0)), e;
      }
      isSafari() {
        var e = !1,
          t = navigator.userAgent.toLowerCase(),
          n = "";
        return t.match(/version\/([\d.]+).safari./) && (n = t.match(/version\/([\d.]+).safari./)[1], parseInt(n.split(".")[0], 10) > -1 && (e = !0)), e;
      }
      isEdge() {
        return navigator.userAgent.toLowerCase().indexOf("edge") > -1;
      }
      isIOS() {
        return !!navigator.userAgent.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/);
      }
    }();
    var h = (e = "https://open.ys7.com/assets/ezuikit_v3.6") => `(function() {\n    const RECORDRTP = 0;  //录制一份未经过转封装的码流原始数据，用于定位问题\n    self.Module = { memoryInitializerRequest: loadMemInitFile(), TOTAL_MEMORY: 128*1024*1024 };\n    importScripts = (function (globalEval) {\n        var xhr = new XMLHttpRequest;\n        return function importScripts() {\n          var\n            args = Array.prototype.slice.call(arguments)\n            , len = args.length\n            , i = 0\n            , meta\n            , data\n            , content\n            ;\n          for (; i < len; i++) {\n            if (args[i].substr(0, 5).toLowerCase() === "data:") {\n              data = args[i];\n              content = data.indexOf(",");\n              meta = data.substr(5, content).toLowerCase();\n              data = decodeURIComponent(data.substr(content + 1));\n              if (/;s*base64s*[;,]/.test(meta)) {\n                data = atob(data);\n              }\n              if (/;s*charset=[uU][tT][fF]-?8s*[;,]/.test(meta)) {\n                data = decodeURIComponent(escape(data));\n              }\n            } else {\n              xhr.open("GET", args[i], false);\n              xhr.send(null);\n              data = xhr.responseText;\n            }\n            globalEval(data);\n          }\n        };\n      }(eval));\n    importScripts('${e}/js/transform/SystemTransform.js');\n\n    Module.postRun.push(function() {\n        postMessage({type: "loaded"});\n    });\n\n    onmessage = function (e) {\n        var data = e.data;\n\n        if ("create" === data.type) {\n            var iHeadLen = data.len;\n            var pHead = Module._malloc(iHeadLen);\n\n            var aData = Module.HEAPU8.subarray(pHead, pHead + iHeadLen);\n            aData.set(new Uint8Array(data.buf));\n\n            var iTransType = data.packType;//目标格式 RTP->PS\n            if (RECORDRTP) {\n                postMessage({type: "created"});\n                postMessage({type: "outputData", buf: data.buf, dType: 1}, [data.buf]);\n            } else {\n                var iRet = Module._ST_Create(pHead, iHeadLen, iTransType);\n                if (iRet != 0) {\n                    console.log("_ST_Create failed!");\n                } else {\n                    Module._ST_Start();\n                    postMessage({type: "created"});\n                }\n            }\n\n        } else if ("inputData" === data.type) {\n            if (RECORDRTP) {\n                var aFileData = new Uint8Array(data.buf);  // 拷贝一份\n                var iBufferLen = aFileData.length;\n                var szBufferLen = iBufferLen.toString(16);\n                if (szBufferLen.length === 1) {\n                    szBufferLen = "000" + szBufferLen;\n                } else if (szBufferLen.length === 2) {\n                    szBufferLen = "00" + szBufferLen;\n                } else if (szBufferLen.length === 3) {\n                    szBufferLen = "0" + szBufferLen;\n                }\n                var aData = [0, 0, parseInt(szBufferLen.substring(0, 2), 16), parseInt(szBufferLen.substring(2, 4), 16)];\n                for(var iIndex = 0, iDataLength = aFileData.length; iIndex < iDataLength; iIndex++) {\n                    aData[iIndex + 4] = aFileData[iIndex]\n                }\n                var dataUint8 = new Uint8Array(aData);\n                postMessage({type: "outputData", buf: dataUint8.buffer, dType: 2}, [dataUint8.buffer]);\n            } else {\n                var iDataLen = data.len;\n                var pData = Module._malloc(iDataLen);\n\n                var aData = Module.HEAPU8.subarray(pData, pData + iDataLen);\n                aData.set(new Uint8Array(data.buf));\n\n                var iRet = Module._ST_InputData(0, pData, iDataLen);\n                if (iRet != 0) {\n                    //console.log("_ST_InputData failed!");// 一开始会有一些失败，但是不影响后面的文件存储\n                }\n\n                Module._free(pData);\n            }\n        } else if ("release" === data.type) {\n            Module._ST_Stop();\n            Module._ST_Release();\n\n            close();\n        }\n    };\n\n    function loadMemInitFile() {\n        var req = new XMLHttpRequest();\n        req.open('GET', '${e}/js/transform/SystemTransform.js.mem');\n        req.responseType = 'arraybuffer';\n        req.send();\n\n        return req;\n    }\n})();`;
    window.requestFileSystem = window.requestFileSystem || window.webkitRequestFileSystem, window.URL = window.URL || window.webkitURL;
    class p {
      constructor(e, t, n, r) {
        this.szUUID = e, this.szFileName = t, this.iStreamType = n, this.szPath = "", this.bStart = !1, this.aStreamList = [], this.options = r;
      }
      init() {
        var e = this;
        0 === this.iStreamType ? this.szPath = "Web/RecordFiles/" : 1 === this.iStreamType && (this.szPath = "Web/PlaybackFiles/"), this.szPath += this.getDateDir();
        var t = e.szPath.split("/");
        return new Promise(function (n) {
          window.requestFileSystem(window.TEMPORARY, e.options.iFileSize, function (r) {
            e.createDir(r.root, t, function () {
              n();
            });
          }, e.errorHandler);
        });
      }
      getDateDir() {
        return c.dateFormat(new Date(), "yyyy-MM-dd");
      }
      createDir(e, t, n) {
        var r = this;
        t.length ? e.getDirectory(t[0], {
          create: !0
        }, function (e) {
          r.createDir(e, t.slice(1), n);
        }, r.errorHandler) : n();
      }
      errorHandler() {}
      writeFileHeader(e) {
        var t = this;
        window.requestFileSystem(window.TEMPORARY, t.options.iFileSize, function (n) {
          n.root.getFile(t.szPath + "/" + t.szFileName, {
            create: !0
          }, function (n) {
            n.createWriter(function (n) {
              n.onwriteend = function () {
                t.bStart = !0, t.writeFile(n);
              }, n.onerror = function () {}, n.seek(n.length);
              var r = new Blob([e]);
              n.write(r);
            }, t.errorHandler);
          }, t.errorHandler);
        }, t.errorHandler);
      }
      writeFileContent(e) {
        this.aStreamList.push(e);
      }
      writeFile(e) {
        var t = this;
        if (this.bStart) if (this.aStreamList.length > 0) {
          var n = this.aStreamList.shift();
          if (e.seek(e.length), e.length >= this.options.iFileSize) return void (this.options.cbEventHandler && this.options.cbEventHandler(3001, this.szUUID));
          var r = new Blob([n]);
          e.write(r);
        } else setTimeout(function () {
          t.writeFile(e);
        }, 1e3);
      }
      stopWriteFile() {
        var e = this;
        return this.bStart = !1, this.aStreamList.length = 0, new Promise(function (t) {
          window.requestFileSystem(window.TEMPORARY, e.options.iFileSize, function (n) {
            n.root.getFile(e.szPath + "/" + e.szFileName, {
              create: !1
            }, function (e) {
              e.file(function (e) {
                t(), c.downloadFile(e, e.name);
              });
            }, e.errorHandler);
          }, e.errorHandler);
        });
      }
    }
    class m {
      constructor(e, t, n, r, a, i, o, s) {
        this.szBasePath = e, this.szUUID = t, this.szFileName = n, this.aHeadBuf = new Uint8Array(r), this.iPackType = a, this.iStreamType = i, this.oWorker = null, this.oFileSystem = null, this.options = o, this.bHead = !0, this.staticPath = s;
      }
      init() {
        var e = this;
        return new Promise(function (t, n) {
          e.initFileSystem().then(function () {
            e.initWorker().then(function () {
              t(e.szUUID);
            }, function (e) {
              n(e);
            });
          }, function (e) {
            n(e);
          });
        });
      }
      initFileSystem() {
        var e = this;
        return this.oFileSystem = new p(this.szUUID, this.szFileName, this.iStreamType, this.options), new Promise(function (t, n) {
          e.oFileSystem.init().then(function () {
            t();
          }, function (e) {
            n(e);
          });
        });
      }
      initWorker() {
        var e = this;
        return new Promise(function (t) {
          var n = new Blob([h(e.staticPath)]);
          const r = URL.createObjectURL(n);
          e.oWorker = new Worker(r), e.oWorker.onmessage = function (n) {
            var r = n.data;
            let a = e.iPackType;
            if (1 === e.options.iPackage && (a = 12), "loaded" === r.type) e.oWorker.postMessage({
              type: "create",
              buf: e.aHeadBuf.buffer,
              len: 40,
              packType: a
            }, [e.aHeadBuf.buffer]);else if ("created" === r.type) t();else if ("outputData" === r.type) {
              var i = new Uint8Array(r.buf);
              1 === e.options.iPackage ? e.bHead ? (e.oFileSystem.writeFileHeader(i), e.bHead = !1) : e.oFileSystem.writeFileContent(i) : 1 === r.dType ? e.oFileSystem.writeFileHeader(i) : e.oFileSystem.writeFileContent(i);
            }
          };
        });
      }
      inputData(e) {
        if (this.oWorker) {
          var t = new Uint8Array(e);
          this.oWorker.postMessage({
            type: "inputData",
            buf: t.buffer,
            len: t.length
          }, [t.buffer]);
        }
      }
      stopRecord() {
        var e = this;
        return new Promise(function (t, n) {
          e.oWorker ? e.oWorker.postMessage({
            type: "release"
          }) : n(), e.oFileSystem ? e.oFileSystem.stopWriteFile().then(function () {
            e.bHead = !0, t();
          }, function () {
            n();
          }) : n();
        });
      }
    }
    var g,
      y = (g = Symbol("STORAGELIST"), class {
        constructor(e, t, n) {
          this.szBasePath = e, this[g] = {}, this.options = {
            iFileSize: 1073741824
          }, Object.assign(this.options, t), "string" == typeof t.staticPath && (this.staticPath = t.staticPath);
        }
        startRecord(e, t, n, r, a) {
          var i = this,
            o = d(),
            s = Object.assign({}, this.options, a),
            u = new m(this.szBasePath, o, e, t, n, r, s, this.staticPath);
          return new Promise(function (e, t) {
            u.init().then(function (t) {
              i[g][t] = u, e(t);
            }, function (e) {
              t(e);
            });
          });
        }
        inputData(e, t) {
          var n = this[g][e];
          n && n.inputData(t);
        }
        stopRecord(e) {
          var t = this;
          return new Promise(function (n, r) {
            var a = t[g][e];
            a ? a.stopRecord().then(function () {
              delete t[g][e], n();
            }, function () {
              r();
            }) : r();
          });
        }
      });
  }]);
});

//const SR_DE_FISH_PLANET                    = 0x11;      ///< 应用于小行星
//const SR_DE_FISH_ARCSPHERE_HORIZONTAL_WALL = 0x12;      ///< 应用于水平壁装弧面鱼眼显示
//const SR_DE_FISH_ARCSPHERE_VERTICAL_WALL   = 0x13;      ///< 应用于垂直壁装弧面鱼眼显示
//const SR_DE_FISH_ANIMATION_SWITCH_CEILING  = 0x14;      ///< 应用于顶装矫正方式动画切换显示
//const SR_DE_FISH_ANIMATION_SWITCH_FLOOR    = 0x15;      ///< 应用于底装矫正方式动画切换显示
//const SR_DE_PANORAMA_SPHERE                = 0x16;      ///< 应用于全景球面展示
//const SR_DE_PANORAMA_PLANET                = 0x17;      ///< 应用于全景小行星展示
//宽、高
var nWidth = 0;
var nHeight = 0;
// 录制
// var aHead = []; 
function downloadFile(oData, szName) {
  console.log("oData", oData);
  let oBlob = oData;
  if (!(oData instanceof Blob || oData instanceof File)) {
    oBlob = new Blob([oData]);
  }
  let szFileUrl = window.URL.createObjectURL(oBlob);
  let oLink = window.document.createElement("a");
  oLink.href = szFileUrl;
  oLink.download = szName;
  let oClick = document.createEvent("MouseEvents");
  oClick.initEvent("click", true, true);
  oLink.dispatchEvent(oClick);
}
// 字母字符串转byte数组
function stringToBytes(str) {
  let ch,
    st,
    re = [];
  for (let i = 0; i < str.length; i++) {
    ch = str.charCodeAt(i); // get char
    st = []; // set up "stack"
    do {
      st.push(ch & 0xFF); // push byte to stack
      ch = ch >> 8; // shift value down by 1 byte
    } while (ch);
    // add stack contents to result
    // done because chars have "wrong" endianness
    re = re.concat(st.reverse());
  }
  // return an array of bytes
  return re;
}
class JSPluginV2 {
  constructor(props) {
    this.oStreamClient = new StreamClient();
    this.oStorageManager = new StorageManager("./transform", {
      staticPath: "https://open.ys7.com/assets/ezuikit_v4.0"
    });
    this.aHead = [];
    this.pCanvasData = null;
    this.bPlaySound = -1;
    this.szStorageUUID = '';
    this.szStreamUUID = '';
    this.g_port = 0;
    this.szId = props.szId;
    this.iWidth = props.iWidth;
    this.iHeight = props.iHeight;
    this.iRate = 1;
    this.decoderVersion = '2.0';
    this.staticPath = props.staticPath || "https://open.ys7.com/assets/ezuikit_v5.0";
    this.bPause = false;
    this.bPlay = false;
    this.bInit = false;
    this.portAvailable = true; // 端口可用状态，可以用来判断播放停止状态。
    //this.staticPath = "http://localhost:9090";
    document.getElementById(props.szId).style = `display:inline-block;width:${props.iWidth}px;height:${props.iHeight}px;background:#000000;background-size:100% 100%;vertical-align:top;`;
    this.JS_Init();
  }
  JS_Init() {
    if (window.initIng) {
      //多个实例同时初始化，判断已有实例正在加载
      return;
    }
    window.initIng = true;
    var that = this;
    if (this.bPlay) {
      return false;
    }
    const promise = new Promise((resolve, reject) => {
      if (window.JSPlayerModuleLoaded) {
        // 实例已经加载过，不需要再次加载
        that.bInit = true;
        resolve();
      }
      loadJSByBlobToHeader(`${that.staticPath}/js/Decoder.js`, () => {
        window.JSPlayerModule({
          staticPath: that.staticPath
        }).then(instance => {
          window.Module = instance;
          that.Module = instance;
          that.bInit = true;
          window.JSPlayerModuleLoaded = true;
          resolve();
        });
      }, that.bInit);
    });
    return promise;
  }
  PlayM4_openStream(ng_port, fileHead) {
    var Module = window.Module;
    let pInputData = Module._malloc(40);
    if (pInputData === null) {
      return RETURN_ERROR;
    }
    let inputData = new Uint8Array(fileHead.buffer);
    Module.writeArrayToMemory(inputData, pInputData);
    let resO = Module._JSPlayM4_OpenStream(ng_port, pInputData, 40, 2 * 1024 * 1024);
    inputData = null;
    if (pInputData !== null) {
      Module._free(pInputData);
      pInputData = null;
    }
    console.log("3.打开流 resO:" + resO);
  }
  //执行播放方法
  _doPlay(szUrl, oParams, iWndNum, szStartTime, szStopTime) {
    const Module = window.Module;
    // if(this.bPlay || this.bPlayIng) {
    //   return false;
    // }
    this.bPlayIng = true;
    var _this = this;
    _this.playURL = oParams.playURL;
    _this.szUrl = szUrl;
    // 创建canvas
    if (!document.getElementById(_this.szId + '-player')) {
      let canvasList = document.getElementById(_this.szId);
      let canvas = document.createElement('canvas');
      canvasList.appendChild(canvas);
      canvas.width = _this.iWidth;
      canvas.height = _this.iHeight;
      canvas.id = _this.szId + '-player';
      //canvas.style.backgroundColor = '#000000';
    } else {
      let canvasList = document.getElementById(this.szId);
      let oldcanv = document.getElementById(this.szId + '-player');
      // oldcanv.id = this.szId + '-player-tmp';
      oldcanv.style = "position: absolute;";
      canvasList.removeChild(oldcanv);
      let canvas = document.createElement('canvas');
      canvas.width = _this.iWidth;
      canvas.height = _this.iHeight;
      canvas.id = _this.szId + '-player';
      //canvas.style.backgroundColor = '#000000';
      canvasList.appendChild(canvas);
    }
    let promise = new Promise((resolve, reject) => {
      // 先停止所有流
      this.oStreamClient.stopAll();
      //获取端口号
      _this.g_port = Module._JSPlayM4_GetPort();
      console.log("1.获取播放库端口号 g_port:" + _this.g_port);
      // 端口已经使用，设置为不可用状态
      this.portAvailable = false;

      //xuehb 开始取流 4
      if (_this.EventCallback && _this.EventCallback.openStreamCallback) {
        console.log('开始取流-----v2');
        _this.EventCallback.openStreamCallback();
      }
      _this.oStreamClient.openStream(szUrl, oParams, data => {
        if (data.errorCode && data.errorCode != '1') {
          //推流异常时触发errorHandler
          if (_this.EventCallback && _this.EventCallback.pluginErrorHandler) {
            _this.EventCallback.pluginErrorHandler(iWndNum, data.errorCode, data);
          }
        }
        if (data.bHead) {
          //判断是否开启预览，用于初始化播放库，回放跳片段是会返回媒体头，不能只判断是否为媒体头
          //xuehb 完成取流返回流头 5 
          if (_this.EventCallback && _this.EventCallback.getStreamHeaderCallback) {
            console.log('完成取流返回流头');
            _this.EventCallback.getStreamHeaderCallback();
          }
          _this.PlayM4_openStream(_this.g_port, data.buf);
          _this.aHead = new Uint8Array(data.buf);
          _this.pCanvasData = Module._malloc(128); //通过emscripten分配C/C++中的堆内存
          if (_this.pCanvasData === null) {
            return;
          }
          //let bufData = stringToBytes (`#${this.szId}-player1`);
          let bufData = stringToBytes(`#${_this.szId}-player`);
          ////方法一：
          let aKeyData = Module.HEAPU8.subarray(_this.pCanvasData, _this.pCanvasData + 128); //堆内存绑定到视图对象
          let u8aTemp = new Uint8Array(128);
          aKeyData.set(u8aTemp, 0);
          aKeyData.set(new Uint8Array(bufData)); // 数据写入到emscripten分配的内存中去
          aKeyData = null;
          let resP = Module._JSPlayM4_Play(_this.g_port, _this.pCanvasData); //pCanvasString或pCanvasData
          console.log("4.开始播放 resP:" + resP);
          try {
            const getQueryString = (name, url) => {
              const reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i');
              const r = url.match(reg);
              if (r != null) {
                return unescape(r[2]);
              }
              return null;
            };
            const date = getQueryString('begin', oParams.playURL);
            const year = parseInt(date.slice(0, 4));
            const month = parseInt(date.slice(4, 6));
            const day = parseInt(date.slice(6, 8));
            const hour = parseInt(date.slice(9, 11));
            const min = parseInt(date.slice(11, 13));
            const second = parseInt(date.slice(13, 15)) + 3;
            const setRes = Module._JSPlayM4_SetGlobalBaseTime(_this.g_port, year, month, day, hour, min, second, 0);
            console.log('设置OSD时间: ', _this.g_port, year + ':' + month + ':' + day + ':' + hour + ':' + min + ':' + second + ':' + 0);
            console.log('设置OSD时间res:', setRes);
          } catch (error) {
            console.log('_JSPlayM4_SetGlobalBaseTime:', error);
          }
          _this.bPlay = true;
          _this.bPlayIng = false;
          // 设置一系列回调
          let resFCB = Module._JSPlayM4_SetFrameInfoCallBack(_this.g_port, 1); //帧信息回调
          if (resFCB != 1) {
            let resFrameInfoError = Module._JSPlayM4_GetLastError(_this.g_port);
            console.log("resFrameInfoError:" + resFrameInfoError);
          }
          Module._JSPlayM4_RenderPrivateData(0, 1, 0);
          Module._JSPlayM4_RenderPrivateData(0, 2, 0);
          Module._JSPlayM4_RenderPrivateData(0, 4, 0);
          Module._JSPlayM4_RenderPrivateData(0, 16, 0);
          this.firstFlag = true;
          this.firstGetData = true;
          resolve(resP);
        } else {
          if (this.firstGetData) {
            // 第一次获取数据
            this.firstGetData = false;

            //xuehb 返回视频流(首次) 6
            if (_this.EventCallback && _this.EventCallback.getVideoStreamCallback) {
              _this.EventCallback.getVideoStreamCallback();
            }
          }
          if (this.firstFlag) {
            let oFrameInfo = Module._JSPlayM4_GetFrameInfo(this.g_port);
            var nWidth = Module.HEAP32[oFrameInfo + 12 >> 2];
            var nHeight = Module.HEAP32[oFrameInfo + 16 >> 2];
            if (nWidth) {
              //xuehb 首屏展示 7
              if (_this.EventCallback && _this.EventCallback.appearFirstFrameCallback) {
                console.log('出现首帧画面（播放成功）------多线程');
                _this.EventCallback.appearFirstFrameCallback();
              }
              //xuehb 平均成功取流的出流耗时 8
              if (_this.EventCallback && _this.EventCallback.averageStreamSuccessCallback) {
                _this.EventCallback.averageStreamSuccessCallback();
              }
              console.log("首帧展示", nWidth, nHeight);
              this.firstFlag = false;
              if (document.getElementById(this.szId + '-player-tmp')) {
                document.getElementById(this.szId + '-player-tmp').parentNode.removeChild(document.getElementById(this.szId + '-player-tmp'));
              }
            }
          }
          let aBuffer = new Uint8Array(data.buf); //拷贝一份数据进行使
          let pInputData1 = Module._malloc(aBuffer.length);
          if (pInputData1 === null) {
            console.log("inputdata malloc failed!!!");
            return -1;
          }
          Module.writeArrayToMemory(aBuffer, pInputData1);
          // 解密-end
          //console.log("resSetSecretKey："+resS);
          // 监听数据错误
          if (data.statusString) {
            if (_this.EventCallback && _this.EventCallback.pluginErrorHandler) {
              _this.EventCallback.pluginErrorHandler(_this.g_port, 1001, data);
            }
          } else if (data.type && data.type === "exception") {
            if (_this.EventCallback && _this.EventCallback.pluginErrorHandler) {
              _this.EventCallback.pluginErrorHandler(_this.g_port, 1002, data);
            }
          }
          Module._JSPlayM4_InputData(_this.g_port, pInputData1, aBuffer.length);
          if (pInputData1 !== null) {
            // console.log('释放内存')
            Module._free(pInputData1);
            pInputData1 = null;
          }
          aBuffer = null;
        }
        // 录制
        if (_this.szStorageUUID) {
          _this.oStorageManager.inputData(_this.szStorageUUID, data.buf);
        }
        // data = null;
      }, function () {
        if (_this.bPlay) {
          if (_this.EventCallback && _this.EventCallback.pluginErrorHandler) {
            _this.EventCallback.pluginErrorHandler(iWndNum, 1003);
          }
        }
        _this.bPlayIng = false;
        reject();
      }).then(id => {
        //websocket onopen事件触发
        _this.szStreamUUID = id;
        _this.oStreamClient.startPlay(id).then(() => {
          let resSSOM = Module._JSPlayM4_SetStreamOpenMode(_this.g_port, 1);
          console.log("2.设置流模式 resSSOM:" + resSSOM);
        }).catch(err => {
          console.log("err", err);
          _this._doRelease();
          _this.bPlayIng = false;
          reject(err.oError);
        });
      }, function (oError) {
        //websocekt 未进入onopen
        _this.bPlayIng = false;
        _this._doRelease();
        reject(oError);
      });
    });
    return promise;
  }
  /**
   * 
   * @param {*} szUrl  "示例：wss://jsdecoder-aeye.hwwt2.com:443"
   * @param {*} oParams  "示例： {playURL: "/live?dev=*********&chn=9&stream=1&ssn=ot.9oovv27v00lck3ft0krfw61n8ugr4j5b-1ao1cqq1fm-1od8d0d-h1lnhi0w0&auth=1&biz=4&cln=100"}"
   * @param {*} iWndNum 
   * @param {*} szStartTime 
   * @param {*} szStopTime 
   * @returns 
   */
  JS_Play(szUrl = "", oParams = {
    playURL: ""
  }, iWndNum, szStartTime, szStopTime) {
    return new Promise((resolve, reject) => {
      let loadWasmTimer = setInterval(() => {
        if (window.JSPlayerModuleLoaded) {
          clearInterval(loadWasmTimer);
          this._doPlay(szUrl, oParams, iWndNum, szStartTime, szStopTime).then(data => {
            resolve(data);
          }).catch(err => {
            reject(err);
          });
        }
      }, 50);
    });
  }
  JS_SetSecretKey(iWndNum, secretKey) {
    const {
      g_port
    } = this;
    const {
      Module
    } = window;
    // 字母字符串转byte数组
    function stringToBytes(str) {
      let ch,
        st,
        re = [];
      for (let i = 0; i < str.length; i++) {
        ch = str.charCodeAt(i); // get char
        st = []; // set up "stack"
        do {
          st.push(ch & 0xFF); // push byte to stack
          ch = ch >> 8; // shift value down by 1 byte
        } while (ch);
        // add stack contents to result
        // done because chars have "wrong" endianness
        re = re.concat(st.reverse());
      }
      // return an array of bytes
      return re;
    }
    let pKeyData = Module._malloc(128);
    if (pKeyData === null) {
      return;
    }
    let bufData = stringToBytes(secretKey);
    let aKeyData = Module.HEAPU8.subarray(pKeyData, pKeyData + 128);
    aKeyData.set(new Uint8Array(bufData));
    let resS = Module._JSPlayM4_SetSecretKey(g_port, 1, pKeyData, 128);
    if (pKeyData !== null) {
      Module._free(pKeyData);
      pKeyData = null;
    }
    console.log("resSetSecretKey：" + resS);
  }
  JS_OpenSound() {
    var {
      Module
    } = window;
    let resPS = Module._JSPlayM4_PlaySound(this.g_port);
    console.log("resPS", resPS);
    if (resPS === 1) {
      this.bPlaySound = 1;
    }
    return resPS;
  }
  JS_CloseSound() {
    var {
      Module
    } = window;
    let resPS = Module._JSPlayM4_StopSound(this.g_port);
    console.log("resPS", resPS);
    if (resPS === 1) {
      this.bPlaySound = -1;
    }
    return resPS;
  }
  JS_StartSave(g_port, fileName = `${new Date().getTime()}`) {
    // this.szStorageUUID =  `11111111`;
    this.oStorageManager.startRecord(`${fileName}.mp4`, this.aHead, 2, 0, {
      cbEventHandler: function (iErrorType) {
        console.log("录制错误", iErrorType);
      },
      iPackage: 0
    }).then(szUUID => {
      this.szStorageUUID = szUUID;
      //resolve();
    }, function () {
      //reject();
    });
  }
  JS_StopSave() {
    this.oStorageManager.stopRecord(this.szStorageUUID).then(() => {
      this.szStorageUUID = ""; //关闭录像  删除存储ID
    }, function (iError) {
      console.log("iError", iError);
    });
  }
  _JSPlayM4_GetFrameInfo() {
    const {
      Module
    } = window;
    let oFrameInfo = Module._JSPlayM4_GetFrameInfo(this.g_port);
    Module.HEAP32[oFrameInfo >> 2];
    Module.HEAP32[oFrameInfo + 4 >> 2];
    Module.HEAP32[oFrameInfo + 8 >> 2];
    let width = Module.HEAP32[oFrameInfo + 12 >> 2];
    let height = Module.HEAP32[oFrameInfo + 16 >> 2];
    console.log("width:" + width + ",height:" + height);
    Module.HEAP32[oFrameInfo + 20 >> 2];
    let year = Module.HEAP16[oFrameInfo + 60 >> 1];
    let month = Module.HEAP16[oFrameInfo + 62 >> 1];
    Module.HEAP16[oFrameInfo + 64 >> 1];
    let day = Module.HEAP16[oFrameInfo + 66 >> 1];
    let hour = Module.HEAP16[oFrameInfo + 68 >> 1];
    let minute = Module.HEAP16[oFrameInfo + 70 >> 1];
    let second = Module.HEAP16[oFrameInfo + 72 >> 1];
    let milliseconds = Module.HEAP16[oFrameInfo + 74 >> 1];
    console.log("OSD时间：" + year + "-" + month + "-" + day + "-" + hour + "-" + minute + "-" + second + "-" + milliseconds);
    return {
      width,
      height,
      year,
      month,
      day,
      hour,
      minute,
      second
    };
  }
  JS_SetFrameInfoCallBack(value) {
    const {
      Module
    } = window;
    let resFCB = Module._JSPlayM4_SetFrameInfoCallBack(this.g_port, value); //帧信息回调
    if (resFCB != 1) {
      let resFrameInfoError = Module._JSPlayM4_GetLastError(this.g_port);
      console.log("resFrameInfoError:" + resFrameInfoError);
    }
    console.log("resFCB:" + resFCB);
  }
  JS_CapturePicture(iWind, fileName = `${new Date().getTime()}.jpeg`, type, callback, isDownload) {
    const {
      g_port
    } = this;
    const {
      Module
    } = window;
    //未播放，暂停状态且有缓冲数据
    if (!this.bPlay && this.bPause && this.tmpCapturePictureData) {
      doCapturePicture(this.tmpCapturePictureData);
      return;
    }
    let oFrameInfo = Module._JSPlayM4_GetFrameInfo(g_port);
    Module.HEAP32[oFrameInfo >> 2];
    let width = Module.HEAP32[oFrameInfo + 12 >> 2];
    let height = Module.HEAP32[oFrameInfo + 16 >> 2];
    nWidth = width;
    nHeight = height;
    if (nWidth && nHeight) {
      // 分配Jpeg空间
      let nJpegSize = nWidth * nHeight * 4 + 60;
      let pJpegData = Module._malloc(nJpegSize);
      let pJpegSize = Module._malloc(4);
      if (pJpegData === null) {
        return;
      }
      Module._JSPlayM4_GetJPEG(g_port, pJpegData, nJpegSize, pJpegSize); //buffer,buffer大小，Jpeg大小
      // 获取Jpeg图片大小
      let nJpegDataSize = Module.getValue(pJpegSize, "i32");
      console.log("JPEGSIZE:" + nJpegDataSize);
      // 获取Jpeg图片数据
      let aJpegData = new Uint8Array(nJpegDataSize);
      aJpegData.set(Module.HEAPU8.subarray(pJpegData, pJpegData + nJpegDataSize));
      doCapturePicture(aJpegData);
      aJpegData = null;
      if (pJpegData != null) {
        Module._free(pJpegData);
        pJpegData = null;
      }
      if (pJpegSize != null) {
        Module._free(pJpegSize);
        pJpegSize = null;
      }
    }
    function doCapturePicture(aJpegData) {
      if (fileName.indexOf(".jpeg") === -1) {
        fileName += '.jpeg';
      }
      if (!callback) {
        downloadFile(aJpegData, fileName);
      } else {
        const arrayBufferToBase64Img = buffer => {
          const arr = new Uint8Array(buffer);
          var str = "";
          for (let i = 0; i < arr.length; i++) {
            str += String.fromCharCode(arr[i]);
          }
          return `data:image/jpeg;base64,${window.btoa(str)}`;
        };
        callback({
          fileName: fileName,
          fileUint8Array: aJpegData,
          base64: arrayBufferToBase64Img(aJpegData)
        });
      }
    }
  }
  JS_GetOSDTime() {
    const {
      g_port
    } = this;
    const {
      Module
    } = window;
    const promise = new Promise((resolve, reject) => {
      let oFrameInfo = Module._JSPlayM4_GetFrameInfo(g_port);
      let year = Module.HEAP16[oFrameInfo + 60 >> 1];
      let month = Module.HEAP16[oFrameInfo + 62 >> 1];
      let day = Module.HEAP16[oFrameInfo + 66 >> 1];
      let hour = Module.HEAP16[oFrameInfo + 68 >> 1];
      let minute = Module.HEAP16[oFrameInfo + 70 >> 1];
      let second = Module.HEAP16[oFrameInfo + 72 >> 1];
      var time = new Date(`${year}-${month}-${day} ${hour}:${minute}:${second}`).getTime() / 1000;
      resolve(time);
    });
    return promise;
  }
  JS_Resize(width, height) {
    const {
      g_port
    } = this;
    const {
      Module
    } = window;
    console.log("width:" + width + ",height:" + height);
    let resSet = Module._JSPlayM4_SetCanvasSize(g_port, width, height);
    this.iWidth = width;
    this.iHeight = height;
    document.getElementById(this.szId).style.height = height + "px";
    document.getElementById(this.szId).style.width = width + "px";
    let oldcanv = document.getElementById(this.szId + '-player');
    if (oldcanv) {
      oldcanv.style.height = height + "px";
      oldcanv.style.width = width + "px";
    }
    console.log("resSet:" + resSet);
  }
  _doRelease() {
    const {
      g_port,
      szStreamUUID
    } = this;
    const {
      Module
    } = window;
    let resCS = Module._JSPlayM4_CloseStream(g_port);
    let resFP = Module._JSPlayM4_FreePort(g_port);
    if (resFP === 1) {
      // 端口是否已经被释放，设置为可用状态。
      this.portAvailable = true;
    }
    console.log("doRelease", resCS, resFP);
    if (this.pCanvasData !== null) {
      Module._free(this.pCanvasData);
      this.pCanvasData = null;
    }
    // this.pCanvasString = null;
    // this.pCanvasString = null;
    console.log("stop end");
    this.bPlay = false;
    this.bPlayIng = false;
  }
  JS_Stop() {
    const {
      g_port,
      szStreamUUID
    } = this;
    const {
      Module
    } = window; //销毁canvas资源

    let canvasList = document.getElementById(this.szId);
    let oldcanv = document.getElementById(this.szId + '-player');
    if (oldcanv) {
      canvasList.removeChild(oldcanv);
    }
    if (this.szStorageUUID) {
      this.JS_StopSave();
    }
    // 暂停缓存截图数据 start

    const callback = data => {
      if (data.fileUint8Array) {
        this.tmpCapturePictureData = data.fileUint8Array;
      }
    };
    const arrayBufferToBase64Img = buffer => {
      const arr = new Uint8Array(buffer);
      var str = "";
      for (let i = 0; i < arr.length; i++) {
        str += String.fromCharCode(arr[i]);
      }
      return `data:image/jpeg;base64,${window.btoa(str)}`;
    };
    this.JS_CapturePicture(0, `${new Date().getTime()}`, "JPEG", callback, true); // 保留最后一帧
    if (this.tmpCapturePictureData && this.tmpCapturePictureData.length > 0) {
      canvasList.style.backgroundImage = `url(${arrayBufferToBase64Img(this.tmpCapturePictureData)})`;
    } // 暂停缓存截图数据 end

    const promise = new Promise((resolve, reject) => {
      if (this.portAvailable) {
        // 当前端口可用，不需要执行停止
        console.log("当前端口可用，不需要执行停止");
        return resolve({});
      }
      var stopStreamPromise = this.oStreamClient.stopAll(szStreamUUID);
      stopStreamPromise.then(data => {
        console.log("防抖 停止取流成功");
        let resS = Module._JSPlayM4_Stop(g_port);
        if (resS == 1) {
          this._doRelease();
          resolve({
            resS
          });
        } else {
          var jsPluginErrorCode = Module._JSPlayM4_GetLastError(g_port);
          if (jsPluginErrorCode == 35) {
            let timer10 = setInterval(() => {
              if (!resS) {
                resS = Module._JSPlayM4_Stop(g_port);
                if (resS == 1) {
                  this._doRelease();
                  resolve({
                    resS
                  });
                } else {
                  jsPluginErrorCode = Module._JSPlayM4_GetLastError(g_port);
                  console.log("stop error ", jsPluginErrorCode); // 停止失败，持续停止
                }
              } else {
                clearInterval(timer10);
              }
            }, 50);
          } else {
            console.log("stop - other error, still resolve", jsPluginErrorCode, resS);
            this._doRelease();
            resolve({
              resS,
              jsPluginErrorCode
            });
          }
        }
      }).catch(err => {
        console.log("停止取流失败_jsPluginErrorCode", err);
        this._doRelease();
        var jsPluginErrorCode = Module._JSPlayM4_GetLastError(g_port);
        console.log("停止取流失败_jsPluginErrorCode", g_port, jsPluginErrorCode);
        resolve({
          jsPluginErrorCode
        });
      });
    });
    return promise;
  }
  JS_DestroyWorker() {
    const {
      Module
    } = window;
    //buffer清空
    //bodyData.splice(0,bodyData.length);
    // aVideoYUVBuffer.splice(0, aVideoYUVBuffer.length);
    // aAudioPCMBuffer.splice(0, aAudioPCMBuffer.length);
    // aPsBuffer.splice(0, aPsBuffer.length);
    //销毁canvas资源
    let canvasList = document.getElementById(this.szId);
    let oldcanv = document.getElementById(this.szId + '-player');
    if (oldcanv) {
      canvasList.removeChild(oldcanv);
    }
    if (this.pCanvasData !== null) {
      Module._free(this.pCanvasData);
      this.pCanvasData = null;
    }
    this.pCanvasString = null;
    console.log("stop end");
  }
  JS_Fast(iWind, next) {
    var nextRate = this.iRate;
    if (next) {
      switch (next) {
        case 1:
        case 2:
        case 4:
          nextRate = next;
          break;
        case 3:
          nextRate = 0.5;
          break;
        default:
          nextRate = this.iRate;
          break;
      }
    } else {
      switch (this.iRate) {
        case 1:
          nextRate = 2;
          break;
        case 2:
          nextRate = 4;
          break;
        default:
          nextRate = this.iRate;
          break;
      }
    }
    this.oStreamClient.setPlayRate(this.szStreamUUID, nextRate);
    this.iRate = nextRate;
    console.log("开启倍速播放,多线程倍速，全解", nextRate);
    if (nextRate == 4) {
      // 用户启动4倍速，执行8线程解码。
      let SetDecodeThreadNumRes = window.Module._JSPlayM4_SetDecodeThreadNum(this.g_port, 8);
      console.log("SetDecodeThreadNumRes", SetDecodeThreadNumRes);
    }
    let res = window.Module._JSPlayM4_Fast(this.g_port);
    console.log("res", res);
    // if(nextRate < 2) {
    //   this.Module._JSPlayM4_SetDecodeFrameType(this.g_port,0)
    // } else {
    //   this.Module._JSPlayM4_SetDecodeFrameType(this.g_port,1);
    //  // this.Module._JSPlayM4_SetIFrameDecInterval(this.g_port,0);
    // }
  }

  JS_Slow() {
    var nextRate = this.iRate;
    switch (this.iRate) {
      case 2:
        nextRate = 1;
        break;
      case 4:
        nextRate = 2;
        break;
      default:
        nextRate = this.iRate;
        break;
    }
    this.oStreamClient.setPlayRate(this.szStreamUUID, nextRate);
    this.iRate = nextRate;
    console.log("开启倍速播放", nextRate);
  }
  JS_Speed(nextRate) {
    this.oStreamClient.setPlayRate(this.szStreamUUID, nextRate);
    window.Module._JSPlayM4_SetDecodeFrameType(1);
    const fastRes = window.Module._JSPlayM4_Fast(this.g_port);
    this.iRate = nextRate;
    console.log("开启倍速播放", nextRate);
    console.log("开启倍速播放res:", fastRes);
  }
  JS_Seek(iWndNum, szStartTime, szStopTime) {
    let oPromise = new Promise((resolve, reject) => {
      var res = window.Module._JSPlayM4_ResetBuffer(this.g_port, 1);
      console.log("seek 清理缓存", res);
      this.oStreamClient.seek(this.szStreamUUID, szStartTime, szStopTime).then(function () {
        resolve();
      }, function (oError) {
        reject(oError);
      });
    });
    return oPromise;
  }
  JSPlayM4_SetPrintLogFlag() {
    let resDR = window.Module._JSPlayM4_SetPrintLogFlag(this.g_port, 2);
    console.log("LogValue:" + 2 + ",resDR:" + resDR);
  }
  _JSPlayM4_SetDisplayRegion(left, right, top, bottom) {
    new Promise((resolve, reject) => {
      let resDR = window.Module._JSPlayM4_SetDisplayRegion(this.g_port, this.szId, left, right, top, bottom);
      resolve({
        code: 0,
        data: resDR
      });
    });
  }
  /**
     * @synopsis 暂停
     *
     * @param {number} iWndNum 窗口号
     *
     * @returns {none} 无
     */
  JS_Pause(iWndNum) {
    if (!this.bPlay) {
      console.log("当前不是播放状态，无法暂停");
      return new Promise((resolve, reject) => {
        reject({
          code: 0,
          msg: "当前不是播放状态，不需要暂停"
        });
      });
    }
    // 暂停缓存截图数据 start
    const callback = data => {
      if (data.fileUint8Array) {
        this.tmpCapturePictureData = data.fileUint8Array;
      }
    };
    this.JS_CapturePicture(0, `${new Date().getTime()}`, "JPEG", callback, true);
    // 暂停缓存截图数据 end

    if (this.playURL.indexOf("playback") !== -1) {
      //回放暂停
      // 如果为回放，需要记录暂停时间
      var pauseTimeFrame = this._JSPlayM4_GetFrameInfo(this.g_port);
      console.log("开始暂停");
      var pauseTime = `${pauseTimeFrame.year}` + (pauseTimeFrame.month > 9 ? pauseTimeFrame.month : `0${pauseTimeFrame.month}`) + (pauseTimeFrame.day > 9 ? pauseTimeFrame.day : `0${pauseTimeFrame.day}`) + (pauseTimeFrame.hour > 9 ? pauseTimeFrame.hour : `0${pauseTimeFrame.hour}`) + (pauseTimeFrame.minute > 9 ? pauseTimeFrame.minute : `0${pauseTimeFrame.minute}`) + (pauseTimeFrame.second > 9 ? pauseTimeFrame.second : `0${pauseTimeFrame.second}`);
      if (pauseTime.length === 14) {
        // 符合yyyymmddhhmmss后赋值
        this.bPauseTime = pauseTime;
      }
    }
    this.JS_GetOSDTime() //记录暂停的OSD时间
    .then(data => {
      var pauseDate = new Date(data * 1000);
      this.bPauseDate = pauseDate.valueOf();
    });
    const promise = new Promise((resolve, reject) => {
      this.JS_Stop().then(() => {
        this.bPlay = false; // 停止后暂停状态需要复原
        this.bPause = true;
        resolve({
          code: 1,
          msg: "暂停成功"
        });
      }).catch(err => {
        reject({
          code: 0,
          msg: "暂停失败"
        });
        console.log("err", err);
      });
    });
    return promise;
  }

  /**
   * @synopsis 恢复
   *
   * @param {number} iWndNum 窗口号
   *
   * @returns {none} 无
   */
  JS_Resume(resumeTime) {
    const promise = new Promise((resolve, reject) => {
      if (resumeTime) {
        if (this.bPauseTime) {
          this.bPauseTime = this.bPauseTime.slice(0, 14 - resumeTime.length) + resumeTime;
        } else {
          this.bPauseTime = resumeTime;
        }
      }
      if (this.playURL.indexOf("playback") !== -1) {
        this.playURL = this.playURL.replace(/begin=[0-9]{8}T[0-9]{6}Z/gi, `begin=${this.bPauseTime.slice(0, 8)}T${this.bPauseTime.slice(8, 14)}Z`);
      }
      console.log('this.bPauseDate:', this.bPauseDate);
      if (this.playURL.indexOf("cloudplayback") !== -1) {
        this.playURL = this.playURL.replace(/"startTime":[0-9]{13}/gi, `"startTime":${this.bPauseDate}`);
      }
      this.bPause = false;
      this.JS_Play(this.szUrl, {
        playURL: this.playURL
      }).then(data => {
        resolve(data);
      }).catch(err => {
        reject(err);
      });
    });
    return promise;
  }
}
window.JSPluginV2 = JSPluginV2;
