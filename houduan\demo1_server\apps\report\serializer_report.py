# from rest_framework.serializers import ModelSerializer
# from demo1_server.apps.report import models
# from rest_framework import serializers

# class ReportModelSerializer(ModelSerializer):
#     class Meta:
#         model = models.ReportEquip
#         fields = ['id','report_index', 'level', 'title', 'status','keyword', 'item', 'score', 'vender', 'enable']


# class MaskModelSerializer(ModelSerializer):
#     class Meta:
#         model = models.Mask
#         fields = ['id','ip', 'is_confines', 'start_time',"end_time","status","time"]
#     start_time = serializers.SerializerMethodField()
#     def get_start_time(self,obj):
#         start_time = obj.start_time
#         if start_time:
#             start_time = str(start_time).split('.')[0]
#         return start_time
#     end_time = serializers.SerializerMethodField()
#     def get_end_time(self, obj):
#         end_time = obj.end_time
#         if end_time:
#             end_time = str(end_time).split('.')[0]
#         return end_time

