import operator
from functools import reduce
from rest_framework import filters
from django.db import models
import re
import IPy
from rest_framework.compat import core<PERSON><PERSON>, coreschema, distinct


class NewFilter(filters.SearchFilter):
    def filter_queryset(self, request, queryset, view):
        search_fields = self.get_search_fields(view, request)
        search_terms = self.get_search_terms(request)

        if not search_fields or not search_terms:
            return queryset

        orm_lookups = [
            self.construct_search(str(search_field))
            for search_field in search_fields
        ]
        base = queryset
        conditions = []
        pk_set = set()
        for search_term in search_terms:
            queries = [
                models.Q(**{orm_lookup: search_term})
                for orm_lookup in orm_lookups
            ]
            conditions.append(reduce(operator.or_, queries))
        path = request.META.get("PATH_INFO")
        others_lookup = []
        if path.startswith('/yewu/'):
            others_lookup = [
                self.construct_search(str(search_field))
                for search_field in ['^network','^network_2','^network_3']
            ]
        if others_lookup:
            for search_term in search_terms:
                if re.findall(r'[\d.:]*', search_term)[0] == search_term:
                    queries = []
                    if is_ipv4(search_term):
                        split_search = search_term.rsplit('.', maxsplit=1)[0]
                        queries = [
                            models.Q(**{orm_lookup: split_search})
                            for orm_lookup in others_lookup
                        ]
                    if is_ipv6(search_term):
                        split_search = search_term.rsplit(':', maxsplit=2)[0]
                        queries = [
                            models.Q(**{orm_lookup: split_search})
                            for orm_lookup in others_lookup
                        ]
                    if queries:
                        new_queryset = queryset.filter(reduce(operator.or_, queries))
                        for obj in new_queryset:
                            if obj.network:
                                for subnet_ip in obj.network.split(','):
                                    if subnet_ip.count('/') == 1:
                                        if search_term in IPy.IP(subnet_ip, make_net=True):
                                            pk_set.add(obj.id)
                            if obj.network_2:
                                for subnet_ip in obj.network_2.split(','):
                                    if subnet_ip.count('/') == 1:
                                        if search_term in IPy.IP(subnet_ip, make_net=True):
                                            pk_set.add(obj.id)
                            if obj.network_3:
                                for subnet_ip in obj.network_3.split(','):
                                    if subnet_ip.count('/') == 1:
                                        if search_term in IPy.IP(subnet_ip, make_net=True):
                                            pk_set.add(obj.id)
        old_queryset = queryset.filter(reduce(operator.and_, conditions))  # 所以前端的条件，相隔表示and
        for obj in old_queryset:
            pk_set.add(obj.id)
        queryset = queryset.filter(id__in=pk_set)
        if self.must_call_distinct(queryset, search_fields):
            # Filtering against a many-to-many field requires us to
            # call queryset.distinct() in order to avoid duplicate items
            # in the resulting queryset.
            # We try to avoid this if possible, for performance reasons.
            queryset = distinct(queryset, base)
        return queryset


def is_ipv4(ip: str) -> bool:  # 判断ip是否合法
    return True if [1] * 4 == [x.isdigit() and 0 <= int(x) <= 255 for x in ip.split(".")] else False


def is_ipv6(ip: str) -> bool:
    try:
        if IPy.IP(ip).version() == 6:
            return True
    except ValueError:
        return False

