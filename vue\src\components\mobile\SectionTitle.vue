<template>
  <div class="section-title">
    <h3 class="title-text">{{ title }}</h3>
  </div>
</template>

<script>
export default {
  name: 'SectionTitle',
  props: {
    title: {
      type: String,
      required: true
    }
  }
}
</script>

<style lang="less" scoped>
.section-title {
  display: flex;
  align-items: center;
  width: 100%;
  height: 50px;
  background-image: url('../ui/subtitled-title.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
  
  .title-text {
    font-size: 18px;
    color: #BDDBF1;
    padding-left: 55px;
    margin-top: 4px;
    font-family: PingFang SC;
    font-weight: 400;
  }
}
</style> 