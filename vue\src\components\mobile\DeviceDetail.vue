<template>
  <div class="device-detail">
    <van-nav-bar :title="deviceName" left-arrow @click-left="onClickLeft" class="device-nav" />

    <!-- 信息展示框 -->
    <div class="equip-box" :style="{ backgroundImage: `url(${backgroundImage})` }">
      <div class="text">
        <div class="info-table">
          <!-- 前三行独占一行 -->
          <div class="info-row full-width">
            <span :style="{ color: labelcolor }">{{ miaoshuFormatted[0].label }}</span>
            <span class="info-value">：{{ miaoshuFormatted[0].value }}</span>
          </div>
          <div class="info-row full-width">
            <span :style="{ color: labelcolor }">{{ miaoshuFormatted[1].label }}</span>
            <span class="info-value">：{{ miaoshuFormatted[1].value }}</span>
          </div>
          <div class="info-row full-width">
            <span :style="{ color: labelcolor }">{{ miaoshuFormatted[2].label }}</span>
            <span class="info-value">：{{ miaoshuFormatted[2].value }}</span>
          </div>
          <!-- 后面每行2个字段，中间保留间隔 -->
          <div class="info-row">
            <span :style="{ color: labelcolor }">{{ miaoshuFormatted[3].label }}</span>
            <span class="info-value">：{{ miaoshuFormatted[3].value }}</span>
            <span class="info-gap"></span>
            <span :style="{ color: labelcolor }">{{ miaoshuFormatted[4].label }}</span>
            <span class="info-value">：{{ miaoshuFormatted[4].value }}</span>
          </div>
          <div class="info-row">
            <span :style="{ color: labelcolor }">{{ miaoshuFormatted[5].label }}</span>
            <span class="info-value">：{{ miaoshuFormatted[5].value }}</span>
            <span class="info-gap"></span>
            <span :style="{ color: labelcolor }">{{ miaoshuFormatted[6].label }}</span>
            <span class="info-value">：{{ miaoshuFormatted[6].value }}</span>
          </div>
          <div class="info-row">
            <span :style="{ color: labelcolor }">{{ miaoshuFormatted[7].label }}</span>
            <span class="info-value">：{{ miaoshuFormatted[7].value }}</span>
            <span class="info-gap"></span>
            <span :style="{ color: labelcolor }">{{ miaoshuFormatted[8].label }}</span>
            <span class="info-value">：{{ miaoshuFormatted[8].value }}</span>
          </div>
          <div class="info-row">
            <span :style="{ color: labelcolor }">{{ miaoshuFormatted[9].label }}</span>
            <span class="info-value">：{{ miaoshuFormatted[9].value }}</span>
            <span class="info-gap"></span>
            <span :style="{ color: labelcolor }">{{ miaoshuFormatted[10].label }}</span>
            <span class="info-value">：{{ miaoshuFormatted[10].value }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 图表部分，结构与大屏一致 -->
    <div class="right-item2" v-for="(title, idx) in chartTitles" :key="idx"
      :class="selectid == 1 ? 'shebei1' : 'shebei2'">
      <span>{{ title }}</span>
      <chartline v-if="lineData && lineData.length > 0 && lineData[idx]" :Data="lineData[idx]"></chartline>
    </div>
  </div>
</template>

<script>
import { select_equipmentv2, shebeilistv2 } from '@/request/api';
import { mapState, mapGetters, mapActions } from 'vuex';
import chartline from '@/components/charts/Line/Line.vue'

export default {
  components: {
    chartline
  },
  // name: 'DeviceDetailView',

  data() {
    return {
      box: 0,
      miaoshu: {
        EQUIPMENT_TYPE: '',
        EQUIPMENT_NAME: '',
        ACQUISITION_TIME: '',
        INTRODUCE: '',
        VOLTAGE: '',
        state: '',
        ORP: '',
        CON: '',
        TEM: '',
        PH: '',
        O2: '',
        SALT: '',
        LIGHT: '',
        WIND_SPEED: '',
        WIND_DIRECTION: '',
        RADIATION: '',
        HUMIDITY: '',
        ATM: '',
        RAINFALL: ''
      },
      selectid: 1,
      lineData: [
        {
          name: "温度",
          data: [],
          xAxisData: [],
          data1: [],
          data2: [],
        },
        {
          name: "溶氧值",
          data: [],
          xAxisData: [],
          data1: [],
          data2: [],
        },
        {
          name: "PH",
          data: [],
          xAxisData: [],
          data1: [],
          data2: [],
        },
        {
          name: "雨量",
          data: [],
          xAxisData: [],
          data1: [],
          data2: [],
        }
      ]
    }
  },

  computed: {
    ...mapGetters('device', ['deviceInfo', 'baseId', 'chartData']),
    deviceId() {
      return this.deviceInfo.id
    },
    deviceName() {
      return this.deviceInfo.name
    },
    deviceType() {
      return this.deviceInfo.type
    },
    fullDeviceData() {
      return this.deviceInfo.fullDeviceData
    },
    backgroundImage() {
      if (this.miaoshu.EQUIPMENT_TYPE === "水质检测") {
        return require('@/assets/ui/map-bg1.png');
      } else {
        return require('@/assets/ui/map-bg2.png');
      }
    },
    labelcolor() {
      return this.miaoshu.EQUIPMENT_TYPE == "水质检测" ? '#5CA0B1' : '#99BB6D'
    },

    miaoshuFormatted() {
      if (this.miaoshu.EQUIPMENT_TYPE == "水质检测") {
        const formattedData = [
          { label: '设备名称', value: this.miaoshu.EQUIPMENT_NAME },
          { label: '采集时间', value: this.miaoshu.ACQUISITION_TIME },
          { label: '设备描述', value: this.miaoshu.INTRODUCE },
          { label: '电压', value: this.miaoshu.VOLTAGE },
          { label: '状态', value: this.miaoshu.state },
          { label: 'ORP', value: this.miaoshu.ORP },
          { label: '电导', value: this.miaoshu.CON },
          { label: '温度', value: this.miaoshu.TEM },
          { label: 'PH', value: this.miaoshu.PH },
          { label: '含氧量', value: this.miaoshu.O2 },
          { label: '盐度', value: this.miaoshu.SALT }
        ];
        return formattedData;
      }
      else if (this.miaoshu.EQUIPMENT_TYPE == "气象检测") {
        const formattedData = [
          { label: '设备名称', value: this.miaoshu.EQUIPMENT_NAME },
          { label: '采集时间', value: this.miaoshu.ACQUISITION_TIME },
          { label: '设备描述', value: this.miaoshu.INTRODUCE },
          { label: '电压', value: this.miaoshu.VOLTAGE },
          { label: '状态', value: this.miaoshu.state },
          { label: '光照', value: this.miaoshu.LIGHT },
          { label: '风速', value: this.miaoshu.WIND_SPEED },
          { label: '风向', value: this.miaoshu.WIND_DIRECTION },
          { label: '辐射', value: this.miaoshu.RADIATION },
          { label: '温度', value: this.miaoshu.TEM },
          { label: '湿度', value: this.miaoshu.HUMIDITY }
        ];
        return formattedData;
      }
      return [];
    },

    chartTitles() {
      if (this.selectid == 1) {
        return ['水温', 'PH', '含氧量'];
      } else if (this.selectid == 2) {
        return ['温度', '湿度', '气压'];
      } else {
        return ['', '', ''];
      }
    }
  },

  created() {
    if (this.deviceType === 'water') {
      this.selectid = 1;
    } else if (this.deviceType === 'meteorological') {
      this.selectid = 2;
    }
    
    if (this.fullDeviceData) {
      this.miaoshu = {
        EQUIPMENT_TYPE: this.deviceType === 'water' ? '水质检测' : '气象检测',
        EQUIPMENT_NAME: this.fullDeviceData.EQUIPMENT_NAME,
        ACQUISITION_TIME: this.fullDeviceData.ACQUISITION_TIME,
        INTRODUCE: this.fullDeviceData.INTRODUCE,
        VOLTAGE: this.fullDeviceData.VOLTAGE,
        state: this.fullDeviceData.STATE,
        ORP: this.fullDeviceData.ORP,
        CON: this.fullDeviceData.CON,
        TEM: this.fullDeviceData.TEM,
        PH: this.fullDeviceData.PH,
        O2: this.fullDeviceData.O2,
        SALT: this.fullDeviceData.SALT,
        LIGHT: this.fullDeviceData.LIGHT,
        WIND_SPEED: this.fullDeviceData.WIND_SPEED,
        WIND_DIRECTION: this.fullDeviceData.WIND_DIRECTION,
        RADIATION: this.fullDeviceData.RADIATION,
        HUMIDITY: this.fullDeviceData.HUMIDITY,
        ATM: this.fullDeviceData.ATM,
        RAINFALL: this.fullDeviceData.RAINFALL
      };
    }

    this.fetchDeviceData();
  },

  methods: {
    ...mapActions('device', ['updateDeviceInfo', 'updateBaseId', 'updateChartData']),
    
    onClickLeft() {
      this.$router.back()
    },
    getshebeidata(item) {
      select_equipmentv2({
        base_id: this.baseId,
        equipmentID: item.ID
      }).then(res => {
        if (res.data && res.data.shuju) {
          const data = res.data.shuju;
          let newLineData = [];

          // 处理温度数据
          newLineData.push({
            name: "温度",
            id: 1,
            data: data.templist[0].y[0].data,
            xAxisData: data.templist[0].x,
            data1: data.templist[0].y[1].data,
            data2: data.templist[0].y[2].data,
            max: data.templist[0].max,
            min: data.templist[0].min,
          });

          if (this.selectid == 1) {
            // 水质设备数据
            newLineData.push(
              {
                name: "溶氧值",
                id: 2,
                data: data.templist[1].y[0].data,
                xAxisData: data.templist[0].x,
                data1: data.templist[1].y[1].data,
                data2: data.templist[1].y[2].data,
                max: data.templist[1].max,
                min: data.templist[1].min,
              },
              {
                name: "PH",
                id: 3,
                data: data.templist[2].y[0].data,
                xAxisData: data.templist[0].x,
                data1: data.templist[2].y[1].data,
                data2: data.templist[2].y[2].data,
                max: data.templist[2].max,
                min: data.templist[2].min,
              }
            );
          } else if (this.selectid == 2) {
            // 气象设备数据
            newLineData.push(
              {
                name: "湿度",
                id: 2,
                data: data.templist[3].y[0].data,
                xAxisData: data.templist[0].x,
                data1: data.templist[3].y[1].data,
                data2: data.templist[3].y[2].data,
                max: data.templist[3].max,
                min: data.templist[3].min,
              },
              {
                name: "气压",
                id: 3,
                data: data.templist[4].y[0].data,
                xAxisData: data.templist[0].x,
                data1: data.templist[4].y[1].data,
                data2: data.templist[4].y[2].data,
                max: data.templist[4].max,
                min: data.templist[4].min,
              }
            );
          }

          this.lineData = newLineData;
          this.updateChartData(newLineData);
        }
      }).catch(error => {
        console.error('获取设备图表数据失败:', error);
      });
    },

    fetchDeviceData() {
      if (!this.baseId || !this.deviceId) {
        console.warn('缺少必要的参数');
        return;
      }

      shebeilistv2({
        base_id: this.baseId,
      }).then(res => {
        if (res && res.data && res.data.shebeilist) {
          const targetDevice = res.data.shebeilist.find(item => 
            item.ID == this.deviceId || item.EQUIPMENT_NAME == this.deviceName
          );

          if (targetDevice) {
            this.getshebeidata(targetDevice);
          }
        }
      }).catch(error => {
        console.error('获取设备列表失败:', error);
      });
    },
  }
}
</script>

<style lang="less" scoped>
.device-detail {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #001528;
  background-image: url('../ui/subtitled-bg.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  overflow-y: auto;
  padding-bottom: 20px;
}

.device-nav {
  flex-shrink: 0;

  :deep(.van-nav-bar__content) {
    background-color: #001528;
  }

  :deep(.van-nav-bar__title) {
    color: #C3E4FF;
  }

  :deep(.van-icon) {
    color: #C3E4FF !important;
  }
}



.equip-box {
  position: relative;
  font-size: 14px;
  // min-height: 100px;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  // color: #A6FFB0;
  padding: 18px;
  // box-sizing: border-box;

  .text {
    position: relative;
    // color: #A6FFB0;
    width: 100%;
    height: auto;
    font-family: 'Consolas', 'PingFang SC', 'Microsoft YaHei', Arial, sans-serif;
  }

  .info-table {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 2px;
  }

  .info-row {
    display: flex;
    align-items: baseline;
    margin-bottom: 2px;
    width: 100%;
  }

  .info-label {
    font-weight: bold;
    margin-right: 2px;
    display: inline-block;
    min-width: 56px;
    text-align: left;
    vertical-align: baseline;
  }

  .info-value {
    color: #fff;
    margin-right: 12px;
    display: inline-block;
    min-width: 60px;
    text-align: left;
    vertical-align: baseline;
  }

  .info-gap {
    display: inline-block;
    width: 24px;
    min-width: 16px;
    max-width: 32px;
  }
}

.erow {
  display: flex;
  height: 30px;
  align-items: center;

  .rowh {
    width: 50%;
    display: flex;
    align-items: center;

    .item {
      width: 30%;
      display: inline-block;
    }

    .underline1 {
      width: 45%;
      display: inline-block;
      border-style: hidden;
    }
  }

  .underline {
    border-style: hidden;
  }
}

.common-box {
  position: relative;
  height: 100%;
  width: 100%;
}

.right-box {
  color: #C3E4FF;
}

.border-box {
  padding: 15px;
  border-radius: 4px;
}

.box-title {
  font-size: 18px;
  margin-bottom: 15px;
  font-weight: bold;
  text-align: center;
}

.flex-vertical {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.flex-header {
  margin-bottom: 20px;
}

.flex-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.flex-content {
  flex: 1;
  text-align: center;
}

.mr-4 {
  margin-right: 16px;
}

.mt-2 {
  margin-top: 8px;
}

.item-title {
  font-size: 14px;
  color: #8BAACE;
  margin-bottom: 5px;
}

.item-value {
  font-size: 18px;
  font-weight: bold;
}

.line-box {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 200px;
}

.tabs-header {
  display: flex;
  margin-bottom: 10px;
}

.tab-item {
  padding: 5px 10px;
  font-size: 14px;
  cursor: pointer;
  margin-right: 10px;
  border: 1px solid rgba(67, 200, 255, 0.3);
  border-radius: 4px;
}

.tab-item.active {
  background-color: rgba(67, 200, 255, 0.2);
  color: #43C8FF;
}

.w-100 {
  width: 100%;
}

.h-100 {
  height: 100%;
}

.info-line {
  background-color: rgba(1, 19, 67, 0.5);
  border: 1px dashed rgba(67, 200, 255, 0.3);
  border-radius: 4px;
  flex: 1;
}

.info-table-green {
  color: #A6FFB0;
  font-size: 14px;
  line-height: 1.7;
  text-align: left;
  font-family: 'Consolas', 'PingFang SC', 'Microsoft YaHei', Arial, sans-serif;
  background: transparent;
}

.info-row-green {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 2px;
}

.info-label-green {
  color: #A6FFB0;
  font-weight: bold;
  margin-right: 2px;
}

.info-value-green {
  color: #fff;
  margin-right: 12px;
}


.right-item2 {
  font-size: 18px;
  color: #FFFFFF;
  max-width: 420px;
  margin: 16px auto;
  width: 90%;
  box-sizing: border-box;
  overflow: hidden;
  // flex-direction: column;

  span {
    flex: 0 0;
    line-height: 18px;
  }
}

.shebei1 {
  height: calc(26.5% - 12px);
}

.shebei2 {
  height: calc(26.5% - 8px);
}
</style>
