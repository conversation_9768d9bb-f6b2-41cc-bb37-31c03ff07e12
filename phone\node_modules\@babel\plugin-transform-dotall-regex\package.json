{"_args": [["@babel/plugin-transform-dotall-regex@7.27.1", "F:\\桌面\\上海海洋大屏2期\\phone"]], "_development": true, "_from": "@babel/plugin-transform-dotall-regex@7.27.1", "_id": "@babel/plugin-transform-dotall-regex@7.27.1", "_inBundle": false, "_integrity": "sha512-gEbkDVGRvjj7+T1ivxrfgygpT7GUd4vmODtYpbs0gZATdkX8/iSnOtZSxiZnsgm1YjTgjI6VKBGSJJevkrclzw==", "_location": "/@babel/plugin-transform-dotall-regex", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@babel/plugin-transform-dotall-regex@7.27.1", "name": "@babel/plugin-transform-dotall-regex", "escapedName": "@babel%2fplugin-transform-dotall-regex", "scope": "@babel", "rawSpec": "7.27.1", "saveSpec": null, "fetchSpec": "7.27.1"}, "_requiredBy": ["/@babel/preset-env"], "_resolved": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.27.1.tgz", "_spec": "7.27.1", "_where": "F:\\桌面\\上海海洋大屏2期\\phone", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "description": "Compile regular expressions using the `s` (`dotAll`) flag to ES5.", "devDependencies": {"@babel/core": "^7.27.1", "@babel/helper-plugin-test-runner": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-dotall-regex", "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "dotall"], "license": "MIT", "main": "./lib/index.js", "name": "@babel/plugin-transform-dotall-regex", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-dotall-regex"}, "type": "commonjs", "version": "7.27.1"}