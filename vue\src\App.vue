<template>
  <div id="app" :class="appClass">
    <!-- 移动端显示 -->
    <template v-if="showPage == 1">
      <router-view name="mobile"></router-view>
    </template>

    <!-- 桌面端显示 -->
    <template v-else>
      <!-- 桌面端菜单布局 (当需要菜单时) -->
      <template v-if="$route.meta.withMenu">
      <div class="left-wrapper flex-label h-100 flex-vertical bg-deep">
<!--"Flex-header"指的是这个头部区域可以根据不同的设备尺寸和屏幕方向自动调整布局和大小，以便更好地适应各种设备-->
        <div class="logo-box flex-header pr-3 flex-item align-items-center" :class="{ 'fold': isCollapse }">
          <div class="logo flex-label d-flex justify-content-center">
            <svg t="1639791648958" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"
              p-id="20138" width="1.6rem" height="1.6rem">
              <path
                d="M844 912H112V112h68v452.34s80.753-52.55 334.716 0 329.284 0 329.284 0V112h68v800h-68zM523.671 516C288.7 447.63 213 516 213 516v-67s129.5-79.04 310.671 0C685.432 519.58 815 449 815 449v67s-101.384 55.27-291.329 0z m0-110C288.7 337.63 213 406 213 406v-67s129.5-79.04 310.671 0C685.432 409.58 815 339 815 339v67s-101.384 55.27-291.329 0z m0-111C288.7 226.63 213 295 213 295v-67s129.5-79.04 310.671 0C685.432 298.58 815 228 815 228v67s-101.384 55.27-291.329 0z"
                fill="#ffffff" p-id="20139"></path>
            </svg>
          </div>
          <div class="flex-content overflow-hidden text-nowrap">{{ base_name }}</div>
        </div>
        <el-menu :collapse="isCollapse" default-active="2" class="flex-content el-menu-vertical-demo h-100 border-0"
          background-color="#031E3E" text-color="#fff" router active-text-color="#fff">
          <!--router 是开启路由模式 直接用index进行跳转 default-active 默认激活的页面-->
          <el-menu-item @click="navigateToDetail()" index="/detail" >
            <i class="el-icon-menu"></i>
            <span slot="title">返回上级</span>
          </el-menu-item>
          <el-submenu index="2">
            <template slot="title">
              <i class="el-icon-location"></i>
              <span>数据中心</span>
            </template>
            <el-menu-item @click="handleMenuClick('/data/history')" index="/data/history">水质历史数据</el-menu-item>
            <el-menu-item  @click="handleMenuClick('/data/history2')" index="/data/history2">气象历史数据</el-menu-item>
            <el-menu-item @click="handleMenuClick('/data/report')" index="/data/report">具体设备数值表</el-menu-item>
            <!-- <el-menu-item index="2-3">算法分析</el-menu-item> -->
            <el-menu-item @click="handleMenuClick('/data/tousi')" index="/data/tousi">投饲计划</el-menu-item>
            
          </el-submenu>
          <el-submenu index="设备中心">
            <template slot="title">
              <i class="el-icon-document"></i>
              <span>设备中心</span>
            </template>
            <el-menu-item @click="handleMenuClick('/device/manage')" index="/device/manage">设备管理</el-menu-item>
            <el-menu-item @click="handleMenuClick('/device/camera')" index="/device/camera">摄像头管理</el-menu-item>
            <!-- <el-menu-item index="/device/template">设备模板</el-menu-item> -->
            <!-- <el-menu-item @click="handleMenuClick('/device/ledger')" index="/device/ledger">设备台账</el-menu-item> -->
            <!-- <el-menu-item index="/device/maintenance">设备维护</el-menu-item>
            <el-menu-item index="/device/operation">运行管理（芯片）</el-menu-item>
            <el-menu-item index="/device/repair">维修进度跟踪</el-menu-item> -->

          </el-submenu>

          <!-- <el-submenu index="基本数据">
            <template slot="title">
              <i class="el-icon-document"></i>
              <span>基本数据</span>
            </template>
            <el-menu-item @click="handleMenuClick('/data/people')" index="/data/people">人员管理</el-menu-item>
          </el-submenu> -->

          <!-- <el-submenu index="报表管理">
            <template slot="title">
              <i class="el-icon-document"></i>
              <span>报表管理</span>
            </template>
            <el-menu-item @click="handleMenuClick('/data/purchase_sale_report')" index="/data/purchase_sale_report">进销存报表</el-menu-item> -->
            <!-- <el-menu-item index="/data/work_report">用工数据报表</el-menu-item> -->
            <!-- <el-menu-item @click="handleMenuClick('/data/production_report')" index="/data/production_report">生产数据报表</el-menu-item>
            <el-menu-item @click="handleMenuClick('/data/sale_report')" index="/data/sale_report">销售数据报表</el-menu-item>
            <el-menu-item @click="handleMenuClick('/data/device_ledger_report')" index="/data/device_ledger_report">设备台账报表</el-menu-item>
            <el-menu-item @click="handleMenuClick('/works/pond_mouth_detection')" index="/works/pond_mouth_detection">塘口诊断卡</el-menu-item>
            <el-menu-item @click="handleMenuClick('/works/sample_record_card')" index="/works/sample_record_card">抽样记录卡</el-menu-item>
            <el-menu-item @click="handleMenuClick('/works/basic_data_fish_pond')" index="/works/basic_data_fish_pond">鱼塘基础数据</el-menu-item>
            <el-menu-item @click="handleMenuClick('/works/breeding_log')" index="/works/breeding_log">养殖日志</el-menu-item> -->
            <!-- <el-menu-item index="/data/device_operation_report">设备运行报表</el-menu-item>
            <el-menu-item index="/data/device_maintenance_report">设备维护报表</el-menu-item> -->
          <!-- </el-submenu> -->
<!-- 
          <el-submenu index="物料管理">
            <template slot="title">
              <i class="el-icon-document"></i>
              <span>物料管理</span>
            </template>
            <el-menu-item @click="handleMenuClick('/data/secondary_warehouse_management')" index="/data/secondary_warehouse_management">塘口二级仓管理</el-menu-item>
            <el-menu-item @click="handleMenuClick('/data/inventory_check_rec')" index="/data/inventory_check_rec">库存结余盘点</el-menu-item>
          </el-submenu> -->
          
          <!-- <el-submenu index="销售管理">
            <template slot="title">
              <i class="el-icon-document"></i>
              <span>销售管理</span>
            </template>
            <el-menu-item  @click="handleMenuClick('/data/sales_customer')" index="/data/sales_customer">客商信息管理</el-menu-item>
            <el-menu-item  @click="handleMenuClick('/data/sales_sales')" index="/data/sales_sales">销售订单管理</el-menu-item>
            <el-menu-item index="/data/sales_delivery">发货检验通知</el-menu-item>
            <el-menu-item  @click="handleMenuClick('/data/sales_records')" index="/data/sales_records">销售记录</el-menu-item>
            <el-menu-item index="/data/sales_return">销售退货</el-menu-item>
            <el-menu-item index="/data/sales_progress">销售进度跟踪</el-menu-item>
            <el-menu-item  @click="handleMenuClick('/works/fish_pond_sales_form')" index="/works/fish_pond_sales_form">塘口销售表</el-menu-item>
          </el-submenu> -->

          <el-menu-item index="/smartCultiver/homeview">
            <i class="el-icon-setting"></i>
            <span slot="title">智能养殖</span>
          </el-menu-item>
          <el-submenu index="用户管理">
            <template slot="title">
              <i class="el-icon-user-solid"></i>
              <span slot="title">用户管理</span>
            </template>
            <el-menu-item  @click="handleMenuClick('/user/add')" index="/user/add">新增用户</el-menu-item>
          </el-submenu>
        </el-menu>
        <div class="fold-box flex-footer py-1 text-center cursor-pointer" :class="{ 'fold': isCollapse }"
          @click="onToggleCollapse">
          <i v-if="isCollapse" class="el-icon-s-unfold text-plus"></i>
          <i v-else class="el-icon-s-fold text-plus"></i>
        </div>
      </div>

      <div class="flex-content flex-vertical">
        <Nav /><!--这是一个 Nav 组件，它被嵌套在上面的 div 中。-->
        <div class="flex-content">
<!--这是一个用于渲染路由组件的占位符。它表示 Vue.js 应该在这里呈现与当前路由匹配的组件。-->
          <router-view name="desktop" />
        </div>
      </div>
      </template>

      <!-- 桌面端无菜单布局 (登录页面等) -->
      <template v-else>
        <router-view name="desktop" />
      </template>
    </template>

  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  data() {
    return {
      isCollapse: false,
      showPage: 2 // 默认显示桌面端，1=移动端，2=桌面端
    }
  },
  computed: {
    ...mapGetters('device', ['deviceInfo', 'isMobile', 'isDesktop']),
    appClass() {
      return {
        'w-100 h-100 flex-item': this.showPage == 2,
        'mobile-app': this.showPage == 1
      }
    }
  },
  created() {
    // 根据设备类型设置显示页面
    if (this._isMobile()) {
      this.showPage = 1
    } else {
      this.showPage = 2
    }
    console.log('设备类型:', this.showPage == 1 ? '移动端' : '桌面端')
  },
  methods: {
    // 设备检测方法
    _isMobile() {
      const flag = navigator.userAgent.match(
        /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i
      )
      return flag
    },
    handleMenuClick(path) {
    this.$router.push({
      path: path, // 目标路由路径
      query: {
        base_id: this.base_id, // 传递 base_id 参数
        base_name: this.base_name // 传递 base_name 参数
      }
    });
  },
    onToggleCollapse() {
      this.isCollapse = !this.isCollapse;
    },
    navigateToDetail() {
      const base_name = this.$route.query.base_name;
      const base_id = this.$route.query.base_id;
      console.log(this.base_name);
    this.$router.push({
      path: '/detail',
      query: {
        base_id: this.base_id,
        base_name: this.base_name,
      },
    });
    console.log(this.base_name);
  },
  },
  watch: {
    // 监听路由变化，确保在路由变化时能够更新 base_id 和 base_name
    '$route.query': {
      handler(newQuery) {
        this.base_id = newQuery.base_id;
        this.base_name = newQuery.base_name;
      },
      immediate: true // 确保首次加载时就能获取到参数
    }
  }
}
</script>


<style lang="less" scoped>
.left-wrapper {
  border-right: 1px solid rgba(255, 255, 255, 0.1);

  .logo-box {
    width: 280px;
    height: 60px;
    transition: all linear 200ms;

    &.fold {
      width: 64px;
    }

    .logo {
      width: 64px;
    }
  }

  /deep/ .el-menu {//"/deep/" 是一个 CSS 伪类选择器，用于匹配所有深度嵌套的元素
    i {
      color: #fff !important;
    }

    .el-menu-item,
    .el-submenu__title {
      height: 3.4rem;
      line-height: 3.4rem;
      font-size: 0.875rem;
    }

    .el-menu-item {
      &.is-active {
        background-color: #42434559 !important;
      }
    }

    &.el-menu-vertical-demo:not(.el-menu--collapse) {
      width: 280px;
      min-height: 400px;
    }
  }
}

.fold-box {
  background: rgba(255, 255, 255, 0.1);
}

/* 移动端样式 */
.mobile-app {
  width: 100%;
  height: 100vh;
  overflow-x: hidden;
  background-color: #f5f5f5;
}

/* 响应式适配 */
@media (max-width: 768px) {
  .left-wrapper {
    display: none !important;
  }

  #app {
    flex-direction: column !important;
  }
}
</style>
