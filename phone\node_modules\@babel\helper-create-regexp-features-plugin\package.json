{"_args": [["@babel/helper-create-regexp-features-plugin@7.27.1", "F:\\桌面\\上海海洋大屏2期\\phone"]], "_development": true, "_from": "@babel/helper-create-regexp-features-plugin@7.27.1", "_id": "@babel/helper-create-regexp-features-plugin@7.27.1", "_inBundle": false, "_integrity": "sha512-uVDC72XVf8UbrH5qQTc18Agb8emwjTiZrQE11Nv3CuBEZmVvTwwE9CBUEvHku06gQCAyYf8Nv6ja1IN+6LMbxQ==", "_location": "/@babel/helper-create-regexp-features-plugin", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@babel/helper-create-regexp-features-plugin@7.27.1", "name": "@babel/helper-create-regexp-features-plugin", "escapedName": "@babel%2fhelper-create-regexp-features-plugin", "scope": "@babel", "rawSpec": "7.27.1", "saveSpec": null, "fetchSpec": "7.27.1"}, "_requiredBy": ["/@babel/plugin-syntax-unicode-sets-regex", "/@babel/plugin-transform-dotall-regex", "/@babel/plugin-transform-duplicate-named-capturing-groups-regex", "/@babel/plugin-transform-named-capturing-groups-regex", "/@babel/plugin-transform-regexp-modifiers", "/@babel/plugin-transform-unicode-property-regex", "/@babel/plugin-transform-unicode-regex", "/@babel/plugin-transform-unicode-sets-regex"], "_resolved": "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.27.1.tgz", "_spec": "7.27.1", "_where": "F:\\桌面\\上海海洋大屏2期\\phone", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "dependencies": {"@babel/helper-annotate-as-pure": "^7.27.1", "regexpu-core": "^6.2.0", "semver": "^6.3.1"}, "description": "Compile ESNext Regular Expressions to ES5", "devDependencies": {"@babel/core": "^7.27.1", "@babel/helper-plugin-test-runner": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://github.com/babel/babel#readme", "keywords": ["babel", "babel-plugin"], "license": "MIT", "main": "./lib/index.js", "name": "@babel/helper-create-regexp-features-plugin", "peerDependencies": {"@babel/core": "^7.0.0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-helper-create-regexp-features-plugin"}, "type": "commonjs", "version": "7.27.1"}