<template>
  <div class="login-page w-100 h-100 position-relative">
    <div class="bg-box w-100 h-100 position-absolute d-flex">
      <div class="left position-relative"></div>
      <div class="right"></div>
    </div>
    <div class="login-box position-absolute flex-item">
      <div class="flex-label"></div>
      <div class="flex-content d-flex flex-column justify-content-center">
        <h2 class="mb-4 color-black text-max">Welcome</h2>
        <el-form ref="form"
                 :model="form"
                 :rules="rules">
          <el-form-item prop="account">
            <el-input v-model="form.username"
                      placeholder="请输入账号">
              <svg t="*************"
                   slot="prefix"
                   viewBox="0 0 1024 1024"
                   version="1.1"
                   xmlns="http://www.w3.org/2000/svg"
                   p-id="2537"
                   width="1.3rem"
                   height="1.3rem">
                <path d="M954.181818 953.746618 512 953.746618 69.818182 953.746618 69.818182 932.126255C78.405818 911.646255 95.604364 892.422982 121.018182 874.945164 146.455273 857.444073 179.618909 841.897891 219.601455 828.772073 286.580364 806.802618 354.141091 798.075345 373.224727 795.934255 395.008 793.490618 414.510545 781.761164 426.612364 763.934255L434.385455 752.297891 437.341091 739.474618C445.789091 702.680436 425.239273 666.002618 412.229818 647.430982 408.040727 641.449891 402.990545 636.120436 397.288727 631.605527 364.776727 605.958982 336.546909 568.280436 315.624727 522.665891 304.872727 499.206982 283.205818 483.125527 258.490182 479.797527 254.557091 478.470982 241.245091 466.718255 235.403636 442.468073 228.794182 415.076073 235.938909 397.412073 238.545455 394.8288 261.213091 381.074618 275.013818 356.056436 274.548364 329.502255 273.128727 245.976436 293.050182 181.301527 333.730909 137.246255 385.186909 81.508073 459.077818 69.825164 512 69.825164 564.922182 69.825164 638.813091 81.508073 690.292364 137.246255 730.973091 181.301527 750.871273 245.976436 749.451636 329.502255 748.986182 356.056436 762.786909 381.074618 784.849455 394.409891 788.061091 397.412073 795.205818 415.076073 788.596364 442.491345 782.754909 466.718255 769.442909 478.470982 766.301091 479.634618 740.770909 483.125527 719.127273 499.206982 708.398545 522.642618 687.453091 568.280436 659.223273 605.958982 626.757818 631.582255 621.009455 636.120436 615.959273 641.449891 611.793455 647.430982 598.760727 666.002618 578.210909 702.680436 586.705455 739.614255L589.986909 753.019345 597.294545 763.771345C609.489455 781.761164 628.992 793.490618 650.752 795.934255 669.858909 798.075345 737.419636 806.802618 804.398545 828.772073 844.381091 841.897891 877.544727 857.444073 902.981818 874.945164 928.628364 892.585891 945.92 911.995345 954.181818 931.986618L954.181818 953.746618ZM1021.975273 913.973527 1021.882182 913.7408C1009.058909 877.225891 982.365091 844.830255 942.568727 817.414982 911.639273 796.143709 872.471273 777.641891 826.181818 762.4448 754.269091 738.846255 683.589818 729.374255 658.571636 726.558255 657.175273 726.395345 655.848727 725.720436 655.057455 724.580073 654.894545 724.324073 654.778182 724.068073 654.708364 723.835345 652.962909 716.225164 660.596364 699.4688 668.974545 687.483345 669.253818 687.064436 669.602909 686.715345 669.998545 686.4128 711.214545 653.854255 746.286545 607.448436 771.863273 551.7568 772.584727 550.174255 774.050909 549.033891 775.773091 548.801164 811.613091 543.890618 844.730182 507.561891 856.482909 458.8288 869.352727 405.441164 854.760727 355.241891 821.666909 335.157527 820.130909 334.203345 819.223273 332.504436 819.246545 330.689164 820.992 228.591709 794.88 147.579345 741.585455 89.886255 672.977455 15.599709 578.862545 0.006982 512 0.006982 445.137455 0.006982 351.022545 15.599709 282.414545 89.886255 229.12 147.579345 203.008 228.591709 204.753455 330.689164 204.776727 332.504436 203.869091 334.203345 202.333091 335.157527 169.239273 355.241891 154.647273 405.441164 167.517091 458.8288 179.269818 507.561891 212.386909 543.890618 248.250182 548.801164 249.949091 549.033891 251.415273 550.174255 252.136727 551.7568 277.713455 607.448436 312.785455 653.854255 354.001455 686.4128 354.397091 686.715345 354.746182 687.064436 355.025455 687.483345 363.403636 699.4688 371.037091 716.225164 369.291636 723.835345 369.221818 724.068073 369.105455 724.324073 368.942545 724.580073 368.151273 725.720436 366.824727 726.395345 365.451636 726.558255 340.410182 729.374255 269.730909 738.846255 197.818182 762.4448 151.528727 777.641891 112.360727 796.143709 81.454545 817.414982 41.634909 844.830255 14.941091 877.225891 2.117818 913.7408L2.024727 913.973527C0.674909 918.022982 0 922.142255 0 926.261527L0 980.370618C0 1004.225164 19.339636 1023.5648 43.194182 1023.5648L512 1023.5648 980.805818 1023.5648C1004.660364 1023.5648 1024 1004.225164 1024 980.370618L1024 926.261527C1024 922.142255 1023.325091 918.022982 1021.975273 913.973527L1021.975273 913.973527Z"
                      p-id="2538"
                      fill="#cdcdcd"></path>
              </svg>
            </el-input>
          </el-form-item>
          <el-form-item prop="password">
            <el-input v-model="form.password"
                      placeholder="请输入密码"
                      type="password"
                      @keydown.native.enter="comlogin()">
              <svg t="1634354595512"
                   slot="prefix"
                   viewBox="0 0 1024 1024"
                   version="1.1"
                   xmlns="http://www.w3.org/2000/svg"
                   p-id="3461"
                   width="1.5rem"
                   height="1.5rem">
                <path d="M511.970836 499.271094c-67.470671 0-124.724653 53.072753-130.051985 119.469975-0.358157 1.096984-0.775666 2.648316-0.775666 4.466732l0 31.441083c0 7.708566 6.224772 13.96199 13.876032 13.96199l31.203676 0c7.65126 0 13.931291-6.252401 13.931291-13.96199l-0.116657-9.052167c-0.535189-2.594081-0.35918-5.598508-0.11768-8.724709 0.11768-1.935072 0.234337-3.840468 0.234337-5.808286 0-39.924291 32.185027-72.467475 71.816652-72.467475 39.627533 0 71.870888 32.544207 71.870888 72.467475 0 31.771611-20.661575 59.697637-50.166563 69.136613l0-45.552475c0-7.68503-6.251378-13.96506-13.902638-13.96506l-31.174 0c-7.65433 0-13.906731 6.281053-13.906731 13.96506l0 94.259804c0 7.68503 6.252401 13.96506 13.906731 13.96506l31.174 0c1.279133 0 2.52859-0.208754 3.839445-0.596588 66.899666-5.985318 119.213126-63.475683 119.241778-131.214461C642.853745 558.38647 584.141552 499.271094 511.970836 499.271094"
                      p-id="3462"
                      fill="#cdcdcd"></path>
                <path d="M806.553061 336.037383l-76.131935-0.090051 0-55.245232c0-104.908328-83.48439-190.268438-186.147584-190.268438L508.27977 90.433662c-101.113909 0-184.302563 83.84357-185.969529 187.18624l-0.177032 1.533936c0 1.057075 0.118704 1.696642 0 1.548262l0.357134 6.104021 0.952698 0c3.393283 12.400425 14.736633 21.497617 27.868721 21.497617 13.219069 0 24.473391-9.097192 27.836999-21.497617l0.983397 0 0.23843-5.819542c3.333932-74.213235 59.962673-132.331911 128.97956-132.331911l33.853016 0c71.311139 0 129.308041 62.390979 129.308041 139.030473l0 48.352241L375.188566 336.037383l0.089028-0.090051-157.800979 0.090051c-48.680722 0-88.307232 40.46255-88.307232 90.124624l0 417.338037c0 49.665143 39.627533 90.066295 88.307232 90.066295L806.553061 933.566338c48.708352 0 88.277556-40.400129 88.277556-90.066295L894.830617 426.162006C894.829594 376.499933 855.260389 336.037383 806.553061 336.037383M839.364351 430.584736l0 408.480298c0 21.019733-16.645099 38.081317-37.128619 38.081317L221.764268 877.146351c-20.484543 0-37.157271-17.061584-37.157271-38.081317L184.606996 430.584736c0-20.976754 16.672728-38.081317 37.157271-38.081317l580.471464 0C822.719252 392.503419 839.364351 409.609005 839.364351 430.584736"
                      p-id="3463"
                      fill="#cdcdcd"></path>
              </svg>
            </el-input>
          </el-form-item>
          <el-form-item class="form-button">
            <el-button round
                       @click="comlogin()">LOGIN</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script>
import { login } from '@/request/api';
import { Toast } from 'vant';

export default {
  name: 'Login',
  data () {
    return {
      form: {
        username: '',
        password: ''
      },
      rules: {
        username: [{ required: true, message: '账号不能为空', blur: true }],
        password: [{ required: true, message: '密码不能为空', blur: true }]
      }
    }
  },
  methods: {
    comlogin() {
      this.$refs.form.validate((valid) => {
        if (!valid) {
          return;
        }

        login({
          username: this.form.username,
          password: this.form.password
        }).then(res => {
          if (res.code == 200) {
            this.$store.commit('set_token', res.data.token);
            localStorage.setItem("token", res.data.token);
            localStorage.setItem("userInfo", JSON.stringify({
              username: this.form.username
            }));
            this.$router.push('/home');
          } else {
            Toast({
              message: res.message || '账号或密码错误',
              position: 'middle',
              duration: 2000,
              className: 'custom-toast'
            });
          }
        }).catch(error => {
          Toast({
            message: '网络错误，请稍后重试',
            position: 'middle',
            duration: 2000,
            className: 'custom-toast'
          });
          console.error('登录失败:', error);
        });
      });
    }
  }
}
</script>

<style lang="less" scoped>
@import "~@/assets/style/variable.less";

.login-page {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #001528;
  overflow: auto;
  display: flex;
  justify-content: center;
  align-items: center;

  .bg-box {
    .left {
      width: 40%;
      background: linear-gradient(
        to bottom,
        @colorLighter 0%,
        @colorLighter 60%,
        @colorLight 60%,
        @colorLight 100%
      );
      
      
    }
    .right {
      width: 60%;
      background: linear-gradient(
        to bottom,
        @colorLight 0%,
        @colorLight 40%,
        @colorLighter 40%,
        @colorLighter 100%
      );
    }
  }

  .login-box {
    position: relative;
    width: 90%;
    max-width: 400px;
    margin: 10% auto;
    border-radius: 1rem;
    overflow: hidden;
    background: #fdfdfd;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
    z-index: 10;
    
    .flex-label {
      display: none; /* 在手机版隐藏装饰图片 */
    }
    
    .flex-content {
      padding: 2rem 1.5rem;
      
      h2 {
        letter-spacing: 0.1rem;
        text-align: center;
        font-size: 1.5rem;
        margin-bottom: 2rem;
      }
      
      /deep/ .el-form {
        .el-form-item {
          margin-bottom: 1.5rem;
          
          input {
            border: none;
            height: 3rem;
            line-height: 3rem;
            border-radius: 3rem;
            padding-left: 3rem;
            box-shadow: 0 0 0.6rem 0 rgba(165, 148, 212, 0.1);
            letter-spacing: 1px;
            font-size: 1rem;
            
            &:focus {
              box-shadow: @colorHigh 0px 0px 0px 1px,
                rgba(69, 96, 217, 0.6) 0px 0px 0px 4px;
            }
            
            &::placeholder {
              color: #ededed;
            }
          }
          
          .el-input__prefix {
            width: 2rem;
            display: flex;
            justify-content: center;
            align-items: center;
          }
          
          .el-form-item__error {
            padding-top: 0.3rem;
            font-size: 0.8rem;
          }

          &.form-button {
            margin-top: 2rem;
            text-align: center;
            
            .el-button {
              width: 100%;
              height: 3rem;
              border-radius: 3rem;
              background-color: @colorDanger;
              color: #fff;
              border: none;
              letter-spacing: 0.1rem;
              box-shadow: 0 0.1rem 0 0 #df5a3c;
              font-size: 1.2rem;
              
              &:hover {
                background-color: #f57a5e;
                transform: scale(1.03);
              }
            }
          }
        }
      }
    }
  }
}

@media screen and (min-width: 768px) {
  .login-page {
    .login-box {
      width: 80%;
      max-width: 500px;
      
      .flex-content {
        padding: 3rem 2rem;
        
        h2 {
          font-size: 1.8rem;
        }
      }
    }
  }
}

:deep(.custom-toast) {
  background-color: rgba(0, 0, 0, 0.8);
  color: #fff;
  font-size: 14px;
  padding: 10px 20px;
  border-radius: 4px;
  max-width: 80%;
  text-align: center;
  word-break: break-all;
  
  @supports (padding-bottom: constant(safe-area-inset-bottom)) or (padding-bottom: env(safe-area-inset-bottom)) {
    padding-bottom: calc(10px + constant(safe-area-inset-bottom));
    padding-bottom: calc(10px + env(safe-area-inset-bottom));
  }
}

:deep(.van-toast) {
  z-index: 2000;
}
</style>
