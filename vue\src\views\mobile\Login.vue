<template>
  <div class="login-page">
    <div class="login-container">
      <div class="login-header">
        <h1 class="login-title">Welcome</h1>
        <p class="login-subtitle">请登录您的账户</p>
      </div>

      <div class="login-form">
        <van-form @submit="comlogin">
          <van-field
            v-model="form.username"
            name="username"
            label="账号"
            placeholder="请输入账号"
            :rules="[{ required: true, message: '请输入账号' }]"
            left-icon="user-o"
          />
          <van-field
            v-model="form.password"
            type="password"
            name="password"
            label="密码"
            placeholder="请输入密码"
            :rules="[{ required: true, message: '请输入密码' }]"
            left-icon="lock"
          />
          <div class="login-button">
            <van-button
              round
              block
              type="primary"
              native-type="submit"
              :loading="loading"
              loading-text="登录中..."
            >
              登录
            </van-button>
          </div>
        </van-form>
      </div>
    </div>
  </div>
</template>

<script>
import { login } from '@/request/api';
import { Toast } from 'vant';

export default {
  name: 'MobileLogin',
  data () {
    return {
      form: {
        username: '',
        password: ''
      },
      loading: false
    }
  },
  methods: {
    comlogin() {
      this.$refs.form.validate((valid) => {
        if (!valid) {
          return;
        }

        login({
          username: this.form.username,
          password: this.form.password
        }).then(res => {
          if (res.code == 200) {
            this.$store.commit('set_token', res.data.token);
            localStorage.setItem("token", res.data.token);
            localStorage.setItem("userInfo", JSON.stringify({
              username: this.form.username
            }));
            this.$router.push('/home');
          } else {
            Toast({
              message: res.message || '账号或密码错误',
              position: 'middle',
              duration: 2000,
              className: 'custom-toast'
            });
          }
        }).catch(error => {
          Toast({
            message: '网络错误，请稍后重试',
            position: 'middle',
            duration: 2000,
            className: 'custom-toast'
          });
          console.error('登录失败:', error);
        });
      });
    }
  }
}
</script>

<style lang="less" scoped>
@import "~@/assets/style/variable.less";

.login-page {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #001528;
  overflow: auto;
  display: flex;
  justify-content: center;
  align-items: center;

  .bg-box {
    .left {
      width: 40%;
      background: linear-gradient(
        to bottom,
        @colorLighter 0%,
        @colorLighter 60%,
        @colorLight 60%,
        @colorLight 100%
      );
      
      
    }
    .right {
      width: 60%;
      background: linear-gradient(
        to bottom,
        @colorLight 0%,
        @colorLight 40%,
        @colorLighter 40%,
        @colorLighter 100%
      );
    }
  }

  .login-box {
    position: relative;
    width: 90%;
    max-width: 400px;
    margin: 10% auto;
    border-radius: 1rem;
    overflow: hidden;
    background: #fdfdfd;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
    z-index: 10;
    
    .flex-label {
      display: none; /* 在手机版隐藏装饰图片 */
    }
    
    .flex-content {
      padding: 2rem 1.5rem;
      
      h2 {
        letter-spacing: 0.1rem;
        text-align: center;
        font-size: 1.5rem;
        margin-bottom: 2rem;
      }
      
      /deep/ .el-form {
        .el-form-item {
          margin-bottom: 1.5rem;
          
          input {
            border: none;
            height: 3rem;
            line-height: 3rem;
            border-radius: 3rem;
            padding-left: 3rem;
            box-shadow: 0 0 0.6rem 0 rgba(165, 148, 212, 0.1);
            letter-spacing: 1px;
            font-size: 1rem;
            
            &:focus {
              box-shadow: @colorHigh 0px 0px 0px 1px,
                rgba(69, 96, 217, 0.6) 0px 0px 0px 4px;
            }
            
            &::placeholder {
              color: #ededed;
            }
          }
          
          .el-input__prefix {
            width: 2rem;
            display: flex;
            justify-content: center;
            align-items: center;
          }
          
          .el-form-item__error {
            padding-top: 0.3rem;
            font-size: 0.8rem;
          }

          &.form-button {
            margin-top: 2rem;
            text-align: center;
            
            .el-button {
              width: 100%;
              height: 3rem;
              border-radius: 3rem;
              background-color: @colorDanger;
              color: #fff;
              border: none;
              letter-spacing: 0.1rem;
              box-shadow: 0 0.1rem 0 0 #df5a3c;
              font-size: 1.2rem;
              
              &:hover {
                background-color: #f57a5e;
                transform: scale(1.03);
              }
            }
          }
        }
      }
    }
  }
}

@media screen and (min-width: 768px) {
  .login-page {
    .login-box {
      width: 80%;
      max-width: 500px;
      
      .flex-content {
        padding: 3rem 2rem;
        
        h2 {
          font-size: 1.8rem;
        }
      }
    }
  }
}

:deep(.custom-toast) {
  background-color: rgba(0, 0, 0, 0.8);
  color: #fff;
  font-size: 14px;
  padding: 10px 20px;
  border-radius: 4px;
  max-width: 80%;
  text-align: center;
  word-break: break-all;
  
  @supports (padding-bottom: constant(safe-area-inset-bottom)) or (padding-bottom: env(safe-area-inset-bottom)) {
    padding-bottom: calc(10px + constant(safe-area-inset-bottom));
    padding-bottom: calc(10px + env(safe-area-inset-bottom));
  }
}

:deep(.van-toast) {
  z-index: 2000;
}
</style>
