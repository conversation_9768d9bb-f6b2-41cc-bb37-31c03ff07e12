{"van-area/value": {"type": "string", "description": "当前选中的省市区`code`, 默认值: -"}, "van-area/title": {"type": "string", "description": "顶部栏标题, 默认值: -"}, "van-area/confirm-button-text": {"type": "string", "description": "确认按钮文字, 默认值: `确认`"}, "van-area/cancel-button-text": {"type": "string", "description": "取消按钮文字, 默认值: `取消`"}, "van-area/area-list": {"type": "object", "description": "省市区数据，格式见下方, 默认值: -"}, "van-area/columns-placeholder": {"type": "string[]", "description": "列占位提示文字, 默认值: `[]`"}, "van-area/loading": {"type": "boolean", "description": "是否显示加载状态, 默认值: `false`"}, "van-area/readonly": {"type": "boolean", "description": "是否为只读状态，只读状态下无法切换选项, 默认值: `false`"}, "van-area/item-height": {"type": "number | string", "description": "选项高度，支持 `px` `vw` `vh` `rem` 单位，默认 `px`, 默认值: `44`"}, "van-area/columns-num": {"type": "number | string", "description": "显示列数，3-省市区，2-省市，1-省, 默认值: `3`"}, "van-area/visible-item-count": {"type": "number | string", "description": "可见的选项个数, 默认值: `6`"}, "van-area/swipe-duration": {"type": "number | string", "description": "快速滑动时惯性滚动的时长，单位`ms`, 默认值: `1000`"}, "van-area/is-oversea-code": {"type": "() => boolean", "description": "根据`code`校验海外地址，海外地址会划分至单独的分类, 默认值: -"}, "van-badge/content": {"type": "number | string", "description": "徽标内容, 默认值: -"}, "van-badge/color": {"type": "string", "description": "徽标背景颜色, 默认值: `#ee0a24`"}, "van-badge/dot": {"type": "boolean", "description": "是否展示为小红点, 默认值: `false`"}, "van-badge/max": {"type": "number | string", "description": "最大值，超过最大值会显示 `{max}+`，仅当 content 为数字时有效, 默认值: -"}, "van-button/type": {"type": "string", "description": "类型，可选值为 `primary` `info` `warning` `danger`, 默认值: `default`"}, "van-button/size": {"type": "string", "description": "尺寸，可选值为 `large` `small` `mini`, 默认值: `normal`"}, "van-button/text": {"type": "string", "description": "按钮文字, 默认值: -"}, "van-button/color": {"type": "string", "description": "按钮颜色，支持传入 `linear-gradient` 渐变色, 默认值: -"}, "van-button/icon": {"type": "string", "description": "左侧[图标名称](#/zh-CN/icon)或图片链接, 默认值: -"}, "van-button/icon-prefix": {"type": "string", "description": "图标类名前缀，同 Icon 组件的 [class-prefix 属性](#/zh-CN/icon#props), 默认值: `van-icon`"}, "van-button/icon-position": {"type": "string", "description": "图标展示位置，可选值为 `right`, 默认值: `left`"}, "van-button/tag": {"type": "string", "description": "按钮根节点的 HTML 标签, 默认值: `button`"}, "van-button/native-type": {"type": "string", "description": "原生 button 标签的 type 属性, 默认值: -"}, "van-button/block": {"type": "boolean", "description": "是否为块级元素, 默认值: `false`"}, "van-button/plain": {"type": "boolean", "description": "是否为朴素按钮, 默认值: `false`"}, "van-button/square": {"type": "boolean", "description": "是否为方形按钮, 默认值: `false`"}, "van-button/round": {"type": "boolean", "description": "是否为圆形按钮, 默认值: `false`"}, "van-button/disabled": {"type": "boolean", "description": "是否禁用按钮, 默认值: `false`"}, "van-button/hairline": {"type": "boolean", "description": "是否使用 0.5px 边框, 默认值: `false`"}, "van-button/loading": {"type": "boolean", "description": "是否显示为加载状态, 默认值: `false`"}, "van-button/loading-text": {"type": "string", "description": "加载状态提示文字, 默认值: -"}, "van-button/loading-type": {"type": "string", "description": "[加载图标类型](#/zh-CN/loading)，可选值为 `spinner`, 默认值: `circular`"}, "van-button/loading-size": {"type": "string", "description": "加载图标大小, 默认值: `20px`"}, "van-button/url": {"type": "string", "description": "点击后跳转的链接地址, 默认值: -"}, "van-button/to": {"type": "string | object", "description": "点击后跳转的目标路由对象，同 vue-router 的 [to 属性](https://router.vuejs.org/zh/api/#to), 默认值: -"}, "van-button/replace": {"type": "boolean", "description": "是否在跳转时替换当前页面历史, 默认值: `false`"}, "van-address-edit/area-list": {"type": "object", "description": "地区列表, 默认值: -"}, "van-address-edit/area-columns-placeholder": {"type": "string[]", "description": "地区选择列占位提示文字, 默认值: `[]`"}, "van-address-edit/area-placeholder": {"type": "string", "description": "地区输入框占位提示文字, 默认值: `选择省 / 市 / 区`"}, "van-address-edit/address-info": {"type": "AddressInfo", "description": "收货人信息初始值, 默认值: `{}`"}, "van-address-edit/search-result": {"type": "SearchResult[]", "description": "详细地址搜索结果, 默认值: `[]`"}, "van-address-edit/show-postal": {"type": "boolean", "description": "是否显示邮政编码, 默认值: `false`"}, "van-address-edit/show-delete": {"type": "boolean", "description": "是否显示删除按钮, 默认值: `false`"}, "van-address-edit/show-set-default": {"type": "boolean", "description": "是否显示默认地址栏, 默认值: `false`"}, "van-address-edit/show-search-result": {"type": "boolean", "description": "是否显示搜索结果, 默认值: `false`"}, "van-address-edit/show-area": {"type": "boolean", "description": "是否显示地区, 默认值: `true`"}, "van-address-edit/show-detail": {"type": "boolean", "description": "是否显示详细地址, 默认值: `true`"}, "van-address-edit/disable-area": {"type": "boolean", "description": "是否禁用地区选择, 默认值: `false`"}, "van-address-edit/save-button-text": {"type": "string", "description": "保存按钮文字, 默认值: `保存`"}, "van-address-edit/delete-button-text": {"type": "string", "description": "删除按钮文字, 默认值: `删除`"}, "van-address-edit/detail-rows": {"type": "number | string", "description": "详细地址输入框行数, 默认值: `1`"}, "van-address-edit/detail-maxlength": {"type": "number | string", "description": "详细地址最大长度, 默认值: `200`"}, "van-address-edit/is-saving": {"type": "boolean", "description": "是否显示保存按钮加载动画, 默认值: `false`"}, "van-address-edit/is-deleting": {"type": "boolean", "description": "是否显示删除按钮加载动画, 默认值: `false`"}, "van-address-edit/tel-validator": {"type": "string => boolean", "description": "手机号格式校验函数, 默认值: -"}, "van-address-edit/tel-maxlength": {"type": "number | string", "description": "手机号最大长度, 默认值: -"}, "van-address-edit/postal-validator": {"type": "string => boolean", "description": "邮政编码格式校验函数, 默认值: -"}, "van-address-edit/validator": {"type": "(key, val) => string", "description": "自定义校验函数, 默认值: -"}, "van-calendar/type": {"type": "string", "description": "选择类型:<br>`single`表示选择单个日期，<br>`multiple`表示选择多个日期，<br>`range`表示选择日期区间, 默认值: `single`"}, "van-calendar/title": {"type": "string", "description": "日历标题, 默认值: `日期选择`"}, "van-calendar/color": {"type": "string", "description": "主题色，对底部按钮和选中日期生效, 默认值: `#ee0a24`"}, "van-calendar/min-date": {"type": "Date", "description": "可选择的最小日期, 默认值: 当前日期"}, "van-calendar/max-date": {"type": "Date", "description": "可选择的最大日期, 默认值: 当前日期的六个月后"}, "van-calendar/default-date": {"type": "Date | Date[] \\", "description": "默认选中的日期，`type` 为 `multiple` 或 `range` 时为数组，传入 `null` 表示默认不选择, 默认值: null_"}, "van-calendar/row-height": {"type": "number | string", "description": "日期行高, 默认值: `64`"}, "van-calendar/formatter": {"type": "(day: Day) => Day", "description": "日期格式化函数, 默认值: -"}, "van-calendar/poppable": {"type": "boolean", "description": "是否以弹层的形式展示日历, 默认值: `true`"}, "van-calendar/lazy-render": {"type": "boolean", "description": "是否只渲染可视区域的内容, 默认值: `true`"}, "van-calendar/show-mark": {"type": "boolean", "description": "是否显示月份背景水印, 默认值: `true`"}, "van-calendar/show-title": {"type": "boolean", "description": "是否展示日历标题, 默认值: `true`"}, "van-calendar/show-subtitle": {"type": "boolean", "description": "是否展示日历副标题（年月）, 默认值: `true`"}, "van-calendar/show-confirm": {"type": "boolean", "description": "是否展示确认按钮, 默认值: `true`"}, "van-calendar/readonly": {"type": "boolean", "description": "是否为只读状态，只读状态下不能选择日期, 默认值: `false`"}, "van-calendar/confirm-text": {"type": "string", "description": "确认按钮的文字, 默认值: `确定`"}, "van-calendar/confirm-disabled-text": {"type": "string", "description": "确认按钮处于禁用状态时的文字, 默认值: `确定`"}, "van-calendar/first-day-of-week": {"type": "0-6", "description": "设置周起始日, 默认值: `0`"}, "van-poppable/v-model": {"type": "boolean", "description": "是否显示日历弹窗, 默认值: `false`"}, "van-poppable/position": {"type": "string", "description": "弹出位置，可选值为 `top` `right` `left`, 默认值: `bottom`"}, "van-poppable/round": {"type": "boolean", "description": "是否显示圆角弹窗, 默认值: `true`"}, "van-poppable/close-on-popstate": {"type": "boolean", "description": "是否在页面回退时自动关闭, 默认值: `true`"}, "van-poppable/close-on-click-overlay": {"type": "boolean", "description": "是否在点击遮罩层后关闭, 默认值: `true`"}, "van-poppable/safe-area-inset-bottom": {"type": "boolean", "description": "是否开启[底部安全区适配](#/zh-CN/advanced-usage#di-bu-an-quan-qu-gua-pei), 默认值: `true`"}, "van-poppable/get-container": {"type": "string | () => Element", "description": "指定挂载的节点，[用法示例](#/zh-CN/popup#zhi-ding-gua-zai-wei-zhi), 默认值: -"}, "van-range/max-range": {"type": "number | string", "description": "日期区间最多可选天数, 默认值: 无限制"}, "van-range/range-prompt": {"type": "string", "description": "范围选择超过最多可选天数时的提示文案, 默认值: `选择天数不能超过 xx 天`"}, "van-range/allow-same-day": {"type": "boolean", "description": "是否允许日期范围的起止时间为同一天, 默认值: `false`"}, "van-multiple/max-range": {"type": "number | string", "description": "日期最多可选天数, 默认值: 无限制"}, "van-multiple/range-prompt": {"type": "string", "description": "选择超过最多可选天数时的提示文案, 默认值: `选择天数不能超过 xx 天`"}, "van-action-sheet/v-model (value)": {"type": "boolean", "description": "是否显示动作面板, 默认值: `false`"}, "van-action-sheet/actions": {"type": "Action[]", "description": "面板选项列表, 默认值: `[]`"}, "van-action-sheet/title": {"type": "string", "description": "顶部标题, 默认值: -"}, "van-action-sheet/cancel-text": {"type": "string", "description": "取消按钮文字, 默认值: -"}, "van-action-sheet/description": {"type": "string", "description": "选项上方的描述信息, 默认值: -"}, "van-action-sheet/closeable": {"type": "boolean", "description": "是否显示关闭图标, 默认值: `true`"}, "van-action-sheet/close-icon": {"type": "string", "description": "关闭[图标名称](#/zh-CN/icon)或图片链接, 默认值: `cross`"}, "van-action-sheet/duration": {"type": "number | string", "description": "动画时长，单位秒, 默认值: `0.3`"}, "van-action-sheet/round": {"type": "boolean", "description": "是否显示圆角, 默认值: `true`"}, "van-action-sheet/overlay": {"type": "boolean", "description": "是否显示遮罩层, 默认值: `true`"}, "van-action-sheet/lock-scroll": {"type": "boolean", "description": "是否锁定背景滚动, 默认值: `true`"}, "van-action-sheet/lazy-render": {"type": "boolean", "description": "是否在显示弹层时才渲染节点, 默认值: `true`"}, "van-action-sheet/close-on-popstate": {"type": "boolean", "description": "是否在页面回退时自动关闭, 默认值: `false`"}, "van-action-sheet/close-on-click-action": {"type": "boolean", "description": "是否在点击选项后关闭, 默认值: `false`"}, "van-action-sheet/close-on-click-overlay": {"type": "boolean", "description": "是否在点击遮罩层后关闭, 默认值: `true`"}, "van-action-sheet/safe-area-inset-bottom": {"type": "boolean", "description": "是否开启[底部安全区适配](#/zh-CN/advanced-usage#di-bu-an-quan-qu-gua-pei), 默认值: `true`"}, "van-action-sheet/get-container": {"type": "string | () => Element", "description": "指定挂载的节点，[用法示例](#/zh-CN/popup#zhi-ding-gua-zai-wei-zhi), 默认值: -"}, "van-address-list/v-model": {"type": "string", "description": "当前选中地址的 id, 默认值: -"}, "van-address-list/list": {"type": "Address[]", "description": "地址列表, 默认值: `[]`"}, "van-address-list/disabled-list": {"type": "Address[]", "description": "不可配送地址列表, 默认值: `[]`"}, "van-address-list/disabled-text": {"type": "string", "description": "不可配送提示文案, 默认值: -"}, "van-address-list/switchable": {"type": "boolean", "description": "是否允许切换地址, 默认值: `true`"}, "van-address-list/add-button-text": {"type": "string", "description": "底部按钮文字, 默认值: `新增地址`"}, "van-address-list/default-tag-text": {"type": "string", "description": "默认地址标签文字, 默认值: -"}, "van-card/thumb": {"type": "string", "description": "左侧图片 URL, 默认值: -"}, "van-card/title": {"type": "string", "description": "标题, 默认值: -"}, "van-card/desc": {"type": "string", "description": "描述, 默认值: -"}, "van-card/tag": {"type": "string", "description": "图片角标, 默认值: -"}, "van-card/num": {"type": "number | string", "description": "商品数量, 默认值: -"}, "van-card/price": {"type": "number | string", "description": "商品价格, 默认值: -"}, "van-card/origin-price": {"type": "number | string", "description": "商品划线原价, 默认值: -"}, "van-card/centered": {"type": "boolean", "description": "内容是否垂直居中, 默认值: `false`"}, "van-card/currency": {"type": "string", "description": "货币符号, 默认值: `¥`"}, "van-card/thumb-link": {"type": "string", "description": "点击左侧图片后跳转的链接地址, 默认值: -"}, "van-card/lazy-load": {"type": "boolean", "description": "是否开启图片懒加载，须配合 [Lazyload](#/zh-CN/lazyload) 组件使用, 默认值: `false`"}, "van-cell-group/title": {"type": "string", "description": "分组标题, 默认值: `-`"}, "van-cell-group/border": {"type": "boolean", "description": "是否显示外边框, 默认值: `true`"}, "van-cell/title": {"type": "number | string", "description": "左侧标题, 默认值: -"}, "van-cell/value": {"type": "number | string", "description": "右侧内容, 默认值: -"}, "van-cell/label": {"type": "string", "description": "标题下方的描述信息, 默认值: -"}, "van-cell/size": {"type": "string", "description": "单元格大小，可选值为 `large`, 默认值: -"}, "van-cell/icon": {"type": "string", "description": "左侧[图标名称](#/zh-CN/icon)或图片链接, 默认值: -"}, "van-cell/icon-prefix": {"type": "string", "description": "图标类名前缀，同 Icon 组件的 [class-prefix 属性](#/zh-CN/icon#props), 默认值: `van-icon`"}, "van-cell/url": {"type": "string", "description": "点击后跳转的链接地址, 默认值: -"}, "van-cell/to": {"type": "string | object", "description": "点击后跳转的目标路由对象，同 vue-router 的 [to 属性](https://router.vuejs.org/zh/api/#to), 默认值: -"}, "van-cell/border": {"type": "boolean", "description": "是否显示内边框, 默认值: `true`"}, "van-cell/replace": {"type": "boolean", "description": "是否在跳转时替换当前页面历史, 默认值: `false`"}, "van-cell/clickable": {"type": "boolean", "description": "是否开启点击反馈, 默认值: `null`"}, "van-cell/is-link": {"type": "boolean", "description": "是否展示右侧箭头并开启点击反馈, 默认值: `false`"}, "van-cell/required": {"type": "boolean", "description": "是否显示表单必填星号, 默认值: `false`"}, "van-cell/center": {"type": "boolean", "description": "是否使内容垂直居中, 默认值: `false`"}, "van-cell/arrow-direction": {"type": "string", "description": "箭头方向，可选值为 `left` `up` `down`, 默认值: `right`"}, "van-cell/title-style": {"type": "any", "description": "左侧标题额外样式, 默认值: -"}, "van-cell/title-class": {"type": "any", "description": "左侧标题额外类名, 默认值: -"}, "van-cell/value-class": {"type": "any", "description": "右侧内容额外类名, 默认值: -"}, "van-cell/label-class": {"type": "any", "description": "描述信息额外类名, 默认值: -"}, "van-cascader/title": {"type": "string", "description": "顶部标题, 默认值: -"}, "van-cascader/value": {"type": "string | number", "description": "选中项的值, 默认值: -"}, "van-cascader/options": {"type": "Option[]", "description": "可选项数据源, 默认值: `[]`"}, "van-cascader/placeholder": {"type": "string", "description": "未选中时的提示文案, 默认值: `请选择`"}, "van-cascader/active-color": {"type": "string", "description": "选中状态的高亮颜色, 默认值: `#ee0a24`"}, "van-cascader/closeable": {"type": "boolean", "description": "是否显示关闭图标, 默认值: `true`"}, "van-cascader/show-header": {"type": "boolean", "description": "是否展示标题栏, 默认值: `true`"}, "van-cascader/field-names": {"type": "object", "description": "自定义 `options` 结构中的字段, 默认值: `{ text: 'text', value: 'value', children: 'children' }`"}, "van-checkbox/v-model (value)": {"type": "boolean", "description": "是否为选中状态, 默认值: `false`"}, "van-checkbox/name": {"type": "any", "description": "标识符, 默认值: -"}, "van-checkbox/shape": {"type": "string", "description": "形状，可选值为 `square`, 默认值: `round`"}, "van-checkbox/disabled": {"type": "boolean", "description": "是否禁用复选框, 默认值: `false`"}, "van-checkbox/label-disabled": {"type": "boolean", "description": "是否禁用复选框文本点击, 默认值: `false`"}, "van-checkbox/label-position": {"type": "string", "description": "文本位置，可选值为 `left`, 默认值: `right`"}, "van-checkbox/icon-size": {"type": "number | string", "description": "图标大小，默认单位为 `px`, 默认值: `20px`"}, "van-checkbox/checked-color": {"type": "string", "description": "选中状态颜色, 默认值: `#1989fa`"}, "van-checkbox/bind-group": {"type": "boolean", "description": "是否与复选框组绑定, 默认值: `true`"}, "van-checkbox-group/v-model (value)": {"type": "any[]", "description": "所有选中项的标识符, 默认值: -"}, "van-checkbox-group/disabled": {"type": "boolean", "description": "是否禁用所有复选框, 默认值: `false`"}, "van-checkbox-group/max": {"type": "number | string", "description": "最大可选数，`0`为无限制, 默认值: `0`"}, "van-checkbox-group/direction": {"type": "string", "description": "排列方向，可选值为 `horizontal`, 默认值: `vertical`"}, "van-checkbox-group/icon-size": {"type": "number | string", "description": "所有复选框的图标大小，默认单位为 `px`, 默认值: `20px`"}, "van-checkbox-group/checked-color": {"type": "string", "description": "所有复选框的选中状态颜色, 默认值: `#1989fa`"}, "van-circle/v-model": {"type": "number", "description": "当前进度, 默认值: -"}, "van-circle/rate": {"type": "number | string", "description": "目标进度, 默认值: `100`"}, "van-circle/size": {"type": "number | string", "description": "圆环直径，默认单位为 `px`, 默认值: `100px`"}, "van-circle/color": {"type": "string | object", "description": "进度条颜色，传入对象格式可以定义渐变色, 默认值: `#1989fa`"}, "van-circle/layer-color": {"type": "string", "description": "轨道颜色, 默认值: `white`"}, "van-circle/fill": {"type": "string", "description": "填充颜色, 默认值: `none`"}, "van-circle/speed": {"type": "number | string", "description": "动画速度（单位为 rate/s）, 默认值: `0`"}, "van-circle/text": {"type": "string", "description": "文字, 默认值: -"}, "van-circle/stroke-width": {"type": "number | string", "description": "进度条宽度, 默认值: `40`"}, "van-circle/stroke-linecap": {"type": "string", "description": "进度条端点的形状，可选值为`square` `butt`, 默认值: `round`"}, "van-circle/clockwise": {"type": "boolean", "description": "是否顺时针增加, 默认值: `true`"}, "van-row/type": {"type": "string", "description": "布局方式，可选值为`flex`, 默认值: -"}, "van-row/gutter": {"type": "number | string", "description": "列元素之间的间距（单位为 px）, 默认值: -"}, "van-row/tag": {"type": "string", "description": "自定义元素标签, 默认值: `div`"}, "van-row/justify": {"type": "string", "description": "Flex 主轴对齐方式，可选值为 `end` `center` <br> `space-around` `space-between`, 默认值: `start`"}, "van-row/align": {"type": "string", "description": "Flex 交叉轴对齐方式，可选值为 `center` `bottom`, 默认值: `top`"}, "van-col/span": {"type": "number | string", "description": "列元素宽度, 默认值: -"}, "van-col/offset": {"type": "number | string", "description": "列元素偏移距离, 默认值: -"}, "van-col/tag": {"type": "string", "description": "自定义元素标签, 默认值: `div`"}, "van-collapse/v-model": {"type": "手风琴模式：_number | string_<br>非手风琴模式：_(number \\", "description": "当前展开面板的 name, 默认值: string)[]_"}, "van-collapse/accordion": {"type": "boolean", "description": "是否开启手风琴模式, 默认值: `false`"}, "van-collapse/border": {"type": "boolean", "description": "是否显示外边框, 默认值: `true`"}, "van-collapse-item/name": {"type": "number | string", "description": "唯一标识符，默认为索引值, 默认值: `index`"}, "van-collapse-item/icon": {"type": "string", "description": "标题栏左侧[图标名称](#/zh-CN/icon)或图片链接, 默认值: -"}, "van-collapse-item/size": {"type": "string", "description": "标题栏大小，可选值为 `large`, 默认值: -"}, "van-collapse-item/title": {"type": "number | string", "description": "标题栏左侧内容, 默认值: -"}, "van-collapse-item/value": {"type": "number | string", "description": "标题栏右侧内容, 默认值: -"}, "van-collapse-item/label": {"type": "number | string", "description": "标题栏描述信息, 默认值: -"}, "van-collapse-item/border": {"type": "boolean", "description": "是否显示内边框, 默认值: `true`"}, "van-collapse-item/is-link": {"type": "boolean", "description": "是否展示标题栏右侧箭头并开启点击反馈, 默认值: `true`"}, "van-collapse-item/disabled": {"type": "boolean", "description": "是否禁用面板, 默认值: `false`"}, "van-collapse-item/lazy-render": {"type": "boolean", "description": "是否在首次展开时才渲染面板内容, 默认值: `true`"}, "van-collapse-item/title-class": {"type": "string", "description": "左侧标题额外类名, 默认值: -"}, "van-collapse-item/value-class": {"type": "string", "description": "右侧内容额外类名, 默认值: -"}, "van-collapse-item/label-class": {"type": "string", "description": "描述信息额外类名, 默认值: -"}, "van-contact-card/type": {"type": "string", "description": "卡片类型，可选值为 `edit`, 默认值: `add`"}, "van-contact-card/name": {"type": "string", "description": "联系人姓名, 默认值: -"}, "van-contact-card/tel": {"type": "string", "description": "联系人手机号, 默认值: -"}, "van-contact-card/add-text": {"type": "string", "description": "添加时的文案提示, 默认值: `添加联系人`"}, "van-contact-edit/contact-info": {"type": "Contact", "description": "联系人信息, 默认值: `{}`"}, "van-contact-edit/is-edit": {"type": "boolean", "description": "是否为编辑联系人, 默认值: `false`"}, "van-contact-edit/is-saving": {"type": "boolean", "description": "是否显示保存按钮加载动画, 默认值: `false`"}, "van-contact-edit/is-deleting": {"type": "boolean", "description": "是否显示删除按钮加载动画, 默认值: `false`"}, "van-contact-edit/tel-validator": {"type": "(tel: string) => boolean", "description": "手机号格式校验函数, 默认值: -"}, "van-contact-edit/show-set-default": {"type": "boolean", "description": "是否显示默认联系人栏, 默认值: `false`"}, "van-contact-edit/set-default-label": {"type": "string", "description": "默认联系人栏文案, 默认值: -"}, "van-contact-list/v-model": {"type": "number | string", "description": "当前选中联系人的 id, 默认值: -"}, "van-contact-list/list": {"type": "Contact[]", "description": "联系人列表, 默认值: `[]`"}, "van-contact-list/add-text": {"type": "string", "description": "新建按钮文案, 默认值: `新建联系人`"}, "van-contact-list/default-tag-text": {"type": "string", "description": "默认联系人标签文案, 默认值: -"}, "van-count-down/time": {"type": "number | string", "description": "倒计时时长，单位毫秒, 默认值: `0`"}, "van-count-down/format": {"type": "string", "description": "时间格式, 默认值: `HH:mm:ss`"}, "van-count-down/auto-start": {"type": "boolean", "description": "是否自动开始倒计时, 默认值: `true`"}, "van-count-down/millisecond": {"type": "boolean", "description": "是否开启毫秒级渲染, 默认值: `false`"}, "van-coupon-cell/title": {"type": "string", "description": "单元格标题, 默认值: `优惠券`"}, "van-coupon-cell/chosen-coupon": {"type": "number | string", "description": "当前选中优惠券的索引, 默认值: `-1`"}, "van-coupon-cell/coupons": {"type": "Coupon[]", "description": "可用优惠券列表, 默认值: `[]`"}, "van-coupon-cell/editable": {"type": "boolean", "description": "能否切换优惠券, 默认值: `true`"}, "van-coupon-cell/border": {"type": "boolean", "description": "是否显示内边框, 默认值: `true`"}, "van-coupon-cell/currency": {"type": "string", "description": "货币符号, 默认值: `¥`"}, "van-coupon-list/v-model": {"type": "string", "description": "当前输入的兑换码, 默认值: -"}, "van-coupon-list/chosen-coupon": {"type": "number", "description": "当前选中优惠券的索引, 默认值: `-1`"}, "van-coupon-list/coupons": {"type": "Coupon[]", "description": "可用优惠券列表, 默认值: `[]`"}, "van-coupon-list/disabled-coupons": {"type": "Coupon[]", "description": "不可用优惠券列表, 默认值: `[]`"}, "van-coupon-list/enabled-title": {"type": "string", "description": "可用优惠券列表标题, 默认值: `可使用优惠券`"}, "van-coupon-list/disabled-title": {"type": "string", "description": "不可用优惠券列表标题, 默认值: `不可使用优惠券`"}, "van-coupon-list/exchange-button-text": {"type": "string", "description": "兑换按钮文字, 默认值: `兑换`"}, "van-coupon-list/exchange-button-loading": {"type": "boolean", "description": "是否显示兑换按钮加载动画, 默认值: `false`"}, "van-coupon-list/exchange-button-disabled": {"type": "boolean", "description": "是否禁用兑换按钮, 默认值: `false`"}, "van-coupon-list/exchange-min-length": {"type": "number", "description": "兑换码最小长度, 默认值: `1`"}, "van-coupon-list/displayed-coupon-index": {"type": "number", "description": "滚动至特定优惠券位置, 默认值: -"}, "van-coupon-list/show-close-button": {"type": "boolean", "description": "是否显示列表底部按钮, 默认值: `true`"}, "van-coupon-list/close-button-text": {"type": "string", "description": "列表底部按钮文字, 默认值: `不使用优惠`"}, "van-coupon-list/input-placeholder": {"type": "string", "description": "输入框文字提示, 默认值: `请输入优惠码`"}, "van-coupon-list/show-exchange-bar": {"type": "boolean", "description": "是否展示兑换栏, 默认值: `true`"}, "van-coupon-list/currency": {"type": "string", "description": "货币符号, 默认值: `¥`"}, "van-coupon-list/empty-image": {"type": "string", "description": "列表为空时的占位图, 默认值: `https://img01.yzcdn.cn/vant/coupon-empty.png`"}, "van-coupon-list/show-count": {"type": "boolean", "description": "是否展示可用 / 不可用数量, 默认值: `true`"}, "van-datetime-picker/type": {"type": "string", "description": "时间类型，可选值为 `date` `time` <br> `year-month` `month-day` `datehour`, 默认值: `datetime`"}, "van-datetime-picker/title": {"type": "string", "description": "顶部栏标题, 默认值: `''`"}, "van-datetime-picker/confirm-button-text": {"type": "string", "description": "确认按钮文字, 默认值: `确认`"}, "van-datetime-picker/cancel-button-text": {"type": "string", "description": "取消按钮文字, 默认值: `取消`"}, "van-datetime-picker/show-toolbar": {"type": "boolean", "description": "是否显示顶部栏, 默认值: `true`"}, "van-datetime-picker/loading": {"type": "boolean", "description": "是否显示加载状态, 默认值: `false`"}, "van-datetime-picker/readonly": {"type": "boolean", "description": "是否为只读状态，只读状态下无法切换选项, 默认值: `false`"}, "van-datetime-picker/filter": {"type": "(type, vals) => vals", "description": "选项过滤函数, 默认值: -"}, "van-datetime-picker/formatter": {"type": "(type, val) => val", "description": "选项格式化函数, 默认值: -"}, "van-datetime-picker/columns-order": {"type": "string[]", "description": "自定义列排序数组, 子项可选值为<br> `year`、`month`、`day`、`hour`、`minute`, 默认值: -"}, "van-datetime-picker/item-height": {"type": "number | string", "description": "选项高度，支持 `px` `vw` `vh` `rem` 单位，默认 `px`, 默认值: `44`"}, "van-datetime-picker/visible-item-count": {"type": "number | string", "description": "可见的选项个数, 默认值: `6`"}, "van-datetime-picker/swipe-duration": {"type": "number | string", "description": "快速滑动时惯性滚动的时长，单位`ms`, 默认值: `1000`"}, "van-date-picker/min-date": {"type": "Date", "description": "可选的最小时间，精确到分钟, 默认值: 十年前"}, "van-date-picker/max-date": {"type": "Date", "description": "可选的最大时间，精确到分钟, 默认值: 十年后"}, "van-time-picker/min-hour": {"type": "number | string", "description": "可选的最小小时, 默认值: `0`"}, "van-time-picker/max-hour": {"type": "number | string", "description": "可选的最大小时, 默认值: `23`"}, "van-time-picker/min-minute": {"type": "number | string", "description": "可选的最小分钟, 默认值: `0`"}, "van-time-picker/max-minute": {"type": "number | string", "description": "可选的最大分钟, 默认值: `59`"}, "van-dialog/v-model": {"type": "boolean", "description": "是否显示弹窗, 默认值: -"}, "van-dialog/title": {"type": "string", "description": "标题, 默认值: -"}, "van-dialog/width": {"type": "number | string", "description": "弹窗宽度，默认单位为 `px`, 默认值: `320px`"}, "van-dialog/message": {"type": "string", "description": "文本内容，支持通过 `\\n` 换行, 默认值: -"}, "van-dialog/message-align": {"type": "string", "description": "内容对齐方式，可选值为 `left` `right`, 默认值: `center`"}, "van-dialog/theme": {"type": "string", "description": "样式风格，可选值为 `round-button`, 默认值: `default`"}, "van-dialog/show-confirm-button": {"type": "boolean", "description": "是否展示确认按钮, 默认值: `true`"}, "van-dialog/show-cancel-button": {"type": "boolean", "description": "是否展示取消按钮, 默认值: `false`"}, "van-dialog/confirm-button-text": {"type": "string", "description": "确认按钮文案, 默认值: `确认`"}, "van-dialog/confirm-button-color": {"type": "string", "description": "确认按钮颜色, 默认值: `#ee0a24`"}, "van-dialog/cancel-button-text": {"type": "string", "description": "取消按钮文案, 默认值: `取消`"}, "van-dialog/cancel-button-color": {"type": "string", "description": "取消按钮颜色, 默认值: `black`"}, "van-dialog/overlay": {"type": "boolean", "description": "是否展示遮罩层, 默认值: `true`"}, "van-dialog/overlay-class": {"type": "string", "description": "自定义遮罩层类名, 默认值: -"}, "van-dialog/overlay-style": {"type": "object", "description": "自定义遮罩层样式, 默认值: -"}, "van-dialog/close-on-popstate": {"type": "boolean", "description": "是否在页面回退时自动关闭, 默认值: `true`"}, "van-dialog/close-on-click-overlay": {"type": "boolean", "description": "是否在点击遮罩层后关闭弹窗, 默认值: `false`"}, "van-dialog/lazy-render": {"type": "boolean", "description": "是否在显示弹层时才渲染节点, 默认值: `true`"}, "van-dialog/lock-scroll": {"type": "boolean", "description": "是否锁定背景滚动, 默认值: `true`"}, "van-dialog/allow-html": {"type": "boolean", "description": "是否允许 message 内容中渲染 HTML, 默认值: `true`"}, "van-dialog/before-close": {"type": "(action, done) => void", "description": "关闭前的回调函数，<br>调用 done() 后关闭弹窗，<br>调用 done(false) 阻止弹窗关闭, 默认值: -"}, "van-dialog/transition": {"type": "string", "description": "动画类名，等价于 [transition](https://cn.vuejs.org/v2/api/index.html#transition) 的 `name` 属性, 默认值: -"}, "van-dialog/get-container": {"type": "string | () => Element", "description": "指定挂载的节点，[用法示例](#/zh-CN/popup#zhi-ding-gua-zai-wei-zhi), 默认值: -"}, "van-divider/dashed": {"type": "boolean", "description": "是否使用虚线, 默认值: `false`"}, "van-divider/hairline": {"type": "boolean", "description": "是否使用 0.5px 线, 默认值: `true`"}, "van-divider/content-position": {"type": "string", "description": "内容位置，可选值为`left` `right`, 默认值: `center`"}, "van-dropdown-menu/active-color": {"type": "string", "description": "菜单标题和选项的选中态颜色, 默认值: `#ee0a24`"}, "van-dropdown-menu/direction": {"type": "string", "description": "菜单展开方向，可选值为`up`, 默认值: `down`"}, "van-dropdown-menu/z-index": {"type": "number | string", "description": "菜单栏 z-index 层级, 默认值: `10`"}, "van-dropdown-menu/duration": {"type": "number | string", "description": "动画时长，单位秒, 默认值: `0.2`"}, "van-dropdown-menu/overlay": {"type": "boolean", "description": "是否显示遮罩层, 默认值: `true`"}, "van-dropdown-menu/close-on-click-overlay": {"type": "boolean", "description": "是否在点击遮罩层后关闭菜单, 默认值: `true`"}, "van-dropdown-menu/close-on-click-outside": {"type": "boolean", "description": "是否在点击外部元素后关闭菜单, 默认值: `true`"}, "van-dropdown-item/value": {"type": "number | string", "description": "当前选中项对应的 value，可以通过`v-model`双向绑定, 默认值: -"}, "van-dropdown-item/title": {"type": "string", "description": "菜单项标题, 默认值: 当前选中项文字"}, "van-dropdown-item/options": {"type": "Option[]", "description": "选项数组, 默认值: `[]`"}, "van-dropdown-item/disabled": {"type": "boolean", "description": "是否禁用菜单, 默认值: `false`"}, "van-dropdown-item/lazy-render": {"type": "boolean", "description": "是否在首次展开时才渲染菜单内容, 默认值: `true`"}, "van-dropdown-item/title-class": {"type": "string", "description": "标题额外类名, 默认值: -"}, "van-dropdown-item/get-container": {"type": "string | () => Element", "description": "指定挂载的节点，[用法示例](#/zh-CN/popup#zhi-ding-gua-zai-wei-zhi), 默认值: -"}, "van-empty/image": {"type": "string", "description": "图片类型，可选值为 `error` `network` `search`，支持传入图片 URL, 默认值: `default`"}, "van-empty/image-size": {"type": "number | string", "description": "图片大小，默认单位为 `px`, 默认值: -"}, "van-empty/description": {"type": "string", "description": "图片下方的描述文字, 默认值: -"}, "van-field/v-model (value)": {"type": "number | string", "description": "当前输入的值, 默认值: -"}, "van-field/label": {"type": "string", "description": "输入框左侧文本, 默认值: -"}, "van-field/name": {"type": "string", "description": "名称，提交表单的标识符, 默认值: -"}, "van-field/type": {"type": "string", "description": "输入框类型, 可选值为 `tel` `digit`<br>`number` `textarea` `password` 等, 默认值: `text`"}, "van-field/size": {"type": "string", "description": "大小，可选值为 `large`, 默认值: -"}, "van-field/maxlength": {"type": "number | string", "description": "输入的最大字符数, 默认值: -"}, "van-field/placeholder": {"type": "string", "description": "输入框占位提示文字, 默认值: -"}, "van-field/border": {"type": "boolean", "description": "是否显示内边框, 默认值: `true`"}, "van-field/disabled": {"type": "boolean", "description": "是否禁用输入框, 默认值: `false`"}, "van-field/readonly": {"type": "boolean", "description": "是否只读, 默认值: `false`"}, "van-field/colon": {"type": "boolean", "description": "是否在 label 后面添加冒号, 默认值: `false`"}, "van-field/required": {"type": "boolean", "description": "是否显示表单必填星号, 默认值: `false`"}, "van-field/center": {"type": "boolean", "description": "是否使内容垂直居中, 默认值: `false`"}, "van-field/clearable": {"type": "boolean", "description": "是否启用清除图标，点击清除图标后会清空输入框, 默认值: `false`"}, "van-field/clear-trigger": {"type": "string", "description": "显示清除图标的时机，`always` 表示输入框不为空时展示，<br>`focus` 表示输入框聚焦且不为空时展示, 默认值: `focus`"}, "van-field/clickable": {"type": "boolean", "description": "是否开启点击反馈, 默认值: `false`"}, "van-field/is-link": {"type": "boolean", "description": "是否展示右侧箭头并开启点击反馈, 默认值: `false`"}, "van-field/autofocus": {"type": "boolean", "description": "是否自动聚焦，iOS 系统不支持该属性, 默认值: `false`"}, "van-field/show-word-limit": {"type": "boolean", "description": "是否显示字数统计，需要设置`maxlength`属性, 默认值: `false`"}, "van-field/error": {"type": "boolean", "description": "是否将输入内容标红, 默认值: `false`"}, "van-field/error-message": {"type": "string", "description": "底部错误提示文案，为空时不展示, 默认值: -"}, "van-field/formatter": {"type": "Function", "description": "输入内容格式化函数, 默认值: -"}, "van-field/format-trigger": {"type": "string", "description": "格式化函数触发的时机，可选值为 `onBlur`, 默认值: `onChange`"}, "van-field/arrow-direction": {"type": "string", "description": "箭头方向，可选值为 `left` `up` `down`, 默认值: `right`"}, "van-field/label-class": {"type": "any", "description": "左侧文本额外类名, 默认值: -"}, "van-field/label-width": {"type": "number | string", "description": "左侧文本宽度，默认单位为`px`, 默认值: `6.2em`"}, "van-field/label-align": {"type": "string", "description": "左侧文本对齐方式，可选值为 `center` `right`, 默认值: `left`"}, "van-field/input-align": {"type": "string", "description": "输入框对齐方式，可选值为 `center` `right`, 默认值: `left`"}, "van-field/error-message-align": {"type": "string", "description": "错误提示文案对齐方式，可选值为 `center` `right`, 默认值: `left`"}, "van-field/autosize": {"type": "boolean | object", "description": "是否自适应内容高度，只对 textarea 有效，<br>可传入对象,如 { maxHeight: 100, minHeight: 50 }，<br>单位为`px`, 默认值: `false`"}, "van-field/left-icon": {"type": "string", "description": "左侧[图标名称](#/zh-CN/icon)或图片链接, 默认值: -"}, "van-field/right-icon": {"type": "string", "description": "右侧[图标名称](#/zh-CN/icon)或图片链接, 默认值: -"}, "van-field/icon-prefix": {"type": "string", "description": "图标类名前缀，同 Icon 组件的 [class-prefix 属性](#/zh-CN/icon#props), 默认值: `van-icon`"}, "van-field/rules": {"type": "Rule[]", "description": "表单校验规则，详见 [Form 组件](#/zh-CN/form#rule-shu-ju-jie-gou), 默认值: -"}, "van-field/autocomplete": {"type": "string", "description": "input 标签原生的[自动完成属性](https://developer.mozilla.org/en-US/docs/Web/HTML/Attributes/autocomplete), 默认值: -"}, "van-form/label-width": {"type": "number | string", "description": "表单项 label 宽度，默认单位为`px`, 默认值: `6.2em`"}, "van-form/label-align": {"type": "string", "description": "\b 表单项 label 对齐方式，可选值为 `center` `right`, 默认值: `left`"}, "van-form/input-align": {"type": "string", "description": "输入框对齐方式，可选值为 `center` `right`, 默认值: `left`"}, "van-form/error-message-align": {"type": "string", "description": "错误提示文案对齐方式，可选值为 `center` `right`, 默认值: `left`"}, "van-form/validate-trigger": {"type": "string", "description": "表单校验触发时机，可选值为 `onChange`、`onSubmit`，详见下表, 默认值: `onBlur`"}, "van-form/colon": {"type": "boolean", "description": "是否在 label 后面添加冒号, 默认值: `false`"}, "van-form/disabled": {"type": "boolean", "description": "是否禁用表单中的所有输入框, 默认值: `false`"}, "van-form/readonly": {"type": "boolean", "description": "是否将表单中的所有输入框设置为只读, 默认值: `false`"}, "van-form/validate-first": {"type": "boolean", "description": "是否在某一项校验不通过时停止校验, 默认值: `false`"}, "van-form/scroll-to-error": {"type": "boolean", "description": "是否在提交表单且校验不通过时滚动至错误的表单项, 默认值: `false`"}, "van-form/show-error": {"type": "boolean", "description": "是否在校验不通过时标红输入框, 默认值: `true`"}, "van-form/show-error-message": {"type": "boolean", "description": "是否在校验不通过时在输入框下方展示错误提示, 默认值: `true`"}, "van-form/submit-on-enter": {"type": "boolean", "description": "是否在按下回车键时提交表单, 默认值: `true`"}, "van-goods-action/safe-area-inset-bottom": {"type": "boolean", "description": "是否开启[底部安全区适配](#/zh-CN/advanced-usage#di-bu-an-quan-qu-gua-pei), 默认值: `true`"}, "van-goods-action-icon/text": {"type": "string", "description": "按钮文字, 默认值: -"}, "van-goods-action-icon/icon": {"type": "string", "description": "图标, 默认值: -"}, "van-goods-action-icon/color": {"type": "string", "description": "图标颜色, 默认值: `#323233`"}, "van-goods-action-icon/icon-class": {"type": "any", "description": "图标额外类名, 默认值: -"}, "van-goods-action-icon/dot": {"type": "boolean", "description": "是否显示图标右上角小红点, 默认值: `false`"}, "van-goods-action-icon/badge": {"type": "number | string", "description": "图标右上角徽标的内容, 默认值: -"}, "van-goods-action-icon/info": {"type": "number | string", "description": "图标右上角徽标的内容（已废弃，请使用 badge 属性）, 默认值: -"}, "van-goods-action-icon/url": {"type": "string", "description": "点击后跳转的链接地址, 默认值: -"}, "van-goods-action-icon/to": {"type": "string | object", "description": "点击后跳转的目标路由对象，同 vue-router 的 [to 属性](https://router.vuejs.org/zh/api/#to), 默认值: -"}, "van-goods-action-icon/replace": {"type": "boolean", "description": "是否在跳转时替换当前页面历史, 默认值: `false`"}, "van-goods-action-button/text": {"type": "string", "description": "按钮文字, 默认值: -"}, "van-goods-action-button/type": {"type": "string", "description": "按钮类型，可选值为 `primary` `info` `warning` `danger`, 默认值: `default`"}, "van-goods-action-button/color": {"type": "string", "description": "按钮颜色，支持传入`linear-gradient`渐变色, 默认值: -"}, "van-goods-action-button/icon": {"type": "string", "description": "左侧[图标名称](#/zh-CN/icon)或图片链接, 默认值: -"}, "van-goods-action-button/disabled": {"type": "boolean", "description": "是否禁用按钮, 默认值: `false`"}, "van-goods-action-button/loading": {"type": "boolean", "description": "是否显示为加载状态, 默认值: `false`"}, "van-goods-action-button/url": {"type": "string", "description": "点击后跳转的链接地址, 默认值: -"}, "van-goods-action-button/to": {"type": "string | object", "description": "点击后跳转的目标路由对象，同 vue-router 的 [to 属性](https://router.vuejs.org/zh/api/#to), 默认值: -"}, "van-goods-action-button/replace": {"type": "boolean", "description": "是否在跳转时替换当前页面历史, 默认值: `false`"}, "van-grid/column-num": {"type": "number | string", "description": "列数, 默认值: `4`"}, "van-grid/icon-size": {"type": "number | string", "description": "图标大小，默认单位为`px`, 默认值: `28px`"}, "van-grid/gutter": {"type": "number | string", "description": "格子之间的间距，默认单位为`px`, 默认值: `0`"}, "van-grid/border": {"type": "boolean", "description": "是否显示边框, 默认值: `true`"}, "van-grid/center": {"type": "boolean", "description": "是否将格子内容居中显示, 默认值: `true`"}, "van-grid/square": {"type": "boolean", "description": "是否将格子固定为正方形, 默认值: `false`"}, "van-grid/clickable": {"type": "boolean", "description": "是否开启格子点击反馈, 默认值: `false`"}, "van-grid/direction": {"type": "string", "description": "格子内容排列的方向，可选值为 `horizontal`, 默认值: `vertical`"}, "van-grid-item/text": {"type": "string", "description": "文字, 默认值: -"}, "van-grid-item/icon": {"type": "string", "description": "[图标名称](#/zh-CN/icon)或图片链接, 默认值: -"}, "van-grid-item/icon-prefix": {"type": "string", "description": "图标类名前缀，同 Icon 组件的 [class-prefix 属性](#/zh-CN/icon#props), 默认值: `van-icon`"}, "van-grid-item/dot": {"type": "boolean", "description": "是否显示图标右上角小红点, 默认值: `false`"}, "van-grid-item/badge": {"type": "number | string", "description": "图标右上角徽标的内容, 默认值: -"}, "van-grid-item/info": {"type": "number | string", "description": "图标右上角徽标的内容（已废弃，请使用 badge 属性）, 默认值: -"}, "van-grid-item/url": {"type": "string", "description": "点击后跳转的链接地址, 默认值: -"}, "van-grid-item/to": {"type": "string | object", "description": "点击后跳转的目标路由对象，同 vue-router 的 [to 属性](https://router.vuejs.org/zh/api/#to), 默认值: -"}, "van-grid-item/replace": {"type": "boolean", "description": "是否在跳转时替换当前页面历史, 默认值: `false`"}, "van-icon/name": {"type": "string", "description": "图标名称或图片链接, 默认值: -"}, "van-icon/dot": {"type": "boolean", "description": "是否显示图标右上角小红点, 默认值: `false`"}, "van-icon/badge": {"type": "number | string", "description": "图标右上角徽标的内容, 默认值: -"}, "van-icon/info": {"type": "number | string", "description": "图标右上角徽标的内容（已废弃，请使用 badge 属性）, 默认值: -"}, "van-icon/color": {"type": "string", "description": "图标颜色, 默认值: `inherit`"}, "van-icon/size": {"type": "number | string", "description": "图标大小，如 `20px` `2em`，默认单位为`px`, 默认值: `inherit`"}, "van-icon/class-prefix": {"type": "string", "description": "类名前缀，用于使用自定义图标, 默认值: `van-icon`"}, "van-icon/tag": {"type": "string", "description": "HTML 标签, 默认值: `i`"}, "van-image/src": {"type": "string", "description": "图片链接, 默认值: -"}, "van-image/fit": {"type": "string", "description": "图片填充模式, 默认值: `fill`"}, "van-image/alt": {"type": "string", "description": "替代文本, 默认值: -"}, "van-image/width": {"type": "number | string", "description": "宽度，默认单位为`px`, 默认值: -"}, "van-image/height": {"type": "number | string", "description": "高度，默认单位为`px`, 默认值: -"}, "van-image/radius": {"type": "number | string", "description": "圆角大小，默认单位为`px`, 默认值: `0`"}, "van-image/round": {"type": "boolean", "description": "是否显示为圆形, 默认值: `false`"}, "van-image/lazy-load": {"type": "boolean", "description": "是否开启图片懒加载，须配合 [Lazyload](#/zh-CN/lazyload) 组件使用, 默认值: `false`"}, "van-image/show-error": {"type": "boolean", "description": "是否展示图片加载失败提示, 默认值: `true`"}, "van-image/show-loading": {"type": "boolean", "description": "是否展示图片加载中提示, 默认值: `true`"}, "van-image/error-icon": {"type": "string", "description": "失败时提示的[图标名称](#/zh-CN/icon)或图片链接, 默认值: `photo-fail`"}, "van-image/loading-icon": {"type": "string", "description": "加载时提示的[图标名称](#/zh-CN/icon)或图片链接, 默认值: `photo`"}, "van-image/icon-prefix": {"type": "string", "description": "图标类名前缀，同 Icon 组件的 [class-prefix 属性](#/zh-CN/icon#props), 默认值: `van-icon`"}, "van-image-preview/images": {"type": "string[]", "description": "需要预览的图片 URL 数组, 默认值: `[]`"}, "van-image-preview/start-position": {"type": "number | string", "description": "图片预览起始位置索引, 默认值: `0`"}, "van-image-preview/swipe-duration": {"type": "number | string", "description": "动画时长，单位为 ms, 默认值: `300`"}, "van-image-preview/show-index": {"type": "boolean", "description": "是否显示页码, 默认值: `true`"}, "van-image-preview/show-indicators": {"type": "boolean", "description": "是否显示轮播指示器, 默认值: `false`"}, "van-image-preview/loop": {"type": "boolean", "description": "是否开启循环播放, 默认值: `true`"}, "van-image-preview/async-close": {"type": "boolean", "description": "是否开启异步关闭, 默认值: `false`"}, "van-image-preview/close-on-popstate": {"type": "boolean", "description": "是否在页面回退时自动关闭, 默认值: `true`"}, "van-image-preview/class-name": {"type": "any", "description": "自定义类名, 默认值: -"}, "van-image-preview/max-zoom": {"type": "number | string", "description": "手势缩放时，最大缩放比例, 默认值: `3`"}, "van-image-preview/min-zoom": {"type": "number | string", "description": "手势缩放时，最小缩放比例, 默认值: `1/3`"}, "van-image-preview/closeable": {"type": "boolean", "description": "是否显示关闭图标, 默认值: `false`"}, "van-image-preview/close-icon": {"type": "string", "description": "关闭图标名称或图片链接, 默认值: `clear`"}, "van-image-preview/close-icon-position": {"type": "string", "description": "关闭图标位置，可选值为`top-left`<br>`bottom-left` `bottom-right`, 默认值: `top-right`"}, "van-image-preview/transition": {"type": "string", "description": "动画类名，等价于 [transition](https://cn.vuejs.org/v2/api/index.html#transition) 的 `name` 属性, 默认值: `van-fade`"}, "van-image-preview/overlay-style": {"type": "object", "description": "自定义遮罩层样式, 默认值: -"}, "van-image-preview/get-container": {"type": "string | () => Element", "description": "指定挂载的节点，[用法示例](#/zh-CN/popup#zhi-ding-gua-zai-wei-zhi), 默认值: -"}, "van-index-bar/index-list": {"type": "string[] | number[]", "description": "索引字符列表, 默认值: `A-Z`"}, "van-index-bar/z-index": {"type": "number | string", "description": "z-index 层级, 默认值: `1`"}, "van-index-bar/sticky": {"type": "boolean", "description": "是否开启锚点自动吸顶, 默认值: `true`"}, "van-index-bar/sticky-offset-top": {"type": "number", "description": "锚点自动吸顶时与顶部的距离, 默认值: `0`"}, "van-index-bar/highlight-color": {"type": "string", "description": "索引字符高亮颜色, 默认值: `#ee0a24`"}, "van-index-anchor/index": {"type": "number | string", "description": "索引字符, 默认值: -"}, "van-list/v-model": {"type": "boolean", "description": "是否处于加载状态，加载过程中不触发`load`事件, 默认值: `false`"}, "van-list/finished": {"type": "boolean", "description": "是否已加载完成，加载完成后不再触发`load`事件, 默认值: `false`"}, "van-list/error": {"type": "boolean", "description": "是否加载失败，加载失败后点击错误提示可以重新<br>触发`load`事件，必须使用`sync`修饰符, 默认值: `false`"}, "van-list/offset": {"type": "number | string", "description": "滚动条与底部距离小于 offset 时触发`load`事件, 默认值: `300`"}, "van-list/loading-text": {"type": "string", "description": "加载过程中的提示文案, 默认值: `加载中...`"}, "van-list/finished-text": {"type": "string", "description": "加载完成后的提示文案, 默认值: -"}, "van-list/error-text": {"type": "string", "description": "加载失败后的提示文案, 默认值: -"}, "van-list/immediate-check": {"type": "boolean", "description": "是否在初始化时立即执行滚动位置检查, 默认值: `true`"}, "van-list/direction": {"type": "string", "description": "滚动触发加载的方向，可选值为`up`, 默认值: `down`"}, "van-loading/color": {"type": "string", "description": "颜色, 默认值: `#c9c9c9`"}, "van-loading/type": {"type": "string", "description": "类型，可选值为 `spinner`, 默认值: `circular`"}, "van-loading/size": {"type": "number | string", "description": "加载图标大小，默认单位为 `px`, 默认值: `30px`"}, "van-loading/text-size": {"type": "number | string", "description": "文字大小，默认单位为 `px`, 默认值: `14px`"}, "van-loading/text-color": {"type": "string", "description": "文字颜色, 默认值: `#c9c9c9`"}, "van-loading/vertical": {"type": "boolean", "description": "是否垂直排列图标和文字内容, 默认值: `false`"}, "van-nav-bar/title": {"type": "string", "description": "标题, 默认值: `''`"}, "van-nav-bar/left-text": {"type": "string", "description": "左侧文案, 默认值: `''`"}, "van-nav-bar/right-text": {"type": "string", "description": "右侧文案, 默认值: `''`"}, "van-nav-bar/left-arrow": {"type": "boolean", "description": "是否显示左侧箭头, 默认值: `false`"}, "van-nav-bar/border": {"type": "boolean", "description": "是否显示下边框, 默认值: `true`"}, "van-nav-bar/fixed": {"type": "boolean", "description": "是否固定在顶部, 默认值: `false`"}, "van-nav-bar/placeholder": {"type": "boolean", "description": "固定在顶部时，是否在标签位置生成一个等高的占位元素, 默认值: `false`"}, "van-nav-bar/z-index": {"type": "number | string", "description": "导航栏 z-index, 默认值: `1`"}, "van-nav-bar/safe-area-inset-top": {"type": "boolean", "description": "是否开启[顶部安全区适配](#/zh-CN/advanced-usage#di-bu-an-quan-qu-gua-pei), 默认值: `false`"}, "van-notice-bar/mode": {"type": "string", "description": "通知栏模式，可选值为 `closeable` `link`, 默认值: `''`"}, "van-notice-bar/text": {"type": "string", "description": "通知文本内容, 默认值: `''`"}, "van-notice-bar/color": {"type": "string", "description": "通知文本颜色, 默认值: `#f60`"}, "van-notice-bar/background": {"type": "string", "description": "滚动条背景, 默认值: `#fff7cc`"}, "van-notice-bar/left-icon": {"type": "string", "description": "左侧[图标名称](#/zh-CN/icon)或图片链接, 默认值: -"}, "van-notice-bar/delay": {"type": "number | string", "description": "动画延迟时间 (s), 默认值: `1`"}, "van-notice-bar/speed": {"type": "number | string", "description": "滚动速率 (px/s), 默认值: `60`"}, "van-notice-bar/scrollable": {"type": "boolean", "description": "是否开启滚动播放，内容长度溢出时默认开启, 默认值: -"}, "van-notice-bar/wrapable": {"type": "boolean", "description": "是否开启文本换行，只在禁用滚动时生效, 默认值: `false`"}, "van-number-keyboard/v-model (value)": {"type": "string", "description": "当前输入值, 默认值: -"}, "van-number-keyboard/show": {"type": "boolean", "description": "是否显示键盘, 默认值: -"}, "van-number-keyboard/title": {"type": "string", "description": "键盘标题, 默认值: -"}, "van-number-keyboard/theme": {"type": "string", "description": "样式风格，可选值为 `custom`, 默认值: `default`"}, "van-number-keyboard/maxlength": {"type": "number | string", "description": "输入值最大长度, 默认值: -"}, "van-number-keyboard/transition": {"type": "boolean", "description": "是否开启过场动画, 默认值: `true`"}, "van-number-keyboard/z-index": {"type": "number | string", "description": "键盘 z-index 层级, 默认值: `100`"}, "van-number-keyboard/extra-key": {"type": "string | string[]", "description": "底部额外按键的内容, 默认值: `''`"}, "van-number-keyboard/close-button-text": {"type": "string", "description": "关闭按钮文字，空则不展示, 默认值: -"}, "van-number-keyboard/delete-button-text": {"type": "string", "description": "删除按钮文字，空则展示删除图标, 默认值: -"}, "van-number-keyboard/close-button-loading": {"type": "boolean", "description": "是否将关闭按钮设置为加载中状态，仅在 `theme=\"custom\"` 时有效, 默认值: `false`"}, "van-number-keyboard/show-delete-key": {"type": "boolean", "description": "是否展示删除图标, 默认值: `true`"}, "van-number-keyboard/hide-on-click-outside": {"type": "boolean", "description": "点击外部时是否收起键盘, 默认值: `true`"}, "van-number-keyboard/get-container": {"type": "string | () => Element", "description": "指定挂载的节点，[用法示例](#/zh-CN/popup#zhi-ding-gua-zai-wei-zhi), 默认值: -"}, "van-number-keyboard/safe-area-inset-bottom": {"type": "boolean", "description": "是否开启[底部安全区适配](#/zh-CN/advanced-usage#di-bu-an-quan-qu-gua-pei), 默认值: `true`"}, "van-number-keyboard/random-key-order": {"type": "boolean", "description": "是否将通过随机顺序展示按键, 默认值: `false`"}, "van-overlay/show": {"type": "boolean", "description": "是否展示遮罩层, 默认值: `false`"}, "van-overlay/z-index": {"type": "number | string", "description": "z-index 层级, 默认值: `1`"}, "van-overlay/duration": {"type": "number | string", "description": "动画时长，单位秒, 默认值: `0.3`"}, "van-overlay/class-name": {"type": "string", "description": "自定义类名, 默认值: -"}, "van-overlay/custom-style": {"type": "object", "description": "自定义样式, 默认值: -"}, "van-overlay/lock-scroll": {"type": "boolean", "description": "是否锁定背景滚动，锁定时蒙层里的内容也将无法滚动, 默认值: `true`"}, "van-pagination/v-model": {"type": "number", "description": "当前页码, 默认值: -"}, "van-pagination/mode": {"type": "string", "description": "显示模式，可选值为 `simple`, 默认值: `multi`"}, "van-pagination/prev-text": {"type": "string", "description": "上一页按钮文字, 默认值: `上一页`"}, "van-pagination/next-text": {"type": "string", "description": "下一页按钮文字, 默认值: `下一页`"}, "van-pagination/page-count": {"type": "number | string", "description": "总页数, 默认值: 根据页数计算"}, "van-pagination/total-items": {"type": "number | string", "description": "总记录数, 默认值: `0`"}, "van-pagination/items-per-page": {"type": "number | string", "description": "每页记录数, 默认值: `10`"}, "van-pagination/show-page-size": {"type": "number | string", "description": "显示的页码个数, 默认值: `5`"}, "van-pagination/force-ellipses": {"type": "boolean", "description": "是否显示省略号, 默认值: `false`"}, "van-panel/title": {"type": "string", "description": "标题, 默认值: -"}, "van-panel/desc": {"type": "string", "description": "描述, 默认值: -"}, "van-panel/status": {"type": "string", "description": "状态, 默认值: -"}, "van-panel/icon": {"type": "string", "description": "标题左侧[图标名称](#/zh-CN/icon)或图片链接, 默认值: -"}, "van-password-input/value": {"type": "string", "description": "密码值, 默认值: `''`"}, "van-password-input/info": {"type": "string", "description": "输入框下方文字提示, 默认值: -"}, "van-password-input/error-info": {"type": "string", "description": "输入框下方错误提示, 默认值: -"}, "van-password-input/length": {"type": "number | string", "description": "密码最大长度, 默认值: `6`"}, "van-password-input/gutter": {"type": "number | string", "description": "输入框格子之间的间距，如 `20px` `2em`，默认单位为`px`, 默认值: `0`"}, "van-password-input/mask": {"type": "boolean", "description": "是否隐藏密码内容, 默认值: `true`"}, "van-password-input/focused": {"type": "boolean", "description": "是否已聚焦，聚焦时会显示光标, 默认值: `false`"}, "van-picker/columns": {"type": "Column[]", "description": "对象数组，配置每一列显示的数据, 默认值: `[]`"}, "van-picker/title": {"type": "string", "description": "顶部栏标题, 默认值: -"}, "van-picker/confirm-button-text": {"type": "string", "description": "确认按钮文字, 默认值: `确认`"}, "van-picker/cancel-button-text": {"type": "string", "description": "取消按钮文字, 默认值: `取消`"}, "van-picker/value-key": {"type": "string", "description": "选项对象中，选项文字对应的键名, 默认值: `text`"}, "van-picker/toolbar-position": {"type": "string", "description": "顶部栏位置，可选值为`bottom`, 默认值: `top`"}, "van-picker/loading": {"type": "boolean", "description": "是否显示加载状态, 默认值: `false`"}, "van-picker/readonly": {"type": "boolean", "description": "是否为只读状态，只读状态下无法切换选项, 默认值: `false`"}, "van-picker/show-toolbar": {"type": "boolean", "description": "是否显示顶部栏, 默认值: `false`"}, "van-picker/allow-html": {"type": "boolean", "description": "是否允许选项内容中渲染 HTML, 默认值: `true`"}, "van-picker/default-index": {"type": "number | string", "description": "单列选择时，默认选中项的索引, 默认值: `0`"}, "van-picker/item-height": {"type": "number | string", "description": "选项高度，支持 `px` `vw` `vh` `rem` 单位，默认 `px`, 默认值: `44`"}, "van-picker/visible-item-count": {"type": "number | string", "description": "可见的选项个数, 默认值: `6`"}, "van-picker/swipe-duration": {"type": "number | string", "description": "快速滑动时惯性滚动的时长，单位 `ms`, 默认值: `1000`"}, "van-popover/v-model": {"type": "boolean", "description": "是否展示气泡弹出层, 默认值: `false`"}, "van-popover/actions": {"type": "Action[]", "description": "选项列表, 默认值: `[]`"}, "van-popover/placement": {"type": "string", "description": "弹出位置, 默认值: `bottom`"}, "van-popover/theme": {"type": "string", "description": "主题风格，可选值为 `dark`, 默认值: `light`"}, "van-popover/trigger": {"type": "-", "description": "触发方式，可选值为 `click`, 默认值: undefined"}, "van-popover/offset": {"type": "[number, number]", "description": "出现位置的偏移量, 默认值: `[0, 8]`"}, "van-popover/overlay": {"type": "boolean", "description": "是否显示遮罩层, 默认值: `false`"}, "van-popover/close-on-click-action": {"type": "boolean", "description": "是否在点击选项后关闭, 默认值: `true`"}, "van-popover/close-on-click-outside": {"type": "boolean", "description": "是否在点击外部元素后关闭菜单, 默认值: `true`"}, "van-popover/get-container": {"type": "string | () => Element", "description": "指定挂载的节点，[用法示例](#/zh-CN/popup#zhi-ding-gua-zai-wei-zhi), 默认值: `body`"}, "van-popup/v-model (value)": {"type": "boolean", "description": "是否显示弹出层, 默认值: `false`"}, "van-popup/overlay": {"type": "boolean", "description": "是否显示遮罩层, 默认值: `true`"}, "van-popup/position": {"type": "string", "description": "弹出位置，可选值为 `top` `bottom` `right` `left`, 默认值: `center`"}, "van-popup/overlay-class": {"type": "string", "description": "自定义遮罩层类名, 默认值: -"}, "van-popup/overlay-style": {"type": "object", "description": "自定义遮罩层样式, 默认值: -"}, "van-popup/duration": {"type": "number | string", "description": "动画时长，单位秒, 默认值: `0.3`"}, "van-popup/round": {"type": "boolean", "description": "是否显示圆角, 默认值: `false`"}, "van-popup/lock-scroll": {"type": "boolean", "description": "是否锁定背景滚动, 默认值: `true`"}, "van-popup/lazy-render": {"type": "boolean", "description": "是否在显示弹层时才渲染节点, 默认值: `true`"}, "van-popup/close-on-popstate": {"type": "boolean", "description": "是否在页面回退时自动关闭, 默认值: `false`"}, "van-popup/close-on-click-overlay": {"type": "boolean", "description": "是否在点击遮罩层后关闭, 默认值: `true`"}, "van-popup/closeable": {"type": "boolean", "description": "是否显示关闭图标, 默认值: `false`"}, "van-popup/close-icon": {"type": "string", "description": "关闭图标名称或图片链接, 默认值: `cross`"}, "van-popup/close-icon-position": {"type": "string", "description": "关闭图标位置，可选值为`top-left`<br>`bottom-left` `bottom-right`, 默认值: `top-right`"}, "van-popup/transition": {"type": "string", "description": "动画类名，等价于 [transition](https://cn.vuejs.org/v2/api/index.html#transition) 的`name`属性, 默认值: -"}, "van-popup/transition-appear": {"type": "boolean", "description": "是否在初始渲染时启用过渡动画, 默认值: `false`"}, "van-popup/get-container": {"type": "string | () => Element", "description": "指定挂载的节点, 默认值: -"}, "van-popup/safe-area-inset-bottom": {"type": "boolean", "description": "是否开启[底部安全区适配](#/zh-CN/advanced-usage#di-bu-an-quan-qu-gua-pei), 默认值: `false`"}, "van-progress/percentage": {"type": "number | string", "description": "进度百分比, 默认值: `0`"}, "van-progress/stroke-width": {"type": "number | string", "description": "进度条粗细，默认单位为`px`, 默认值: `4px`"}, "van-progress/color": {"type": "string", "description": "进度条颜色, 默认值: `#1989fa`"}, "van-progress/track-color": {"type": "string", "description": "轨道颜色, 默认值: `#e5e5e5`"}, "van-progress/pivot-text": {"type": "string", "description": "进度文字内容, 默认值: 百分比"}, "van-progress/pivot-color": {"type": "string", "description": "进度文字背景色, 默认值: 同进度条颜色"}, "van-progress/text-color": {"type": "string", "description": "进度文字颜色, 默认值: `white`"}, "van-progress/inactive": {"type": "boolean", "description": "是否置灰, 默认值: `false`"}, "van-progress/show-pivot": {"type": "boolean", "description": "是否显示进度文字, 默认值: `true`"}, "van-pull-refresh/v-model": {"type": "boolean", "description": "是否处于加载中状态, 默认值: -"}, "van-pull-refresh/pulling-text": {"type": "string", "description": "下拉过程提示文案, 默认值: `下拉即可刷新...`"}, "van-pull-refresh/loosing-text": {"type": "string", "description": "释放过程提示文案, 默认值: `释放即可刷新...`"}, "van-pull-refresh/loading-text": {"type": "string", "description": "加载过程提示文案, 默认值: `加载中...`"}, "van-pull-refresh/success-text": {"type": "string", "description": "刷新成功提示文案, 默认值: -"}, "van-pull-refresh/success-duration": {"type": "number | string", "description": "刷新成功提示展示时长(ms), 默认值: `500`"}, "van-pull-refresh/animation-duration": {"type": "number | string", "description": "动画时长, 默认值: `300`"}, "van-pull-refresh/head-height": {"type": "number | string", "description": "顶部内容高度, 默认值: `50`"}, "van-pull-refresh/pull-distance": {"type": "number | string", "description": "触发下拉刷新的距离, 默认值: 与 `head-height` 一致"}, "van-pull-refresh/disabled": {"type": "boolean", "description": "是否禁用下拉刷新, 默认值: `false`"}, "van-radio/name": {"type": "any", "description": "标识符, 默认值: -"}, "van-radio/shape": {"type": "string", "description": "形状，可选值为 `square`, 默认值: `round`"}, "van-radio/disabled": {"type": "boolean", "description": "是否为禁用状态, 默认值: `false`"}, "van-radio/label-disabled": {"type": "boolean", "description": "是否禁用文本内容点击, 默认值: `false`"}, "van-radio/label-position": {"type": "string", "description": "文本位置，可选值为 `left`, 默认值: `right`"}, "van-radio/icon-size": {"type": "number | string", "description": "图标大小，默认单位为`px`, 默认值: `20px`"}, "van-radio/checked-color": {"type": "string", "description": "选中状态颜色, 默认值: `#1989fa`"}, "van-radio-group/v-model (value)": {"type": "any", "description": "当前选中项的标识符, 默认值: -"}, "van-radio-group/disabled": {"type": "boolean", "description": "是否禁用所有单选框, 默认值: `false`"}, "van-radio-group/direction": {"type": "string", "description": "排列方向，可选值为`horizontal`, 默认值: `vertical`"}, "van-radio-group/icon-size": {"type": "number | string", "description": "所有单选框的图标大小，默认单位为`px`, 默认值: `20px`"}, "van-radio-group/checked-color": {"type": "string", "description": "所有单选框的选中状态颜色, 默认值: `#1989fa`"}, "van-rate/v-model": {"type": "number", "description": "当前分值, 默认值: -"}, "van-rate/count": {"type": "number | string", "description": "图标总数, 默认值: `5`"}, "van-rate/size": {"type": "number | string", "description": "图标大小，默认单位为`px`, 默认值: `20px`"}, "van-rate/gutter": {"type": "number | string", "description": "图标间距，默认单位为`px`, 默认值: `4px`"}, "van-rate/color": {"type": "string", "description": "选中时的颜色, 默认值: `#ee0a24`"}, "van-rate/void-color": {"type": "string", "description": "未选中时的颜色, 默认值: `#c8c9cc`"}, "van-rate/disabled-color": {"type": "string", "description": "禁用时的颜色, 默认值: `#c8c9cc`"}, "van-rate/icon": {"type": "string", "description": "选中时的[图标名称](#/zh-CN/icon)或图片链接, 默认值: `star`"}, "van-rate/void-icon": {"type": "string", "description": "未选中时的[图标名称](#/zh-CN/icon)或图片链接, 默认值: `star-o`"}, "van-rate/icon-prefix": {"type": "string", "description": "图标类名前缀，同 Icon 组件的 [class-prefix 属性](#/zh-CN/icon#props), 默认值: `van-icon`"}, "van-rate/allow-half": {"type": "boolean", "description": "是否允许半选, 默认值: `false`"}, "van-rate/readonly": {"type": "boolean", "description": "是否为只读状态 \b, 默认值: `false`"}, "van-rate/disabled": {"type": "boolean", "description": "是否禁用评分, 默认值: `false`"}, "van-rate/touchable": {"type": "boolean", "description": "是否可以通过滑动手势选择评分, 默认值: `true`"}, "van-search/label": {"type": "string", "description": "搜索框左侧文本, 默认值: -"}, "van-search/shape": {"type": "string", "description": "搜索框形状，可选值为 `round`, 默认值: `square`"}, "van-search/background": {"type": "string", "description": "搜索框外部背景色, 默认值: `#f2f2f2`"}, "van-search/maxlength": {"type": "number | string", "description": "输入的最大字符数, 默认值: -"}, "van-search/placeholder": {"type": "string", "description": "占位提示文字, 默认值: -"}, "van-search/clearable": {"type": "boolean", "description": "是否启用清除图标，点击清除图标后会清空输入框, 默认值: `true`"}, "van-search/clear-trigger": {"type": "string", "description": "显示清除图标的时机，`always` 表示输入框不为空时展示，<br>`focus` 表示输入框聚焦且不为空时展示, 默认值: `focus`"}, "van-search/autofocus": {"type": "boolean", "description": "是否自动聚焦，iOS 系统不支持该属性, 默认值: `false`"}, "van-search/show-action": {"type": "boolean", "description": "是否在搜索框右侧显示取消按钮, 默认值: `false`"}, "van-search/action-text": {"type": "boolean", "description": "取消按钮文字, 默认值: `取消`"}, "van-search/disabled": {"type": "boolean", "description": "是否禁用输入框, 默认值: `false`"}, "van-search/readonly": {"type": "boolean", "description": "是否将输入框设为只读, 默认值: `false`"}, "van-search/error": {"type": "boolean", "description": "是否将输入内容标红, 默认值: `false`"}, "van-search/input-align": {"type": "string", "description": "输入框内容对齐方式，可选值为 `center` `right`, 默认值: `left`"}, "van-search/left-icon": {"type": "string", "description": "输入框左侧[图标名称](#/zh-CN/icon)或图片链接, 默认值: `search`"}, "van-search/right-icon": {"type": "string", "description": "输入框右侧[图标名称](#/zh-CN/icon)或图片链接, 默认值: -"}, "van-share-sheet/options": {"type": "Option[]", "description": "分享选项, 默认值: `[]`"}, "van-share-sheet/title": {"type": "string", "description": "顶部标题, 默认值: -"}, "van-share-sheet/cancel-text": {"type": "string", "description": "取消按钮文字，传入空字符串可以隐藏按钮, 默认值: `'取消'`"}, "van-share-sheet/description": {"type": "string", "description": "标题下方的辅助描述文字, 默认值: -"}, "van-share-sheet/duration": {"type": "number | string", "description": "动画时长，单位秒, 默认值: `0.3`"}, "van-share-sheet/overlay": {"type": "boolean", "description": "是否显示遮罩层, 默认值: `true`"}, "van-share-sheet/lock-scroll": {"type": "boolean", "description": "是否锁定背景滚动, 默认值: `true`"}, "van-share-sheet/lazy-render": {"type": "boolean", "description": "是否在显示弹层时才渲染内容, 默认值: `true`"}, "van-share-sheet/close-on-popstate": {"type": "boolean", "description": "是否在页面回退时自动关闭, 默认值: `true`"}, "van-share-sheet/close-on-click-overlay": {"type": "boolean", "description": "是否在点击遮罩层后关闭, 默认值: `true`"}, "van-share-sheet/safe-area-inset-bottom": {"type": "boolean", "description": "是否开启[底部安全区适配](#/zh-CN/advanced-usage#di-bu-an-quan-qu-gua-pei), 默认值: `true`"}, "van-share-sheet/get-container": {"type": "string | () => Element", "description": "指定挂载的节点，[用法示例](#/zh-CN/popup#zhi-ding-gua-zai-wei-zhi), 默认值: -"}, "van-sidebar/v-model": {"type": "number | string", "description": "当前导航项的索引, 默认值: `0`"}, "van-sidebar-item/title": {"type": "string", "description": "内容, 默认值: `''`"}, "van-sidebar-item/dot": {"type": "boolean", "description": "是否显示右上角小红点, 默认值: `false`"}, "van-sidebar-item/badge": {"type": "number | string", "description": "图标右上角徽标的内容, 默认值: -"}, "van-sidebar-item/info": {"type": "number | string", "description": "图标右上角徽标的内容（已废弃，请使用 badge 属性）, 默认值: -"}, "van-sidebar-item/disabled": {"type": "boolean", "description": "是否禁用该项, 默认值: `false`"}, "van-sidebar-item/url": {"type": "string", "description": "点击后跳转的链接地址, 默认值: -"}, "van-sidebar-item/to": {"type": "string | object", "description": "点击后跳转的目标路由对象，同 vue-router 的 [to 属性](https://router.vuejs.org/zh/api/#to), 默认值: -"}, "van-sidebar-item/replace": {"type": "boolean", "description": "是否在跳转时替换当前页面历史, 默认值: `false`"}, "van-skeleton/row": {"type": "number | string", "description": "段落占位图行数, 默认值: `0`"}, "van-skeleton/row-width": {"type": "number | string \\", "description": "段落占位图宽度，可传数组来设置每一行的宽度, 默认值: <br>(number \\"}, "van-skeleton/title": {"type": "boolean", "description": "是否显示标题占位图, 默认值: `false`"}, "van-skeleton/avatar": {"type": "boolean", "description": "是否显示头像占位图, 默认值: `false`"}, "van-skeleton/loading": {"type": "boolean", "description": "是否显示骨架屏，传 `false` 时会展示子组件内容, 默认值: `true`"}, "van-skeleton/animate": {"type": "boolean", "description": "是否开启动画, 默认值: `true`"}, "van-skeleton/round": {"type": "boolean", "description": "是否将标题和段落显示为圆角风格, 默认值: `false`"}, "van-skeleton/title-width": {"type": "number | string", "description": "标题占位图宽度, 默认值: `40%`"}, "van-skeleton/avatar-size": {"type": "number | string", "description": "头像占位图大小, 默认值: `32px`"}, "van-skeleton/avatar-shape": {"type": "string", "description": "头像占位图形状，可选值为`square`, 默认值: `round`"}, "van-sku/v-model": {"type": "boolean", "description": "是否显示商品规格弹窗, 默认值: `false`"}, "van-sku/sku": {"type": "object", "description": "商品 sku 数据, 默认值: -"}, "van-sku/goods": {"type": "object", "description": "商品信息, 默认值: -"}, "van-sku/goods-id": {"type": "number | string", "description": "商品 id, 默认值: -"}, "van-sku/price-tag": {"type": "string", "description": "显示在价格后面的标签, 默认值: -"}, "van-sku/hide-stock": {"type": "boolean", "description": "是否显示商品剩余库存, 默认值: `false`"}, "van-sku/hide-quota-text": {"type": "boolean", "description": "是否显示限购提示, 默认值: `false`"}, "van-sku/hide-selected-text": {"type": "boolean", "description": "是否隐藏已选提示, 默认值: `false`"}, "van-sku/stock-threshold": {"type": "boolean", "description": "库存阈值。低于这个值会把库存数高亮显示, 默认值: `50`"}, "van-sku/show-add-cart-btn": {"type": "boolean", "description": "是否显示加入购物车按钮, 默认值: `true`"}, "van-sku/buy-text": {"type": "string", "description": "购买按钮文字, 默认值: `立即购买`"}, "van-sku/add-cart-text": {"type": "string", "description": "加入购物车按钮文字, 默认值: `加入购物车`"}, "van-sku/quota": {"type": "number", "description": "限购数，0 表示不限购, 默认值: `0`"}, "van-sku/quota-used": {"type": "number", "description": "已经购买过的数量, 默认值: `0`"}, "van-sku/reset-stepper-on-hide": {"type": "boolean", "description": "隐藏时重置选择的商品数量, 默认值: `false`"}, "van-sku/reset-selected-sku-on-hide": {"type": "boolean", "description": "隐藏时重置已选择的 sku, 默认值: `false`"}, "van-sku/disable-stepper-input": {"type": "boolean", "description": "是否禁用步进器输入, 默认值: `false`"}, "van-sku/close-on-click-overlay": {"type": "boolean", "description": "是否在点击遮罩层后关闭, 默认值: `true`"}, "van-sku/stepper-title": {"type": "string", "description": "数量选择组件左侧文案, 默认值: `购买数量`"}, "van-sku/custom-stepper-config": {"type": "object", "description": "步进器相关自定义配置, 默认值: `{}`"}, "van-sku/message-config": {"type": "object", "description": "留言相关配置, 默认值: `{}`"}, "van-sku/get-container": {"type": "string | () => Element", "description": "指定挂载的节点，[用法示例](#/zh-CN/popup#zhi-ding-gua-zai-wei-zhi), 默认值: -"}, "van-sku/initial-sku": {"type": "object", "description": "默认选中的 sku，具体参考高级用法, 默认值: `{}`"}, "van-sku/show-soldout-sku": {"type": "boolean", "description": "是否展示售罄的 sku，默认展示并置灰, 默认值: `true`"}, "van-sku/disable-soldout-sku": {"type": "boolean", "description": "是否禁用售罄的 sku, 默认值: `true`"}, "van-sku/safe-area-inset-bottom": {"type": "boolean", "description": "是否开启[底部安全区适配](#/zh-CN/advanced-usage#di-bu-an-quan-qu-gua-pei), 默认值: `true`"}, "van-sku/start-sale-num": {"type": "number", "description": "起售数量, 默认值: `1`"}, "van-sku/properties": {"type": "array", "description": "商品属性, 默认值: -"}, "van-sku/preview-on-click-image": {"type": "boolean", "description": "是否在点击商品图片时自动预览, 默认值: `true`"}, "van-sku/show-header-image": {"type": "boolean", "description": "是否展示头部图片, 默认值: `true`"}, "van-sku/lazy-load": {"type": "boolean", "description": "是否开启图片懒加载，须配合 [Lazyload](#/zh-CN/lazyload) 组件使用, 默认值: `false`"}, "van-slider/value": {"type": "number | array", "description": "当前进度百分比, 默认值: `0`"}, "van-slider/max": {"type": "number | string", "description": "最大值, 默认值: `100`"}, "van-slider/min": {"type": "number | string", "description": "最小值, 默认值: `0`"}, "van-slider/step": {"type": "number | string", "description": "步长, 默认值: `1`"}, "van-slider/bar-height": {"type": "number | string", "description": "进度条高度，默认单位为`px`, 默认值: `2px`"}, "van-slider/button-size": {"type": "number | string", "description": "滑块按钮大小，默认单位为`px`, 默认值: `24px`"}, "van-slider/active-color": {"type": "string", "description": "进度条激活态颜色, 默认值: `#1989fa`"}, "van-slider/inactive-color": {"type": "string", "description": "进度条非激活态颜色, 默认值: `#e5e5e5`"}, "van-slider/range": {"type": "boolean", "description": "是否开启双滑块模式, 默认值: `false`"}, "van-slider/disabled": {"type": "boolean", "description": "是否禁用滑块, 默认值: `false`"}, "van-slider/vertical": {"type": "boolean", "description": "是否垂直展示, 默认值: `false`"}, "van-stepper/v-model": {"type": "number | string", "description": "当前输入的值, 默认值: -"}, "van-stepper/min": {"type": "number | string", "description": "最小值, 默认值: `1`"}, "van-stepper/max": {"type": "number | string", "description": "最大值, 默认值: -"}, "van-stepper/default-value": {"type": "number | string", "description": "初始值，当 v-model 为空时生效, 默认值: `1`"}, "van-stepper/step": {"type": "number | string", "description": "步长，每次点击时改变的值, 默认值: `1`"}, "van-stepper/name": {"type": "number | string", "description": "标识符，可以在`change`事件回调参数中获取, 默认值: -"}, "van-stepper/input-width": {"type": "number | string", "description": "输入框宽度，默认单位为`px`, 默认值: `32px`"}, "van-stepper/button-size": {"type": "number | string", "description": "按钮大小以及输入框高度，默认单位为`px`, 默认值: `28px`"}, "van-stepper/decimal-length": {"type": "number | string", "description": "固定显示的小数位数, 默认值: -"}, "van-stepper/theme": {"type": "string", "description": "样式风格，可选值为 `round`, 默认值: -"}, "van-stepper/placeholder": {"type": "string", "description": "输入框占位提示文字, 默认值: -"}, "van-stepper/integer": {"type": "boolean", "description": "是否只允许输入整数, 默认值: `false`"}, "van-stepper/disabled": {"type": "boolean", "description": "是否禁用步进器, 默认值: `false`"}, "van-stepper/disable-plus": {"type": "boolean", "description": "是否禁用增加按钮, 默认值: `false`"}, "van-stepper/disable-minus": {"type": "boolean", "description": "是否禁用减少按钮, 默认值: `false`"}, "van-stepper/disable-input": {"type": "boolean", "description": "是否禁用输入框, 默认值: `false`"}, "van-stepper/async-change": {"type": "boolean", "description": "是否开启异步变更，开启后需要手动控制输入值, 默认值: `false`"}, "van-stepper/show-plus": {"type": "boolean", "description": "是否显示增加按钮, 默认值: `true`"}, "van-stepper/show-minus": {"type": "boolean", "description": "是否显示减少按钮, 默认值: `true`"}, "van-stepper/show-input": {"type": "boolean", "description": "是否显示输入框, 默认值: `true`"}, "van-stepper/long-press": {"type": "boolean", "description": "是否开启长按手势, 默认值: `true`"}, "van-stepper/allow-empty": {"type": "boolean", "description": "是否允许输入的值为空, 默认值: `false`"}, "van-steps/active": {"type": "number | string", "description": "当前步骤, 默认值: `0`"}, "van-steps/direction": {"type": "string", "description": "显示方向，可选值为 `vertical`, 默认值: `horizontal`"}, "van-steps/active-color": {"type": "string", "description": "激活状态颜色, 默认值: `#07c160`"}, "van-steps/inactive-color": {"type": "string", "description": "未激活状态颜色, 默认值: `#969799`"}, "van-steps/active-icon": {"type": "string", "description": "激活状态底部图标，可选值见 [Icon 组件](#/zh-CN/icon), 默认值: `checked`"}, "van-steps/inactive-icon": {"type": "string", "description": "未激活状态底部图标，可选值见 [Icon 组件](#/zh-CN/icon), 默认值: -"}, "van-steps/finish-icon": {"type": "string", "description": "已完成步骤对应的底部图标，优先级高于 `inactive-icon`，可选值见 [Icon 组件](#/zh-CN/icon), 默认值: -"}, "van-steps/icon-prefix": {"type": "string", "description": "图标类名前缀，同 Icon 组件的 [class-prefix 属性](#/zh-CN/icon#props), 默认值: `van-icon`"}, "van-sticky/offset-top": {"type": "number | string", "description": "吸顶时与顶部的距离，支持 `px` `vw` `vh` `rem` 单位，默认 `px`, 默认值: `0`"}, "van-sticky/z-index": {"type": "number | string", "description": "吸顶时的 z-index, 默认值: `99`"}, "van-sticky/container": {"type": "Element", "description": "容器对应的 HTML 节点, 默认值: -"}, "van-submit-bar/price": {"type": "number", "description": "价格（单位分）, 默认值: -"}, "van-submit-bar/decimal-length": {"type": "number | string", "description": "价格小数点位数, 默认值: `2`"}, "van-submit-bar/label": {"type": "string", "description": "价格左侧文案, 默认值: `合计：`"}, "van-submit-bar/suffix-label": {"type": "string", "description": "价格右侧文案, 默认值: -"}, "van-submit-bar/text-align": {"type": "string", "description": "价格文案对齐方向，可选值为 `left`, 默认值: `right`"}, "van-submit-bar/button-text": {"type": "string", "description": "按钮文字, 默认值: -"}, "van-submit-bar/button-type": {"type": "string", "description": "按钮类型, 默认值: `danger`"}, "van-submit-bar/button-color": {"type": "string", "description": "自定义按钮颜色, 默认值: -"}, "van-submit-bar/tip": {"type": "string", "description": "在订单栏上方的提示文案, 默认值: -"}, "van-submit-bar/tip-icon": {"type": "string", "description": "提示文案左侧的[图标名称](#/zh-CN/icon)或图片链接, 默认值: -"}, "van-submit-bar/currency": {"type": "string", "description": "货币符号, 默认值: `¥`"}, "van-submit-bar/disabled": {"type": "boolean", "description": "是否禁用按钮, 默认值: `false`"}, "van-submit-bar/loading": {"type": "boolean", "description": "是否显示将按钮显示为加载中状态, 默认值: `false`"}, "van-submit-bar/safe-area-inset-bottom": {"type": "boolean", "description": "是否开启[底部安全区适配](#/zh-CN/advanced-usage#di-bu-an-quan-qu-gua-pei), 默认值: `true`"}, "van-swipe/autoplay": {"type": "number | string", "description": "自动轮播间隔，单位为 ms, 默认值: -"}, "van-swipe/duration": {"type": "number | string", "description": "动画时长，单位为 ms, 默认值: `500`"}, "van-swipe/initial-swipe": {"type": "number | string", "description": "初始位置索引值, 默认值: `0`"}, "van-swipe/width": {"type": "number | string", "description": "滑块宽度，单位为`px`, 默认值: `auto`"}, "van-swipe/height": {"type": "number | string", "description": "滑块高度，单位为`px`, 默认值: `auto`"}, "van-swipe/loop": {"type": "boolean", "description": "是否开启循环播放, 默认值: `true`"}, "van-swipe/show-indicators": {"type": "boolean", "description": "是否显示指示器, 默认值: `true`"}, "van-swipe/vertical": {"type": "boolean", "description": "是否为纵向滚动, 默认值: `false`"}, "van-swipe/touchable": {"type": "boolean", "description": "是否可以通过手势滑动, 默认值: `true`"}, "van-swipe/stop-propagation": {"type": "boolean", "description": "是否阻止滑动事件冒泡, 默认值: `true`"}, "van-swipe/lazy-render": {"type": "boolean", "description": "是否延迟渲染未展示的轮播, 默认值: `false`"}, "van-swipe/indicator-color": {"type": "string", "description": "指示器颜色, 默认值: `#1989fa`"}, "van-swipe-cell/name": {"type": "number | string", "description": "标识符，可以在事件参数中获取到, 默认值: -"}, "van-swipe-cell/left-width": {"type": "number | string", "description": "指定左侧滑动区域宽度，单位为`px`, 默认值: `auto`"}, "van-swipe-cell/right-width": {"type": "number | string", "description": "指定右侧滑动区域宽度，单位为`px`, 默认值: `auto`"}, "van-swipe-cell/before-close": {"type": "Function", "description": "关闭前的回调函数, 默认值: -"}, "van-swipe-cell/disabled": {"type": "boolean", "description": "是否禁用滑动, 默认值: `false`"}, "van-swipe-cell/stop-propagation": {"type": "boolean", "description": "是否阻止滑动事件冒泡, 默认值: `false`"}, "van-switch/v-model": {"type": "any", "description": "开关选中状态, 默认值: `false`"}, "van-switch/loading": {"type": "boolean", "description": "是否为加载状态, 默认值: `false`"}, "van-switch/disabled": {"type": "boolean", "description": "是否为禁用状态, 默认值: `false`"}, "van-switch/size": {"type": "number | string", "description": "开关尺寸，默认单位为`px`, 默认值: `30px`"}, "van-switch/active-color": {"type": "string", "description": "打开时的背景色, 默认值: `#1989fa`"}, "van-switch/inactive-color": {"type": "string", "description": "关闭时的背景色, 默认值: `white`"}, "van-switch/active-value": {"type": "any", "description": "打开时对应的值, 默认值: `true`"}, "van-switch/inactive-value": {"type": "any", "description": "关闭时对应的值, 默认值: `false`"}, "van-switch-cell/v-model": {"type": "any", "description": "开关状态, 默认值: `false`"}, "van-switch-cell/title": {"type": "string", "description": "左侧标题, 默认值: `''`"}, "van-switch-cell/border": {"type": "boolean", "description": "是否展示单元格内边框, 默认值: `true`"}, "van-switch-cell/cell-size": {"type": "string", "description": "单元格大小，可选值为 `large`, 默认值: -"}, "van-switch-cell/loading": {"type": "boolean", "description": "是否为加载状态, 默认值: `false`"}, "van-switch-cell/disabled": {"type": "boolean", "description": "是否为禁用状态, 默认值: `false`"}, "van-switch-cell/size": {"type": "number | string", "description": "开关尺寸, 默认值: `24px`"}, "van-switch-cell/active-color": {"type": "string", "description": "开关时的背景色, 默认值: `#1989fa`"}, "van-switch-cell/inactive-color": {"type": "string", "description": "开关时的背景色, 默认值: `white`"}, "van-switch-cell/active-value": {"type": "any", "description": "打开时的值, 默认值: `true`"}, "van-switch-cell/inactive-value": {"type": "any", "description": "关闭时的值, 默认值: `false`"}, "van-tabs/v-model": {"type": "number | string", "description": "绑定当前选中标签的标识符, 默认值: `0`"}, "van-tabs/type": {"type": "string", "description": "样式风格类型，可选值为 `card`, 默认值: `line`"}, "van-tabs/color": {"type": "string", "description": "标签主题色, 默认值: `#ee0a24`"}, "van-tabs/background": {"type": "string", "description": "标签栏背景色, 默认值: `white`"}, "van-tabs/duration": {"type": "number | string", "description": "动画时间，单位秒, 默认值: `0.3`"}, "van-tabs/line-width": {"type": "number | string", "description": "底部条宽度，默认单位 `px`, 默认值: `40px`"}, "van-tabs/line-height": {"type": "number | string", "description": "底部条高度，默认单位 `px`, 默认值: `3px`"}, "van-tabs/animated": {"type": "boolean", "description": "是否开启切换标签内容时的转场动画, 默认值: `false`"}, "van-tabs/border": {"type": "boolean", "description": "是否显示标签栏外边框，仅在 `type=\"line\"` 时有效, 默认值: `false`"}, "van-tabs/ellipsis": {"type": "boolean", "description": "是否省略过长的标题文字, 默认值: `true`"}, "van-tabs/sticky": {"type": "boolean", "description": "是否使用粘性定位布局, 默认值: `false`"}, "van-tabs/swipeable": {"type": "boolean", "description": "是否开启手势滑动切换, 默认值: `false`"}, "van-tabs/lazy-render": {"type": "boolean", "description": "是否开启延迟渲染（首次切换到标签时才触发内容渲染）, 默认值: `true`"}, "van-tabs/scrollspy": {"type": "boolean", "description": "是否开启滚动导航, 默认值: `false`"}, "van-tabs/offset-top": {"type": "number | string", "description": "粘性定位布局下与顶部的最小距离，支持 `px` `vw` `vh` `rem` 单位，默认 `px`, 默认值: `0`"}, "van-tabs/swipe-threshold": {"type": "number | string", "description": "滚动阈值，标签数量超过阈值且总宽度超过标签栏宽度时开始横向滚动, 默认值: `5`"}, "van-tabs/title-active-color": {"type": "string", "description": "标题选中态颜色, 默认值: -"}, "van-tabs/title-inactive-color": {"type": "string", "description": "标题默认态颜色, 默认值: -"}, "van-tabs/before-change": {"type": "(name) => boolean | Promise", "description": "切换标签前的回调函数，返回 `false` 可阻止切换，支持返回 Promise, 默认值: -"}, "van-tab/title": {"type": "string", "description": "标题, 默认值: -"}, "van-tab/disabled": {"type": "boolean", "description": "是否禁用标签, 默认值: `false`"}, "van-tab/dot": {"type": "boolean", "description": "是否在标题右上角显示小红点, 默认值: `false`"}, "van-tab/badge": {"type": "number | string", "description": "图标右上角徽标的内容, 默认值: -"}, "van-tab/info": {"type": "number | string", "description": "图标右上角徽标的内容（已废弃，请使用 badge 属性）, 默认值: -"}, "van-tab/name": {"type": "number | string", "description": "标签名称，作为匹配的标识符, 默认值: 标签的索引值"}, "van-tab/url": {"type": "string", "description": "点击后跳转的链接地址, 默认值: -"}, "van-tab/to": {"type": "string | object", "description": "点击后跳转的目标路由对象，同 vue-router 的 [to 属性](https://router.vuejs.org/zh/api/#to), 默认值: -"}, "van-tab/replace": {"type": "boolean", "description": "是否在跳转时替换当前页面历史, 默认值: `false`"}, "van-tab/title-style": {"type": "any", "description": "自定义标题样式, 默认值: -"}, "van-tab/title-class": {"type": "any", "description": "自定义标题类名, 默认值: -"}, "van-tabbar/v-model": {"type": "number | string", "description": "当前选中标签的名称或索引值, 默认值: `0`"}, "van-tabbar/fixed": {"type": "boolean", "description": "是否固定在底部, 默认值: `true`"}, "van-tabbar/border": {"type": "boolean", "description": "是否显示外边框, 默认值: `true`"}, "van-tabbar/z-index": {"type": "number | string", "description": "元素 z-index, 默认值: `1`"}, "van-tabbar/active-color": {"type": "string", "description": "选中标签的颜色, 默认值: `#1989fa`"}, "van-tabbar/inactive-color": {"type": "string", "description": "未选中标签的颜色, 默认值: `#7d7e80`"}, "van-tabbar/route": {"type": "boolean", "description": "是否开启路由模式, 默认值: `false`"}, "van-tabbar/placeholder": {"type": "boolean", "description": "固定在底部时，是否在标签位置生成一个等高的占位元素, 默认值: `false`"}, "van-tabbar/safe-area-inset-bottom": {"type": "boolean", "description": "是否开启[底部安全区适配](#/zh-CN/advanced-usage#di-bu-an-quan-qu-gua-pei)，设置 fixed 时默认开启, 默认值: `false`"}, "van-tabbar/before-change": {"type": "(name) => boolean | Promise", "description": "切换标签前的回调函数，返回 `false` 可阻止切换，支持返回 Promise, 默认值: -"}, "van-tabbar-item/name": {"type": "number | string", "description": "标签名称，作为匹配的标识符, 默认值: 当前标签的索引值"}, "van-tabbar-item/icon": {"type": "string", "description": "[图标名称](#/zh-CN/icon)或图片链接, 默认值: -"}, "van-tabbar-item/icon-prefix": {"type": "string", "description": "图标类名前缀，同 Icon 组件的 [class-prefix 属性](#/zh-CN/icon#props), 默认值: `van-icon`"}, "van-tabbar-item/dot": {"type": "boolean", "description": "是否显示图标右上角小红点, 默认值: `false`"}, "van-tabbar-item/badge": {"type": "number | string", "description": "图标右上角徽标的内容, 默认值: -"}, "van-tabbar-item/info": {"type": "number | string", "description": "图标右上角徽标的内容（已废弃，请使用 badge 属性）, 默认值: -"}, "van-tabbar-item/url": {"type": "string", "description": "点击后跳转的链接地址, 默认值: -"}, "van-tabbar-item/to": {"type": "string | object", "description": "点击后跳转的目标路由对象，同 vue-router 的 [to 属性](https://router.vuejs.org/zh/api/#to), 默认值: -"}, "van-tabbar-item/replace": {"type": "boolean", "description": "是否在跳转时替换当前页面历史, 默认值: `false`"}, "van-tag/type": {"type": "string", "description": "类型，可选值为`primary` `success` `danger` `warning`, 默认值: `default`"}, "van-tag/size": {"type": "string", "description": "大小, 可选值为`large` `medium`, 默认值: -"}, "van-tag/color": {"type": "string", "description": "标签颜色, 默认值: -"}, "van-tag/plain": {"type": "boolean", "description": "是否为空心样式, 默认值: `false`"}, "van-tag/round": {"type": "boolean", "description": "是否为圆角样式, 默认值: `false`"}, "van-tag/mark": {"type": "boolean", "description": "是否为标记样式, 默认值: `false`"}, "van-tag/text-color": {"type": "string", "description": "文本颜色，优先级高于`color`属性, 默认值: `white`"}, "van-tag/closeable": {"type": "boolean", "description": "是否为可关闭标签, 默认值: `false`"}, "van-tree-select/items": {"type": "Item[]", "description": "分类显示所需的数据, 默认值: `[]`"}, "van-tree-select/height": {"type": "number | string", "description": "高度，默认单位为`px`, 默认值: `300`"}, "van-tree-select/main-active-index": {"type": "number | string", "description": "左侧选中项的索引, 默认值: `0`"}, "van-tree-select/active-id": {"type": "number | string \\", "description": "右侧选中项的 id，支持传入数组, 默认值: <br>(number \\"}, "van-tree-select/max": {"type": "number | string", "description": "右侧项最大选中个数, 默认值: `Infinity`"}, "van-tree-select/selected-icon": {"type": "string", "description": "自定义右侧栏选中状态的图标, 默认值: `success`"}, "van-uploader/v-model (fileList)": {"type": "FileListItem[]", "description": "已上传的文件列表, 默认值: -"}, "van-uploader/accept": {"type": "string", "description": "允许上传的文件类型，[详细说明](https://developer.mozilla.org/zh-CN/docs/Web/HTML/Element/Input/file#%E9%99%90%E5%88%B6%E5%85%81%E8%AE%B8%E7%9A%84%E6%96%87%E4%BB%B6%E7%B1%BB%E5%9E%8B), 默认值: `image/*`"}, "van-uploader/name": {"type": "number | string", "description": "标识符，可以在回调函数的第二项参数中获取, 默认值: -"}, "van-uploader/preview-size": {"type": "number | string", "description": "预览图和上传区域的尺寸，默认单位为 `px`, 默认值: `80px`"}, "van-uploader/preview-image": {"type": "boolean", "description": "是否在上传完成后展示预览图, 默认值: `true`"}, "van-uploader/preview-full-image": {"type": "boolean", "description": "是否在点击预览图后展示全屏图片预览, 默认值: `true`"}, "van-uploader/preview-options": {"type": "object", "description": "全屏图片预览的配置项，可选值见 [ImagePreview](#/zh-CN/image-preview), 默认值: -"}, "van-uploader/multiple": {"type": "boolean", "description": "是否开启图片多选，部分安卓机型不支持, 默认值: `false`"}, "van-uploader/disabled": {"type": "boolean", "description": "是否禁用文件上传, 默认值: `false`"}, "van-uploader/readonly": {"type": "boolean", "description": "是否将上传区域设置为只读状态, 默认值: `false`"}, "van-uploader/deletable": {"type": "boolean", "description": "是否展示删除按钮, 默认值: `true`"}, "van-uploader/show-upload": {"type": "boolean", "description": "是否展示上传区域, 默认值: `true`"}, "van-uploader/lazy-load": {"type": "boolean", "description": "是否开启图片懒加载，须配合 [Lazyload](#/zh-CN/lazyload) 组件使用, 默认值: `false`"}, "van-uploader/capture": {"type": "string", "description": "图片选取模式，可选值为 `camera` (直接调起摄像头), 默认值: -"}, "van-uploader/after-read": {"type": "Function", "description": "文件读取完成后的回调函数, 默认值: -"}, "van-uploader/before-read": {"type": "Function", "description": "文件读取前的回调函数，返回 `false` 可终止文件读取，<br>支持返回 `Promise`, 默认值: -"}, "van-uploader/before-delete": {"type": "Function", "description": "文件删除前的回调函数，返回 `false` 可终止文件读取，<br>支持返回 `Promise`, 默认值: -"}, "van-uploader/max-size": {"type": "number | string \\", "description": "文件大小限制，单位为 `byte`, 默认值: (file: File) => boolean_"}, "van-uploader/max-count": {"type": "number | string", "description": "文件上传数量限制, 默认值: -"}, "van-uploader/result-type": {"type": "string", "description": "文件读取结果类型，可选值为 `file` `text`, 默认值: `dataUrl`"}, "van-uploader/upload-text": {"type": "string", "description": "上传区域文字提示, 默认值: -"}, "van-uploader/image-fit": {"type": "string", "description": "预览图裁剪模式，可选值见 [Image](#/zh-CN/image) 组件, 默认值: `cover`"}, "van-uploader/upload-icon": {"type": "string", "description": "上传区域[图标名称](#/zh-CN/icon)或图片链接, 默认值: `photograph`"}}