@import '../style/var';

.van-grid-item {
  position: relative;
  box-sizing: border-box;

  &--square {
    height: 0;
  }

  &__icon {
    font-size: @grid-item-icon-size;
  }

  &__icon-wrapper {
    position: relative;
  }

  &__text {
    color: @grid-item-text-color;
    font-size: @grid-item-text-font-size;
    line-height: 1.5;
    // https://github.com/youzan/vant/issues/3894
    word-break: break-all;
  }

  &__icon + &__text {
    margin-top: @padding-xs;
  }

  &__content {
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    height: 100%;
    padding: @grid-item-content-padding;
    background-color: @grid-item-content-background-color;

    &::after {
      z-index: 1;
      border-width: 0 @border-width-base @border-width-base 0;
    }

    &--square {
      position: absolute;
      top: 0;
      right: 0;
      left: 0;
    }

    &--center {
      align-items: center;
      justify-content: center;
    }

    &--horizontal {
      flex-direction: row;

      .van-grid-item__icon + .van-grid-item__text {
        margin-top: 0;
        margin-left: @padding-xs;
      }
    }

    &--surround {
      &::after {
        border-width: @border-width-base;
      }
    }

    &--clickable {
      cursor: pointer;

      &:active {
        background-color: @grid-item-content-active-color;
      }
    }
  }
}
