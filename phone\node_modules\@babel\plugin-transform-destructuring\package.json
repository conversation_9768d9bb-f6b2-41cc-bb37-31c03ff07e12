{"_args": [["@babel/plugin-transform-destructuring@7.27.1", "F:\\桌面\\上海海洋大屏2期\\phone"]], "_development": true, "_from": "@babel/plugin-transform-destructuring@7.27.1", "_id": "@babel/plugin-transform-destructuring@7.27.1", "_inBundle": false, "_integrity": "sha512-ttDCqhfvpE9emVkXbPD8vyxxh4TWYACVybGkDj+oReOGwnp066ITEivDlLwe0b1R0+evJ13IXQuLNB5w1fhC5Q==", "_location": "/@babel/plugin-transform-destructuring", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@babel/plugin-transform-destructuring@7.27.1", "name": "@babel/plugin-transform-destructuring", "escapedName": "@babel%2fplugin-transform-destructuring", "scope": "@babel", "rawSpec": "7.27.1", "saveSpec": null, "fetchSpec": "7.27.1"}, "_requiredBy": ["/@babel/plugin-transform-object-rest-spread", "/@babel/preset-env"], "_resolved": "https://registry.npmjs.org/@babel/plugin-transform-destructuring/-/plugin-transform-destructuring-7.27.1.tgz", "_spec": "7.27.1", "_where": "F:\\桌面\\上海海洋大屏2期\\phone", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "description": "Compile ES2015 destructuring to ES5", "devDependencies": {"@babel/core": "^7.27.1", "@babel/helper-plugin-test-runner": "^7.27.1", "@babel/traverse": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-destructuring", "keywords": ["babel-plugin"], "license": "MIT", "main": "./lib/index.js", "name": "@babel/plugin-transform-destructuring", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-destructuring"}, "type": "commonjs", "version": "7.27.1"}