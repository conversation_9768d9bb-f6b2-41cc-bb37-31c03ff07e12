import Vue from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
import './assets/style/index.less'
import components  from './components'
import dayjs from 'dayjs'
import AMapLoader from '@amap/amap-jsapi-loader'
import Notifications from "vue-notification"
import axios from 'axios'
import * as echarts from 'echarts'
import EChartsPlugin from '../src/plugins/EChartsConfig'
import VScaleScreen from 'v-scale-screen'
import deviceDetector from '@/utils/deviceDetect'

// 桌面端默认加载 Element UI
import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
Vue.use(ElementUI)

// 移动端动态加载 Vant
if (deviceDetector.isMobile()) {
  import('vant').then(Vant => {
    Vue.use(Vant.default)
  })
  import('vant/lib/index.css')
}


Vue.prototype.$axios = axios
Vue.prototype.$dayjs = dayjs
Vue.prototype.$AMapLoader = AMapLoader
Vue.prototype.$echarts = echarts
Vue.prototype.$deviceDetector = deviceDetector

Vue.config.productionTip = false
Vue.use(Notifications)
Vue.use(components)
Vue.use(EChartsPlugin)
Vue.use(VScaleScreen)

new Vue({
  router,
  store,
  created() {
    // 初始化 Store 状态
    this.$store.dispatch('initializeStore')

    // 初始化设备信息到store
    this.$store.dispatch('device/updateDeviceInfo', deviceDetector.getDeviceInfo())

    // 监听窗口大小变化
    deviceDetector.onResize((deviceInfo) => {
      this.$store.dispatch('device/updateDeviceInfo', deviceInfo)
    })
  },
  render: h => h(App)
}).$mount('#app')
