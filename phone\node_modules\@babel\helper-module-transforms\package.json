{"_args": [["@babel/helper-module-transforms@7.27.1", "F:\\桌面\\上海海洋大屏2期\\phone"]], "_development": true, "_from": "@babel/helper-module-transforms@7.27.1", "_id": "@babel/helper-module-transforms@7.27.1", "_inBundle": false, "_integrity": "sha512-9yHn519/8KvTU5BjTVEEeIM3w9/2yXNKoD82JifINImhpKkARMJKPP59kLo+BafpdN5zgNeIcS4jsGDmd3l58g==", "_location": "/@babel/helper-module-transforms", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@babel/helper-module-transforms@7.27.1", "name": "@babel/helper-module-transforms", "escapedName": "@babel%2fhelper-module-transforms", "scope": "@babel", "rawSpec": "7.27.1", "saveSpec": null, "fetchSpec": "7.27.1"}, "_requiredBy": ["/@babel/core", "/@babel/plugin-transform-modules-amd", "/@babel/plugin-transform-modules-commonjs", "/@babel/plugin-transform-modules-systemjs", "/@babel/plugin-transform-modules-umd"], "_resolved": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.27.1.tgz", "_spec": "7.27.1", "_where": "F:\\桌面\\上海海洋大屏2期\\phone", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "dependencies": {"@babel/helper-module-imports": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1", "@babel/traverse": "^7.27.1"}, "description": "Babel helper functions for implementing ES6 module transformations", "devDependencies": {"@babel/core": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "license": "MIT", "main": "./lib/index.js", "name": "@babel/helper-module-transforms", "peerDependencies": {"@babel/core": "^7.0.0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-helper-module-transforms"}, "type": "commonjs", "version": "7.27.1"}