@import '../style/var';

.van-progress {
  position: relative;
  height: @progress-height;
  background: @progress-background-color;
  border-radius: @progress-height;

  &__portion {
    position: absolute;
    left: 0;
    height: 100%;
    background: @progress-color;
    border-radius: inherit;
  }

  &__pivot {
    position: absolute;
    top: 50%;
    box-sizing: border-box;
    min-width: 3.6em;
    padding: @progress-pivot-padding;
    color: @progress-pivot-text-color;
    font-size: @progress-pivot-font-size;
    line-height: @progress-pivot-line-height;
    text-align: center;
    word-break: keep-all;
    background-color: @progress-pivot-background-color;
    border-radius: 1em;
    transform: translate(0, -50%);
  }
}
