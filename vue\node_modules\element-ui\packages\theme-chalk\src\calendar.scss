@import "mixins/mixins";
@import "common/var";
@import "button";
@import "button-group";

@include b(calendar) {
  background-color:#fff;

  @include e(header) {
    display: flex;
    justify-content: space-between;
    padding: 12px 20px;
    border-bottom: $--table-border;
  }

  @include e(title) {
    color: #000000;
    align-self: center;
  }

  @include e(body) {
    padding: 12px 20px 35px;
  }
}

@include b(calendar-table) {
  table-layout: fixed;
  width: 100%;

  thead th {
    padding: 12px 0;
    color: $--color-text-regular;
    font-weight: normal;
  }

  &:not(.is-range) {
    td.prev,
    td.next {
      color: $--color-text-placeholder;
    }
  }

  td {
    border-bottom: $--calendar-border;
    border-right: $--calendar-border;
    vertical-align: top;
    transition: background-color 0.2s ease;

    @include when(selected) {
      background-color: $--calendar-selected-background-color;
    }

    @include when(today) {
      color: $--color-primary;
    }
  }

  tr:first-child td {
    border-top: $--calendar-border;
  }

  tr td:first-child {
    border-left: $--calendar-border;
  }

  tr.el-calendar-table__row--hide-border td {
    border-top: none;
  }

  @include b(calendar-day) {
    box-sizing: border-box;
    padding: 8px;
    height: $--calendar-cell-width;
    &:hover {
      cursor: pointer;
      background-color: $--calendar-selected-background-color;
    }
  }
}
