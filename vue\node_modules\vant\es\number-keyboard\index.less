@import '../style/var';

.van-number-keyboard {
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: @number-keyboard-z-index;
  width: 100%;
  padding-bottom: 22px;
  background-color: @number-keyboard-background-color;
  user-select: none;

  &--with-title {
    border-radius: 20px 20px 0 0;
  }

  &__header {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: content-box;
    height: @number-keyboard-title-height;
    padding-top: 6px;
    color: @number-keyboard-title-color;
    font-size: @number-keyboard-title-font-size;
  }

  &__title {
    display: inline-block;
    font-weight: normal;

    &-left {
      position: absolute;
      left: 0;
    }
  }

  &__body {
    display: flex;
    padding: 6px 0 0 6px;
  }

  &__keys {
    display: flex;
    flex: 3;
    flex-wrap: wrap;
  }

  &__close {
    position: absolute;
    right: 0;
    height: 100%;
    padding: @number-keyboard-close-padding;
    color: @number-keyboard-close-color;
    font-size: @number-keyboard-close-font-size;
    background-color: transparent;
    border: none;
    cursor: pointer;

    &:active {
      opacity: @active-opacity;
    }
  }

  &__sidebar {
    display: flex;
    flex: 1;
    flex-direction: column;
  }

  &--unfit {
    padding-bottom: 0;
  }
}

.van-key {
  display: flex;
  align-items: center;
  justify-content: center;
  height: @number-keyboard-key-height;
  font-size: @number-keyboard-key-font-size;
  line-height: 1.5;
  background-color: @white;
  border-radius: @border-radius-lg;
  cursor: pointer;

  &--large {
    // height: 100% can't fill flex parent on legacy safari
    // see: https://stackoverflow.com/questions/33636796
    position: absolute;
    top: 0;
    right: 6px;
    bottom: 6px;
    left: 0;
    height: auto;
  }

  &--blue,
  &--delete {
    font-size: @number-keyboard-delete-font-size;
  }

  &--active {
    background-color: @number-keyboard-key-active-color;
  }

  &--blue {
    color: @number-keyboard-button-text-color;
    background-color: @number-keyboard-button-background-color;

    &.van-key--active {
      background-color: darken(@number-keyboard-button-background-color, 10%);
    }
  }

  &__wrapper {
    position: relative;
    flex: 1;
    flex-basis: 33%;
    box-sizing: border-box;
    padding: 0 6px 6px 0;

    &--wider {
      flex-basis: 66%;
    }
  }

  &__delete-icon {
    width: 32px;
    height: 22px;
  }

  &__collapse-icon {
    width: 30px;
    height: 24px;
  }

  &__loading-icon {
    color: @number-keyboard-button-text-color;
  }
}
