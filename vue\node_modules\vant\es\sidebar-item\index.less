@import '../style/var';

.van-sidebar-item {
  position: relative;
  display: block;
  box-sizing: border-box;
  padding: @sidebar-padding;
  overflow: hidden;
  color: @sidebar-text-color;
  font-size: @sidebar-font-size;
  line-height: @sidebar-line-height;
  background-color: @sidebar-background-color;
  cursor: pointer;
  user-select: none;

  &:active {
    background-color: @sidebar-active-color;
  }

  &__text {
    position: relative;
    display: inline-block;
    // https://github.com/youzan/vant/issues/7455
    word-break: break-all;
  }

  &:not(:last-child)::after {
    border-bottom-width: 1px;
  }

  &--select {
    color: @sidebar-selected-text-color;
    font-weight: @sidebar-selected-font-weight;

    &,
    &:active {
      background-color: @sidebar-selected-background-color;
    }

    &::before {
      position: absolute;
      top: 50%;
      left: 0;
      width: @sidebar-selected-border-width;
      height: @sidebar-selected-border-height;
      background-color: @sidebar-selected-border-color;
      transform: translateY(-50%);
      content: '';
    }
  }

  &--disabled {
    color: @sidebar-disabled-text-color;
    cursor: not-allowed;

    &:active {
      background-color: @sidebar-background-color;
    }
  }
}
