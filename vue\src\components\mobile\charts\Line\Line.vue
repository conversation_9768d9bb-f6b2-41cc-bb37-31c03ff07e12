<template>
    <div ref="chartContainer" style="width: 100%; height: 100%;"></div>
</template>

<script>
import 'echarts-gl'
import { createChartOption } from './option.js'; // 确保路径正确
import * as echarts from 'echarts'

export default {
    props: {
        Data: {
            type: Object,
            required: true
        }
    },
    data() {
        return {
            chartInstance2: null,
            resizeObserver: null,
        };
    },
    watch: {
        Data: {
            handler(newData) {
                if (newData && Object.keys(newData).length > 0) {
                    this.updateChart();
                }
            },
            deep: true,
            immediate: true
        }
    },
    mounted() {
        if (this.$refs.chartContainer) {
            this.chartInstance2 = echarts.init(this.$refs.chartContainer);
            // option && this.chartInstance2.setOption(option, true);
            this.updateChart();
            this.resizeObserver = new ResizeObserver(() => {
                this.chartInstance2 && this.chartInstance2.resize();
            });
            this.resizeObserver.observe(this.$refs.chartContainer);
        }
    },
    methods: {
        updateChart() {
            if (!this.Data || !this.Data.data || !this.Data.xAxisData) {
                console.warn('数据不完整，无法更新图表');
                return;
            }
            const data = this.Data; // 您的数据
            const xAxisData = data.xAxisData; //
            let color = ''
            if (data.id == '1') {
                color = '#fad25f'
            }
            else if (data.id == '2') {
                color = '#51b7de'
            }
            else {
                color = '#fad25f'
            }
            const option = createChartOption(data.data, xAxisData, color, data.data1, '#ff7979', data.data2, '#1dd1a1',data.max,data.min);
            this.chartInstance2 && this.chartInstance2.setOption(option, true);
        },

    },
    beforeDestroy() {
        if (this.chartInstance2) {
            this.chartInstance2.dispose();
        }
        if (this.resizeObserver && this.$refs.chartContainer) {
            this.resizeObserver.unobserve(this.$refs.chartContainer);
            this.resizeObserver.disconnect();
        }
    }
};
</script>
