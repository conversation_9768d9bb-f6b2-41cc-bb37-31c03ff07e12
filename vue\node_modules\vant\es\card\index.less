@import '../style/var';

.van-card {
  position: relative;
  box-sizing: border-box;
  padding: @card-padding;
  color: @card-text-color;
  font-size: @card-font-size;
  background-color: @card-background-color;

  &:not(:first-child) {
    margin-top: @padding-xs;
  }

  &__header {
    display: flex;
  }

  &__thumb {
    position: relative;
    flex: none;
    width: @card-thumb-size;
    height: @card-thumb-size;
    margin-right: @padding-xs;

    img {
      border-radius: @card-thumb-border-radius;
    }
  }

  &__content {
    position: relative;
    display: flex;
    flex: 1;
    flex-direction: column;
    justify-content: space-between;
    min-width: 0; /* hack for flex box ellipsis */
    min-height: @card-thumb-size;

    &--centered {
      justify-content: center;
    }
  }

  &__title,
  &__desc {
    word-wrap: break-word;
  }

  &__title {
    max-height: 32px;
    font-weight: @font-weight-bold;
    line-height: @card-title-line-height;
  }

  &__desc {
    max-height: @card-desc-line-height;
    color: @card-desc-color;
    line-height: @card-desc-line-height;
  }

  &__bottom {
    line-height: @line-height-md;
  }

  &__price {
    display: inline-block;
    color: @card-price-color;
    font-weight: @font-weight-bold;
    font-size: @card-price-font-size;
  }

  &__price-integer {
    font-size: @card-price-integer-font-size;
    font-family: @card-price-font-family;
  }

  &__price-decimal {
    font-family: @card-price-font-family;
  }

  &__origin-price {
    display: inline-block;
    margin-left: 5px;
    color: @card-origin-price-color;
    font-size: @card-origin-price-font-size;
    text-decoration: line-through;
  }

  &__num {
    float: right;
    color: @card-num-color;
  }

  &__tag {
    position: absolute;
    top: 2px;
    left: 0;
  }

  &__footer {
    flex: none;
    text-align: right;

    .van-button {
      margin-left: 5px;
    }
  }
}
