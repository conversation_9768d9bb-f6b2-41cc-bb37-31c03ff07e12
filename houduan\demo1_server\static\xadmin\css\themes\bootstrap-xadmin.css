
/* results table */
.table th {
  background-color: #FAFAFA;
  background-image: -moz-linear-gradient(top, white, #F2F2F2);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(white), to(#F2F2F2));
  background-image: -webkit-linear-gradient(top, white, #F2F2F2);
  background-image: -o-linear-gradient(top, white, #F2F2F2);
  background-image: linear-gradient(to bottom, white, #F2F2F2);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffffffff', endColorstr='#fff2f2f2', GradientType=0);
  -webkit-box-shadow: 0 1px 4px rgba(0, 0, 0, 0.065);
  -moz-box-shadow: 0 1px 4px rgba(0, 0, 0, 0.065);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.065);
  border-bottom-width: 1px !important;
}

/** panel **/
.panel-default {
  border-radius: 2px;
}
.panel-default > .panel-heading {
  background-color: #F8F8F8;
  background-image: -moz-linear-gradient(top, #FDFDFD, #F6F6F6);
  background-image: -ms-linear-gradient(top, #FDFDFD, #F6F6F6);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#FDFDFD), to(#F6F6F6));
  background-image: -webkit-linear-gradient(top, #FDFDFD, #F6F6F6);
  background-image: -o-linear-gradient(top, #FDFDFD, #F6F6F6);
  background-image: linear-gradient(top, #FDFDFD, #F6F6F6);
}

.form-group {
  border-bottom: 1px solid #EEE;
  background-color: #FBFBFB;
  margin-top: 0px !important;
  margin-bottom: 0px !important;
}
.controls {
  background-color: white;
  padding: 15px !important;
}
.control-label {
  margin-bottom: 0px !important;
}
.fieldset .panel-body {
  padding-top: 0px !important;
  padding-bottom: 0px !important;
}
.form-inline .form-group {
  background: transparent;
  border: none;
}

/** fieldset **/
@media (min-width: 768px) {
  .form-horizontal .controls{
    border-left: 1px solid #EEE;
  }
  .form-horizontal .control-label {
    padding-top: 22px !important;
  }
}