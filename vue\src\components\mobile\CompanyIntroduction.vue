<template>
  <div class="company-container">
    <section-title title="企业介绍" class="white-title" />
    <div class="company-intro">
      <p>上海城市电力发展有限公司是一家集水产养殖、新能源、农业种植、农业休闲旅游观光的综合性开发公司。</p>
      <p>公司以发展绿色能源、生态养殖为使命，严格按照绿色、环保、生态的总体定位和产学研科研创新发展战略，现有主要养殖品种有加州鲈鱼、河豚、南美白对虾等，养殖占地面积3390.6亩，亩产效益达15000元以上。</p>
      <p>近年来，公司致力于实现从低端养殖向高端养殖转型，以智慧农业信息技术、现代农业种养殖技术、农产品深加工技术、农业生物技术、农业生产自动化智能化技术等全力推进MAP战略，建立多方共赢的现代农业服务"生态圈"。</p>
    </div>
  </div>
</template>

<script>
import SectionTitle from './SectionTitle.vue'

export default {
  name: 'CompanyIntroduction',
  components: {
    SectionTitle
  }
}
</script>

<style lang="less" scoped>
.company-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  background-image: url('../ui/subtitled-bg.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.company-intro {
  font-family: PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #fff;
  line-height: 24px;
  overflow-y: auto;
  flex: 1;
  padding:0 15px;
}

.company-intro p {
  text-indent: 2em;
  margin: 0;
  margin-bottom: 12px;
  padding: 0;
}

.company-intro p:last-child {
  margin-bottom: 0;
}

/* 修改content-block让高度自适应内容 */
.content-block {
  margin: 15px;
  background: rgba(1, 19, 67, 0.8);
  border: 1px solid rgba(67, 200, 255, 0.3);
  border-radius: 4px;
  padding: 15px;
  height: auto; /* 确保高度自适应 */
  min-height: auto; /* 移除最小高度限制 */
  overflow: visible; /* 允许内容溢出 */
}

/* 修改flex-vertical不设置固定高度 */
.flex-vertical {
  display: flex;
  flex-direction: column;
  height: auto; /* 修改为auto */
}

/* 修改line-box去除固定最小高度 */
.line-box {
  flex: 0 1 auto; /* 修改flex属性，不强制扩展 */
  display: flex;
  flex-direction: column;
  min-height: auto; /* 移除最小高度限制 */
}

/* 图表容器可以设置最小高度 */
.info-line {
  background-color: rgba(1, 19, 67, 0.5);
  border: 1px dashed rgba(67, 200, 255, 0.3);
  border-radius: 4px;
  min-height: 150px; /* 为图表保留一定高度 */
  width: 100%;
}

/* 确保equip-box也是自适应的 */
.equip-box {
  position: relative;
  font-size: 12px;
  min-height: auto;
  height: auto;
}
</style> 