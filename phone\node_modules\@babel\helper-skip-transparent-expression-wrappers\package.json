{"_args": [["@babel/helper-skip-transparent-expression-wrappers@7.27.1", "F:\\桌面\\上海海洋大屏2期\\phone"]], "_development": true, "_from": "@babel/helper-skip-transparent-expression-wrappers@7.27.1", "_id": "@babel/helper-skip-transparent-expression-wrappers@7.27.1", "_inBundle": false, "_integrity": "sha512-Tub4ZKEXqbPjXgWLl2+3JpQAYBJ8+ikpQ2Ocj/q/r0LwE3UhENh7EUabyHjz2kCEsrRY83ew2DQdHluuiDQFzg==", "_location": "/@babel/helper-skip-transparent-expression-wrappers", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@babel/helper-skip-transparent-expression-wrappers@7.27.1", "name": "@babel/helper-skip-transparent-expression-wrappers", "escapedName": "@babel%2fhelper-skip-transparent-expression-wrappers", "scope": "@babel", "rawSpec": "7.27.1", "saveSpec": null, "fetchSpec": "7.27.1"}, "_requiredBy": ["/@babel/helper-create-class-features-plugin", "/@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining", "/@babel/plugin-transform-for-of", "/@babel/plugin-transform-optional-chaining", "/@babel/plugin-transform-spread"], "_resolved": "https://registry.npmjs.org/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-7.27.1.tgz", "_spec": "7.27.1", "_where": "F:\\桌面\\上海海洋大屏2期\\phone", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "dependencies": {"@babel/traverse": "^7.27.1", "@babel/types": "^7.27.1"}, "description": "Helper which skips types and parentheses", "engines": {"node": ">=6.9.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "homepage": "https://github.com/babel/babel#readme", "license": "MIT", "main": "./lib/index.js", "name": "@babel/helper-skip-transparent-expression-wrappers", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-helper-skip-transparent-expression-wrappers"}, "type": "commonjs", "version": "7.27.1"}