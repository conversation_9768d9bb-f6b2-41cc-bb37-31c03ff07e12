<template>
  <div class="w-100 h-100 p-4 border-box bg-deep color-white font3">
    <div class="form-box border-box bg-cover round-md d-flex justify-content-center">
      <el-form :model="form"
               :rules="rules"
               label-width="auto"
               class="w-60">
        <h2 class="text-center mb-4">用户注册</h2>
        <el-form-item label="姓名"
                      prop="nickname">
          <el-input v-model="form.nickname"
                    placeholder="请输入姓名"></el-input>
        </el-form-item>
        <el-form-item label="账号"
                      prop="username">
          <el-input v-model="form.username"
                    placeholder="请输入账号"></el-input>
        </el-form-item>
        <el-form-item label="密码"
                      prop="password">
          <el-input v-model="form.password"
                    type="password"
                    placeholder="请输入密码"></el-input>
        </el-form-item>
        <el-form-item>
          <div class="text-right">
            <el-button type="primary"
                       @click="register()">注 册</el-button>
            <el-button @click="toLogin">返回登录</el-button>
          </div>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
import { adduser } from '@/request/api';
export default {
  name: 'Register',
  data () {
    return {
      form: {
        nickname: '',
        username: '',
        password: ''
      },
      rules: {
        nickname: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
        username: [{ required: true, message: '请输入登录账号', trigger: 'blur' }],
        password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
      }
    }
  },
  methods: {
    register() {
      adduser(this.form).then(res => {
        console.log(res)
        if (res.code == 200) {
          this.$notify({
            message: "注册成功"
          });
          this.$router.push('/login');
        } else {
          this.$notify({
            type: 'error',
            message: res.message || "注册失败，请稍后再试"
          });
        }
      }).catch(err => {
        console.error(err);
        this.$notify({
          type: 'error',
          message: "注册失败，请稍后再试"
        });
      })
    },
    toLogin() {
      this.$router.push('/login');
    }
  }
}
</script>

<style lang="less" scoped>
@import "~@/assets/style/variable.less";
/deep/ .form-box {
  width: 80%;
  margin: 10% auto 0;
  padding: 6% 0;
  .el-form {
    .el-form-item__label {
      color: @colorWhite;
    }
    .el-input__inner {
      background: rgba(255, 255, 255, 0.1) !important;
      border: none;
      color: @colorWhite!important;
    }
  }
}
</style> 