from django.db import models
import time,threading



# class ReportEquip(models.Model):
#     error_level = (
#         ("Information",'第七级错误等级'),
#         ("Notice",'第六级错误等级'),
#         ("Warning",'第五级错误等级'),
#         ("Error",'第四级错误等级'),
#         ("Critical",'第三级错误等级'),
#         ("Alert",'第二级错误等级'),
#         ("Emergency",'第一级错误等级'),
#     )
#     # error_type =(
#     #     ("硬件",'硬件类告警'),
#     #     ("端口通断类","端口通断类告警"),
#     #     ("协议类","协议类告警"),
#     #     ("性能类","性能类告警"),
#     #     ("其它","其它类型")
#     # )
#     id = models.AutoField(primary_key=True)
#     report_index = models.IntegerField(verbose_name="告警序号",null=True)
#     level = models.CharField(choices=error_level,verbose_name="错误等级",max_length=16)
#     title = models.CharField(max_length=64,verbose_name="名称")
#     keyword = models.CharField(max_length=128,verbose_name="关键字")
#     item = models.CharField(max_length=128,verbose_name="项目",null=True)
#     # type = models.CharField(choices=error_type,verbose_name="错误类别",max_length=10)
#     status = models.BooleanField(verbose_name="是否异常",default=1)
#     vender = models.CharField(max_length=32,verbose_name="厂商")
#     score = models.IntegerField(verbose_name="分数",default=0)
#     enable = models.BooleanField(verbose_name="开启",default=1)
#     delete = models.BooleanField(verbose_name="删除",default=0)


# class Mask(models.Model):
#     id = models.AutoField(primary_key=True)
#     ip = models.TextField()
#     mask_type = (
#         ('ip', "只有ip"),
#         ('ip+', "ip+名称"),
#     )
#     status_type = (
#         ("upcoming", '即将开始'),
#         ("going", '进行中'),
#         ("over",'已结束'),
#     )
#     is_confines = models.CharField(choices=mask_type,verbose_name="是否是ip",default="ip",max_length=10)
#     start_time = models.DateTimeField(null=True)
#     end_time = models.DateTimeField(null=True)
#     status = models.CharField(choices=status_type,default="upcoming",verbose_name="状态码",max_length=16)
#     delete = models.BooleanField(verbose_name="删除",default=0)
#     time = models.DateTimeField(verbose_name='添加时间',auto_now=True)
#     operate = models.CharField(verbose_name='操作人',max_length=100)
#     title = models.CharField(verbose_name='标题',max_length=100)



