# import pymysql,time
#
#
# conn = pymysql.connect(host='************',
#                            user='dsw',
#                            password='Dsw123123!',
#                            port=3306,
#                            db='alert',
#                            charset='utf8mb4',
#                            cursorclass=pymysql.cursors.DictCursor)
#
#
#
#
#
# conn2 = pymysql.connect(host='************',
#                              user='dsw',
#                              password='Dsw123123!',
#                              port = 3306,
#                              db='pt',
#                              charset='utf8mb4',
#                              cursorclass=pymysql.cursors.DictCursor)
#
#
# cursor = conn.cursor()
# cursor2 = conn2.cursor()
#
# cursor.execute('select * from alarm_log where alarm_id > 20000')
# cursor2.execute('select ip from information_equip')
# data = cursor.fetchall()
# data2 = cursor2.fetchall()
# # for i in data:
# #     print(i['alarm_id'],i['alarm_source'],i['alarm_app'],i['alarm_detail'])
# data = cursor.fetchall()
#
# ip_set=set([i['ip'] for i in data2])
# # print(ip_set)
# ip_score={}
# if data:
#     print(int(data[-1]['alarm_id']))
#     cache.set('index', int(data[-1]['alarm_id']), timeout=None)
#     for i in data:
#         # print(i['alarm_id'],i['alarm_source'],i['alarm_app'],i['alarm_detail'])
#         if 'smartping' in i['alarm_app']:
#             # print([ip.split(' ')[0] for ip in i['alarm_detail'].split('|')])
#             ip_list = [ip.split(' ')[0] for ip in i['alarm_detail'].split('|')]
#             if int(i['alarm_clock']) > 157850000 - 60:
#                 for ip in ip_list:
#                     if ip in ip_set:
#                         error_message = cache.get('alert_%s' % ip)
#                         if not error_message:
#                             error_dict['alert_%s' % ip] = ['ping不可达', ]
#                         else:
#                             error_dict['alert_%s' % ip] = error_message.append('ping不可达')
#
#
#         else:
#             ip = i['alarm_detail'].split('\n')[0].split(':')[-1].strip()
#             if ip in ip_set:
#                 error_message = cache.get('alert_%s' % ip)
#                 error = i['alarm_detail'].split('\n')  # 错误
#                 status = i['alarm_detail'].split('\n')  # 状态
#                 if error == 'Session并发会话数大于8000万':
#                     vender = models.Equip.objects.filter(ip=ip).first().vender
#                     error = vender + error
#                 if status != 'ok':
#                     if not error_message:
#                         error_dict['alert_%s' % ip] = [error, ]
#                     else:
#                         error_dict['alert_%s' % ip] = error_message.append(error)
#                 else:
#                     error_dict['alert_%s' % ip].remove(error)
#
#
#             # ip=[ip. for ip in ip_list]
#             # print(ip)
# #
# #
# # print(ip_score)
# import os
# import sys
# from information import models
#
#
# from django.core.cache import cache
#
import os
import sys

# if __name__ == "__main__":
#     os.environ.setdefault("DJANGO_SETTINGS_MODULE", "demo1_server.settings.proj")
#     import django
#     django.setup()
#
#     from information import models
#     import pymysql
#     from django.core.cache import cache
#     from celery import task
#     from demo1_server.settings.constant import SCORE_LIST
#
#     if not cache.get('index'):
#         index=20000
#         print('ruaruarua')
#         ip_set=set([ip['ip'] for ip in models.Equip.objects.filter(judge=1).values('ip')])
#         # for ip in ip_set:
#         #     models.Score.objects.create(
#         #         addr=ip,
#         #         score=100,
#         #         error_message=None
#         #     )
#     else:
#         index = cache.get('index')
#         print('r123123',index)
#     conn = pymysql.connect(host='************',
#                            user='dsw',
#                            password='Dsw123123!',
#                            port=3306,
#                            db='alert',
#                            charset='utf8mb4',
#                            cursorclass=pymysql.cursors.DictCursor)
#     cursor = conn.cursor()
#     cursor.execute('select * from alarm_log where alarm_id > %s'%index)
#
#     data = cursor.fetchall()
#     # print(ip_set)
#     obj_list = models.Equip.objects.filter(judge=True).values('ip')
#     ip_set = set([ip['ip'] for ip in obj_list])
#     error_dict = {}
#     if data:
#         print(int(data[-1]['alarm_id']))
#         cache.set('index',int(data[-1]['alarm_id']),timeout=None)
#         for i in data:
#             # print(i['alarm_id'],i['alarm_source'],i['alarm_app'],i['alarm_detail'])
#             if 'smartping' in i['alarm_app']:
#                 # print([ip.split(' ')[0] for ip in i['alarm_detail'].split('|')])
#                 ip_list = [ip.split(' ')[0] for ip in i['alarm_detail'].split('|')]
#                 if int(i['alarm_clock']) > 157850000 - 60:
#                     for ip in ip_list:
#                         if ip in ip_set:
#                             error_message=cache.get('alert_%s' % ip)
#                             if not error_message:
#                                 error_dict['alert_%s'%ip]=['ping不可达',]
#                             else:
#                                 error_dict['alert_%s'%ip]=error_message.append('ping不可达')
#
#
#             else:
#                 ip=i['alarm_detail'].split('\n')[0].split(':')[-1].strip()
#                 if ip in ip_set:
#                     error_message = cache.get('alert_%s' % ip)
#                     error=i['alarm_detail'].split('\n')  #错误
#                     status=i['alarm_detail'].split('\n') #状态
#                     if error == 'Session并发会话数大于8000万':
#                         vender=models.Equip.objects.filter(ip=ip).first().vender
#                         error=vender+error
#                     if status!='ok':
#                         if not error_message:
#                             error_dict['alert_%s'%ip]=[error,]
#                         else:
#                             error_dict['alert_%s'%ip]=error_message.append(error)
#                     else:
#                         error_dict['alert_%s'%ip].remove(error)
#
#
#
#
#             # elif i['alarm_app'] == 'zabbix':
#             #     ip = i['alarm_detail'].split('\n')[0].split(':')[-1].strip()
#             #     if ip in obj_list:
#             #         print(ip)
#
#             # ip_sum =[]
#             # for obj in obj_list:
#             #     addr =obj.ip
#             #     if addr not in ip_sum:
#             #         ip_sum.append(addr)
#             #         models.Score.objects.create(
#             #             addr = addr
#             #         # )
#
#
#     cache_alert = cache.keys("alert_*")
#     for alert in cache_alert:
#         if (alert not in error_dict) and ('ping不可达' in cache.get(alert)): #60s后没有ping不可达，说明已修复，移除
#             error_dict[alert] = cache.get(alert)
#             error_dict[alert].remove('ping不可达')
#             if not error_dict[alert]:
#                 error_dict.pop(alert)
#                 cache.delete(alert)
#                 # models.Score.objects.create(
#                 #     addr=alert.split('_')[1],
#                 #     score=100,
#                 #     error_message=None
#                 # )
#
#     if error_dict:
#         for ip, error_list in error_dict.items():
#             score_list = []
#             error_had = []
#             alert_score = 0
#             if error_list :
#                 for error in error_list:
#                     if error in SCORE_LIST:
#                         error_had.append(error)
#                         score_list.append(SCORE_LIST[error])
#                 if error_had:
#                     cache.set(ip, error_had)
#                     alert_score=max(score_list)
#             if not error_had:
#                 error_had=None
#             # models.Score.objects.create(
#             #     addr=ip.split('_')[1],
#             #     score=100 - alert_score,
#             #     error_message=error_had
#             # )
