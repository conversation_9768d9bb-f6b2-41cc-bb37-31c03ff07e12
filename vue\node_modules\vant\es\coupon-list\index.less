@import '../style/var';

.van-coupon-list {
  position: relative;
  height: 100%;
  background-color: @coupon-list-background-color;

  &__field {
    padding: @coupon-list-field-padding;

    .van-field__body {
      height: 34px;
      padding-left: @padding-sm;
      line-height: 34px;
      background: @gray-1;
      border-radius: 17px;

      &::placeholder {
        color: @gray-5;
      }
    }

    .van-field__clear {
      margin-right: 0;
    }
  }

  &__exchange-bar {
    display: flex;
    align-items: center;
    background-color: @white;
  }

  &__exchange {
    flex: none;
    height: @coupon-list-exchange-button-height;
    font-size: @font-size-lg;
    line-height: @coupon-list-exchange-button-height - 2px;
    border: 0;
  }

  .van-tabs__wrap {
    box-shadow: 0 6px 12px -12px @gray-6;
  }

  &__list {
    box-sizing: border-box;
    padding: @padding-md 0 @padding-lg;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;

    &--with-bottom {
      padding-bottom: @padding-md + 50px;
    }
  }

  &__bottom {
    position: absolute;
    bottom: 0;
    left: 0;
    z-index: 999;
    box-sizing: border-box;
    width: 100%;
    padding: 5px @padding-md;
    font-weight: @font-weight-bold;
    background-color: @white;
  }

  &__close {
    height: @coupon-list-close-button-height;
  }

  &__empty {
    padding-top: 60px;
    text-align: center;

    p {
      margin: @padding-md 0;
      color: @coupon-list-empty-tip-color;
      font-size: @coupon-list-empty-tip-font-size;
      line-height: @coupon-list-empty-tip-line-height;
    }

    img {
      width: @coupon-list-empty-image-size;
      height: @coupon-list-empty-image-size;
    }
  }
}
