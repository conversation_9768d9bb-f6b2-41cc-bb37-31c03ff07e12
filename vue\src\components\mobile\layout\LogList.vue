<template>
  <div class="w-100 h-100">
    <ul class="area-list">
      <li v-for="(i, index) in localBaselist" :key="index"
        :class="['area-item cursor-pointer', { 'odd-item': index % 2 === 0 }]">
        <div class="item-left">
          <img src="~@/assets/ui/base.png" alt="">
        </div>
        <el-tooltip 
          :content="i.content" 
          placement="top" 
          effect="dark"
          :enterable="false">
          <div class="item-middle">{{ i.content }}</div>
        </el-tooltip>
        <div class="item-right">
          <i class="el-icon-time"></i>{{i.time}}
        </div>
      </li>
    </ul>
  </div>
</template>

<script>
import { Tooltip } from 'element-ui'

export default {
  components: {
    [Tooltip.name]: Tooltip
  },
  props: {
    baselist: {
      type: Array,
      required: true
    },
  },
  data() {
    return {
      localBaselist: []
    }
  },
  watch: {
    baselist: {
      immediate: true,
      handler(newVal) {
        console.log('LogList组件接收到的数据:', newVal);
        this.localBaselist = newVal.map(item => ({
          ...item,
          time: item.time ? item.time.replace('T', ' ') : item.time
        }));
      },
      deep: true
    }
  }
}
</script>

<style lang="less" scoped>
.area-list {
  width: 100%;
  position: relative;
  padding: 0;
  margin: 0;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch; /* 增强iOS滚动体验 */
  height: 100%;
}

.area-item {
  height: 50px;
  display: flex;
  align-items: center;
  font-family: PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #BDDBF1;
  border-bottom: 1px solid rgba(85, 150, 223, 0.2);
  transition: background 0.2s;
}

.area-item:hover {
  background: rgba(67, 200, 255, 0.08);
}

.area-item:nth-child(odd) {
  background-color: rgba(1, 10, 20, .6);
}

.item-left {
  width: 40px;
  display: flex;
  align-items: center;
  justify-content: center;

  img {
    height: 20px;
    width: 20px;
    object-fit: contain;
  }
}

.item-middle {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding-right: 10px;
}

.item-right {
  width: 120px;
  text-align: right;
  padding-right: 15px;
  color: #43C8FF;
  font-size: 12px;
  
  i {
    margin-right: 5px;
  }
}

.odd-item {
  background-color: rgba(1, 10, 20, .6);
}

/deep/ .el-tooltip__popper.is-dark {
  background-color: rgba(0, 0, 0, 0.8);
  z-index: 9999;
}
</style>