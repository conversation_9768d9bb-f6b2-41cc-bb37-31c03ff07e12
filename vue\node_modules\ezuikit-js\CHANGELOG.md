v 0.6.5

[feat] 播放暂停时播放窗口保留最后一帧画面

[feat] 切换地址方法增加参数 accesstoken 用于支持不同账号下设备切换

[fix] 开启录制时切换地址则录制自动完成

v 0.6.4  

[fix]修复地址切换频繁多路流导致画面跳变问题 

[fix]优化多线程版本单个页面创建多路视频播放性能及稳定性  

[fix]修复多线程内存泄露问题  

[fix]修复模板主题回放拖动声音被关闭，设备名称展示不全，全屏高度计算异常问题。  

v 0.6.3  

[feta]支持模板内回放时间轴自定义设置拖动防抖间隔  

[feta]支持模板内网页全屏状态下调用日历切换日期  

[fix]修复单线程模式下，设备断网视频未保留最后一帧问题  

[fix]修复模板内使用电子放大开启中切换设备未重置问题  


v 0.6.2  

[feta] 支持用户通过开启谷歌实验室特性启动多线程解码  

[feta] 支持用户在开放平台控制台（https://open.ys7.com/console/ezuikit/template.html）创建，修改，删除，查询多个模板  

[feta] 优化模板内高清/标清切换缓慢问题。  

[feta] 支持切换主题  

[feta] 支持根据主题ID自定义主题  

[feta] 新增pc端电子放大，移动端双指缩放功能  

[feta] 新增倍速回放功能  


v 0.6.1  

[feta] 支持本地配置解码库，解决远程拉取缓慢问题。  

v 0.6.0  

[fix]兼容低版本火狐浏览器播放视频   

[fix]修复网站全屏模式下，再次执行视频全屏冲突问题  


v 0.5.9  

[feta]支持用户通过开启谷歌实验室特性启动多线程解码   

[feta]新增开启开启谷歌实验室特性引导说明  

v 0.5.8  
[feta-beta]支持多线程解码视频-beta

v 0.5.7  
[feta]新增对讲成功回调，用于国标对讲成功，开启视频声音  

[feta]新增对讲失败回调，返回对讲失败错误信息  

[fix]修复无子账号对讲权限不足，错误未捕获问题  

v 0.5.5  
[fix]修复无子账号对讲权限不足，错误未捕获问题  

[feta]增加设备能力探测，对讲前检测设备能力  

[feta]增加loading状态文案字体大小样式  

[fix]修复乾坤框架下，全局对象获取不到导致对讲失败问题  


v 0.5.4  
[fix]修复视频宽度小于500，按钮过大问题

v 0.5.3  

[feta]兼容部分浏览器formData格式  

[feta]结束对讲释放麦克风  

V 0.5.1  

Date: 2022年3月31日 19:39:49  

[feat]支持切换主题  

[fix]修复对讲结束问题问题  

[feat]移动端主题支持隐藏回放时间轴  
