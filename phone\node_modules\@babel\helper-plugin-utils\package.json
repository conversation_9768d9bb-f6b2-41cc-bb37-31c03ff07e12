{"_args": [["@babel/helper-plugin-utils@7.27.1", "F:\\桌面\\上海海洋大屏2期\\phone"]], "_development": true, "_from": "@babel/helper-plugin-utils@7.27.1", "_id": "@babel/helper-plugin-utils@7.27.1", "_inBundle": false, "_integrity": "sha512-1gn1Up5YXka3YYAHGKpbideQ5Yjf1tDa9qYcgysz+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw==", "_location": "/@babel/helper-plugin-utils", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@babel/helper-plugin-utils@7.27.1", "name": "@babel/helper-plugin-utils", "escapedName": "@babel%2fhelper-plugin-utils", "scope": "@babel", "rawSpec": "7.27.1", "saveSpec": null, "fetchSpec": "7.27.1"}, "_requiredBy": ["/@babel/helper-define-polyfill-provider", "/@babel/plugin-bugfix-firefox-class-in-computed-class-key", "/@babel/plugin-bugfix-safari-class-field-initializer-scope", "/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "/@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining", "/@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly", "/@babel/plugin-proposal-class-properties", "/@babel/plugin-proposal-decorators", "/@babel/plugin-syntax-decorators", "/@babel/plugin-syntax-dynamic-import", "/@babel/plugin-syntax-import-assertions", "/@babel/plugin-syntax-import-attributes", "/@babel/plugin-syntax-jsx", "/@babel/plugin-syntax-unicode-sets-regex", "/@babel/plugin-transform-arrow-functions", "/@babel/plugin-transform-async-generator-functions", "/@babel/plugin-transform-async-to-generator", "/@babel/plugin-transform-block-scoped-functions", "/@babel/plugin-transform-block-scoping", "/@babel/plugin-transform-class-properties", "/@babel/plugin-transform-class-static-block", "/@babel/plugin-transform-classes", "/@babel/plugin-transform-computed-properties", "/@babel/plugin-transform-destructuring", "/@babel/plugin-transform-dotall-regex", "/@babel/plugin-transform-duplicate-keys", "/@babel/plugin-transform-duplicate-named-capturing-groups-regex", "/@babel/plugin-transform-dynamic-import", "/@babel/plugin-transform-exponentiation-operator", "/@babel/plugin-transform-export-namespace-from", "/@babel/plugin-transform-for-of", "/@babel/plugin-transform-function-name", "/@babel/plugin-transform-json-strings", "/@babel/plugin-transform-literals", "/@babel/plugin-transform-logical-assignment-operators", "/@babel/plugin-transform-member-expression-literals", "/@babel/plugin-transform-modules-amd", "/@babel/plugin-transform-modules-commonjs", "/@babel/plugin-transform-modules-systemjs", "/@babel/plugin-transform-modules-umd", "/@babel/plugin-transform-named-capturing-groups-regex", "/@babel/plugin-transform-new-target", "/@babel/plugin-transform-nullish-coalescing-operator", "/@babel/plugin-transform-numeric-separator", "/@babel/plugin-transform-object-rest-spread", "/@babel/plugin-transform-object-super", "/@babel/plugin-transform-optional-catch-binding", "/@babel/plugin-transform-optional-chaining", "/@babel/plugin-transform-parameters", "/@babel/plugin-transform-private-methods", "/@babel/plugin-transform-private-property-in-object", "/@babel/plugin-transform-property-literals", "/@babel/plugin-transform-regenerator", "/@babel/plugin-transform-regexp-modifiers", "/@babel/plugin-transform-reserved-words", "/@babel/plugin-transform-runtime", "/@babel/plugin-transform-shorthand-properties", "/@babel/plugin-transform-spread", "/@babel/plugin-transform-sticky-regex", "/@babel/plugin-transform-template-literals", "/@babel/plugin-transform-typeof-symbol", "/@babel/plugin-transform-unicode-escapes", "/@babel/plugin-transform-unicode-property-regex", "/@babel/plugin-transform-unicode-regex", "/@babel/plugin-transform-unicode-sets-regex", "/@babel/preset-env", "/@babel/preset-modules", "/@vue/babel-plugin-jsx", "/@vue/babel-plugin-resolve-type"], "_resolved": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "_spec": "7.27.1", "_where": "F:\\桌面\\上海海洋大屏2期\\phone", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "description": "General utilities for plugins to use", "devDependencies": {"@babel/core": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-helper-plugin-utils", "license": "MIT", "main": "./lib/index.js", "name": "@babel/helper-plugin-utils", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-helper-plugin-utils"}, "type": "commonjs", "version": "7.27.1"}