.editable-handler .popover{
  width: auto;
}
.editable .popover-title{
  white-space:nowrap; 
}
.editable .popover-title .close {
  margin: -4px 0px 0px 10px;
}
.editable form .btn-ajax{
  display: none;
}
.editable form .control-wrap{
  padding-right: 0px;
}
.editable form{
  margin-bottom: 0px;
}
.editable form .controls{
  margin: 0px;
  padding: 0px !important;
  border: 0px;
}
.editable form .form-group {
  margin: 0px 0px 10px !important;
}
.editable form .control-label{
  display: none;
}
.editable form .controls label {
  white-space:nowrap; 
}

@media (min-width: 768px) {
.editable form .text-field, 
.editable form .textinput {
    width: 200px;
}
.editable form .int-field {
    width: 150px;
}
.editable form .textarea-field{
    width: 250px;
    height: 50px;
}

.editable form select, 
.editable form .select2-container {
  max-width: 200px;
  min-width: 100px;
  width: 100%;
}
}
@media (max-width: 767px) {
  .popover.editpop{
    position: fixed;
    top: 30px !important;
    left: 10px !important;
    right: 10px !important;
    z-index: 1040;
    max-width: none;
  }
}
