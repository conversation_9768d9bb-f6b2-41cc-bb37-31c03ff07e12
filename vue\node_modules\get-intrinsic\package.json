{"name": "get-intrinsic", "version": "1.1.1", "description": "Get and robustly cache all JS language-level intrinsics at first require time", "main": "index.js", "exports": {".": [{"default": "./index.js"}, "./index.js"], "./package.json": "./package.json"}, "scripts": {"prelint": "evalmd README.md", "lint": "eslint --ext=.js,.mjs .", "pretest": "npm run lint", "tests-only": "nyc tape 'test/**/*.js'", "test": "npm run tests-only", "posttest": "aud --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git+https://github.com/ljharb/get-intrinsic.git"}, "keywords": ["javascript", "ecmascript", "es", "js", "intrinsic", "getintrinsic", "es-abstract"], "author": "<PERSON> <<EMAIL>>", "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "bugs": {"url": "https://github.com/ljharb/get-intrinsic/issues"}, "homepage": "https://github.com/ljharb/get-intrinsic#readme", "devDependencies": {"@ljharb/eslint-config": "^17.5.0", "aud": "^1.1.3", "auto-changelog": "^2.2.1", "call-bind": "^1.0.2", "es-abstract": "^1.18.0-next.2", "es-value-fixtures": "^1.0.0", "eslint": "^7.19.0", "evalmd": "^0.0.19", "foreach": "^2.0.5", "has-bigints": "^1.0.1", "make-async-function": "^1.0.0", "make-async-generator-function": "^1.0.0", "make-generator-function": "^2.0.0", "nyc": "^10.3.2", "object-inspect": "^1.9.0", "tape": "^5.1.1"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "dependencies": {"function-bind": "^1.1.1", "has": "^1.0.3", "has-symbols": "^1.0.1"}}