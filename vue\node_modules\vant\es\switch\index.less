@import '../style/var';

.van-switch {
  position: relative;
  display: inline-block;
  box-sizing: content-box;
  width: @switch-width;
  height: @switch-height;
  font-size: @switch-size;
  background-color: @switch-background-color;
  border: @switch-border;
  border-radius: @switch-node-size;
  cursor: pointer;
  transition: background-color @switch-transition-duration;

  &__node {
    position: absolute;
    top: 0;
    left: 0;
    width: @switch-node-size;
    height: @switch-node-size;
    // https://github.com/youzan/vant/issues/9839
    font-size: inherit;
    background-color: @switch-node-background-color;
    border-radius: 100%;
    box-shadow: @switch-node-box-shadow;
    transition: transform @switch-transition-duration
      cubic-bezier(0.3, 1.05, 0.4, 1.05);
  }

  &__loading {
    top: 25%;
    left: 25%;
    width: 50%;
    height: 50%;
    line-height: 1;
  }

  &--on {
    background-color: @switch-on-background-color;

    .van-switch__node {
      transform: translateX(@switch-width - @switch-node-size);
    }

    .van-switch__loading {
      color: @switch-on-background-color;
    }
  }

  &--disabled {
    cursor: not-allowed;
    opacity: @switch-disabled-opacity;
  }

  &--loading {
    cursor: default;
  }
}
