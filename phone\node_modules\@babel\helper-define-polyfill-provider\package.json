{"_args": [["@babel/helper-define-polyfill-provider@0.6.4", "F:\\桌面\\上海海洋大屏2期\\phone"]], "_development": true, "_from": "@babel/helper-define-polyfill-provider@0.6.4", "_id": "@babel/helper-define-polyfill-provider@0.6.4", "_inBundle": false, "_integrity": "sha512-jljfR1rGnXXNWnmQg2K3+bvhkxB51Rl32QRaOTuwwjviGrHzIbSc8+x9CpraDtbT7mfyjXObULP4w/adunNwAw==", "_location": "/@babel/helper-define-polyfill-provider", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@babel/helper-define-polyfill-provider@0.6.4", "name": "@babel/helper-define-polyfill-provider", "escapedName": "@babel%2fhelper-define-polyfill-provider", "scope": "@babel", "rawSpec": "0.6.4", "saveSpec": null, "fetchSpec": "0.6.4"}, "_requiredBy": ["/babel-plugin-polyfill-corejs2", "/babel-plugin-polyfill-corejs3", "/babel-plugin-polyfill-regenerator"], "_resolved": "https://registry.npmjs.org/@babel/helper-define-polyfill-provider/-/helper-define-polyfill-provider-0.6.4.tgz", "_spec": "0.6.4", "_where": "F:\\桌面\\上海海洋大屏2期\\phone", "browser": {"./lib/node/dependencies.js": "./lib/browser/dependencies.js", "./src/node/dependencies.js": "./src/browser/dependencies.js"}, "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "dependencies": {"@babel/helper-compilation-targets": "^7.22.6", "@babel/helper-plugin-utils": "^7.22.5", "debug": "^4.1.1", "lodash.debounce": "^4.0.8", "resolve": "^1.14.2"}, "description": "Babel helper to create your own polyfill provider", "devDependencies": {"@babel/cli": "^7.22.6", "@babel/core": "^7.22.6", "@babel/generator": "^7.22.5", "@babel/plugin-transform-modules-commonjs": "^7.22.5", "@babel/traverse": "^7.22.6", "babel-loader": "^8.1.0", "rollup": "^2.3.2", "rollup-plugin-babel": "^4.4.0", "strip-ansi": "^6.0.0", "webpack": "^4.42.1", "webpack-cli": "^3.3.11"}, "exports": {".": [{"import": {"node": "./esm/index.node.mjs", "browser": "./esm/index.browser.mjs"}, "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "d87c29c909148920ad18690b63d450c561842298", "homepage": "https://github.com/babel/babel-polyfills#readme", "keywords": ["babel-plugin"], "license": "MIT", "main": "lib/index.js", "name": "@babel/helper-define-polyfill-provider", "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel-polyfills.git", "directory": "packages/babel-helper-define-polyfill-provider"}, "version": "0.6.4"}