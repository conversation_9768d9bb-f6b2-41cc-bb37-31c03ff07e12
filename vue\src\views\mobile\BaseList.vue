<template>
  <div class="base-list">
    <section-title title="企业介绍" class="white-title" />
    <van-nav-bar
      :title="baseName"
      left-arrow
      @click-left="onClickLeft"
      class="base-nav"
    />
    <div class="device-list-wrapper">
      <device-list :shebeilist="deviceList" @link-shebei="handleLinkShebei" />
    </div>
  </div>
</template>

<script>
import DeviceList from '../components/DeviceList.vue'

export default {
  name: 'BaseListView',
  components: {
    DeviceList
  },
  data() {
    return {
      baseId: null,
      baseName: '',
      deviceList: {
        water: [
          {
            ID: 1,
            BACILITIES_NAME: '1号养殖池',
            EQUIPMENT_NAME: '水质监测仪',
            STATE: 1
          },
          {
            ID: 2,
            BACILITIES_NAME: '2号养殖池',
            EQUIPMENT_NAME: '溶解氧监测仪',
            STATE: 1
          },
          {
            ID: 3,
            BACILITIES_NAME: '3号养殖池',
            EQUIPMENT_NAME: 'PH值监测仪',
            STATE: 1
          }
        ],
        meteorological: [
          {
            ID: 4,
            BACILITIES_NAME: '养殖基地',
            EQUIPMENT_NAME: '温度监测仪',
            STATE: 1
          },
          {
            ID: 5,
            BACILITIES_NAME: '养殖基地',
            EQUIPMENT_NAME: '湿度监测仪',
            STATE: 1
          }
        ],
        monitoring: [
          {
            ID: 6,
            camera_name: '1号池塘监控',
            state: 1
          },
          {
            ID: 7,
            camera_name: '2号池塘监控',
            state: 0
          },
          {
            ID: 8,
            camera_name: '基地大门监控',
            state: 1
          }
        ]
      }
    }
  },
  created() {
    // 从路由获取基地ID和名称
    if (this.$route.query.base_id) {
      this.baseId = this.$route.query.base_id
    }
    if (this.$route.query.base_name) {
      this.baseName = this.$route.query.base_name
    }
  },
  methods: {
    onClickLeft() {
      this.$router.back()
    },
    handleLinkShebei(type, item, index) {
      console.log('设备类型:', type)
      console.log('设备信息:', item)
      
      // 水质和气象设备跳转到详情页
      if (type === 'water' || type === 'meteorological') {
        this.$router.push({
          path: '/detail',
          query: {
            type: type,
            id: item.ID,
            name: item.EQUIPMENT_NAME
          }
        })
      } else if (type === 'monitoring') {
        // 监控设备跳转到监控画面
        this.$router.push({
          path: '/monitoring',
          query: {
            id: item.ID,
            name: item.camera_name,
            state: item.state
          }
        })
      }
    }
  }
}
</script>

<style lang="less" scoped>
.base-list {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #001528;
  background-image: url('../ui/subtitled-bg.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.base-nav {
  flex-shrink: 0;
  
  :deep(.van-nav-bar__content) {
    background-color: #001528;
  }
  
  :deep(.van-nav-bar__title) {
    color: #C3E4FF;
  }
  
  :deep(.van-icon) {
    color: #C3E4FF !important;
  }
}

.device-list-wrapper {
  flex: 1;
  padding: 0 15px 15px;
  overflow: hidden;
}
</style> 