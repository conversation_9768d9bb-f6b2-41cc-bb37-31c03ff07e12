{"$schema": "https://raw.githubusercontent.com/JetBrains/web-types/master/schema/web-types.json", "framework": "vue", "name": "vant", "version": "2.12.44", "contributions": {"html": {"tags": [{"name": "van-area", "slots": [{"name": "title", "description": "自定义标题内容"}, {"name": "columns-top", "description": "自定义选项上方内容"}, {"name": "columns-bottom", "description": "自定义选项下方内容"}], "events": [{"name": "confirm", "description": "点击右上方完成按钮"}, {"name": "cancel", "description": "点击取消按钮时"}, {"name": "change", "description": "选项改变时触发"}], "attributes": [{"name": "value", "default": "-", "description": "当前选中的省市区`code`", "value": {"type": "string", "kind": "expression"}}, {"name": "title", "default": "-", "description": "顶部栏标题", "value": {"type": "string", "kind": "expression"}}, {"name": "confirm-button-text", "default": "`确认`", "description": "确认按钮文字", "value": {"type": "string", "kind": "expression"}}, {"name": "cancel-button-text", "default": "`取消`", "description": "取消按钮文字", "value": {"type": "string", "kind": "expression"}}, {"name": "area-list", "default": "-", "description": "省市区数据，格式见下方", "value": {"type": "object", "kind": "expression"}}, {"name": "columns-placeholder", "default": "`[]`", "description": "列占位提示文字", "value": {"type": "string[]", "kind": "expression"}}, {"name": "loading", "default": "`false`", "description": "是否显示加载状态", "value": {"type": "boolean", "kind": "expression"}}, {"name": "readonly", "default": "`false`", "description": "是否为只读状态，只读状态下无法切换选项", "value": {"type": "boolean", "kind": "expression"}}, {"name": "item-height", "default": "`44`", "description": "选项高度，支持 `px` `vw` `vh` `rem` 单位，默认 `px`", "value": {"type": "number | string", "kind": "expression"}}, {"name": "columns-num", "default": "`3`", "description": "显示列数，3-省市区，2-省市，1-省", "value": {"type": "number | string", "kind": "expression"}}, {"name": "visible-item-count", "default": "`6`", "description": "可见的选项个数", "value": {"type": "number | string", "kind": "expression"}}, {"name": "swipe-duration", "default": "`1000`", "description": "快速滑动时惯性滚动的时长，单位`ms`", "value": {"type": "number | string", "kind": "expression"}}, {"name": "is-oversea-code", "default": "-", "description": "根据`code`校验海外地址，海外地址会划分至单独的分类", "value": {"type": "() => boolean", "kind": "expression"}}]}, {"name": "van-badge", "slots": [{"name": "default", "description": "徽标包裹的子元素"}, {"name": "content", "description": "自定义徽标内容"}], "events": [], "attributes": [{"name": "content", "default": "-", "description": "徽标内容", "value": {"type": "number | string", "kind": "expression"}}, {"name": "color", "default": "`#ee0a24`", "description": "徽标背景颜色", "value": {"type": "string", "kind": "expression"}}, {"name": "dot", "default": "`false`", "description": "是否展示为小红点", "value": {"type": "boolean", "kind": "expression"}}, {"name": "max", "default": "-", "description": "最大值，超过最大值会显示 `{max}+`，仅当 content 为数字时有效", "value": {"type": "number | string", "kind": "expression"}}]}, {"name": "van-button", "slots": [{"name": "default", "description": "按钮内容"}, {"name": "icon", "description": "自定义图标"}, {"name": "loading", "description": "自定义加载图标"}], "events": [{"name": "click", "description": "点击按钮，且按钮状态不为加载或禁用时触发"}, {"name": "touchstart", "description": "开始触摸按钮时触发"}], "attributes": [{"name": "type", "default": "`default`", "description": "类型，可选值为 `primary` `info` `warning` `danger`", "value": {"type": "string", "kind": "expression"}}, {"name": "size", "default": "`normal`", "description": "尺寸，可选值为 `large` `small` `mini`", "value": {"type": "string", "kind": "expression"}}, {"name": "text", "default": "-", "description": "按钮文字", "value": {"type": "string", "kind": "expression"}}, {"name": "color", "default": "-", "description": "按钮颜色，支持传入 `linear-gradient` 渐变色", "value": {"type": "string", "kind": "expression"}}, {"name": "icon", "default": "-", "description": "左侧[图标名称](#/zh-CN/icon)或图片链接", "value": {"type": "string", "kind": "expression"}}, {"name": "icon-prefix", "default": "`van-icon`", "description": "图标类名前缀，同 Icon 组件的 [class-prefix 属性](#/zh-CN/icon#props)", "value": {"type": "string", "kind": "expression"}}, {"name": "icon-position", "default": "`left`", "description": "图标展示位置，可选值为 `right`", "value": {"type": "string", "kind": "expression"}}, {"name": "tag", "default": "`button`", "description": "按钮根节点的 HTML 标签", "value": {"type": "string", "kind": "expression"}}, {"name": "native-type", "default": "-", "description": "原生 button 标签的 type 属性", "value": {"type": "string", "kind": "expression"}}, {"name": "block", "default": "`false`", "description": "是否为块级元素", "value": {"type": "boolean", "kind": "expression"}}, {"name": "plain", "default": "`false`", "description": "是否为朴素按钮", "value": {"type": "boolean", "kind": "expression"}}, {"name": "square", "default": "`false`", "description": "是否为方形按钮", "value": {"type": "boolean", "kind": "expression"}}, {"name": "round", "default": "`false`", "description": "是否为圆形按钮", "value": {"type": "boolean", "kind": "expression"}}, {"name": "disabled", "default": "`false`", "description": "是否禁用按钮", "value": {"type": "boolean", "kind": "expression"}}, {"name": "hairline", "default": "`false`", "description": "是否使用 0.5px 边框", "value": {"type": "boolean", "kind": "expression"}}, {"name": "loading", "default": "`false`", "description": "是否显示为加载状态", "value": {"type": "boolean", "kind": "expression"}}, {"name": "loading-text", "default": "-", "description": "加载状态提示文字", "value": {"type": "string", "kind": "expression"}}, {"name": "loading-type", "default": "`circular`", "description": "[加载图标类型](#/zh-CN/loading)，可选值为 `spinner`", "value": {"type": "string", "kind": "expression"}}, {"name": "loading-size", "default": "`20px`", "description": "加载图标大小", "value": {"type": "string", "kind": "expression"}}, {"name": "url", "default": "-", "description": "点击后跳转的链接地址", "value": {"type": "string", "kind": "expression"}}, {"name": "to", "default": "-", "description": "点击后跳转的目标路由对象，同 vue-router 的 [to 属性](https://router.vuejs.org/zh/api/#to)", "value": {"type": "string | object", "kind": "expression"}}, {"name": "replace", "default": "`false`", "description": "是否在跳转时替换当前页面历史", "value": {"type": "boolean", "kind": "expression"}}]}, {"name": "van-address-edit", "slots": [{"name": "default", "description": "在邮政编码下方插入内容"}], "events": [{"name": "save", "description": "点击保存按钮时触发"}, {"name": "focus", "description": "输入框聚焦时触发"}, {"name": "delete", "description": "确认删除地址时触发"}, {"name": "cancel-delete", "description": "取消删除地址时触发"}, {"name": "select-search", "description": "选中搜索结果时触发"}, {"name": "click-area", "description": "点击收件地区时触发"}, {"name": "change-area", "description": "修改收件地区时触发"}, {"name": "change-detail", "description": "修改详细地址时触发"}, {"name": "change-default", "description": "切换是否使用默认地址时触发"}], "attributes": [{"name": "area-list", "default": "-", "description": "地区列表", "value": {"type": "object", "kind": "expression"}}, {"name": "area-columns-placeholder", "default": "`[]`", "description": "地区选择列占位提示文字", "value": {"type": "string[]", "kind": "expression"}}, {"name": "area-placeholder", "default": "`选择省 / 市 / 区`", "description": "地区输入框占位提示文字", "value": {"type": "string", "kind": "expression"}}, {"name": "address-info", "default": "`{}`", "description": "收货人信息初始值", "value": {"type": "AddressInfo", "kind": "expression"}}, {"name": "search-result", "default": "`[]`", "description": "详细地址搜索结果", "value": {"type": "SearchResult[]", "kind": "expression"}}, {"name": "show-postal", "default": "`false`", "description": "是否显示邮政编码", "value": {"type": "boolean", "kind": "expression"}}, {"name": "show-delete", "default": "`false`", "description": "是否显示删除按钮", "value": {"type": "boolean", "kind": "expression"}}, {"name": "show-set-default", "default": "`false`", "description": "是否显示默认地址栏", "value": {"type": "boolean", "kind": "expression"}}, {"name": "show-search-result", "default": "`false`", "description": "是否显示搜索结果", "value": {"type": "boolean", "kind": "expression"}}, {"name": "show-area", "default": "`true`", "description": "是否显示地区", "value": {"type": "boolean", "kind": "expression"}}, {"name": "show-detail", "default": "`true`", "description": "是否显示详细地址", "value": {"type": "boolean", "kind": "expression"}}, {"name": "disable-area", "default": "`false`", "description": "是否禁用地区选择", "value": {"type": "boolean", "kind": "expression"}}, {"name": "save-button-text", "default": "`保存`", "description": "保存按钮文字", "value": {"type": "string", "kind": "expression"}}, {"name": "delete-button-text", "default": "`删除`", "description": "删除按钮文字", "value": {"type": "string", "kind": "expression"}}, {"name": "detail-rows", "default": "`1`", "description": "详细地址输入框行数", "value": {"type": "number | string", "kind": "expression"}}, {"name": "detail-maxlength", "default": "`200`", "description": "详细地址最大长度", "value": {"type": "number | string", "kind": "expression"}}, {"name": "is-saving", "default": "`false`", "description": "是否显示保存按钮加载动画", "value": {"type": "boolean", "kind": "expression"}}, {"name": "is-deleting", "default": "`false`", "description": "是否显示删除按钮加载动画", "value": {"type": "boolean", "kind": "expression"}}, {"name": "tel-validator", "default": "-", "description": "手机号格式校验函数", "value": {"type": "string => boolean", "kind": "expression"}}, {"name": "tel-maxlength", "default": "-", "description": "手机号最大长度", "value": {"type": "number | string", "kind": "expression"}}, {"name": "postal-validator", "default": "-", "description": "邮政编码格式校验函数", "value": {"type": "string => boolean", "kind": "expression"}}, {"name": "validator", "default": "-", "description": "自定义校验函数", "value": {"type": "(key, val) => string", "kind": "expression"}}]}, {"name": "van-calendar", "slots": [{"name": "title", "description": "自定义标题"}, {"name": "footer", "description": "自定义底部区域内容"}, {"name": "top-info", "description": "自定义日期上方的提示信息"}, {"name": "bottom-info", "description": "自定义日期下方的提示信息"}], "events": [{"name": "select", "description": "点击并选中任意日期时触发"}, {"name": "confirm", "description": "日期选择完成后触发，若`show-confirm`为`true`，则点击确认按钮后触发"}, {"name": "open", "description": "打开弹出层时触发"}, {"name": "close", "description": "关闭弹出层时触发"}, {"name": "opened", "description": "打开弹出层且动画结束后触发"}, {"name": "closed", "description": "关闭弹出层且动画结束后触发"}, {"name": "unselect", "description": "当日历组件的 `type` 为 `multiple` 时，取消选中日期时触发"}, {"name": "month-show", "description": "当某个月份进入可视区域时触发"}], "attributes": [{"name": "type", "default": "`single`", "description": "选择类型:<br>`single`表示选择单个日期，<br>`multiple`表示选择多个日期，<br>`range`表示选择日期区间", "value": {"type": "string", "kind": "expression"}}, {"name": "title", "default": "`日期选择`", "description": "日历标题", "value": {"type": "string", "kind": "expression"}}, {"name": "color", "default": "`#ee0a24`", "description": "主题色，对底部按钮和选中日期生效", "value": {"type": "string", "kind": "expression"}}, {"name": "min-date", "default": "当前日期", "description": "可选择的最小日期", "value": {"type": "Date", "kind": "expression"}}, {"name": "max-date", "default": "当前日期的六个月后", "description": "可选择的最大日期", "value": {"type": "Date", "kind": "expression"}}, {"name": "default-date", "default": "null_", "description": "默认选中的日期，`type` 为 `multiple` 或 `range` 时为数组，传入 `null` 表示默认不选择", "value": {"type": "Date | Date[] \\", "kind": "expression"}}, {"name": "row-height", "default": "`64`", "description": "日期行高", "value": {"type": "number | string", "kind": "expression"}}, {"name": "formatter", "default": "-", "description": "日期格式化函数", "value": {"type": "(day: Day) => Day", "kind": "expression"}}, {"name": "poppable", "default": "`true`", "description": "是否以弹层的形式展示日历", "value": {"type": "boolean", "kind": "expression"}}, {"name": "lazy-render", "default": "`true`", "description": "是否只渲染可视区域的内容", "value": {"type": "boolean", "kind": "expression"}}, {"name": "show-mark", "default": "`true`", "description": "是否显示月份背景水印", "value": {"type": "boolean", "kind": "expression"}}, {"name": "show-title", "default": "`true`", "description": "是否展示日历标题", "value": {"type": "boolean", "kind": "expression"}}, {"name": "show-subtitle", "default": "`true`", "description": "是否展示日历副标题（年月）", "value": {"type": "boolean", "kind": "expression"}}, {"name": "show-confirm", "default": "`true`", "description": "是否展示确认按钮", "value": {"type": "boolean", "kind": "expression"}}, {"name": "readonly", "default": "`false`", "description": "是否为只读状态，只读状态下不能选择日期", "value": {"type": "boolean", "kind": "expression"}}, {"name": "confirm-text", "default": "`确定`", "description": "确认按钮的文字", "value": {"type": "string", "kind": "expression"}}, {"name": "confirm-disabled-text", "default": "`确定`", "description": "确认按钮处于禁用状态时的文字", "value": {"type": "string", "kind": "expression"}}, {"name": "first-day-of-week", "default": "`0`", "description": "设置周起始日", "value": {"type": "0-6", "kind": "expression"}}]}, {"name": "van-poppable", "slots": [], "events": [], "attributes": [{"name": "v-model", "default": "`false`", "description": "是否显示日历弹窗", "value": {"type": "boolean", "kind": "expression"}}, {"name": "position", "default": "`bottom`", "description": "弹出位置，可选值为 `top` `right` `left`", "value": {"type": "string", "kind": "expression"}}, {"name": "round", "default": "`true`", "description": "是否显示圆角弹窗", "value": {"type": "boolean", "kind": "expression"}}, {"name": "close-on-popstate", "default": "`true`", "description": "是否在页面回退时自动关闭", "value": {"type": "boolean", "kind": "expression"}}, {"name": "close-on-click-overlay", "default": "`true`", "description": "是否在点击遮罩层后关闭", "value": {"type": "boolean", "kind": "expression"}}, {"name": "safe-area-inset-bottom", "default": "`true`", "description": "是否开启[底部安全区适配](#/zh-CN/advanced-usage#di-bu-an-quan-qu-gua-pei)", "value": {"type": "boolean", "kind": "expression"}}, {"name": "get-container", "default": "-", "description": "指定挂载的节点，[用法示例](#/zh-CN/popup#zhi-ding-gua-zai-wei-zhi)", "value": {"type": "string | () => Element", "kind": "expression"}}]}, {"name": "van-range", "slots": [], "events": [], "attributes": [{"name": "max-range", "default": "无限制", "description": "日期区间最多可选天数", "value": {"type": "number | string", "kind": "expression"}}, {"name": "range-prompt", "default": "`选择天数不能超过 xx 天`", "description": "范围选择超过最多可选天数时的提示文案", "value": {"type": "string", "kind": "expression"}}, {"name": "allow-same-day", "default": "`false`", "description": "是否允许日期范围的起止时间为同一天", "value": {"type": "boolean", "kind": "expression"}}]}, {"name": "van-multiple", "slots": [], "events": [], "attributes": [{"name": "max-range", "default": "无限制", "description": "日期最多可选天数", "value": {"type": "number | string", "kind": "expression"}}, {"name": "range-prompt", "default": "`选择天数不能超过 xx 天`", "description": "选择超过最多可选天数时的提示文案", "value": {"type": "string", "kind": "expression"}}]}, {"name": "van-action-sheet", "slots": [{"name": "default", "description": "自定义面板的展示内容"}, {"name": "description", "description": "自定义描述文案"}], "events": [{"name": "select", "description": "点击选项时触发，禁用或加载状态下不会触发"}, {"name": "cancel", "description": "点击取消按钮时触发"}, {"name": "open", "description": "打开面板时触发"}, {"name": "close", "description": "关闭面板时触发"}, {"name": "opened", "description": "打开面板且动画结束后触发"}, {"name": "closed", "description": "关闭面板且动画结束后触发"}, {"name": "click-overlay", "description": "点击遮罩层时触发"}], "attributes": [{"name": "v-model (value)", "default": "`false`", "description": "是否显示动作面板", "value": {"type": "boolean", "kind": "expression"}}, {"name": "actions", "default": "`[]`", "description": "面板选项列表", "value": {"type": "Action[]", "kind": "expression"}}, {"name": "title", "default": "-", "description": "顶部标题", "value": {"type": "string", "kind": "expression"}}, {"name": "cancel-text", "default": "-", "description": "取消按钮文字", "value": {"type": "string", "kind": "expression"}}, {"name": "description", "default": "-", "description": "选项上方的描述信息", "value": {"type": "string", "kind": "expression"}}, {"name": "closeable", "default": "`true`", "description": "是否显示关闭图标", "value": {"type": "boolean", "kind": "expression"}}, {"name": "close-icon", "default": "`cross`", "description": "关闭[图标名称](#/zh-CN/icon)或图片链接", "value": {"type": "string", "kind": "expression"}}, {"name": "duration", "default": "`0.3`", "description": "动画时长，单位秒", "value": {"type": "number | string", "kind": "expression"}}, {"name": "round", "default": "`true`", "description": "是否显示圆角", "value": {"type": "boolean", "kind": "expression"}}, {"name": "overlay", "default": "`true`", "description": "是否显示遮罩层", "value": {"type": "boolean", "kind": "expression"}}, {"name": "lock-scroll", "default": "`true`", "description": "是否锁定背景滚动", "value": {"type": "boolean", "kind": "expression"}}, {"name": "lazy-render", "default": "`true`", "description": "是否在显示弹层时才渲染节点", "value": {"type": "boolean", "kind": "expression"}}, {"name": "close-on-popstate", "default": "`false`", "description": "是否在页面回退时自动关闭", "value": {"type": "boolean", "kind": "expression"}}, {"name": "close-on-click-action", "default": "`false`", "description": "是否在点击选项后关闭", "value": {"type": "boolean", "kind": "expression"}}, {"name": "close-on-click-overlay", "default": "`true`", "description": "是否在点击遮罩层后关闭", "value": {"type": "boolean", "kind": "expression"}}, {"name": "safe-area-inset-bottom", "default": "`true`", "description": "是否开启[底部安全区适配](#/zh-CN/advanced-usage#di-bu-an-quan-qu-gua-pei)", "value": {"type": "boolean", "kind": "expression"}}, {"name": "get-container", "default": "-", "description": "指定挂载的节点，[用法示例](#/zh-CN/popup#zhi-ding-gua-zai-wei-zhi)", "value": {"type": "string | () => Element", "kind": "expression"}}]}, {"name": "van-address-list", "slots": [{"name": "default", "description": "在列表下方插入内容"}, {"name": "top", "description": "在顶部插入内容"}, {"name": "item-bottom", "description": "在列表项底部插入内容"}, {"name": "tag", "description": "列表项标签内容自定义"}], "events": [{"name": "add", "description": "点击新增按钮时触发"}, {"name": "edit", "description": "点击编辑按钮时触发"}, {"name": "select", "description": "切换选中的地址时触发"}, {"name": "edit-disabled", "description": "编辑不可配送的地址时触发"}, {"name": "select-disabled", "description": "选中不可配送的地址时触发"}, {"name": "click-item", "description": "点击任意地址时触发"}], "attributes": [{"name": "v-model", "default": "-", "description": "当前选中地址的 id", "value": {"type": "string", "kind": "expression"}}, {"name": "list", "default": "`[]`", "description": "地址列表", "value": {"type": "Address[]", "kind": "expression"}}, {"name": "disabled-list", "default": "`[]`", "description": "不可配送地址列表", "value": {"type": "Address[]", "kind": "expression"}}, {"name": "disabled-text", "default": "-", "description": "不可配送提示文案", "value": {"type": "string", "kind": "expression"}}, {"name": "switchable", "default": "`true`", "description": "是否允许切换地址", "value": {"type": "boolean", "kind": "expression"}}, {"name": "add-button-text", "default": "`新增地址`", "description": "底部按钮文字", "value": {"type": "string", "kind": "expression"}}, {"name": "default-tag-text", "default": "-", "description": "默认地址标签文字", "value": {"type": "string", "kind": "expression"}}]}, {"name": "van-card", "slots": [{"name": "title", "description": "自定义标题"}, {"name": "desc", "description": "自定义描述"}, {"name": "num", "description": "自定义数量"}, {"name": "price", "description": "自定义价格"}, {"name": "origin-price", "description": "自定义商品原价"}, {"name": "price-top", "description": "自定义价格上方区域"}, {"name": "bottom", "description": "自定义价格下方区域"}, {"name": "thumb", "description": "自定义图片"}, {"name": "tag", "description": "自定义图片角标"}, {"name": "tags", "description": "自定义描述下方标签区域"}, {"name": "footer", "description": "自定义右下角内容"}], "events": [{"name": "click", "description": "点击时触发"}, {"name": "click-thumb", "description": "点击自定义图片时触发"}], "attributes": [{"name": "thumb", "default": "-", "description": "左侧图片 URL", "value": {"type": "string", "kind": "expression"}}, {"name": "title", "default": "-", "description": "标题", "value": {"type": "string", "kind": "expression"}}, {"name": "desc", "default": "-", "description": "描述", "value": {"type": "string", "kind": "expression"}}, {"name": "tag", "default": "-", "description": "图片角标", "value": {"type": "string", "kind": "expression"}}, {"name": "num", "default": "-", "description": "商品数量", "value": {"type": "number | string", "kind": "expression"}}, {"name": "price", "default": "-", "description": "商品价格", "value": {"type": "number | string", "kind": "expression"}}, {"name": "origin-price", "default": "-", "description": "商品划线原价", "value": {"type": "number | string", "kind": "expression"}}, {"name": "centered", "default": "`false`", "description": "内容是否垂直居中", "value": {"type": "boolean", "kind": "expression"}}, {"name": "currency", "default": "`¥`", "description": "货币符号", "value": {"type": "string", "kind": "expression"}}, {"name": "thumb-link", "default": "-", "description": "点击左侧图片后跳转的链接地址", "value": {"type": "string", "kind": "expression"}}, {"name": "lazy-load", "default": "`false`", "description": "是否开启图片懒加载，须配合 [Lazyload](#/zh-CN/lazyload) 组件使用", "value": {"type": "boolean", "kind": "expression"}}]}, {"name": "van-cell-group", "slots": [{"name": "default", "description": "默认插槽"}, {"name": "title", "description": "自定义分组标题"}], "events": [], "attributes": [{"name": "title", "default": "`-`", "description": "分组标题", "value": {"type": "string", "kind": "expression"}}, {"name": "border", "default": "`true`", "description": "是否显示外边框", "value": {"type": "boolean", "kind": "expression"}}]}, {"name": "van-cell", "slots": [{"name": "default", "description": "自定义右侧 value 的内容"}, {"name": "title", "description": "自定义左侧 title 的内容"}, {"name": "label", "description": "自定义标题下方 label 的内容"}, {"name": "icon", "description": "自定义左侧图标"}, {"name": "right-icon", "description": "自定义右侧按钮，默认为`arrow`"}, {"name": "extra", "description": "自定义单元格最右侧的额外内容"}], "events": [{"name": "click", "description": "点击单元格时触发"}], "attributes": [{"name": "title", "default": "-", "description": "左侧标题", "value": {"type": "number | string", "kind": "expression"}}, {"name": "value", "default": "-", "description": "右侧内容", "value": {"type": "number | string", "kind": "expression"}}, {"name": "label", "default": "-", "description": "标题下方的描述信息", "value": {"type": "string", "kind": "expression"}}, {"name": "size", "default": "-", "description": "单元格大小，可选值为 `large`", "value": {"type": "string", "kind": "expression"}}, {"name": "icon", "default": "-", "description": "左侧[图标名称](#/zh-CN/icon)或图片链接", "value": {"type": "string", "kind": "expression"}}, {"name": "icon-prefix", "default": "`van-icon`", "description": "图标类名前缀，同 Icon 组件的 [class-prefix 属性](#/zh-CN/icon#props)", "value": {"type": "string", "kind": "expression"}}, {"name": "url", "default": "-", "description": "点击后跳转的链接地址", "value": {"type": "string", "kind": "expression"}}, {"name": "to", "default": "-", "description": "点击后跳转的目标路由对象，同 vue-router 的 [to 属性](https://router.vuejs.org/zh/api/#to)", "value": {"type": "string | object", "kind": "expression"}}, {"name": "border", "default": "`true`", "description": "是否显示内边框", "value": {"type": "boolean", "kind": "expression"}}, {"name": "replace", "default": "`false`", "description": "是否在跳转时替换当前页面历史", "value": {"type": "boolean", "kind": "expression"}}, {"name": "clickable", "default": "`null`", "description": "是否开启点击反馈", "value": {"type": "boolean", "kind": "expression"}}, {"name": "is-link", "default": "`false`", "description": "是否展示右侧箭头并开启点击反馈", "value": {"type": "boolean", "kind": "expression"}}, {"name": "required", "default": "`false`", "description": "是否显示表单必填星号", "value": {"type": "boolean", "kind": "expression"}}, {"name": "center", "default": "`false`", "description": "是否使内容垂直居中", "value": {"type": "boolean", "kind": "expression"}}, {"name": "arrow-direction", "default": "`right`", "description": "箭头方向，可选值为 `left` `up` `down`", "value": {"type": "string", "kind": "expression"}}, {"name": "title-style", "default": "-", "description": "左侧标题额外样式", "value": {"type": "any", "kind": "expression"}}, {"name": "title-class", "default": "-", "description": "左侧标题额外类名", "value": {"type": "any", "kind": "expression"}}, {"name": "value-class", "default": "-", "description": "右侧内容额外类名", "value": {"type": "any", "kind": "expression"}}, {"name": "label-class", "default": "-", "description": "描述信息额外类名", "value": {"type": "any", "kind": "expression"}}]}, {"name": "van-cascader", "slots": [{"name": "title", "description": "自定义顶部标题"}, {"name": "option", "description": "自定义选项文字"}], "events": [{"name": "change", "description": "选中项变化时触发"}, {"name": "finish", "description": "全部选项选择完成后触发"}, {"name": "close", "description": "点击关闭图标时触发"}], "attributes": [{"name": "title", "default": "-", "description": "顶部标题", "value": {"type": "string", "kind": "expression"}}, {"name": "value", "default": "-", "description": "选中项的值", "value": {"type": "string | number", "kind": "expression"}}, {"name": "options", "default": "`[]`", "description": "可选项数据源", "value": {"type": "Option[]", "kind": "expression"}}, {"name": "placeholder", "default": "`请选择`", "description": "未选中时的提示文案", "value": {"type": "string", "kind": "expression"}}, {"name": "active-color", "default": "`#ee0a24`", "description": "选中状态的高亮颜色", "value": {"type": "string", "kind": "expression"}}, {"name": "closeable", "default": "`true`", "description": "是否显示关闭图标", "value": {"type": "boolean", "kind": "expression"}}, {"name": "show-header", "default": "`true`", "description": "是否展示标题栏", "value": {"type": "boolean", "kind": "expression"}}, {"name": "field-names", "default": "`{ text: 'text', value: 'value', children: 'children' }`", "description": "自定义 `options` 结构中的字段", "value": {"type": "object", "kind": "expression"}}]}, {"name": "van-checkbox", "slots": [{"name": "default", "description": "自定义文本"}, {"name": "icon", "description": "自定义图标"}], "events": [{"name": "change", "description": "当绑定值变化时触发的事件"}, {"name": "click", "description": "点击复选框时触发"}], "attributes": [{"name": "v-model (value)", "default": "`false`", "description": "是否为选中状态", "value": {"type": "boolean", "kind": "expression"}}, {"name": "name", "default": "-", "description": "标识符", "value": {"type": "any", "kind": "expression"}}, {"name": "shape", "default": "`round`", "description": "形状，可选值为 `square`", "value": {"type": "string", "kind": "expression"}}, {"name": "disabled", "default": "`false`", "description": "是否禁用复选框", "value": {"type": "boolean", "kind": "expression"}}, {"name": "label-disabled", "default": "`false`", "description": "是否禁用复选框文本点击", "value": {"type": "boolean", "kind": "expression"}}, {"name": "label-position", "default": "`right`", "description": "文本位置，可选值为 `left`", "value": {"type": "string", "kind": "expression"}}, {"name": "icon-size", "default": "`20px`", "description": "图标大小，默认单位为 `px`", "value": {"type": "number | string", "kind": "expression"}}, {"name": "checked-color", "default": "`#1989fa`", "description": "选中状态颜色", "value": {"type": "string", "kind": "expression"}}, {"name": "bind-group", "default": "`true`", "description": "是否与复选框组绑定", "value": {"type": "boolean", "kind": "expression"}}]}, {"name": "van-checkbox-group", "slots": [], "events": [{"name": "change", "description": "当绑定值变化时触发的事件"}], "attributes": [{"name": "v-model (value)", "default": "-", "description": "所有选中项的标识符", "value": {"type": "any[]", "kind": "expression"}}, {"name": "disabled", "default": "`false`", "description": "是否禁用所有复选框", "value": {"type": "boolean", "kind": "expression"}}, {"name": "max", "default": "`0`", "description": "最大可选数，`0`为无限制", "value": {"type": "number | string", "kind": "expression"}}, {"name": "direction", "default": "`vertical`", "description": "排列方向，可选值为 `horizontal`", "value": {"type": "string", "kind": "expression"}}, {"name": "icon-size", "default": "`20px`", "description": "所有复选框的图标大小，默认单位为 `px`", "value": {"type": "number | string", "kind": "expression"}}, {"name": "checked-color", "default": "`#1989fa`", "description": "所有复选框的选中状态颜色", "value": {"type": "string", "kind": "expression"}}]}, {"name": "van-circle", "slots": [{"name": "default", "description": "自定义文字内容"}], "events": [], "attributes": [{"name": "v-model", "default": "-", "description": "当前进度", "value": {"type": "number", "kind": "expression"}}, {"name": "rate", "default": "`100`", "description": "目标进度", "value": {"type": "number | string", "kind": "expression"}}, {"name": "size", "default": "`100px`", "description": "圆环直径，默认单位为 `px`", "value": {"type": "number | string", "kind": "expression"}}, {"name": "color", "default": "`#1989fa`", "description": "进度条颜色，传入对象格式可以定义渐变色", "value": {"type": "string | object", "kind": "expression"}}, {"name": "layer-color", "default": "`white`", "description": "轨道颜色", "value": {"type": "string", "kind": "expression"}}, {"name": "fill", "default": "`none`", "description": "填充颜色", "value": {"type": "string", "kind": "expression"}}, {"name": "speed", "default": "`0`", "description": "动画速度（单位为 rate/s）", "value": {"type": "number | string", "kind": "expression"}}, {"name": "text", "default": "-", "description": "文字", "value": {"type": "string", "kind": "expression"}}, {"name": "stroke-width", "default": "`40`", "description": "进度条宽度", "value": {"type": "number | string", "kind": "expression"}}, {"name": "stroke-linecap", "default": "`round`", "description": "进度条端点的形状，可选值为`square` `butt`", "value": {"type": "string", "kind": "expression"}}, {"name": "clockwise", "default": "`true`", "description": "是否顺时针增加", "value": {"type": "boolean", "kind": "expression"}}]}, {"name": "van-row", "slots": [], "events": [{"name": "click", "description": "点击时触发"}], "attributes": [{"name": "type", "default": "-", "description": "布局方式，可选值为`flex`", "value": {"type": "string", "kind": "expression"}}, {"name": "gutter", "default": "-", "description": "列元素之间的间距（单位为 px）", "value": {"type": "number | string", "kind": "expression"}}, {"name": "tag", "default": "`div`", "description": "自定义元素标签", "value": {"type": "string", "kind": "expression"}}, {"name": "justify", "default": "`start`", "description": "Flex 主轴对齐方式，可选值为 `end` `center` <br> `space-around` `space-between`", "value": {"type": "string", "kind": "expression"}}, {"name": "align", "default": "`top`", "description": "Flex 交叉轴对齐方式，可选值为 `center` `bottom`", "value": {"type": "string", "kind": "expression"}}]}, {"name": "van-col", "slots": [], "events": [{"name": "click", "description": "点击时触发"}], "attributes": [{"name": "span", "default": "-", "description": "列元素宽度", "value": {"type": "number | string", "kind": "expression"}}, {"name": "offset", "default": "-", "description": "列元素偏移距离", "value": {"type": "number | string", "kind": "expression"}}, {"name": "tag", "default": "`div`", "description": "自定义元素标签", "value": {"type": "string", "kind": "expression"}}]}, {"name": "van-collapse", "slots": [], "events": [{"name": "change", "description": "切换面板时触发"}], "attributes": [{"name": "v-model", "default": "string)[]_", "description": "当前展开面板的 name", "value": {"type": "手风琴模式：_number | string_<br>非手风琴模式：_(number \\", "kind": "expression"}}, {"name": "accordion", "default": "`false`", "description": "是否开启手风琴模式", "value": {"type": "boolean", "kind": "expression"}}, {"name": "border", "default": "`true`", "description": "是否显示外边框", "value": {"type": "boolean", "kind": "expression"}}]}, {"name": "van-collapse-item", "slots": [{"name": "default", "description": "面板内容"}, {"name": "value", "description": "自定义显示内容"}, {"name": "icon", "description": "自定义 `icon`"}, {"name": "title", "description": "自定义 `title`"}, {"name": "right-icon", "description": "自定义右侧按钮，默认是 `arrow`"}], "events": [], "attributes": [{"name": "name", "default": "`index`", "description": "唯一标识符，默认为索引值", "value": {"type": "number | string", "kind": "expression"}}, {"name": "icon", "default": "-", "description": "标题栏左侧[图标名称](#/zh-CN/icon)或图片链接", "value": {"type": "string", "kind": "expression"}}, {"name": "size", "default": "-", "description": "标题栏大小，可选值为 `large`", "value": {"type": "string", "kind": "expression"}}, {"name": "title", "default": "-", "description": "标题栏左侧内容", "value": {"type": "number | string", "kind": "expression"}}, {"name": "value", "default": "-", "description": "标题栏右侧内容", "value": {"type": "number | string", "kind": "expression"}}, {"name": "label", "default": "-", "description": "标题栏描述信息", "value": {"type": "number | string", "kind": "expression"}}, {"name": "border", "default": "`true`", "description": "是否显示内边框", "value": {"type": "boolean", "kind": "expression"}}, {"name": "is-link", "default": "`true`", "description": "是否展示标题栏右侧箭头并开启点击反馈", "value": {"type": "boolean", "kind": "expression"}}, {"name": "disabled", "default": "`false`", "description": "是否禁用面板", "value": {"type": "boolean", "kind": "expression"}}, {"name": "lazy-render", "default": "`true`", "description": "是否在首次展开时才渲染面板内容", "value": {"type": "boolean", "kind": "expression"}}, {"name": "title-class", "default": "-", "description": "左侧标题额外类名", "value": {"type": "string", "kind": "expression"}}, {"name": "value-class", "default": "-", "description": "右侧内容额外类名", "value": {"type": "string", "kind": "expression"}}, {"name": "label-class", "default": "-", "description": "描述信息额外类名", "value": {"type": "string", "kind": "expression"}}]}, {"name": "van-contact-card", "slots": [], "events": [{"name": "click", "description": "点击时触发"}], "attributes": [{"name": "type", "default": "`add`", "description": "卡片类型，可选值为 `edit`", "value": {"type": "string", "kind": "expression"}}, {"name": "name", "default": "-", "description": "联系人姓名", "value": {"type": "string", "kind": "expression"}}, {"name": "tel", "default": "-", "description": "联系人手机号", "value": {"type": "string", "kind": "expression"}}, {"name": "add-text", "default": "`添加联系人`", "description": "添加时的文案提示", "value": {"type": "string", "kind": "expression"}}]}, {"name": "van-contact-edit", "slots": [], "events": [{"name": "save", "description": "点击保存按钮时触发"}, {"name": "delete", "description": "点击删除按钮时触发"}], "attributes": [{"name": "contact-info", "default": "`{}`", "description": "联系人信息", "value": {"type": "Contact", "kind": "expression"}}, {"name": "is-edit", "default": "`false`", "description": "是否为编辑联系人", "value": {"type": "boolean", "kind": "expression"}}, {"name": "is-saving", "default": "`false`", "description": "是否显示保存按钮加载动画", "value": {"type": "boolean", "kind": "expression"}}, {"name": "is-deleting", "default": "`false`", "description": "是否显示删除按钮加载动画", "value": {"type": "boolean", "kind": "expression"}}, {"name": "tel-validator", "default": "-", "description": "手机号格式校验函数", "value": {"type": "(tel: string) => boolean", "kind": "expression"}}, {"name": "show-set-default", "default": "`false`", "description": "是否显示默认联系人栏", "value": {"type": "boolean", "kind": "expression"}}, {"name": "set-default-label", "default": "-", "description": "默认联系人栏文案", "value": {"type": "string", "kind": "expression"}}]}, {"name": "van-contact-list", "slots": [], "events": [{"name": "add", "description": "点击新增按钮时触发"}, {"name": "edit", "description": "点击编辑按钮时触发"}, {"name": "select", "description": "切换选中的联系人时触发"}], "attributes": [{"name": "v-model", "default": "-", "description": "当前选中联系人的 id", "value": {"type": "number | string", "kind": "expression"}}, {"name": "list", "default": "`[]`", "description": "联系人列表", "value": {"type": "Contact[]", "kind": "expression"}}, {"name": "add-text", "default": "`新建联系人`", "description": "新建按钮文案", "value": {"type": "string", "kind": "expression"}}, {"name": "default-tag-text", "default": "-", "description": "默认联系人标签文案", "value": {"type": "string", "kind": "expression"}}]}, {"name": "van-count-down", "slots": [{"name": "default", "description": "自定义内容"}], "events": [{"name": "finish", "description": "倒计时结束时触发"}, {"name": "change", "description": "倒计时变化时触发"}], "attributes": [{"name": "time", "default": "`0`", "description": "倒计时时长，单位毫秒", "value": {"type": "number | string", "kind": "expression"}}, {"name": "format", "default": "`HH:mm:ss`", "description": "时间格式", "value": {"type": "string", "kind": "expression"}}, {"name": "auto-start", "default": "`true`", "description": "是否自动开始倒计时", "value": {"type": "boolean", "kind": "expression"}}, {"name": "millisecond", "default": "`false`", "description": "是否开启毫秒级渲染", "value": {"type": "boolean", "kind": "expression"}}]}, {"name": "van-coupon-cell", "slots": [], "events": [], "attributes": [{"name": "title", "default": "`优惠券`", "description": "单元格标题", "value": {"type": "string", "kind": "expression"}}, {"name": "chosen-coupon", "default": "`-1`", "description": "当前选中优惠券的索引", "value": {"type": "number | string", "kind": "expression"}}, {"name": "coupons", "default": "`[]`", "description": "可用优惠券列表", "value": {"type": "Coupon[]", "kind": "expression"}}, {"name": "editable", "default": "`true`", "description": "能否切换优惠券", "value": {"type": "boolean", "kind": "expression"}}, {"name": "border", "default": "`true`", "description": "是否显示内边框", "value": {"type": "boolean", "kind": "expression"}}, {"name": "currency", "default": "`¥`", "description": "货币符号", "value": {"type": "string", "kind": "expression"}}]}, {"name": "van-coupon-list", "slots": [{"name": "list-footer", "description": "优惠券列表底部"}, {"name": "disabled-list-footer", "description": "不可用优惠券列表底部"}], "events": [{"name": "change", "description": "优惠券切换回调"}, {"name": "exchange", "description": "兑换优惠券回调"}], "attributes": [{"name": "v-model", "default": "-", "description": "当前输入的兑换码", "value": {"type": "string", "kind": "expression"}}, {"name": "chosen-coupon", "default": "`-1`", "description": "当前选中优惠券的索引", "value": {"type": "number", "kind": "expression"}}, {"name": "coupons", "default": "`[]`", "description": "可用优惠券列表", "value": {"type": "Coupon[]", "kind": "expression"}}, {"name": "disabled-coupons", "default": "`[]`", "description": "不可用优惠券列表", "value": {"type": "Coupon[]", "kind": "expression"}}, {"name": "enabled-title", "default": "`可使用优惠券`", "description": "可用优惠券列表标题", "value": {"type": "string", "kind": "expression"}}, {"name": "disabled-title", "default": "`不可使用优惠券`", "description": "不可用优惠券列表标题", "value": {"type": "string", "kind": "expression"}}, {"name": "exchange-button-text", "default": "`兑换`", "description": "兑换按钮文字", "value": {"type": "string", "kind": "expression"}}, {"name": "exchange-button-loading", "default": "`false`", "description": "是否显示兑换按钮加载动画", "value": {"type": "boolean", "kind": "expression"}}, {"name": "exchange-button-disabled", "default": "`false`", "description": "是否禁用兑换按钮", "value": {"type": "boolean", "kind": "expression"}}, {"name": "exchange-min-length", "default": "`1`", "description": "兑换码最小长度", "value": {"type": "number", "kind": "expression"}}, {"name": "displayed-coupon-index", "default": "-", "description": "滚动至特定优惠券位置", "value": {"type": "number", "kind": "expression"}}, {"name": "show-close-button", "default": "`true`", "description": "是否显示列表底部按钮", "value": {"type": "boolean", "kind": "expression"}}, {"name": "close-button-text", "default": "`不使用优惠`", "description": "列表底部按钮文字", "value": {"type": "string", "kind": "expression"}}, {"name": "input-placeholder", "default": "`请输入优惠码`", "description": "输入框文字提示", "value": {"type": "string", "kind": "expression"}}, {"name": "show-exchange-bar", "default": "`true`", "description": "是否展示兑换栏", "value": {"type": "boolean", "kind": "expression"}}, {"name": "currency", "default": "`¥`", "description": "货币符号", "value": {"type": "string", "kind": "expression"}}, {"name": "empty-image", "default": "`https://img01.yzcdn.cn/vant/coupon-empty.png`", "description": "列表为空时的占位图", "value": {"type": "string", "kind": "expression"}}, {"name": "show-count", "default": "`true`", "description": "是否展示可用 / 不可用数量", "value": {"type": "boolean", "kind": "expression"}}]}, {"name": "van-datetime-picker", "slots": [{"name": "default", "description": "自定义整个顶部栏的内容"}, {"name": "title", "description": "自定义标题内容"}, {"name": "confirm", "description": "自定义确认按钮内容"}, {"name": "cancel", "description": "自定义取消按钮内容"}, {"name": "option", "description": "自定义选项内容"}, {"name": "columns-top", "description": "自定义选项上方内容"}, {"name": "columns-bottom", "description": "自定义选项下方内容"}], "events": [{"name": "change", "description": "当值变化时触发的事件"}, {"name": "confirm", "description": "点击完成按钮时触发的事件"}, {"name": "cancel", "description": "点击取消按钮时触发的事件"}], "attributes": [{"name": "type", "default": "`datetime`", "description": "时间类型，可选值为 `date` `time` <br> `year-month` `month-day` `datehour`", "value": {"type": "string", "kind": "expression"}}, {"name": "title", "default": "`''`", "description": "顶部栏标题", "value": {"type": "string", "kind": "expression"}}, {"name": "confirm-button-text", "default": "`确认`", "description": "确认按钮文字", "value": {"type": "string", "kind": "expression"}}, {"name": "cancel-button-text", "default": "`取消`", "description": "取消按钮文字", "value": {"type": "string", "kind": "expression"}}, {"name": "show-toolbar", "default": "`true`", "description": "是否显示顶部栏", "value": {"type": "boolean", "kind": "expression"}}, {"name": "loading", "default": "`false`", "description": "是否显示加载状态", "value": {"type": "boolean", "kind": "expression"}}, {"name": "readonly", "default": "`false`", "description": "是否为只读状态，只读状态下无法切换选项", "value": {"type": "boolean", "kind": "expression"}}, {"name": "filter", "default": "-", "description": "选项过滤函数", "value": {"type": "(type, vals) => vals", "kind": "expression"}}, {"name": "formatter", "default": "-", "description": "选项格式化函数", "value": {"type": "(type, val) => val", "kind": "expression"}}, {"name": "columns-order", "default": "-", "description": "自定义列排序数组, 子项可选值为<br> `year`、`month`、`day`、`hour`、`minute`", "value": {"type": "string[]", "kind": "expression"}}, {"name": "item-height", "default": "`44`", "description": "选项高度，支持 `px` `vw` `vh` `rem` 单位，默认 `px`", "value": {"type": "number | string", "kind": "expression"}}, {"name": "visible-item-count", "default": "`6`", "description": "可见的选项个数", "value": {"type": "number | string", "kind": "expression"}}, {"name": "swipe-duration", "default": "`1000`", "description": "快速滑动时惯性滚动的时长，单位`ms`", "value": {"type": "number | string", "kind": "expression"}}]}, {"name": "van-date-picker", "slots": [], "events": [], "attributes": [{"name": "min-date", "default": "十年前", "description": "可选的最小时间，精确到分钟", "value": {"type": "Date", "kind": "expression"}}, {"name": "max-date", "default": "十年后", "description": "可选的最大时间，精确到分钟", "value": {"type": "Date", "kind": "expression"}}]}, {"name": "van-time-picker", "slots": [], "events": [], "attributes": [{"name": "min-hour", "default": "`0`", "description": "可选的最小小时", "value": {"type": "number | string", "kind": "expression"}}, {"name": "max-hour", "default": "`23`", "description": "可选的最大小时", "value": {"type": "number | string", "kind": "expression"}}, {"name": "min-minute", "default": "`0`", "description": "可选的最小分钟", "value": {"type": "number | string", "kind": "expression"}}, {"name": "max-minute", "default": "`59`", "description": "可选的最大分钟", "value": {"type": "number | string", "kind": "expression"}}]}, {"name": "van-dialog", "slots": [{"name": "default", "description": "自定义内容"}, {"name": "title", "description": "自定义标题"}], "events": [{"name": "confirm", "description": "点击确认按钮时触发"}, {"name": "cancel", "description": "点击取消按钮时触发"}, {"name": "open", "description": "打开弹窗时触发"}, {"name": "close", "description": "关闭弹窗时触发"}, {"name": "opened", "description": "打开弹窗且动画结束后触发"}, {"name": "closed", "description": "关闭弹窗且动画结束后触发"}], "attributes": [{"name": "v-model", "default": "-", "description": "是否显示弹窗", "value": {"type": "boolean", "kind": "expression"}}, {"name": "title", "default": "-", "description": "标题", "value": {"type": "string", "kind": "expression"}}, {"name": "width", "default": "`320px`", "description": "弹窗宽度，默认单位为 `px`", "value": {"type": "number | string", "kind": "expression"}}, {"name": "message", "default": "-", "description": "文本内容，支持通过 `\\n` 换行", "value": {"type": "string", "kind": "expression"}}, {"name": "message-align", "default": "`center`", "description": "内容对齐方式，可选值为 `left` `right`", "value": {"type": "string", "kind": "expression"}}, {"name": "theme", "default": "`default`", "description": "样式风格，可选值为 `round-button`", "value": {"type": "string", "kind": "expression"}}, {"name": "show-confirm-button", "default": "`true`", "description": "是否展示确认按钮", "value": {"type": "boolean", "kind": "expression"}}, {"name": "show-cancel-button", "default": "`false`", "description": "是否展示取消按钮", "value": {"type": "boolean", "kind": "expression"}}, {"name": "confirm-button-text", "default": "`确认`", "description": "确认按钮文案", "value": {"type": "string", "kind": "expression"}}, {"name": "confirm-button-color", "default": "`#ee0a24`", "description": "确认按钮颜色", "value": {"type": "string", "kind": "expression"}}, {"name": "cancel-button-text", "default": "`取消`", "description": "取消按钮文案", "value": {"type": "string", "kind": "expression"}}, {"name": "cancel-button-color", "default": "`black`", "description": "取消按钮颜色", "value": {"type": "string", "kind": "expression"}}, {"name": "overlay", "default": "`true`", "description": "是否展示遮罩层", "value": {"type": "boolean", "kind": "expression"}}, {"name": "overlay-class", "default": "-", "description": "自定义遮罩层类名", "value": {"type": "string", "kind": "expression"}}, {"name": "overlay-style", "default": "-", "description": "自定义遮罩层样式", "value": {"type": "object", "kind": "expression"}}, {"name": "close-on-popstate", "default": "`true`", "description": "是否在页面回退时自动关闭", "value": {"type": "boolean", "kind": "expression"}}, {"name": "close-on-click-overlay", "default": "`false`", "description": "是否在点击遮罩层后关闭弹窗", "value": {"type": "boolean", "kind": "expression"}}, {"name": "lazy-render", "default": "`true`", "description": "是否在显示弹层时才渲染节点", "value": {"type": "boolean", "kind": "expression"}}, {"name": "lock-scroll", "default": "`true`", "description": "是否锁定背景滚动", "value": {"type": "boolean", "kind": "expression"}}, {"name": "allow-html", "default": "`true`", "description": "是否允许 message 内容中渲染 HTML", "value": {"type": "boolean", "kind": "expression"}}, {"name": "before-close", "default": "-", "description": "关闭前的回调函数，<br>调用 done() 后关闭弹窗，<br>调用 done(false) 阻止弹窗关闭", "value": {"type": "(action, done) => void", "kind": "expression"}}, {"name": "transition", "default": "-", "description": "动画类名，等价于 [transition](https://cn.vuejs.org/v2/api/index.html#transition) 的 `name` 属性", "value": {"type": "string", "kind": "expression"}}, {"name": "get-container", "default": "-", "description": "指定挂载的节点，[用法示例](#/zh-CN/popup#zhi-ding-gua-zai-wei-zhi)", "value": {"type": "string | () => Element", "kind": "expression"}}]}, {"name": "van-divider", "slots": [{"name": "default", "description": "内容"}], "events": [], "attributes": [{"name": "dashed", "default": "`false`", "description": "是否使用虚线", "value": {"type": "boolean", "kind": "expression"}}, {"name": "hairline", "default": "`true`", "description": "是否使用 0.5px 线", "value": {"type": "boolean", "kind": "expression"}}, {"name": "content-position", "default": "`center`", "description": "内容位置，可选值为`left` `right`", "value": {"type": "string", "kind": "expression"}}]}, {"name": "van-dropdown-menu", "slots": [], "events": [], "attributes": [{"name": "active-color", "default": "`#ee0a24`", "description": "菜单标题和选项的选中态颜色", "value": {"type": "string", "kind": "expression"}}, {"name": "direction", "default": "`down`", "description": "菜单展开方向，可选值为`up`", "value": {"type": "string", "kind": "expression"}}, {"name": "z-index", "default": "`10`", "description": "菜单栏 z-index 层级", "value": {"type": "number | string", "kind": "expression"}}, {"name": "duration", "default": "`0.2`", "description": "动画时长，单位秒", "value": {"type": "number | string", "kind": "expression"}}, {"name": "overlay", "default": "`true`", "description": "是否显示遮罩层", "value": {"type": "boolean", "kind": "expression"}}, {"name": "close-on-click-overlay", "default": "`true`", "description": "是否在点击遮罩层后关闭菜单", "value": {"type": "boolean", "kind": "expression"}}, {"name": "close-on-click-outside", "default": "`true`", "description": "是否在点击外部元素后关闭菜单", "value": {"type": "boolean", "kind": "expression"}}]}, {"name": "van-dropdown-item", "slots": [{"name": "default", "description": "菜单内容"}, {"name": "title", "description": "自定义菜单项标题"}], "events": [{"name": "change", "description": "点击选项导致 value 变化时触发"}, {"name": "open", "description": "打开菜单栏时触发"}, {"name": "close", "description": "关闭菜单栏时触发"}, {"name": "opened", "description": "打开菜单栏且动画结束后触发"}, {"name": "closed", "description": "关闭菜单栏且动画结束后触发"}], "attributes": [{"name": "value", "default": "-", "description": "当前选中项对应的 value，可以通过`v-model`双向绑定", "value": {"type": "number | string", "kind": "expression"}}, {"name": "title", "default": "当前选中项文字", "description": "菜单项标题", "value": {"type": "string", "kind": "expression"}}, {"name": "options", "default": "`[]`", "description": "选项数组", "value": {"type": "Option[]", "kind": "expression"}}, {"name": "disabled", "default": "`false`", "description": "是否禁用菜单", "value": {"type": "boolean", "kind": "expression"}}, {"name": "lazy-render", "default": "`true`", "description": "是否在首次展开时才渲染菜单内容", "value": {"type": "boolean", "kind": "expression"}}, {"name": "title-class", "default": "-", "description": "标题额外类名", "value": {"type": "string", "kind": "expression"}}, {"name": "get-container", "default": "-", "description": "指定挂载的节点，[用法示例](#/zh-CN/popup#zhi-ding-gua-zai-wei-zhi)", "value": {"type": "string | () => Element", "kind": "expression"}}]}, {"name": "van-empty", "slots": [{"name": "default", "description": "自定义底部内容"}, {"name": "image", "description": "自定义图标"}, {"name": "description", "description": "自定义描述文字"}], "events": [], "attributes": [{"name": "image", "default": "`default`", "description": "图片类型，可选值为 `error` `network` `search`，支持传入图片 URL", "value": {"type": "string", "kind": "expression"}}, {"name": "image-size", "default": "-", "description": "图片大小，默认单位为 `px`", "value": {"type": "number | string", "kind": "expression"}}, {"name": "description", "default": "-", "description": "图片下方的描述文字", "value": {"type": "string", "kind": "expression"}}]}, {"name": "van-field", "slots": [{"name": "label", "description": "自定义输入框 label 标签"}, {"name": "input", "description": "自定义输入框，使用此插槽后，与输入框相关的属性和事件将失效。<br>在 Form 组件进行表单校验时，会使用 input 插槽中子组件的 `value`，而不是 Field 组件的 `value`。"}, {"name": "left-icon", "description": "自定义输入框头部图标"}, {"name": "right-icon", "description": "自定义输入框尾部图标"}, {"name": "button", "description": "自定义输入框尾部按钮"}, {"name": "extra", "description": "自定义输入框最右侧的额外内容"}], "events": [{"name": "input", "description": "输入框内容变化时触发"}, {"name": "focus", "description": "输入框获得焦点时触发"}, {"name": "blur", "description": "输入框失去焦点时触发"}, {"name": "clear", "description": "点击清除按钮时触发"}, {"name": "click", "description": "点击 Field 时触发"}, {"name": "click-input", "description": "点击输入区域时触发"}, {"name": "click-left-icon", "description": "点击左侧图标时触发"}, {"name": "click-right-icon", "description": "点击右侧图标时触发"}], "attributes": [{"name": "v-model (value)", "default": "-", "description": "当前输入的值", "value": {"type": "number | string", "kind": "expression"}}, {"name": "label", "default": "-", "description": "输入框左侧文本", "value": {"type": "string", "kind": "expression"}}, {"name": "name", "default": "-", "description": "名称，提交表单的标识符", "value": {"type": "string", "kind": "expression"}}, {"name": "type", "default": "`text`", "description": "输入框类型, 可选值为 `tel` `digit`<br>`number` `textarea` `password` 等", "value": {"type": "string", "kind": "expression"}}, {"name": "size", "default": "-", "description": "大小，可选值为 `large`", "value": {"type": "string", "kind": "expression"}}, {"name": "maxlength", "default": "-", "description": "输入的最大字符数", "value": {"type": "number | string", "kind": "expression"}}, {"name": "placeholder", "default": "-", "description": "输入框占位提示文字", "value": {"type": "string", "kind": "expression"}}, {"name": "border", "default": "`true`", "description": "是否显示内边框", "value": {"type": "boolean", "kind": "expression"}}, {"name": "disabled", "default": "`false`", "description": "是否禁用输入框", "value": {"type": "boolean", "kind": "expression"}}, {"name": "readonly", "default": "`false`", "description": "是否只读", "value": {"type": "boolean", "kind": "expression"}}, {"name": "colon", "default": "`false`", "description": "是否在 label 后面添加冒号", "value": {"type": "boolean", "kind": "expression"}}, {"name": "required", "default": "`false`", "description": "是否显示表单必填星号", "value": {"type": "boolean", "kind": "expression"}}, {"name": "center", "default": "`false`", "description": "是否使内容垂直居中", "value": {"type": "boolean", "kind": "expression"}}, {"name": "clearable", "default": "`false`", "description": "是否启用清除图标，点击清除图标后会清空输入框", "value": {"type": "boolean", "kind": "expression"}}, {"name": "clear-trigger", "default": "`focus`", "description": "显示清除图标的时机，`always` 表示输入框不为空时展示，<br>`focus` 表示输入框聚焦且不为空时展示", "value": {"type": "string", "kind": "expression"}}, {"name": "clickable", "default": "`false`", "description": "是否开启点击反馈", "value": {"type": "boolean", "kind": "expression"}}, {"name": "is-link", "default": "`false`", "description": "是否展示右侧箭头并开启点击反馈", "value": {"type": "boolean", "kind": "expression"}}, {"name": "autofocus", "default": "`false`", "description": "是否自动聚焦，iOS 系统不支持该属性", "value": {"type": "boolean", "kind": "expression"}}, {"name": "show-word-limit", "default": "`false`", "description": "是否显示字数统计，需要设置`maxlength`属性", "value": {"type": "boolean", "kind": "expression"}}, {"name": "error", "default": "`false`", "description": "是否将输入内容标红", "value": {"type": "boolean", "kind": "expression"}}, {"name": "error-message", "default": "-", "description": "底部错误提示文案，为空时不展示", "value": {"type": "string", "kind": "expression"}}, {"name": "formatter", "default": "-", "description": "输入内容格式化函数", "value": {"type": "Function", "kind": "expression"}}, {"name": "format-trigger", "default": "`onChange`", "description": "格式化函数触发的时机，可选值为 `onBlur`", "value": {"type": "string", "kind": "expression"}}, {"name": "arrow-direction", "default": "`right`", "description": "箭头方向，可选值为 `left` `up` `down`", "value": {"type": "string", "kind": "expression"}}, {"name": "label-class", "default": "-", "description": "左侧文本额外类名", "value": {"type": "any", "kind": "expression"}}, {"name": "label-width", "default": "`6.2em`", "description": "左侧文本宽度，默认单位为`px`", "value": {"type": "number | string", "kind": "expression"}}, {"name": "label-align", "default": "`left`", "description": "左侧文本对齐方式，可选值为 `center` `right`", "value": {"type": "string", "kind": "expression"}}, {"name": "input-align", "default": "`left`", "description": "输入框对齐方式，可选值为 `center` `right`", "value": {"type": "string", "kind": "expression"}}, {"name": "error-message-align", "default": "`left`", "description": "错误提示文案对齐方式，可选值为 `center` `right`", "value": {"type": "string", "kind": "expression"}}, {"name": "autosize", "default": "`false`", "description": "是否自适应内容高度，只对 textarea 有效，<br>可传入对象,如 { maxHeight: 100, minHeight: 50 }，<br>单位为`px`", "value": {"type": "boolean | object", "kind": "expression"}}, {"name": "left-icon", "default": "-", "description": "左侧[图标名称](#/zh-CN/icon)或图片链接", "value": {"type": "string", "kind": "expression"}}, {"name": "right-icon", "default": "-", "description": "右侧[图标名称](#/zh-CN/icon)或图片链接", "value": {"type": "string", "kind": "expression"}}, {"name": "icon-prefix", "default": "`van-icon`", "description": "图标类名前缀，同 Icon 组件的 [class-prefix 属性](#/zh-CN/icon#props)", "value": {"type": "string", "kind": "expression"}}, {"name": "rules", "default": "-", "description": "表单校验规则，详见 [Form 组件](#/zh-CN/form#rule-shu-ju-jie-gou)", "value": {"type": "Rule[]", "kind": "expression"}}, {"name": "autocomplete", "default": "-", "description": "input 标签原生的[自动完成属性](https://developer.mozilla.org/en-US/docs/Web/HTML/Attributes/autocomplete)", "value": {"type": "string", "kind": "expression"}}]}, {"name": "van-form", "slots": [{"name": "default", "description": "表单内容"}], "events": [{"name": "submit", "description": "提交表单且验证通过后触发"}, {"name": "failed", "description": "提交表单且验证不通过后触发"}], "attributes": [{"name": "label-width", "default": "`6.2em`", "description": "表单项 label 宽度，默认单位为`px`", "value": {"type": "number | string", "kind": "expression"}}, {"name": "label-align", "default": "`left`", "description": "\b 表单项 label 对齐方式，可选值为 `center` `right`", "value": {"type": "string", "kind": "expression"}}, {"name": "input-align", "default": "`left`", "description": "输入框对齐方式，可选值为 `center` `right`", "value": {"type": "string", "kind": "expression"}}, {"name": "error-message-align", "default": "`left`", "description": "错误提示文案对齐方式，可选值为 `center` `right`", "value": {"type": "string", "kind": "expression"}}, {"name": "validate-trigger", "default": "`onBlur`", "description": "表单校验触发时机，可选值为 `onChange`、`onSubmit`，详见下表", "value": {"type": "string", "kind": "expression"}}, {"name": "colon", "default": "`false`", "description": "是否在 label 后面添加冒号", "value": {"type": "boolean", "kind": "expression"}}, {"name": "disabled", "default": "`false`", "description": "是否禁用表单中的所有输入框", "value": {"type": "boolean", "kind": "expression"}}, {"name": "readonly", "default": "`false`", "description": "是否将表单中的所有输入框设置为只读", "value": {"type": "boolean", "kind": "expression"}}, {"name": "validate-first", "default": "`false`", "description": "是否在某一项校验不通过时停止校验", "value": {"type": "boolean", "kind": "expression"}}, {"name": "scroll-to-error", "default": "`false`", "description": "是否在提交表单且校验不通过时滚动至错误的表单项", "value": {"type": "boolean", "kind": "expression"}}, {"name": "show-error", "default": "`true`", "description": "是否在校验不通过时标红输入框", "value": {"type": "boolean", "kind": "expression"}}, {"name": "show-error-message", "default": "`true`", "description": "是否在校验不通过时在输入框下方展示错误提示", "value": {"type": "boolean", "kind": "expression"}}, {"name": "submit-on-enter", "default": "`true`", "description": "是否在按下回车键时提交表单", "value": {"type": "boolean", "kind": "expression"}}]}, {"name": "van-goods-action", "slots": [], "events": [], "attributes": [{"name": "safe-area-inset-bottom", "default": "`true`", "description": "是否开启[底部安全区适配](#/zh-CN/advanced-usage#di-bu-an-quan-qu-gua-pei)", "value": {"type": "boolean", "kind": "expression"}}]}, {"name": "van-goods-action-icon", "slots": [{"name": "default", "description": "文本内容"}, {"name": "icon", "description": "自定义图标"}], "events": [], "attributes": [{"name": "text", "default": "-", "description": "按钮文字", "value": {"type": "string", "kind": "expression"}}, {"name": "icon", "default": "-", "description": "图标", "value": {"type": "string", "kind": "expression"}}, {"name": "color", "default": "`#323233`", "description": "图标颜色", "value": {"type": "string", "kind": "expression"}}, {"name": "icon-class", "default": "-", "description": "图标额外类名", "value": {"type": "any", "kind": "expression"}}, {"name": "dot", "default": "`false`", "description": "是否显示图标右上角小红点", "value": {"type": "boolean", "kind": "expression"}}, {"name": "badge", "default": "-", "description": "图标右上角徽标的内容", "value": {"type": "number | string", "kind": "expression"}}, {"name": "info", "default": "-", "description": "图标右上角徽标的内容（已废弃，请使用 badge 属性）", "value": {"type": "number | string", "kind": "expression"}}, {"name": "url", "default": "-", "description": "点击后跳转的链接地址", "value": {"type": "string", "kind": "expression"}}, {"name": "to", "default": "-", "description": "点击后跳转的目标路由对象，同 vue-router 的 [to 属性](https://router.vuejs.org/zh/api/#to)", "value": {"type": "string | object", "kind": "expression"}}, {"name": "replace", "default": "`false`", "description": "是否在跳转时替换当前页面历史", "value": {"type": "boolean", "kind": "expression"}}]}, {"name": "van-goods-action-button", "slots": [{"name": "default", "description": "按钮显示内容"}], "events": [], "attributes": [{"name": "text", "default": "-", "description": "按钮文字", "value": {"type": "string", "kind": "expression"}}, {"name": "type", "default": "`default`", "description": "按钮类型，可选值为 `primary` `info` `warning` `danger`", "value": {"type": "string", "kind": "expression"}}, {"name": "color", "default": "-", "description": "按钮颜色，支持传入`linear-gradient`渐变色", "value": {"type": "string", "kind": "expression"}}, {"name": "icon", "default": "-", "description": "左侧[图标名称](#/zh-CN/icon)或图片链接", "value": {"type": "string", "kind": "expression"}}, {"name": "disabled", "default": "`false`", "description": "是否禁用按钮", "value": {"type": "boolean", "kind": "expression"}}, {"name": "loading", "default": "`false`", "description": "是否显示为加载状态", "value": {"type": "boolean", "kind": "expression"}}, {"name": "url", "default": "-", "description": "点击后跳转的链接地址", "value": {"type": "string", "kind": "expression"}}, {"name": "to", "default": "-", "description": "点击后跳转的目标路由对象，同 vue-router 的 [to 属性](https://router.vuejs.org/zh/api/#to)", "value": {"type": "string | object", "kind": "expression"}}, {"name": "replace", "default": "`false`", "description": "是否在跳转时替换当前页面历史", "value": {"type": "boolean", "kind": "expression"}}]}, {"name": "van-grid", "slots": [], "events": [], "attributes": [{"name": "column-num", "default": "`4`", "description": "列数", "value": {"type": "number | string", "kind": "expression"}}, {"name": "icon-size", "default": "`28px`", "description": "图标大小，默认单位为`px`", "value": {"type": "number | string", "kind": "expression"}}, {"name": "gutter", "default": "`0`", "description": "格子之间的间距，默认单位为`px`", "value": {"type": "number | string", "kind": "expression"}}, {"name": "border", "default": "`true`", "description": "是否显示边框", "value": {"type": "boolean", "kind": "expression"}}, {"name": "center", "default": "`true`", "description": "是否将格子内容居中显示", "value": {"type": "boolean", "kind": "expression"}}, {"name": "square", "default": "`false`", "description": "是否将格子固定为正方形", "value": {"type": "boolean", "kind": "expression"}}, {"name": "clickable", "default": "`false`", "description": "是否开启格子点击反馈", "value": {"type": "boolean", "kind": "expression"}}, {"name": "direction", "default": "`vertical`", "description": "格子内容排列的方向，可选值为 `horizontal`", "value": {"type": "string", "kind": "expression"}}]}, {"name": "van-grid-item", "slots": [{"name": "default", "description": "自定义宫格的所有内容"}, {"name": "icon", "description": "自定义图标"}, {"name": "text", "description": "自定义文字"}], "events": [{"name": "click", "description": "点击格子时触发"}], "attributes": [{"name": "text", "default": "-", "description": "文字", "value": {"type": "string", "kind": "expression"}}, {"name": "icon", "default": "-", "description": "[图标名称](#/zh-CN/icon)或图片链接", "value": {"type": "string", "kind": "expression"}}, {"name": "icon-prefix", "default": "`van-icon`", "description": "图标类名前缀，同 Icon 组件的 [class-prefix 属性](#/zh-CN/icon#props)", "value": {"type": "string", "kind": "expression"}}, {"name": "dot", "default": "`false`", "description": "是否显示图标右上角小红点", "value": {"type": "boolean", "kind": "expression"}}, {"name": "badge", "default": "-", "description": "图标右上角徽标的内容", "value": {"type": "number | string", "kind": "expression"}}, {"name": "info", "default": "-", "description": "图标右上角徽标的内容（已废弃，请使用 badge 属性）", "value": {"type": "number | string", "kind": "expression"}}, {"name": "url", "default": "-", "description": "点击后跳转的链接地址", "value": {"type": "string", "kind": "expression"}}, {"name": "to", "default": "-", "description": "点击后跳转的目标路由对象，同 vue-router 的 [to 属性](https://router.vuejs.org/zh/api/#to)", "value": {"type": "string | object", "kind": "expression"}}, {"name": "replace", "default": "`false`", "description": "是否在跳转时替换当前页面历史", "value": {"type": "boolean", "kind": "expression"}}]}, {"name": "van-icon", "slots": [], "events": [{"name": "click", "description": "点击图标时触发"}], "attributes": [{"name": "name", "default": "-", "description": "图标名称或图片链接", "value": {"type": "string", "kind": "expression"}}, {"name": "dot", "default": "`false`", "description": "是否显示图标右上角小红点", "value": {"type": "boolean", "kind": "expression"}}, {"name": "badge", "default": "-", "description": "图标右上角徽标的内容", "value": {"type": "number | string", "kind": "expression"}}, {"name": "info", "default": "-", "description": "图标右上角徽标的内容（已废弃，请使用 badge 属性）", "value": {"type": "number | string", "kind": "expression"}}, {"name": "color", "default": "`inherit`", "description": "图标颜色", "value": {"type": "string", "kind": "expression"}}, {"name": "size", "default": "`inherit`", "description": "图标大小，如 `20px` `2em`，默认单位为`px`", "value": {"type": "number | string", "kind": "expression"}}, {"name": "class-prefix", "default": "`van-icon`", "description": "类名前缀，用于使用自定义图标", "value": {"type": "string", "kind": "expression"}}, {"name": "tag", "default": "`i`", "description": "HTML 标签", "value": {"type": "string", "kind": "expression"}}]}, {"name": "van-image", "slots": [{"name": "default", "description": "自定义图片下方的内容"}, {"name": "loading", "description": "自定义加载中的提示内容"}, {"name": "error", "description": "自定义加载失败时的提示内容"}], "events": [{"name": "click", "description": "点击图片时触发"}, {"name": "load", "description": "图片加载完毕时触发"}, {"name": "error", "description": "图片加载失败时触发"}], "attributes": [{"name": "src", "default": "-", "description": "图片链接", "value": {"type": "string", "kind": "expression"}}, {"name": "fit", "default": "`fill`", "description": "图片填充模式", "value": {"type": "string", "kind": "expression"}}, {"name": "alt", "default": "-", "description": "替代文本", "value": {"type": "string", "kind": "expression"}}, {"name": "width", "default": "-", "description": "宽度，默认单位为`px`", "value": {"type": "number | string", "kind": "expression"}}, {"name": "height", "default": "-", "description": "高度，默认单位为`px`", "value": {"type": "number | string", "kind": "expression"}}, {"name": "radius", "default": "`0`", "description": "圆角大小，默认单位为`px`", "value": {"type": "number | string", "kind": "expression"}}, {"name": "round", "default": "`false`", "description": "是否显示为圆形", "value": {"type": "boolean", "kind": "expression"}}, {"name": "lazy-load", "default": "`false`", "description": "是否开启图片懒加载，须配合 [Lazyload](#/zh-CN/lazyload) 组件使用", "value": {"type": "boolean", "kind": "expression"}}, {"name": "show-error", "default": "`true`", "description": "是否展示图片加载失败提示", "value": {"type": "boolean", "kind": "expression"}}, {"name": "show-loading", "default": "`true`", "description": "是否展示图片加载中提示", "value": {"type": "boolean", "kind": "expression"}}, {"name": "error-icon", "default": "`photo-fail`", "description": "失败时提示的[图标名称](#/zh-CN/icon)或图片链接", "value": {"type": "string", "kind": "expression"}}, {"name": "loading-icon", "default": "`photo`", "description": "加载时提示的[图标名称](#/zh-CN/icon)或图片链接", "value": {"type": "string", "kind": "expression"}}, {"name": "icon-prefix", "default": "`van-icon`", "description": "图标类名前缀，同 Icon 组件的 [class-prefix 属性](#/zh-CN/icon#props)", "value": {"type": "string", "kind": "expression"}}]}, {"name": "van-image-preview", "slots": [{"name": "index", "description": "自定义页码内容"}, {"name": "cover", "description": "自定义覆盖在图片预览上方的内容"}], "events": [{"name": "close", "description": "关闭时触发"}, {"name": "closed", "description": "关闭且且动画结束后触发"}, {"name": "change", "description": "切换当前图片时触发"}, {"name": "scale", "description": "缩放当前图片时触发"}], "attributes": [{"name": "images", "default": "`[]`", "description": "需要预览的图片 URL 数组", "value": {"type": "string[]", "kind": "expression"}}, {"name": "start-position", "default": "`0`", "description": "图片预览起始位置索引", "value": {"type": "number | string", "kind": "expression"}}, {"name": "swipe-duration", "default": "`300`", "description": "动画时长，单位为 ms", "value": {"type": "number | string", "kind": "expression"}}, {"name": "show-index", "default": "`true`", "description": "是否显示页码", "value": {"type": "boolean", "kind": "expression"}}, {"name": "show-indicators", "default": "`false`", "description": "是否显示轮播指示器", "value": {"type": "boolean", "kind": "expression"}}, {"name": "loop", "default": "`true`", "description": "是否开启循环播放", "value": {"type": "boolean", "kind": "expression"}}, {"name": "async-close", "default": "`false`", "description": "是否开启异步关闭", "value": {"type": "boolean", "kind": "expression"}}, {"name": "close-on-popstate", "default": "`true`", "description": "是否在页面回退时自动关闭", "value": {"type": "boolean", "kind": "expression"}}, {"name": "class-name", "default": "-", "description": "自定义类名", "value": {"type": "any", "kind": "expression"}}, {"name": "max-zoom", "default": "`3`", "description": "手势缩放时，最大缩放比例", "value": {"type": "number | string", "kind": "expression"}}, {"name": "min-zoom", "default": "`1/3`", "description": "手势缩放时，最小缩放比例", "value": {"type": "number | string", "kind": "expression"}}, {"name": "closeable", "default": "`false`", "description": "是否显示关闭图标", "value": {"type": "boolean", "kind": "expression"}}, {"name": "close-icon", "default": "`clear`", "description": "关闭图标名称或图片链接", "value": {"type": "string", "kind": "expression"}}, {"name": "close-icon-position", "default": "`top-right`", "description": "关闭图标位置，可选值为`top-left`<br>`bottom-left` `bottom-right`", "value": {"type": "string", "kind": "expression"}}, {"name": "transition", "default": "`van-fade`", "description": "动画类名，等价于 [transition](https://cn.vuejs.org/v2/api/index.html#transition) 的 `name` 属性", "value": {"type": "string", "kind": "expression"}}, {"name": "overlay-style", "default": "-", "description": "自定义遮罩层样式", "value": {"type": "object", "kind": "expression"}}, {"name": "get-container", "default": "-", "description": "指定挂载的节点，[用法示例](#/zh-CN/popup#zhi-ding-gua-zai-wei-zhi)", "value": {"type": "string | () => Element", "kind": "expression"}}]}, {"name": "van-index-bar", "slots": [], "events": [{"name": "select", "description": "点击索引栏的字符时触发"}, {"name": "change", "description": "当前高亮的索引字符变化时触发"}], "attributes": [{"name": "index-list", "default": "`A-Z`", "description": "索引字符列表", "value": {"type": "string[] | number[]", "kind": "expression"}}, {"name": "z-index", "default": "`1`", "description": "z-index 层级", "value": {"type": "number | string", "kind": "expression"}}, {"name": "sticky", "default": "`true`", "description": "是否开启锚点自动吸顶", "value": {"type": "boolean", "kind": "expression"}}, {"name": "sticky-offset-top", "default": "`0`", "description": "锚点自动吸顶时与顶部的距离", "value": {"type": "number", "kind": "expression"}}, {"name": "highlight-color", "default": "`#ee0a24`", "description": "索引字符高亮颜色", "value": {"type": "string", "kind": "expression"}}]}, {"name": "van-index-anchor", "slots": [{"name": "default", "description": "锚点位置显示内容，默认为索引字符"}], "events": [], "attributes": [{"name": "index", "default": "-", "description": "索引字符", "value": {"type": "number | string", "kind": "expression"}}]}, {"name": "van-list", "slots": [{"name": "default", "description": "列表内容"}, {"name": "loading", "description": "自定义底部加载中提示"}, {"name": "finished", "description": "自定义加载完成后的提示文案"}, {"name": "error", "description": "自定义加载失败后的提示文案"}], "events": [{"name": "load", "description": "滚动条与底部距离小于 offset 时触发"}], "attributes": [{"name": "v-model", "default": "`false`", "description": "是否处于加载状态，加载过程中不触发`load`事件", "value": {"type": "boolean", "kind": "expression"}}, {"name": "finished", "default": "`false`", "description": "是否已加载完成，加载完成后不再触发`load`事件", "value": {"type": "boolean", "kind": "expression"}}, {"name": "error", "default": "`false`", "description": "是否加载失败，加载失败后点击错误提示可以重新<br>触发`load`事件，必须使用`sync`修饰符", "value": {"type": "boolean", "kind": "expression"}}, {"name": "offset", "default": "`300`", "description": "滚动条与底部距离小于 offset 时触发`load`事件", "value": {"type": "number | string", "kind": "expression"}}, {"name": "loading-text", "default": "`加载中...`", "description": "加载过程中的提示文案", "value": {"type": "string", "kind": "expression"}}, {"name": "finished-text", "default": "-", "description": "加载完成后的提示文案", "value": {"type": "string", "kind": "expression"}}, {"name": "error-text", "default": "-", "description": "加载失败后的提示文案", "value": {"type": "string", "kind": "expression"}}, {"name": "immediate-check", "default": "`true`", "description": "是否在初始化时立即执行滚动位置检查", "value": {"type": "boolean", "kind": "expression"}}, {"name": "direction", "default": "`down`", "description": "滚动触发加载的方向，可选值为`up`", "value": {"type": "string", "kind": "expression"}}]}, {"name": "van-loading", "slots": [{"name": "default", "description": "加载文案"}], "events": [], "attributes": [{"name": "color", "default": "`#c9c9c9`", "description": "颜色", "value": {"type": "string", "kind": "expression"}}, {"name": "type", "default": "`circular`", "description": "类型，可选值为 `spinner`", "value": {"type": "string", "kind": "expression"}}, {"name": "size", "default": "`30px`", "description": "加载图标大小，默认单位为 `px`", "value": {"type": "number | string", "kind": "expression"}}, {"name": "text-size", "default": "`14px`", "description": "文字大小，默认单位为 `px`", "value": {"type": "number | string", "kind": "expression"}}, {"name": "text-color", "default": "`#c9c9c9`", "description": "文字颜色", "value": {"type": "string", "kind": "expression"}}, {"name": "vertical", "default": "`false`", "description": "是否垂直排列图标和文字内容", "value": {"type": "boolean", "kind": "expression"}}]}, {"name": "van-nav-bar", "slots": [{"name": "title", "description": "自定义标题"}, {"name": "left", "description": "自定义左侧区域内容"}, {"name": "right", "description": "自定义右侧区域内容"}], "events": [{"name": "click-left", "description": "点击左侧按钮时触发"}, {"name": "click-right", "description": "点击右侧按钮时触发"}], "attributes": [{"name": "title", "default": "`''`", "description": "标题", "value": {"type": "string", "kind": "expression"}}, {"name": "left-text", "default": "`''`", "description": "左侧文案", "value": {"type": "string", "kind": "expression"}}, {"name": "right-text", "default": "`''`", "description": "右侧文案", "value": {"type": "string", "kind": "expression"}}, {"name": "left-arrow", "default": "`false`", "description": "是否显示左侧箭头", "value": {"type": "boolean", "kind": "expression"}}, {"name": "border", "default": "`true`", "description": "是否显示下边框", "value": {"type": "boolean", "kind": "expression"}}, {"name": "fixed", "default": "`false`", "description": "是否固定在顶部", "value": {"type": "boolean", "kind": "expression"}}, {"name": "placeholder", "default": "`false`", "description": "固定在顶部时，是否在标签位置生成一个等高的占位元素", "value": {"type": "boolean", "kind": "expression"}}, {"name": "z-index", "default": "`1`", "description": "导航栏 z-index", "value": {"type": "number | string", "kind": "expression"}}, {"name": "safe-area-inset-top", "default": "`false`", "description": "是否开启[顶部安全区适配](#/zh-CN/advanced-usage#di-bu-an-quan-qu-gua-pei)", "value": {"type": "boolean", "kind": "expression"}}]}, {"name": "van-notice-bar", "slots": [{"name": "default", "description": "通知文本内容"}, {"name": "left-icon", "description": "自定义左侧图标"}, {"name": "right-icon", "description": "自定义右侧图标"}], "events": [{"name": "click", "description": "点击通知栏时触发"}, {"name": "close", "description": "关闭通知栏时触发"}, {"name": "replay", "description": "每当滚动栏重新开始滚动时触发"}], "attributes": [{"name": "mode", "default": "`''`", "description": "通知栏模式，可选值为 `closeable` `link`", "value": {"type": "string", "kind": "expression"}}, {"name": "text", "default": "`''`", "description": "通知文本内容", "value": {"type": "string", "kind": "expression"}}, {"name": "color", "default": "`#f60`", "description": "通知文本颜色", "value": {"type": "string", "kind": "expression"}}, {"name": "background", "default": "`#fff7cc`", "description": "滚动条背景", "value": {"type": "string", "kind": "expression"}}, {"name": "left-icon", "default": "-", "description": "左侧[图标名称](#/zh-CN/icon)或图片链接", "value": {"type": "string", "kind": "expression"}}, {"name": "delay", "default": "`1`", "description": "动画延迟时间 (s)", "value": {"type": "number | string", "kind": "expression"}}, {"name": "speed", "default": "`60`", "description": "滚动速率 (px/s)", "value": {"type": "number | string", "kind": "expression"}}, {"name": "scrollable", "default": "-", "description": "是否开启滚动播放，内容长度溢出时默认开启", "value": {"type": "boolean", "kind": "expression"}}, {"name": "wrapable", "default": "`false`", "description": "是否开启文本换行，只在禁用滚动时生效", "value": {"type": "boolean", "kind": "expression"}}]}, {"name": "van-number-keyboard", "slots": [{"name": "delete", "description": "自定义删除按键内容"}, {"name": "extra-key", "description": "自定义左下角按键内容"}, {"name": "title-left", "description": "自定义标题栏左侧内容"}], "events": [{"name": "input", "description": "点击按键时触发"}, {"name": "delete", "description": "点击删除键时触发"}, {"name": "close", "description": "点击关闭按钮时触发"}, {"name": "blur", "description": "点击关闭按钮或非键盘区域时触发"}, {"name": "show", "description": "键盘完全弹出时触发"}, {"name": "hide", "description": "键盘完全收起时触发"}], "attributes": [{"name": "v-model (value)", "default": "-", "description": "当前输入值", "value": {"type": "string", "kind": "expression"}}, {"name": "show", "default": "-", "description": "是否显示键盘", "value": {"type": "boolean", "kind": "expression"}}, {"name": "title", "default": "-", "description": "键盘标题", "value": {"type": "string", "kind": "expression"}}, {"name": "theme", "default": "`default`", "description": "样式风格，可选值为 `custom`", "value": {"type": "string", "kind": "expression"}}, {"name": "maxlength", "default": "-", "description": "输入值最大长度", "value": {"type": "number | string", "kind": "expression"}}, {"name": "transition", "default": "`true`", "description": "是否开启过场动画", "value": {"type": "boolean", "kind": "expression"}}, {"name": "z-index", "default": "`100`", "description": "键盘 z-index 层级", "value": {"type": "number | string", "kind": "expression"}}, {"name": "extra-key", "default": "`''`", "description": "底部额外按键的内容", "value": {"type": "string | string[]", "kind": "expression"}}, {"name": "close-button-text", "default": "-", "description": "关闭按钮文字，空则不展示", "value": {"type": "string", "kind": "expression"}}, {"name": "delete-button-text", "default": "-", "description": "删除按钮文字，空则展示删除图标", "value": {"type": "string", "kind": "expression"}}, {"name": "close-button-loading", "default": "`false`", "description": "是否将关闭按钮设置为加载中状态，仅在 `theme=\"custom\"` 时有效", "value": {"type": "boolean", "kind": "expression"}}, {"name": "show-delete-key", "default": "`true`", "description": "是否展示删除图标", "value": {"type": "boolean", "kind": "expression"}}, {"name": "hide-on-click-outside", "default": "`true`", "description": "点击外部时是否收起键盘", "value": {"type": "boolean", "kind": "expression"}}, {"name": "get-container", "default": "-", "description": "指定挂载的节点，[用法示例](#/zh-CN/popup#zhi-ding-gua-zai-wei-zhi)", "value": {"type": "string | () => Element", "kind": "expression"}}, {"name": "safe-area-inset-bottom", "default": "`true`", "description": "是否开启[底部安全区适配](#/zh-CN/advanced-usage#di-bu-an-quan-qu-gua-pei)", "value": {"type": "boolean", "kind": "expression"}}, {"name": "random-key-order", "default": "`false`", "description": "是否将通过随机顺序展示按键", "value": {"type": "boolean", "kind": "expression"}}]}, {"name": "van-overlay", "slots": [{"name": "default", "description": "默认插槽，用于在遮罩层上方嵌入内容"}], "events": [{"name": "click", "description": "点击时触发"}], "attributes": [{"name": "show", "default": "`false`", "description": "是否展示遮罩层", "value": {"type": "boolean", "kind": "expression"}}, {"name": "z-index", "default": "`1`", "description": "z-index 层级", "value": {"type": "number | string", "kind": "expression"}}, {"name": "duration", "default": "`0.3`", "description": "动画时长，单位秒", "value": {"type": "number | string", "kind": "expression"}}, {"name": "class-name", "default": "-", "description": "自定义类名", "value": {"type": "string", "kind": "expression"}}, {"name": "custom-style", "default": "-", "description": "自定义样式", "value": {"type": "object", "kind": "expression"}}, {"name": "lock-scroll", "default": "`true`", "description": "是否锁定背景滚动，锁定时蒙层里的内容也将无法滚动", "value": {"type": "boolean", "kind": "expression"}}]}, {"name": "van-pagination", "slots": [{"name": "page", "description": "自定义页码"}, {"name": "prev-text", "description": "自定义上一页按钮文字"}, {"name": "next-text", "description": "自定义下一页按钮文字"}], "events": [{"name": "change", "description": "页码改变时触发"}], "attributes": [{"name": "v-model", "default": "-", "description": "当前页码", "value": {"type": "number", "kind": "expression"}}, {"name": "mode", "default": "`multi`", "description": "显示模式，可选值为 `simple`", "value": {"type": "string", "kind": "expression"}}, {"name": "prev-text", "default": "`上一页`", "description": "上一页按钮文字", "value": {"type": "string", "kind": "expression"}}, {"name": "next-text", "default": "`下一页`", "description": "下一页按钮文字", "value": {"type": "string", "kind": "expression"}}, {"name": "page-count", "default": "根据页数计算", "description": "总页数", "value": {"type": "number | string", "kind": "expression"}}, {"name": "total-items", "default": "`0`", "description": "总记录数", "value": {"type": "number | string", "kind": "expression"}}, {"name": "items-per-page", "default": "`10`", "description": "每页记录数", "value": {"type": "number | string", "kind": "expression"}}, {"name": "show-page-size", "default": "`5`", "description": "显示的页码个数", "value": {"type": "number | string", "kind": "expression"}}, {"name": "force-ellipses", "default": "`false`", "description": "是否显示省略号", "value": {"type": "boolean", "kind": "expression"}}]}, {"name": "van-panel", "slots": [{"name": "default", "description": "自定义内容"}, {"name": "header", "description": "自定义 header"}, {"name": "footer", "description": "自定义 footer"}], "events": [], "attributes": [{"name": "title", "default": "-", "description": "标题", "value": {"type": "string", "kind": "expression"}}, {"name": "desc", "default": "-", "description": "描述", "value": {"type": "string", "kind": "expression"}}, {"name": "status", "default": "-", "description": "状态", "value": {"type": "string", "kind": "expression"}}, {"name": "icon", "default": "-", "description": "标题左侧[图标名称](#/zh-CN/icon)或图片链接", "value": {"type": "string", "kind": "expression"}}]}, {"name": "van-password-input", "slots": [], "events": [{"name": "focus", "description": "输入框聚焦时触发"}], "attributes": [{"name": "value", "default": "`''`", "description": "密码值", "value": {"type": "string", "kind": "expression"}}, {"name": "info", "default": "-", "description": "输入框下方文字提示", "value": {"type": "string", "kind": "expression"}}, {"name": "error-info", "default": "-", "description": "输入框下方错误提示", "value": {"type": "string", "kind": "expression"}}, {"name": "length", "default": "`6`", "description": "密码最大长度", "value": {"type": "number | string", "kind": "expression"}}, {"name": "gutter", "default": "`0`", "description": "输入框格子之间的间距，如 `20px` `2em`，默认单位为`px`", "value": {"type": "number | string", "kind": "expression"}}, {"name": "mask", "default": "`true`", "description": "是否隐藏密码内容", "value": {"type": "boolean", "kind": "expression"}}, {"name": "focused", "default": "`false`", "description": "是否已聚焦，聚焦时会显示光标", "value": {"type": "boolean", "kind": "expression"}}]}, {"name": "van-picker", "slots": [{"name": "default", "description": "自定义整个顶部栏的内容"}, {"name": "title", "description": "自定义标题内容"}, {"name": "confirm", "description": "自定义确认按钮内容"}, {"name": "cancel", "description": "自定义取消按钮内容"}, {"name": "option", "description": "自定义选项内容"}, {"name": "columns-top", "description": "自定义选项上方内容"}, {"name": "columns-bottom", "description": "自定义选项下方内容"}], "events": [{"name": "confirm", "description": "点击完成按钮时触发"}, {"name": "cancel", "description": "点击取消按钮时触发"}, {"name": "change", "description": "选项改变时触发"}], "attributes": [{"name": "columns", "default": "`[]`", "description": "对象数组，配置每一列显示的数据", "value": {"type": "Column[]", "kind": "expression"}}, {"name": "title", "default": "-", "description": "顶部栏标题", "value": {"type": "string", "kind": "expression"}}, {"name": "confirm-button-text", "default": "`确认`", "description": "确认按钮文字", "value": {"type": "string", "kind": "expression"}}, {"name": "cancel-button-text", "default": "`取消`", "description": "取消按钮文字", "value": {"type": "string", "kind": "expression"}}, {"name": "value-key", "default": "`text`", "description": "选项对象中，选项文字对应的键名", "value": {"type": "string", "kind": "expression"}}, {"name": "toolbar-position", "default": "`top`", "description": "顶部栏位置，可选值为`bottom`", "value": {"type": "string", "kind": "expression"}}, {"name": "loading", "default": "`false`", "description": "是否显示加载状态", "value": {"type": "boolean", "kind": "expression"}}, {"name": "readonly", "default": "`false`", "description": "是否为只读状态，只读状态下无法切换选项", "value": {"type": "boolean", "kind": "expression"}}, {"name": "show-toolbar", "default": "`false`", "description": "是否显示顶部栏", "value": {"type": "boolean", "kind": "expression"}}, {"name": "allow-html", "default": "`true`", "description": "是否允许选项内容中渲染 HTML", "value": {"type": "boolean", "kind": "expression"}}, {"name": "default-index", "default": "`0`", "description": "单列选择时，默认选中项的索引", "value": {"type": "number | string", "kind": "expression"}}, {"name": "item-height", "default": "`44`", "description": "选项高度，支持 `px` `vw` `vh` `rem` 单位，默认 `px`", "value": {"type": "number | string", "kind": "expression"}}, {"name": "visible-item-count", "default": "`6`", "description": "可见的选项个数", "value": {"type": "number | string", "kind": "expression"}}, {"name": "swipe-duration", "default": "`1000`", "description": "快速滑动时惯性滚动的时长，单位 `ms`", "value": {"type": "number | string", "kind": "expression"}}]}, {"name": "van-popover", "slots": [{"name": "default", "description": "自定义菜单内容"}, {"name": "reference", "description": "触发 Popover 显示的元素内容"}], "events": [{"name": "select", "description": "点击选项时触发"}, {"name": "open", "description": "打开菜单时触发"}, {"name": "close", "description": "关闭菜单时触发"}, {"name": "opened", "description": "打开菜单且动画结束后触发"}, {"name": "closed", "description": "关闭菜单且动画结束后触发"}], "attributes": [{"name": "v-model", "default": "`false`", "description": "是否展示气泡弹出层", "value": {"type": "boolean", "kind": "expression"}}, {"name": "actions", "default": "`[]`", "description": "选项列表", "value": {"type": "Action[]", "kind": "expression"}}, {"name": "placement", "default": "`bottom`", "description": "弹出位置", "value": {"type": "string", "kind": "expression"}}, {"name": "theme", "default": "`light`", "description": "主题风格，可选值为 `dark`", "value": {"type": "string", "kind": "expression"}}, {"name": "trigger", "description": "触发方式，可选值为 `click`", "value": {"type": "-", "kind": "expression"}}, {"name": "offset", "default": "`[0, 8]`", "description": "出现位置的偏移量", "value": {"type": "[number, number]", "kind": "expression"}}, {"name": "overlay", "default": "`false`", "description": "是否显示遮罩层", "value": {"type": "boolean", "kind": "expression"}}, {"name": "close-on-click-action", "default": "`true`", "description": "是否在点击选项后关闭", "value": {"type": "boolean", "kind": "expression"}}, {"name": "close-on-click-outside", "default": "`true`", "description": "是否在点击外部元素后关闭菜单", "value": {"type": "boolean", "kind": "expression"}}, {"name": "get-container", "default": "`body`", "description": "指定挂载的节点，[用法示例](#/zh-CN/popup#zhi-ding-gua-zai-wei-zhi)", "value": {"type": "string | () => Element", "kind": "expression"}}]}, {"name": "van-popup", "slots": [], "events": [{"name": "click", "description": "点击弹出层时触发"}, {"name": "click-overlay", "description": "点击遮罩层时触发"}, {"name": "click-close-icon", "description": "点击关闭图标时触发"}, {"name": "open", "description": "打开弹出层时触发"}, {"name": "close", "description": "关闭弹出层时触发"}, {"name": "opened", "description": "打开弹出层且动画结束后触发"}, {"name": "closed", "description": "关闭弹出层且动画结束后触发"}], "attributes": [{"name": "v-model (value)", "default": "`false`", "description": "是否显示弹出层", "value": {"type": "boolean", "kind": "expression"}}, {"name": "overlay", "default": "`true`", "description": "是否显示遮罩层", "value": {"type": "boolean", "kind": "expression"}}, {"name": "position", "default": "`center`", "description": "弹出位置，可选值为 `top` `bottom` `right` `left`", "value": {"type": "string", "kind": "expression"}}, {"name": "overlay-class", "default": "-", "description": "自定义遮罩层类名", "value": {"type": "string", "kind": "expression"}}, {"name": "overlay-style", "default": "-", "description": "自定义遮罩层样式", "value": {"type": "object", "kind": "expression"}}, {"name": "duration", "default": "`0.3`", "description": "动画时长，单位秒", "value": {"type": "number | string", "kind": "expression"}}, {"name": "round", "default": "`false`", "description": "是否显示圆角", "value": {"type": "boolean", "kind": "expression"}}, {"name": "lock-scroll", "default": "`true`", "description": "是否锁定背景滚动", "value": {"type": "boolean", "kind": "expression"}}, {"name": "lazy-render", "default": "`true`", "description": "是否在显示弹层时才渲染节点", "value": {"type": "boolean", "kind": "expression"}}, {"name": "close-on-popstate", "default": "`false`", "description": "是否在页面回退时自动关闭", "value": {"type": "boolean", "kind": "expression"}}, {"name": "close-on-click-overlay", "default": "`true`", "description": "是否在点击遮罩层后关闭", "value": {"type": "boolean", "kind": "expression"}}, {"name": "closeable", "default": "`false`", "description": "是否显示关闭图标", "value": {"type": "boolean", "kind": "expression"}}, {"name": "close-icon", "default": "`cross`", "description": "关闭图标名称或图片链接", "value": {"type": "string", "kind": "expression"}}, {"name": "close-icon-position", "default": "`top-right`", "description": "关闭图标位置，可选值为`top-left`<br>`bottom-left` `bottom-right`", "value": {"type": "string", "kind": "expression"}}, {"name": "transition", "default": "-", "description": "动画类名，等价于 [transition](https://cn.vuejs.org/v2/api/index.html#transition) 的`name`属性", "value": {"type": "string", "kind": "expression"}}, {"name": "transition-appear", "default": "`false`", "description": "是否在初始渲染时启用过渡动画", "value": {"type": "boolean", "kind": "expression"}}, {"name": "get-container", "default": "-", "description": "指定挂载的节点", "value": {"type": "string | () => Element", "kind": "expression"}}, {"name": "safe-area-inset-bottom", "default": "`false`", "description": "是否开启[底部安全区适配](#/zh-CN/advanced-usage#di-bu-an-quan-qu-gua-pei)", "value": {"type": "boolean", "kind": "expression"}}]}, {"name": "van-progress", "slots": [], "events": [], "attributes": [{"name": "percentage", "default": "`0`", "description": "进度百分比", "value": {"type": "number | string", "kind": "expression"}}, {"name": "stroke-width", "default": "`4px`", "description": "进度条粗细，默认单位为`px`", "value": {"type": "number | string", "kind": "expression"}}, {"name": "color", "default": "`#1989fa`", "description": "进度条颜色", "value": {"type": "string", "kind": "expression"}}, {"name": "track-color", "default": "`#e5e5e5`", "description": "轨道颜色", "value": {"type": "string", "kind": "expression"}}, {"name": "pivot-text", "default": "百分比", "description": "进度文字内容", "value": {"type": "string", "kind": "expression"}}, {"name": "pivot-color", "default": "同进度条颜色", "description": "进度文字背景色", "value": {"type": "string", "kind": "expression"}}, {"name": "text-color", "default": "`white`", "description": "进度文字颜色", "value": {"type": "string", "kind": "expression"}}, {"name": "inactive", "default": "`false`", "description": "是否置灰", "value": {"type": "boolean", "kind": "expression"}}, {"name": "show-pivot", "default": "`true`", "description": "是否显示进度文字", "value": {"type": "boolean", "kind": "expression"}}]}, {"name": "van-pull-refresh", "slots": [{"name": "default", "description": "自定义内容"}, {"name": "normal", "description": "非下拉状态时顶部内容"}, {"name": "pulling", "description": "下拉过程中顶部内容"}, {"name": "loosing", "description": "释放过程中顶部内容"}, {"name": "loading", "description": "加载过程中顶部内容"}, {"name": "success", "description": "刷新成功提示内容"}], "events": [{"name": "refresh", "description": "下拉刷新时触发"}], "attributes": [{"name": "v-model", "default": "-", "description": "是否处于加载中状态", "value": {"type": "boolean", "kind": "expression"}}, {"name": "pulling-text", "default": "`下拉即可刷新...`", "description": "下拉过程提示文案", "value": {"type": "string", "kind": "expression"}}, {"name": "loosing-text", "default": "`释放即可刷新...`", "description": "释放过程提示文案", "value": {"type": "string", "kind": "expression"}}, {"name": "loading-text", "default": "`加载中...`", "description": "加载过程提示文案", "value": {"type": "string", "kind": "expression"}}, {"name": "success-text", "default": "-", "description": "刷新成功提示文案", "value": {"type": "string", "kind": "expression"}}, {"name": "success-duration", "default": "`500`", "description": "刷新成功提示展示时长(ms)", "value": {"type": "number | string", "kind": "expression"}}, {"name": "animation-duration", "default": "`300`", "description": "动画时长", "value": {"type": "number | string", "kind": "expression"}}, {"name": "head-height", "default": "`50`", "description": "顶部内容高度", "value": {"type": "number | string", "kind": "expression"}}, {"name": "pull-distance", "default": "与 `head-height` 一致", "description": "触发下拉刷新的距离", "value": {"type": "number | string", "kind": "expression"}}, {"name": "disabled", "default": "`false`", "description": "是否禁用下拉刷新", "value": {"type": "boolean", "kind": "expression"}}]}, {"name": "van-radio", "slots": [{"name": "default", "description": "自定义文本"}, {"name": "icon", "description": "自定义图标"}], "events": [{"name": "click", "description": "点击单选框时触发"}], "attributes": [{"name": "name", "default": "-", "description": "标识符", "value": {"type": "any", "kind": "expression"}}, {"name": "shape", "default": "`round`", "description": "形状，可选值为 `square`", "value": {"type": "string", "kind": "expression"}}, {"name": "disabled", "default": "`false`", "description": "是否为禁用状态", "value": {"type": "boolean", "kind": "expression"}}, {"name": "label-disabled", "default": "`false`", "description": "是否禁用文本内容点击", "value": {"type": "boolean", "kind": "expression"}}, {"name": "label-position", "default": "`right`", "description": "文本位置，可选值为 `left`", "value": {"type": "string", "kind": "expression"}}, {"name": "icon-size", "default": "`20px`", "description": "图标大小，默认单位为`px`", "value": {"type": "number | string", "kind": "expression"}}, {"name": "checked-color", "default": "`#1989fa`", "description": "选中状态颜色", "value": {"type": "string", "kind": "expression"}}]}, {"name": "van-radio-group", "slots": [], "events": [{"name": "change", "description": "当绑定值变化时触发的事件"}], "attributes": [{"name": "v-model (value)", "default": "-", "description": "当前选中项的标识符", "value": {"type": "any", "kind": "expression"}}, {"name": "disabled", "default": "`false`", "description": "是否禁用所有单选框", "value": {"type": "boolean", "kind": "expression"}}, {"name": "direction", "default": "`vertical`", "description": "排列方向，可选值为`horizontal`", "value": {"type": "string", "kind": "expression"}}, {"name": "icon-size", "default": "`20px`", "description": "所有单选框的图标大小，默认单位为`px`", "value": {"type": "number | string", "kind": "expression"}}, {"name": "checked-color", "default": "`#1989fa`", "description": "所有单选框的选中状态颜色", "value": {"type": "string", "kind": "expression"}}]}, {"name": "van-rate", "slots": [], "events": [{"name": "change", "description": "当前分值变化时触发的事件"}], "attributes": [{"name": "v-model", "default": "-", "description": "当前分值", "value": {"type": "number", "kind": "expression"}}, {"name": "count", "default": "`5`", "description": "图标总数", "value": {"type": "number | string", "kind": "expression"}}, {"name": "size", "default": "`20px`", "description": "图标大小，默认单位为`px`", "value": {"type": "number | string", "kind": "expression"}}, {"name": "gutter", "default": "`4px`", "description": "图标间距，默认单位为`px`", "value": {"type": "number | string", "kind": "expression"}}, {"name": "color", "default": "`#ee0a24`", "description": "选中时的颜色", "value": {"type": "string", "kind": "expression"}}, {"name": "void-color", "default": "`#c8c9cc`", "description": "未选中时的颜色", "value": {"type": "string", "kind": "expression"}}, {"name": "disabled-color", "default": "`#c8c9cc`", "description": "禁用时的颜色", "value": {"type": "string", "kind": "expression"}}, {"name": "icon", "default": "`star`", "description": "选中时的[图标名称](#/zh-CN/icon)或图片链接", "value": {"type": "string", "kind": "expression"}}, {"name": "void-icon", "default": "`star-o`", "description": "未选中时的[图标名称](#/zh-CN/icon)或图片链接", "value": {"type": "string", "kind": "expression"}}, {"name": "icon-prefix", "default": "`van-icon`", "description": "图标类名前缀，同 Icon 组件的 [class-prefix 属性](#/zh-CN/icon#props)", "value": {"type": "string", "kind": "expression"}}, {"name": "allow-half", "default": "`false`", "description": "是否允许半选", "value": {"type": "boolean", "kind": "expression"}}, {"name": "readonly", "default": "`false`", "description": "是否为只读状态 \b", "value": {"type": "boolean", "kind": "expression"}}, {"name": "disabled", "default": "`false`", "description": "是否禁用评分", "value": {"type": "boolean", "kind": "expression"}}, {"name": "touchable", "default": "`true`", "description": "是否可以通过滑动手势选择评分", "value": {"type": "boolean", "kind": "expression"}}]}, {"name": "van-search", "slots": [{"name": "left", "description": "自定义左侧内容（搜索框外）"}, {"name": "action", "description": "自定义右侧内容（搜索框外），设置`show-action`属性后展示"}, {"name": "label", "description": "自定义左侧文本（搜索框内）"}, {"name": "left-icon", "description": "自定义左侧图标（搜索框内）"}, {"name": "right-icon", "description": "自定义右侧图标（搜索框内）"}], "events": [{"name": "search", "description": "确定搜索时触发"}, {"name": "input", "description": "输入框内容变化时触发"}, {"name": "focus", "description": "输入框获得焦点时触发"}, {"name": "blur", "description": "输入框失去焦点时触发"}, {"name": "clear", "description": "点击清除按钮后触发"}, {"name": "cancel", "description": "点击取消按钮时触发"}], "attributes": [{"name": "label", "default": "-", "description": "搜索框左侧文本", "value": {"type": "string", "kind": "expression"}}, {"name": "shape", "default": "`square`", "description": "搜索框形状，可选值为 `round`", "value": {"type": "string", "kind": "expression"}}, {"name": "background", "default": "`#f2f2f2`", "description": "搜索框外部背景色", "value": {"type": "string", "kind": "expression"}}, {"name": "maxlength", "default": "-", "description": "输入的最大字符数", "value": {"type": "number | string", "kind": "expression"}}, {"name": "placeholder", "default": "-", "description": "占位提示文字", "value": {"type": "string", "kind": "expression"}}, {"name": "clearable", "default": "`true`", "description": "是否启用清除图标，点击清除图标后会清空输入框", "value": {"type": "boolean", "kind": "expression"}}, {"name": "clear-trigger", "default": "`focus`", "description": "显示清除图标的时机，`always` 表示输入框不为空时展示，<br>`focus` 表示输入框聚焦且不为空时展示", "value": {"type": "string", "kind": "expression"}}, {"name": "autofocus", "default": "`false`", "description": "是否自动聚焦，iOS 系统不支持该属性", "value": {"type": "boolean", "kind": "expression"}}, {"name": "show-action", "default": "`false`", "description": "是否在搜索框右侧显示取消按钮", "value": {"type": "boolean", "kind": "expression"}}, {"name": "action-text", "default": "`取消`", "description": "取消按钮文字", "value": {"type": "boolean", "kind": "expression"}}, {"name": "disabled", "default": "`false`", "description": "是否禁用输入框", "value": {"type": "boolean", "kind": "expression"}}, {"name": "readonly", "default": "`false`", "description": "是否将输入框设为只读", "value": {"type": "boolean", "kind": "expression"}}, {"name": "error", "default": "`false`", "description": "是否将输入内容标红", "value": {"type": "boolean", "kind": "expression"}}, {"name": "input-align", "default": "`left`", "description": "输入框内容对齐方式，可选值为 `center` `right`", "value": {"type": "string", "kind": "expression"}}, {"name": "left-icon", "default": "`search`", "description": "输入框左侧[图标名称](#/zh-CN/icon)或图片链接", "value": {"type": "string", "kind": "expression"}}, {"name": "right-icon", "default": "-", "description": "输入框右侧[图标名称](#/zh-CN/icon)或图片链接", "value": {"type": "string", "kind": "expression"}}]}, {"name": "van-share-sheet", "slots": [{"name": "title", "description": "自定义顶部标题"}, {"name": "description", "description": "自定义描述文字"}], "events": [{"name": "select", "description": "点击分享选项时触发"}, {"name": "cancel", "description": "点击取消按钮时触发"}, {"name": "click-overlay", "description": "点击遮罩层时触发"}], "attributes": [{"name": "options", "default": "`[]`", "description": "分享选项", "value": {"type": "Option[]", "kind": "expression"}}, {"name": "title", "default": "-", "description": "顶部标题", "value": {"type": "string", "kind": "expression"}}, {"name": "cancel-text", "default": "`'取消'`", "description": "取消按钮文字，传入空字符串可以隐藏按钮", "value": {"type": "string", "kind": "expression"}}, {"name": "description", "default": "-", "description": "标题下方的辅助描述文字", "value": {"type": "string", "kind": "expression"}}, {"name": "duration", "default": "`0.3`", "description": "动画时长，单位秒", "value": {"type": "number | string", "kind": "expression"}}, {"name": "overlay", "default": "`true`", "description": "是否显示遮罩层", "value": {"type": "boolean", "kind": "expression"}}, {"name": "lock-scroll", "default": "`true`", "description": "是否锁定背景滚动", "value": {"type": "boolean", "kind": "expression"}}, {"name": "lazy-render", "default": "`true`", "description": "是否在显示弹层时才渲染内容", "value": {"type": "boolean", "kind": "expression"}}, {"name": "close-on-popstate", "default": "`true`", "description": "是否在页面回退时自动关闭", "value": {"type": "boolean", "kind": "expression"}}, {"name": "close-on-click-overlay", "default": "`true`", "description": "是否在点击遮罩层后关闭", "value": {"type": "boolean", "kind": "expression"}}, {"name": "safe-area-inset-bottom", "default": "`true`", "description": "是否开启[底部安全区适配](#/zh-CN/advanced-usage#di-bu-an-quan-qu-gua-pei)", "value": {"type": "boolean", "kind": "expression"}}, {"name": "get-container", "default": "-", "description": "指定挂载的节点，[用法示例](#/zh-CN/popup#zhi-ding-gua-zai-wei-zhi)", "value": {"type": "string | () => Element", "kind": "expression"}}]}, {"name": "van-sidebar", "slots": [], "events": [{"name": "change", "description": "切换导航项时触发"}], "attributes": [{"name": "v-model", "default": "`0`", "description": "当前导航项的索引", "value": {"type": "number | string", "kind": "expression"}}]}, {"name": "van-sidebar-item", "slots": [{"name": "title", "description": "自定义标题"}], "events": [{"name": "click", "description": "点击时触发"}], "attributes": [{"name": "title", "default": "`''`", "description": "内容", "value": {"type": "string", "kind": "expression"}}, {"name": "dot", "default": "`false`", "description": "是否显示右上角小红点", "value": {"type": "boolean", "kind": "expression"}}, {"name": "badge", "default": "-", "description": "图标右上角徽标的内容", "value": {"type": "number | string", "kind": "expression"}}, {"name": "info", "default": "-", "description": "图标右上角徽标的内容（已废弃，请使用 badge 属性）", "value": {"type": "number | string", "kind": "expression"}}, {"name": "disabled", "default": "`false`", "description": "是否禁用该项", "value": {"type": "boolean", "kind": "expression"}}, {"name": "url", "default": "-", "description": "点击后跳转的链接地址", "value": {"type": "string", "kind": "expression"}}, {"name": "to", "default": "-", "description": "点击后跳转的目标路由对象，同 vue-router 的 [to 属性](https://router.vuejs.org/zh/api/#to)", "value": {"type": "string | object", "kind": "expression"}}, {"name": "replace", "default": "`false`", "description": "是否在跳转时替换当前页面历史", "value": {"type": "boolean", "kind": "expression"}}]}, {"name": "van-skeleton", "slots": [], "events": [], "attributes": [{"name": "row", "default": "`0`", "description": "段落占位图行数", "value": {"type": "number | string", "kind": "expression"}}, {"name": "row-width", "default": "<br>(number \\", "description": "段落占位图宽度，可传数组来设置每一行的宽度", "value": {"type": "number | string \\", "kind": "expression"}}, {"name": "title", "default": "`false`", "description": "是否显示标题占位图", "value": {"type": "boolean", "kind": "expression"}}, {"name": "avatar", "default": "`false`", "description": "是否显示头像占位图", "value": {"type": "boolean", "kind": "expression"}}, {"name": "loading", "default": "`true`", "description": "是否显示骨架屏，传 `false` 时会展示子组件内容", "value": {"type": "boolean", "kind": "expression"}}, {"name": "animate", "default": "`true`", "description": "是否开启动画", "value": {"type": "boolean", "kind": "expression"}}, {"name": "round", "default": "`false`", "description": "是否将标题和段落显示为圆角风格", "value": {"type": "boolean", "kind": "expression"}}, {"name": "title-width", "default": "`40%`", "description": "标题占位图宽度", "value": {"type": "number | string", "kind": "expression"}}, {"name": "avatar-size", "default": "`32px`", "description": "头像占位图大小", "value": {"type": "number | string", "kind": "expression"}}, {"name": "avatar-shape", "default": "`round`", "description": "头像占位图形状，可选值为`square`", "value": {"type": "string", "kind": "expression"}}]}, {"name": "van-sku", "slots": [{"name": "sku-header", "description": "商品信息展示区，包含商品图片、名称、价格等信息"}, {"name": "sku-header-price", "description": "自定义 sku 头部价格展示"}, {"name": "sku-header-origin-price", "description": "自定义 sku 头部原价展示"}, {"name": "sku-header-extra", "description": "额外 sku 头部区域"}, {"name": "sku-header-image-extra", "description": "自定义 sku 头部图片额外的展示"}, {"name": "sku-body-top", "description": "sku 展示区上方的内容，无默认展示内容，按需使用"}, {"name": "sku-group", "description": "商品 sku 展示区"}, {"name": "extra-sku-group", "description": "额外商品 sku 展示区，一般用不到"}, {"name": "sku-stepper", "description": "商品数量选择区"}, {"name": "sku-messages", "description": "商品留言区"}, {"name": "sku-actions-top", "description": "操作按钮区顶部内容，无默认展示内容，按需使用"}, {"name": "sku-actions", "description": "操作按钮区"}], "events": [{"name": "add-cart", "description": "点击添加购物车回调"}, {"name": "buy-clicked", "description": "点击购买回调"}, {"name": "stepper-change", "description": "购买数量变化时触发"}, {"name": "sku-selected", "description": "切换规格类目时触发"}, {"name": "sku-prop-selected", "description": "切换商品属性时触发"}, {"name": "open-preview", "description": "打开商品图片预览时触发"}, {"name": "close-preview", "description": "关闭商品图片预览时触发"}, {"name": "sku-reset", "description": "规格和属性被重置时触发"}], "attributes": [{"name": "v-model", "default": "`false`", "description": "是否显示商品规格弹窗", "value": {"type": "boolean", "kind": "expression"}}, {"name": "sku", "default": "-", "description": "商品 sku 数据", "value": {"type": "object", "kind": "expression"}}, {"name": "goods", "default": "-", "description": "商品信息", "value": {"type": "object", "kind": "expression"}}, {"name": "goods-id", "default": "-", "description": "商品 id", "value": {"type": "number | string", "kind": "expression"}}, {"name": "price-tag", "default": "-", "description": "显示在价格后面的标签", "value": {"type": "string", "kind": "expression"}}, {"name": "hide-stock", "default": "`false`", "description": "是否显示商品剩余库存", "value": {"type": "boolean", "kind": "expression"}}, {"name": "hide-quota-text", "default": "`false`", "description": "是否显示限购提示", "value": {"type": "boolean", "kind": "expression"}}, {"name": "hide-selected-text", "default": "`false`", "description": "是否隐藏已选提示", "value": {"type": "boolean", "kind": "expression"}}, {"name": "stock-threshold", "default": "`50`", "description": "库存阈值。低于这个值会把库存数高亮显示", "value": {"type": "boolean", "kind": "expression"}}, {"name": "show-add-cart-btn", "default": "`true`", "description": "是否显示加入购物车按钮", "value": {"type": "boolean", "kind": "expression"}}, {"name": "buy-text", "default": "`立即购买`", "description": "购买按钮文字", "value": {"type": "string", "kind": "expression"}}, {"name": "add-cart-text", "default": "`加入购物车`", "description": "加入购物车按钮文字", "value": {"type": "string", "kind": "expression"}}, {"name": "quota", "default": "`0`", "description": "限购数，0 表示不限购", "value": {"type": "number", "kind": "expression"}}, {"name": "quota-used", "default": "`0`", "description": "已经购买过的数量", "value": {"type": "number", "kind": "expression"}}, {"name": "reset-stepper-on-hide", "default": "`false`", "description": "隐藏时重置选择的商品数量", "value": {"type": "boolean", "kind": "expression"}}, {"name": "reset-selected-sku-on-hide", "default": "`false`", "description": "隐藏时重置已选择的 sku", "value": {"type": "boolean", "kind": "expression"}}, {"name": "disable-stepper-input", "default": "`false`", "description": "是否禁用步进器输入", "value": {"type": "boolean", "kind": "expression"}}, {"name": "close-on-click-overlay", "default": "`true`", "description": "是否在点击遮罩层后关闭", "value": {"type": "boolean", "kind": "expression"}}, {"name": "stepper-title", "default": "`购买数量`", "description": "数量选择组件左侧文案", "value": {"type": "string", "kind": "expression"}}, {"name": "custom-stepper-config", "default": "`{}`", "description": "步进器相关自定义配置", "value": {"type": "object", "kind": "expression"}}, {"name": "message-config", "default": "`{}`", "description": "留言相关配置", "value": {"type": "object", "kind": "expression"}}, {"name": "get-container", "default": "-", "description": "指定挂载的节点，[用法示例](#/zh-CN/popup#zhi-ding-gua-zai-wei-zhi)", "value": {"type": "string | () => Element", "kind": "expression"}}, {"name": "initial-sku", "default": "`{}`", "description": "默认选中的 sku，具体参考高级用法", "value": {"type": "object", "kind": "expression"}}, {"name": "show-soldout-sku", "default": "`true`", "description": "是否展示售罄的 sku，默认展示并置灰", "value": {"type": "boolean", "kind": "expression"}}, {"name": "disable-soldout-sku", "default": "`true`", "description": "是否禁用售罄的 sku", "value": {"type": "boolean", "kind": "expression"}}, {"name": "safe-area-inset-bottom", "default": "`true`", "description": "是否开启[底部安全区适配](#/zh-CN/advanced-usage#di-bu-an-quan-qu-gua-pei)", "value": {"type": "boolean", "kind": "expression"}}, {"name": "start-sale-num", "default": "`1`", "description": "起售数量", "value": {"type": "number", "kind": "expression"}}, {"name": "properties", "default": "-", "description": "商品属性", "value": {"type": "array", "kind": "expression"}}, {"name": "preview-on-click-image", "default": "`true`", "description": "是否在点击商品图片时自动预览", "value": {"type": "boolean", "kind": "expression"}}, {"name": "show-header-image", "default": "`true`", "description": "是否展示头部图片", "value": {"type": "boolean", "kind": "expression"}}, {"name": "lazy-load", "default": "`false`", "description": "是否开启图片懒加载，须配合 [Lazyload](#/zh-CN/lazyload) 组件使用", "value": {"type": "boolean", "kind": "expression"}}]}, {"name": "van-slider", "slots": [{"name": "button", "description": "自定义滑动按钮"}, {"name": "left-button", "description": "自定义左侧滑块按钮（双滑块模式下）"}, {"name": "right-button", "description": "自定义右侧滑块按钮（双滑块模式下）"}], "events": [{"name": "input", "description": "进度变化时实时触发"}, {"name": "change", "description": "进度变化且结束拖动后触发"}, {"name": "drag-start", "description": "开始拖动时触发"}, {"name": "drag-end", "description": "结束拖动时触发"}], "attributes": [{"name": "value", "default": "`0`", "description": "当前进度百分比", "value": {"type": "number | array", "kind": "expression"}}, {"name": "max", "default": "`100`", "description": "最大值", "value": {"type": "number | string", "kind": "expression"}}, {"name": "min", "default": "`0`", "description": "最小值", "value": {"type": "number | string", "kind": "expression"}}, {"name": "step", "default": "`1`", "description": "步长", "value": {"type": "number | string", "kind": "expression"}}, {"name": "bar-height", "default": "`2px`", "description": "进度条高度，默认单位为`px`", "value": {"type": "number | string", "kind": "expression"}}, {"name": "button-size", "default": "`24px`", "description": "滑块按钮大小，默认单位为`px`", "value": {"type": "number | string", "kind": "expression"}}, {"name": "active-color", "default": "`#1989fa`", "description": "进度条激活态颜色", "value": {"type": "string", "kind": "expression"}}, {"name": "inactive-color", "default": "`#e5e5e5`", "description": "进度条非激活态颜色", "value": {"type": "string", "kind": "expression"}}, {"name": "range", "default": "`false`", "description": "是否开启双滑块模式", "value": {"type": "boolean", "kind": "expression"}}, {"name": "disabled", "default": "`false`", "description": "是否禁用滑块", "value": {"type": "boolean", "kind": "expression"}}, {"name": "vertical", "default": "`false`", "description": "是否垂直展示", "value": {"type": "boolean", "kind": "expression"}}]}, {"name": "van-stepper", "slots": [], "events": [{"name": "change", "description": "当绑定值变化时触发的事件"}, {"name": "overlimit", "description": "点击不可用的按钮时触发"}, {"name": "plus", "description": "点击增加按钮时触发"}, {"name": "minus", "description": "点击减少按钮时触发"}, {"name": "focus", "description": "输入框聚焦时触发"}, {"name": "blur", "description": "输入框失焦时触发"}], "attributes": [{"name": "v-model", "default": "-", "description": "当前输入的值", "value": {"type": "number | string", "kind": "expression"}}, {"name": "min", "default": "`1`", "description": "最小值", "value": {"type": "number | string", "kind": "expression"}}, {"name": "max", "default": "-", "description": "最大值", "value": {"type": "number | string", "kind": "expression"}}, {"name": "default-value", "default": "`1`", "description": "初始值，当 v-model 为空时生效", "value": {"type": "number | string", "kind": "expression"}}, {"name": "step", "default": "`1`", "description": "步长，每次点击时改变的值", "value": {"type": "number | string", "kind": "expression"}}, {"name": "name", "default": "-", "description": "标识符，可以在`change`事件回调参数中获取", "value": {"type": "number | string", "kind": "expression"}}, {"name": "input-width", "default": "`32px`", "description": "输入框宽度，默认单位为`px`", "value": {"type": "number | string", "kind": "expression"}}, {"name": "button-size", "default": "`28px`", "description": "按钮大小以及输入框高度，默认单位为`px`", "value": {"type": "number | string", "kind": "expression"}}, {"name": "decimal-length", "default": "-", "description": "固定显示的小数位数", "value": {"type": "number | string", "kind": "expression"}}, {"name": "theme", "default": "-", "description": "样式风格，可选值为 `round`", "value": {"type": "string", "kind": "expression"}}, {"name": "placeholder", "default": "-", "description": "输入框占位提示文字", "value": {"type": "string", "kind": "expression"}}, {"name": "integer", "default": "`false`", "description": "是否只允许输入整数", "value": {"type": "boolean", "kind": "expression"}}, {"name": "disabled", "default": "`false`", "description": "是否禁用步进器", "value": {"type": "boolean", "kind": "expression"}}, {"name": "disable-plus", "default": "`false`", "description": "是否禁用增加按钮", "value": {"type": "boolean", "kind": "expression"}}, {"name": "disable-minus", "default": "`false`", "description": "是否禁用减少按钮", "value": {"type": "boolean", "kind": "expression"}}, {"name": "disable-input", "default": "`false`", "description": "是否禁用输入框", "value": {"type": "boolean", "kind": "expression"}}, {"name": "async-change", "default": "`false`", "description": "是否开启异步变更，开启后需要手动控制输入值", "value": {"type": "boolean", "kind": "expression"}}, {"name": "show-plus", "default": "`true`", "description": "是否显示增加按钮", "value": {"type": "boolean", "kind": "expression"}}, {"name": "show-minus", "default": "`true`", "description": "是否显示减少按钮", "value": {"type": "boolean", "kind": "expression"}}, {"name": "show-input", "default": "`true`", "description": "是否显示输入框", "value": {"type": "boolean", "kind": "expression"}}, {"name": "long-press", "default": "`true`", "description": "是否开启长按手势", "value": {"type": "boolean", "kind": "expression"}}, {"name": "allow-empty", "default": "`false`", "description": "是否允许输入的值为空", "value": {"type": "boolean", "kind": "expression"}}]}, {"name": "van-steps", "slots": [], "events": [{"name": "click-step", "description": "点击步骤的标题或图标时触发"}], "attributes": [{"name": "active", "default": "`0`", "description": "当前步骤", "value": {"type": "number | string", "kind": "expression"}}, {"name": "direction", "default": "`horizontal`", "description": "显示方向，可选值为 `vertical`", "value": {"type": "string", "kind": "expression"}}, {"name": "active-color", "default": "`#07c160`", "description": "激活状态颜色", "value": {"type": "string", "kind": "expression"}}, {"name": "inactive-color", "default": "`#969799`", "description": "未激活状态颜色", "value": {"type": "string", "kind": "expression"}}, {"name": "active-icon", "default": "`checked`", "description": "激活状态底部图标，可选值见 [Icon 组件](#/zh-CN/icon)", "value": {"type": "string", "kind": "expression"}}, {"name": "inactive-icon", "default": "-", "description": "未激活状态底部图标，可选值见 [Icon 组件](#/zh-CN/icon)", "value": {"type": "string", "kind": "expression"}}, {"name": "finish-icon", "default": "-", "description": "已完成步骤对应的底部图标，优先级高于 `inactive-icon`，可选值见 [Icon 组件](#/zh-CN/icon)", "value": {"type": "string", "kind": "expression"}}, {"name": "icon-prefix", "default": "`van-icon`", "description": "图标类名前缀，同 Icon 组件的 [class-prefix 属性](#/zh-CN/icon#props)", "value": {"type": "string", "kind": "expression"}}]}, {"name": "van-step", "slots": [{"name": "active-icon", "description": "自定义激活状态图标"}, {"name": "inactive-icon", "description": "自定义未激活状态图标"}, {"name": "finish-icon", "description": "自定义已完成步骤对应的底部图标，优先级高于 `inactive-icon`"}], "events": [], "attributes": []}, {"name": "van-sticky", "slots": [], "events": [{"name": "change", "description": "当吸顶状态改变时触发"}, {"name": "scroll", "description": "滚动时触发"}], "attributes": [{"name": "offset-top", "default": "`0`", "description": "吸顶时与顶部的距离，支持 `px` `vw` `vh` `rem` 单位，默认 `px`", "value": {"type": "number | string", "kind": "expression"}}, {"name": "z-index", "default": "`99`", "description": "吸顶时的 z-index", "value": {"type": "number | string", "kind": "expression"}}, {"name": "container", "default": "-", "description": "容器对应的 HTML 节点", "value": {"type": "Element", "kind": "expression"}}]}, {"name": "van-submit-bar", "slots": [{"name": "default", "description": "自定义订单栏左侧内容"}, {"name": "button", "description": "自定义按钮"}, {"name": "top", "description": "自定义订单栏上方内容"}, {"name": "tip", "description": "提示文案中的额外内容"}], "events": [{"name": "submit", "description": "按钮点击事件回调"}], "attributes": [{"name": "price", "default": "-", "description": "价格（单位分）", "value": {"type": "number", "kind": "expression"}}, {"name": "decimal-length", "default": "`2`", "description": "价格小数点位数", "value": {"type": "number | string", "kind": "expression"}}, {"name": "label", "default": "`合计：`", "description": "价格左侧文案", "value": {"type": "string", "kind": "expression"}}, {"name": "suffix-label", "default": "-", "description": "价格右侧文案", "value": {"type": "string", "kind": "expression"}}, {"name": "text-align", "default": "`right`", "description": "价格文案对齐方向，可选值为 `left`", "value": {"type": "string", "kind": "expression"}}, {"name": "button-text", "default": "-", "description": "按钮文字", "value": {"type": "string", "kind": "expression"}}, {"name": "button-type", "default": "`danger`", "description": "按钮类型", "value": {"type": "string", "kind": "expression"}}, {"name": "button-color", "default": "-", "description": "自定义按钮颜色", "value": {"type": "string", "kind": "expression"}}, {"name": "tip", "default": "-", "description": "在订单栏上方的提示文案", "value": {"type": "string", "kind": "expression"}}, {"name": "tip-icon", "default": "-", "description": "提示文案左侧的[图标名称](#/zh-CN/icon)或图片链接", "value": {"type": "string", "kind": "expression"}}, {"name": "currency", "default": "`¥`", "description": "货币符号", "value": {"type": "string", "kind": "expression"}}, {"name": "disabled", "default": "`false`", "description": "是否禁用按钮", "value": {"type": "boolean", "kind": "expression"}}, {"name": "loading", "default": "`false`", "description": "是否显示将按钮显示为加载中状态", "value": {"type": "boolean", "kind": "expression"}}, {"name": "safe-area-inset-bottom", "default": "`true`", "description": "是否开启[底部安全区适配](#/zh-CN/advanced-usage#di-bu-an-quan-qu-gua-pei)", "value": {"type": "boolean", "kind": "expression"}}]}, {"name": "van-swipe", "slots": [{"name": "default", "description": "轮播内容"}, {"name": "indicator", "description": "自定义指示器"}], "events": [{"name": "change", "description": "每一页轮播结束后触发"}], "attributes": [{"name": "autoplay", "default": "-", "description": "自动轮播间隔，单位为 ms", "value": {"type": "number | string", "kind": "expression"}}, {"name": "duration", "default": "`500`", "description": "动画时长，单位为 ms", "value": {"type": "number | string", "kind": "expression"}}, {"name": "initial-swipe", "default": "`0`", "description": "初始位置索引值", "value": {"type": "number | string", "kind": "expression"}}, {"name": "width", "default": "`auto`", "description": "滑块宽度，单位为`px`", "value": {"type": "number | string", "kind": "expression"}}, {"name": "height", "default": "`auto`", "description": "滑块高度，单位为`px`", "value": {"type": "number | string", "kind": "expression"}}, {"name": "loop", "default": "`true`", "description": "是否开启循环播放", "value": {"type": "boolean", "kind": "expression"}}, {"name": "show-indicators", "default": "`true`", "description": "是否显示指示器", "value": {"type": "boolean", "kind": "expression"}}, {"name": "vertical", "default": "`false`", "description": "是否为纵向滚动", "value": {"type": "boolean", "kind": "expression"}}, {"name": "touchable", "default": "`true`", "description": "是否可以通过手势滑动", "value": {"type": "boolean", "kind": "expression"}}, {"name": "stop-propagation", "default": "`true`", "description": "是否阻止滑动事件冒泡", "value": {"type": "boolean", "kind": "expression"}}, {"name": "lazy-render", "default": "`false`", "description": "是否延迟渲染未展示的轮播", "value": {"type": "boolean", "kind": "expression"}}, {"name": "indicator-color", "default": "`#1989fa`", "description": "指示器颜色", "value": {"type": "string", "kind": "expression"}}]}, {"name": "van-swipe-item", "slots": [], "events": [{"name": "click", "description": "点击时触发"}], "attributes": []}, {"name": "van-swipe-cell", "slots": [{"name": "default", "description": "自定义显示内容"}, {"name": "left", "description": "左侧滑动内容"}, {"name": "right", "description": "右侧滑动内容"}], "events": [{"name": "click", "description": "点击时触发"}, {"name": "open", "description": "打开时触发"}, {"name": "close", "description": "关闭时触发"}], "attributes": [{"name": "name", "default": "-", "description": "标识符，可以在事件参数中获取到", "value": {"type": "number | string", "kind": "expression"}}, {"name": "left-width", "default": "`auto`", "description": "指定左侧滑动区域宽度，单位为`px`", "value": {"type": "number | string", "kind": "expression"}}, {"name": "right-width", "default": "`auto`", "description": "指定右侧滑动区域宽度，单位为`px`", "value": {"type": "number | string", "kind": "expression"}}, {"name": "before-close", "default": "-", "description": "关闭前的回调函数", "value": {"type": "Function", "kind": "expression"}}, {"name": "disabled", "default": "`false`", "description": "是否禁用滑动", "value": {"type": "boolean", "kind": "expression"}}, {"name": "stop-propagation", "default": "`false`", "description": "是否阻止滑动事件冒泡", "value": {"type": "boolean", "kind": "expression"}}]}, {"name": "van-switch", "slots": [], "events": [{"name": "change", "description": "开关状态切换时触发"}, {"name": "click", "description": "点击时触发"}], "attributes": [{"name": "v-model", "default": "`false`", "description": "开关选中状态", "value": {"type": "any", "kind": "expression"}}, {"name": "loading", "default": "`false`", "description": "是否为加载状态", "value": {"type": "boolean", "kind": "expression"}}, {"name": "disabled", "default": "`false`", "description": "是否为禁用状态", "value": {"type": "boolean", "kind": "expression"}}, {"name": "size", "default": "`30px`", "description": "开关尺寸，默认单位为`px`", "value": {"type": "number | string", "kind": "expression"}}, {"name": "active-color", "default": "`#1989fa`", "description": "打开时的背景色", "value": {"type": "string", "kind": "expression"}}, {"name": "inactive-color", "default": "`white`", "description": "关闭时的背景色", "value": {"type": "string", "kind": "expression"}}, {"name": "active-value", "default": "`true`", "description": "打开时对应的值", "value": {"type": "any", "kind": "expression"}}, {"name": "inactive-value", "default": "`false`", "description": "关闭时对应的值", "value": {"type": "any", "kind": "expression"}}]}, {"name": "van-switch-cell", "slots": [], "events": [{"name": "change", "description": "开关状态切换回调"}], "attributes": [{"name": "v-model", "default": "`false`", "description": "开关状态", "value": {"type": "any", "kind": "expression"}}, {"name": "title", "default": "`''`", "description": "左侧标题", "value": {"type": "string", "kind": "expression"}}, {"name": "border", "default": "`true`", "description": "是否展示单元格内边框", "value": {"type": "boolean", "kind": "expression"}}, {"name": "cell-size", "default": "-", "description": "单元格大小，可选值为 `large`", "value": {"type": "string", "kind": "expression"}}, {"name": "loading", "default": "`false`", "description": "是否为加载状态", "value": {"type": "boolean", "kind": "expression"}}, {"name": "disabled", "default": "`false`", "description": "是否为禁用状态", "value": {"type": "boolean", "kind": "expression"}}, {"name": "size", "default": "`24px`", "description": "开关尺寸", "value": {"type": "number | string", "kind": "expression"}}, {"name": "active-color", "default": "`#1989fa`", "description": "开关时的背景色", "value": {"type": "string", "kind": "expression"}}, {"name": "inactive-color", "default": "`white`", "description": "开关时的背景色", "value": {"type": "string", "kind": "expression"}}, {"name": "active-value", "default": "`true`", "description": "打开时的值", "value": {"type": "any", "kind": "expression"}}, {"name": "inactive-value", "default": "`false`", "description": "关闭时的值", "value": {"type": "any", "kind": "expression"}}]}, {"name": "van-tabs", "slots": [{"name": "nav-left", "description": "标题左侧内容"}, {"name": "nav-right", "description": "标题右侧内容"}], "events": [{"name": "click", "description": "点击标签时触发"}, {"name": "change", "description": "当前激活的标签改变时触发"}, {"name": "disabled", "description": "点击被禁用的标签时触发"}, {"name": "rendered", "description": "标签内容首次渲染时触发（仅在开启延迟渲染后触发）"}, {"name": "scroll", "description": "滚动时触发，仅在 sticky 模式下生效"}], "attributes": [{"name": "v-model", "default": "`0`", "description": "绑定当前选中标签的标识符", "value": {"type": "number | string", "kind": "expression"}}, {"name": "type", "default": "`line`", "description": "样式风格类型，可选值为 `card`", "value": {"type": "string", "kind": "expression"}}, {"name": "color", "default": "`#ee0a24`", "description": "标签主题色", "value": {"type": "string", "kind": "expression"}}, {"name": "background", "default": "`white`", "description": "标签栏背景色", "value": {"type": "string", "kind": "expression"}}, {"name": "duration", "default": "`0.3`", "description": "动画时间，单位秒", "value": {"type": "number | string", "kind": "expression"}}, {"name": "line-width", "default": "`40px`", "description": "底部条宽度，默认单位 `px`", "value": {"type": "number | string", "kind": "expression"}}, {"name": "line-height", "default": "`3px`", "description": "底部条高度，默认单位 `px`", "value": {"type": "number | string", "kind": "expression"}}, {"name": "animated", "default": "`false`", "description": "是否开启切换标签内容时的转场动画", "value": {"type": "boolean", "kind": "expression"}}, {"name": "border", "default": "`false`", "description": "是否显示标签栏外边框，仅在 `type=\"line\"` 时有效", "value": {"type": "boolean", "kind": "expression"}}, {"name": "ellipsis", "default": "`true`", "description": "是否省略过长的标题文字", "value": {"type": "boolean", "kind": "expression"}}, {"name": "sticky", "default": "`false`", "description": "是否使用粘性定位布局", "value": {"type": "boolean", "kind": "expression"}}, {"name": "swipeable", "default": "`false`", "description": "是否开启手势滑动切换", "value": {"type": "boolean", "kind": "expression"}}, {"name": "lazy-render", "default": "`true`", "description": "是否开启延迟渲染（首次切换到标签时才触发内容渲染）", "value": {"type": "boolean", "kind": "expression"}}, {"name": "scrollspy", "default": "`false`", "description": "是否开启滚动导航", "value": {"type": "boolean", "kind": "expression"}}, {"name": "offset-top", "default": "`0`", "description": "粘性定位布局下与顶部的最小距离，支持 `px` `vw` `vh` `rem` 单位，默认 `px`", "value": {"type": "number | string", "kind": "expression"}}, {"name": "swipe-threshold", "default": "`5`", "description": "滚动阈值，标签数量超过阈值且总宽度超过标签栏宽度时开始横向滚动", "value": {"type": "number | string", "kind": "expression"}}, {"name": "title-active-color", "default": "-", "description": "标题选中态颜色", "value": {"type": "string", "kind": "expression"}}, {"name": "title-inactive-color", "default": "-", "description": "标题默认态颜色", "value": {"type": "string", "kind": "expression"}}, {"name": "before-change", "default": "-", "description": "切换标签前的回调函数，返回 `false` 可阻止切换，支持返回 Promise", "value": {"type": "(name) => boolean | Promise", "kind": "expression"}}]}, {"name": "van-tab", "slots": [{"name": "default", "description": "标签页内容"}, {"name": "title", "description": "自定义标题"}], "events": [], "attributes": [{"name": "title", "default": "-", "description": "标题", "value": {"type": "string", "kind": "expression"}}, {"name": "disabled", "default": "`false`", "description": "是否禁用标签", "value": {"type": "boolean", "kind": "expression"}}, {"name": "dot", "default": "`false`", "description": "是否在标题右上角显示小红点", "value": {"type": "boolean", "kind": "expression"}}, {"name": "badge", "default": "-", "description": "图标右上角徽标的内容", "value": {"type": "number | string", "kind": "expression"}}, {"name": "info", "default": "-", "description": "图标右上角徽标的内容（已废弃，请使用 badge 属性）", "value": {"type": "number | string", "kind": "expression"}}, {"name": "name", "default": "标签的索引值", "description": "标签名称，作为匹配的标识符", "value": {"type": "number | string", "kind": "expression"}}, {"name": "url", "default": "-", "description": "点击后跳转的链接地址", "value": {"type": "string", "kind": "expression"}}, {"name": "to", "default": "-", "description": "点击后跳转的目标路由对象，同 vue-router 的 [to 属性](https://router.vuejs.org/zh/api/#to)", "value": {"type": "string | object", "kind": "expression"}}, {"name": "replace", "default": "`false`", "description": "是否在跳转时替换当前页面历史", "value": {"type": "boolean", "kind": "expression"}}, {"name": "title-style", "default": "-", "description": "自定义标题样式", "value": {"type": "any", "kind": "expression"}}, {"name": "title-class", "default": "-", "description": "自定义标题类名", "value": {"type": "any", "kind": "expression"}}]}, {"name": "van-tabbar", "slots": [], "events": [{"name": "change", "description": "切换标签时触发"}], "attributes": [{"name": "v-model", "default": "`0`", "description": "当前选中标签的名称或索引值", "value": {"type": "number | string", "kind": "expression"}}, {"name": "fixed", "default": "`true`", "description": "是否固定在底部", "value": {"type": "boolean", "kind": "expression"}}, {"name": "border", "default": "`true`", "description": "是否显示外边框", "value": {"type": "boolean", "kind": "expression"}}, {"name": "z-index", "default": "`1`", "description": "元素 z-index", "value": {"type": "number | string", "kind": "expression"}}, {"name": "active-color", "default": "`#1989fa`", "description": "选中标签的颜色", "value": {"type": "string", "kind": "expression"}}, {"name": "inactive-color", "default": "`#7d7e80`", "description": "未选中标签的颜色", "value": {"type": "string", "kind": "expression"}}, {"name": "route", "default": "`false`", "description": "是否开启路由模式", "value": {"type": "boolean", "kind": "expression"}}, {"name": "placeholder", "default": "`false`", "description": "固定在底部时，是否在标签位置生成一个等高的占位元素", "value": {"type": "boolean", "kind": "expression"}}, {"name": "safe-area-inset-bottom", "default": "`false`", "description": "是否开启[底部安全区适配](#/zh-CN/advanced-usage#di-bu-an-quan-qu-gua-pei)，设置 fixed 时默认开启", "value": {"type": "boolean", "kind": "expression"}}, {"name": "before-change", "default": "-", "description": "切换标签前的回调函数，返回 `false` 可阻止切换，支持返回 Promise", "value": {"type": "(name) => boolean | Promise", "kind": "expression"}}]}, {"name": "van-tabbar-item", "slots": [{"name": "icon", "description": "自定义图标"}], "events": [], "attributes": [{"name": "name", "default": "当前标签的索引值", "description": "标签名称，作为匹配的标识符", "value": {"type": "number | string", "kind": "expression"}}, {"name": "icon", "default": "-", "description": "[图标名称](#/zh-CN/icon)或图片链接", "value": {"type": "string", "kind": "expression"}}, {"name": "icon-prefix", "default": "`van-icon`", "description": "图标类名前缀，同 Icon 组件的 [class-prefix 属性](#/zh-CN/icon#props)", "value": {"type": "string", "kind": "expression"}}, {"name": "dot", "default": "`false`", "description": "是否显示图标右上角小红点", "value": {"type": "boolean", "kind": "expression"}}, {"name": "badge", "default": "-", "description": "图标右上角徽标的内容", "value": {"type": "number | string", "kind": "expression"}}, {"name": "info", "default": "-", "description": "图标右上角徽标的内容（已废弃，请使用 badge 属性）", "value": {"type": "number | string", "kind": "expression"}}, {"name": "url", "default": "-", "description": "点击后跳转的链接地址", "value": {"type": "string", "kind": "expression"}}, {"name": "to", "default": "-", "description": "点击后跳转的目标路由对象，同 vue-router 的 [to 属性](https://router.vuejs.org/zh/api/#to)", "value": {"type": "string | object", "kind": "expression"}}, {"name": "replace", "default": "`false`", "description": "是否在跳转时替换当前页面历史", "value": {"type": "boolean", "kind": "expression"}}]}, {"name": "van-tag", "slots": [{"name": "default", "description": "标签显示内容"}], "events": [{"name": "click", "description": "点击时触发"}, {"name": "close", "description": "关闭标签时触发"}], "attributes": [{"name": "type", "default": "`default`", "description": "类型，可选值为`primary` `success` `danger` `warning`", "value": {"type": "string", "kind": "expression"}}, {"name": "size", "default": "-", "description": "大小, 可选值为`large` `medium`", "value": {"type": "string", "kind": "expression"}}, {"name": "color", "default": "-", "description": "标签颜色", "value": {"type": "string", "kind": "expression"}}, {"name": "plain", "default": "`false`", "description": "是否为空心样式", "value": {"type": "boolean", "kind": "expression"}}, {"name": "round", "default": "`false`", "description": "是否为圆角样式", "value": {"type": "boolean", "kind": "expression"}}, {"name": "mark", "default": "`false`", "description": "是否为标记样式", "value": {"type": "boolean", "kind": "expression"}}, {"name": "text-color", "default": "`white`", "description": "文本颜色，优先级高于`color`属性", "value": {"type": "string", "kind": "expression"}}, {"name": "closeable", "default": "`false`", "description": "是否为可关闭标签", "value": {"type": "boolean", "kind": "expression"}}]}, {"name": "van-tree-select", "slots": [{"name": "content", "description": "自定义右侧区域内容"}], "events": [{"name": "click-nav", "description": "点击左侧导航时触发"}, {"name": "click-item", "description": "点击右侧选择项时触发"}], "attributes": [{"name": "items", "default": "`[]`", "description": "分类显示所需的数据", "value": {"type": "Item[]", "kind": "expression"}}, {"name": "height", "default": "`300`", "description": "高度，默认单位为`px`", "value": {"type": "number | string", "kind": "expression"}}, {"name": "main-active-index", "default": "`0`", "description": "左侧选中项的索引", "value": {"type": "number | string", "kind": "expression"}}, {"name": "active-id", "default": "<br>(number \\", "description": "右侧选中项的 id，支持传入数组", "value": {"type": "number | string \\", "kind": "expression"}}, {"name": "max", "default": "`Infinity`", "description": "右侧项最大选中个数", "value": {"type": "number | string", "kind": "expression"}}, {"name": "selected-icon", "default": "`success`", "description": "自定义右侧栏选中状态的图标", "value": {"type": "string", "kind": "expression"}}]}, {"name": "van-uploader", "slots": [{"name": "default", "description": "自定义上传区域"}, {"name": "preview-cover", "description": "自定义覆盖在预览区域上方的内容"}], "events": [{"name": "oversize", "description": "文件大小超过限制时触发"}, {"name": "click-upload", "description": "点击上传区域时触发"}, {"name": "click-preview", "description": "点击预览图时触发"}, {"name": "close-preview", "description": "关闭全屏图片预览时触发"}, {"name": "delete", "description": "删除文件预览时触发"}], "attributes": [{"name": "v-model (fileList)", "default": "-", "description": "已上传的文件列表", "value": {"type": "FileListItem[]", "kind": "expression"}}, {"name": "accept", "default": "`image/*`", "description": "允许上传的文件类型，[详细说明](https://developer.mozilla.org/zh-CN/docs/Web/HTML/Element/Input/file#%E9%99%90%E5%88%B6%E5%85%81%E8%AE%B8%E7%9A%84%E6%96%87%E4%BB%B6%E7%B1%BB%E5%9E%8B)", "value": {"type": "string", "kind": "expression"}}, {"name": "name", "default": "-", "description": "标识符，可以在回调函数的第二项参数中获取", "value": {"type": "number | string", "kind": "expression"}}, {"name": "preview-size", "default": "`80px`", "description": "预览图和上传区域的尺寸，默认单位为 `px`", "value": {"type": "number | string", "kind": "expression"}}, {"name": "preview-image", "default": "`true`", "description": "是否在上传完成后展示预览图", "value": {"type": "boolean", "kind": "expression"}}, {"name": "preview-full-image", "default": "`true`", "description": "是否在点击预览图后展示全屏图片预览", "value": {"type": "boolean", "kind": "expression"}}, {"name": "preview-options", "default": "-", "description": "全屏图片预览的配置项，可选值见 [ImagePreview](#/zh-CN/image-preview)", "value": {"type": "object", "kind": "expression"}}, {"name": "multiple", "default": "`false`", "description": "是否开启图片多选，部分安卓机型不支持", "value": {"type": "boolean", "kind": "expression"}}, {"name": "disabled", "default": "`false`", "description": "是否禁用文件上传", "value": {"type": "boolean", "kind": "expression"}}, {"name": "readonly", "default": "`false`", "description": "是否将上传区域设置为只读状态", "value": {"type": "boolean", "kind": "expression"}}, {"name": "deletable", "default": "`true`", "description": "是否展示删除按钮", "value": {"type": "boolean", "kind": "expression"}}, {"name": "show-upload", "default": "`true`", "description": "是否展示上传区域", "value": {"type": "boolean", "kind": "expression"}}, {"name": "lazy-load", "default": "`false`", "description": "是否开启图片懒加载，须配合 [Lazyload](#/zh-CN/lazyload) 组件使用", "value": {"type": "boolean", "kind": "expression"}}, {"name": "capture", "default": "-", "description": "图片选取模式，可选值为 `camera` (直接调起摄像头)", "value": {"type": "string", "kind": "expression"}}, {"name": "after-read", "default": "-", "description": "文件读取完成后的回调函数", "value": {"type": "Function", "kind": "expression"}}, {"name": "before-read", "default": "-", "description": "文件读取前的回调函数，返回 `false` 可终止文件读取，<br>支持返回 `Promise`", "value": {"type": "Function", "kind": "expression"}}, {"name": "before-delete", "default": "-", "description": "文件删除前的回调函数，返回 `false` 可终止文件读取，<br>支持返回 `Promise`", "value": {"type": "Function", "kind": "expression"}}, {"name": "max-size", "default": "(file: File) => boolean_", "description": "文件大小限制，单位为 `byte`", "value": {"type": "number | string \\", "kind": "expression"}}, {"name": "max-count", "default": "-", "description": "文件上传数量限制", "value": {"type": "number | string", "kind": "expression"}}, {"name": "result-type", "default": "`dataUrl`", "description": "文件读取结果类型，可选值为 `file` `text`", "value": {"type": "string", "kind": "expression"}}, {"name": "upload-text", "default": "-", "description": "上传区域文字提示", "value": {"type": "string", "kind": "expression"}}, {"name": "image-fit", "default": "`cover`", "description": "预览图裁剪模式，可选值见 [Image](#/zh-CN/image) 组件", "value": {"type": "string", "kind": "expression"}}, {"name": "upload-icon", "default": "`photograph`", "description": "上传区域[图标名称](#/zh-CN/icon)或图片链接", "value": {"type": "string", "kind": "expression"}}]}], "attributes": [], "types-syntax": "typescript"}}}