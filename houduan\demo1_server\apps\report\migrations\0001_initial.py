# Generated by Django 2.1.8 on 2020-09-24 14:41

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Mask',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('ip', models.TextField()),
                ('is_confines', models.CharField(choices=[('ip', '只有ip'), ('ip+', 'ip+名称')], default='ip', max_length=10, verbose_name='是否是ip')),
                ('start_time', models.DateTimeField(null=True)),
                ('end_time', models.DateTimeField(null=True)),
                ('status', models.CharField(choices=[('upcoming', '即将开始'), ('going', '进行中'), ('over', '已结束')], default='upcoming', max_length=16, verbose_name='状态码')),
                ('delete', models.BooleanField(default=0, verbose_name='删除')),
                ('time', models.DateTimeField(auto_now=True, verbose_name='添加时间')),
            ],
        ),
        migrations.CreateModel(
            name='ReportEquip',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('report_index', models.IntegerField(null=True, verbose_name='告警序号')),
                ('level', models.CharField(choices=[('Information', '第七级错误等级'), ('Notice', '第六级错误等级'), ('Warning', '第五级错误等级'), ('Error', '第四级错误等级'), ('Critical', '第三级错误等级'), ('Alert', '第二级错误等级'), ('Emergency', '第一级错误等级')], max_length=16, verbose_name='错误等级')),
                ('title', models.CharField(max_length=64, verbose_name='名称')),
                ('keyword', models.CharField(max_length=128, verbose_name='关键字')),
                ('item', models.CharField(max_length=128, null=True, verbose_name='项目')),
                ('status', models.BooleanField(default=1, verbose_name='是否异常')),
                ('vender', models.CharField(max_length=32, verbose_name='厂商')),
                ('score', models.IntegerField(default=0, verbose_name='分数')),
                ('enable', models.BooleanField(default=1, verbose_name='开启')),
                ('delete', models.BooleanField(default=0, verbose_name='删除')),
            ],
        ),
    ]
