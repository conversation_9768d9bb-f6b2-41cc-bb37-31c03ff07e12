{"_args": [["@babel/helper-wrap-function@7.27.1", "F:\\桌面\\上海海洋大屏2期\\phone"]], "_development": true, "_from": "@babel/helper-wrap-function@7.27.1", "_id": "@babel/helper-wrap-function@7.27.1", "_inBundle": false, "_integrity": "sha512-NFJK2sHUvrjo8wAU/nQTWU890/zB2jj0qBcCbZbbf+005cAsv6tMjXz31fBign6M5ov1o0Bllu+9nbqkfsjjJQ==", "_location": "/@babel/helper-wrap-function", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@babel/helper-wrap-function@7.27.1", "name": "@babel/helper-wrap-function", "escapedName": "@babel%2fhelper-wrap-function", "scope": "@babel", "rawSpec": "7.27.1", "saveSpec": null, "fetchSpec": "7.27.1"}, "_requiredBy": ["/@babel/helper-remap-async-to-generator"], "_resolved": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.27.1.tgz", "_spec": "7.27.1", "_where": "F:\\桌面\\上海海洋大屏2期\\phone", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "dependencies": {"@babel/template": "^7.27.1", "@babel/traverse": "^7.27.1", "@babel/types": "^7.27.1"}, "description": "Helper to wrap functions inside a function call.", "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-helper-wrap-function", "license": "MIT", "main": "./lib/index.js", "name": "@babel/helper-wrap-function", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-helper-wrap-function"}, "type": "commonjs", "version": "7.27.1"}