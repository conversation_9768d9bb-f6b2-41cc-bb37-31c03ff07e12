from django.utils.deprecation import MiddlewareMixin
from django.conf import settings
import time
from demo1_server.apps.user.models import User
from .utils import json_response


class AuthenticationMiddleware(MiddlewareMixin):
    """
    登录验证
    """

    def process_request(self, request):
        if request.path in settings.AUTHENTICATION_EXCLUDES:
            return None
        # if any(x.match(request.path) for x in settings.AUTHENTICATION_EXCLUDES if hasattr(x, 'match')):
        #     return None
        access_token = request.META.get('HTTP_XTOKEN') or request.GET.get('xtoken')
        # print(request.META)
        if access_token and len(access_token) == 32:
            x_real_ip = request.META.get('REMOTE_ADDR', '')
            user = User.objects.filter(access_token=access_token).first()
            if user and user.token_expired >= time.time() and user.is_active:
                request.user = user
                # print(type(request.user))
                user.token_expired = time.time() + 8 * 60 * 60
                user.save()
                return None
        response = json_response(error="验证失败，请重新登录")
        response.status_code = 401
        return response
