/**
 * 移动端路由配置
 */

// 移动端页面组件
const MobileHome = () => import('@/views/mobile/Home.vue')
const MobileLogin = () => import('@/views/mobile/Login.vue')
const MobileRegister = () => import('@/views/mobile/Register.vue')
const MobileBaseList = () => import('@/views/mobile/BaseList.vue')
const MobileSectionTitlePage = () => import('@/views/mobile/SectionTitlePage.vue')

// 移动端组件
const DeviceList = () => import('@/components/mobile/DeviceList.vue')
const DeviceDetail = () => import('@/components/mobile/DeviceDetail.vue')

const mobileRoutes = [
  {
    path: '/mobile',
    redirect: '/mobile/login'
  },
  {
    path: '/mobile/login',
    name: 'MobileLogin',
    component: MobileLogin,
    meta: { 
      title: '登录',
      requiresAuth: false,
      platform: 'mobile'
    }
  },
  {
    path: '/mobile/register',
    name: 'MobileRegister',
    component: MobileRegister,
    meta: { 
      title: '注册',
      requiresAuth: false,
      platform: 'mobile'
    }
  },
  {
    path: '/mobile/home',
    name: 'MobileHome',
    component: MobileHome,
    meta: { 
      title: '首页',
      requiresAuth: true,
      platform: 'mobile'
    }
  },
  {
    path: '/mobile/list',
    name: 'MobileBaseList',
    component: MobileBaseList,
    meta: { 
      title: '基地列表',
      requiresAuth: true,
      platform: 'mobile'
    }
  },
  {
    path: '/mobile/detail',
    name: 'MobileDeviceList',
    component: DeviceList,
    meta: { 
      title: '设备列表',
      requiresAuth: true,
      platform: 'mobile'
    }
  },
  {
    path: '/mobile/device-detail',
    name: 'MobileDeviceDetail',
    component: DeviceDetail,
    meta: { 
      title: '设备详情',
      requiresAuth: true,
      platform: 'mobile'
    }
  },
  {
    path: '/mobile/section-title',
    name: 'MobileSectionTitlePage',
    component: MobileSectionTitlePage,
    meta: { 
      title: '区域标题',
      requiresAuth: true,
      platform: 'mobile'
    }
  }
]

export default mobileRoutes
