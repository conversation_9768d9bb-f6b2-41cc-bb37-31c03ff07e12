"""
WSGI config for demo1_server project.

It exposes the WSGI callable as a module-level variable named ``application``.

For more information on this file, see
https://docs.djangoproject.com/en/2.2/howto/deployment/wsgi/
"""

import os

from django.core.wsgi import get_wsgi_application

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'demo1_server.settings.dev')

application = get_wsgi_application()
from demo1_server.apps.learning import tests
# 假如脚本名是：tests
tests.mqtt_run()