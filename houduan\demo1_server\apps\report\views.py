from cProfile import label
from learning.models import BreedingBase
from datetime import datetime, timedelta
from django.db import connection
from django.http import JsonResponse
import json
from django.views.generic import View
# from demo1_server.apps.user.models import User
# from demo1_server.apps.log.models import UserLog
from django.shortcuts import render
# from rest_framework.views import APIView
# from rest_framework.response import Response
# from rest_framework.generics import ListAPIView, DestroyAPIView
# from demo1_server.apps.report import models
# from report.serializer_report import ReportModelSerializer, MaskModelSerializer
from rest_framework import filters
# from apscheduler.schedulers.background import BackgroundScheduler, BlockingScheduler
import time
import logging
import asyncio
import threading
from apscheduler.executors.pool import ThreadPoolExecutor, ProcessPoolExecutor

error_json = {'code': 200, 'state': 'error'}
executors = {
    'default': ThreadPoolExecutor(50),
    'processpool': ProcessPoolExecutor(3)
}

logger = logging.getLogger()


# def get_going(id):
#     print('going')
#     models.Mask.objects.filter(id=id).update(status='going')


# def get_over(id):
#     print('over')

#     models.Mask.objects.filter(id=id).update(status='over')


# class GetReportEquip(ListAPIView):
#     queryset = models.ReportEquip.objects.filter(delete=0)
#     serializer_class = ReportModelSerializer
#     filter_backends = [filters.SearchFilter]
#     search_fields = ['id', 'level', 'report_index',
#                      'title', 'keyword', 'score', 'item', 'vender']


# class GetMask(ListAPIView):
#     queryset = models.Mask.objects.filter(
#         delete=0, status__in=['upcoming', 'going'])
#     serializer_class = MaskModelSerializer
#     filter_backends = [filters.SearchFilter]
#     search_fields = ['status', 'ip', 'is_confines']


# class AddMask(APIView):
#     def post(self, request):
#         scheduler = BlockingScheduler(daemonic=False)
#         ip = request.data.get('ip')
#         start_time = request.data.get('start_time')
#         end_time = request.data.get('end_time')
#         local_time = int(time.time())
#         start_time_stamp = time.mktime(
#             time.strptime(start_time, '%Y-%m-%d %H:%M:%S'))
#         end_time_stamp = time.mktime(
#             time.strptime(end_time, '%Y-%m-%d %H:%M:%S'))
#         if local_time < int(start_time_stamp):
#             status = "upcoming"
#         elif local_time < int(end_time_stamp):
#             status = "going"
#         else:
#             status = 'over'
#         if ':' in ip:
#             is_confines = 1
#         else:
#             is_confines = 0
#         print(status)
#         try:
#             obj = models.Mask(ip=ip, status=status, start_time=start_time,
#                               end_time=end_time, is_confines=is_confines)
#             obj.save()
#             if obj.status == 'upcoming':
#                 scheduler.add_job(get_going, 'date', run_date=start_time, args=[
#                                   obj.id], id='start'+ip)
#                 scheduler.add_job(get_over, 'date', run_date=end_time, args=[
#                                   obj.id], id='over'+ip)
#                 t = threading.Thread(target=scheduler.start, args=())
#                 t.start()
#             elif obj.status == 'going':
#                 scheduler.add_job(get_over, 'date', run_date=end_time, args=[
#                                   obj.id],  id='over'+ip)
#                 t = threading.Thread(target=scheduler.start, args=())
#                 t.start()
#         except Exception as e:
#             # logger.error(e)
#             print(e)
#             return Response({
#                 'status': '1',
#                 "result": "添加失败"
#             })
#         return Response({
#             'status': '0',
#             "result": "添加成功",
#             "start_time": start_time,
#             'end_time': end_time,
#             "time_status": status,
#         })


# class AddReportEquip(APIView):
#     def post(self, request):
#         level = request.data.get('level')
#         title = request.data.get('title')
#         keyword = request.data.get('keyword')
#         item = request.data.get('item')
#         score = request.data.get('score')
#         if score:
#             score = int(score)
#         else:
#             score = 0
#         status = request.data.get('status')
#         if status:
#             status = 1
#         else:
#             status = 0
#         vender = request.data.get('vender')
#         enable = request.data.get('enable')
#         if enable:
#             enable = 1
#         else:
#             enable = 0
#         try:
#             obj = models.ReportEquip.objects.create(
#                 level=level, title=title, keyword=keyword, score=score, item=item, vender=vender, enable=enable, status=status)
#         except Exception as e:
#             # logger.error(e)
#             print(e)
#             return Response({
#                 'status': '1',
#                 "result": "添加失败",
#             })
#         return Response({
#             'status': '0',
#             "result": "添加成功",
#             "id": obj.id
#         })


# class DeleteReport(APIView):
#     def delete(self, request, *args, **kwargs):
#         cid = request.data.get('id')
#         if type(cid) != list:
#             obj = models.ReportEquip.objects.filter(id=cid)
#         else:
#             obj = models.ReportEquip.objects.filter(id__in=cid)
#         if obj:
#             obj.update(delete=1)
#             return Response({
#                 'status': '0',
#                 "result": "删除成功"
#             })
#         else:
#             return Response({
#                 'status': '1',
#                 "result": "删除失败"
#             })


# class DeleteMask(APIView):
#     def delete(self, request, *args, **kwargs):
#         cid = request.data.get('id')
#         print(cid)
#         if type(cid) != list:
#             obj = models.Mask.objects.filter(id=cid)
#         else:
#             obj = models.Mask.objects.filter(id__in=cid)
#         if obj:
#             obj.update(delete=1)
#             return Response({
#                 'status': '0',
#                 "result": "删除成功"
#             })
#         else:
#             return Response({
#                 'status': '1',
#                 "result": "删除失败"
#             })


# class UpdateReport(APIView):
#     def put(self, request):
#         cid = request.data.get('id')
#         enable = request.data.get('enable')
#         if enable:
#             enable = 1
#         else:
#             enable = 0
#         obj = models.ReportEquip.objects.filter(id=cid)
#         if obj:
#             obj.update(enable=enable)
#             return Response({
#                 'status': '0',
#                 "result": "更改成功"
#             })
#         else:
#             return Response({
#                 'status': '1',
#                 "result": "更改失败"
#             })


# class UpdateMask(APIView):
#     def put(self, request):
#         scheduler = BlockingScheduler(daemonic=False)
#         cid = request.data.get('id')
#         ip = request.data.get('ip')
#         start_time = request.data.get('start_time')
#         end_time = request.data.get('end_time')
#         local_time = int(time.time())
#         if local_time < int(time.mktime(time.strptime(start_time, "%Y-%m-%d %H:%M:%S"))):
#             status = "upcoming"
#         elif local_time < int(time.mktime(time.strptime(end_time, "%Y-%m-%d %H:%M:%S"))):
#             status = "going"
#         else:
#             status = 'over'
#         is_confines = 0
#         if ip and ':' in ip:
#             is_confines = 1
#         obj = models.Mask.objects.filter(id=cid).first()
#         if obj:
#             obj.is_confines = is_confines
#             obj.start_time = start_time
#             obj.end_time = end_time
#             obj.ip = ip
#             obj.status = status
#             obj.save()
#             if obj.status == 'upcoming':
#                 scheduler.add_job(get_going, 'date', run_date=start_time, args=[
#                                   obj.id], id='start'+ip)
#                 scheduler.add_job(get_over, 'date', run_date=end_time, args=[
#                                   obj.id], id='over'+ip)
#                 t = threading.Thread(target=scheduler.start, args=())
#                 t.start()
#             elif obj.status == 'going':
#                 scheduler.add_job(get_over, 'date', run_date=end_time, args=[
#                                   obj.id], id='over'+ip)
#                 t = threading.Thread(target=scheduler.start, args=())
#                 t.start()
#             return Response({
#                 'status': '0',
#                 "result": "更改成功",
#                 "time_status": status,
#             })
#         else:
#             return Response({
#                 'status': '1',
#                 "result": "更改失败"
#             })


# Create your views here.


# class GetMaskinfo(View):
#     def post(self, request):
#         itemlist = []
#         # 获取url参数
#         data = json.loads(request.body)
#         limit = data.get('limit')
#         page = data.get('page')
#         info = {}
#         info["search"] = data.get('search')
#         info["title"] = data.get('title')
#         info["radio"] = data.get('radio')
#         page = (page - 1) * limit
#         from django.db.models import Q
#         if "search" in info and info["search"] != "" and info["title"] != "":
#             title = info["title"]
#             queryset_fw = models.Mask.objects.filter(Q(title__contains=title) | Q(ip__contains=title, delete=0, status__in=['upcoming', 'going'])).order_by('-id')[page:][
#                 :limit]
#             queryset_fw_count = models.Mask.objects.filter(Q(title__contains=title) | Q(
#                 ip__contains=title, delete=0, status__in=['upcoming', 'going'])).order_by('-id').count()
#         elif info["radio"] == "1":
#             queryset_fw = models.Mask.objects.filter(
#                 delete=0, status__in=['over']).order_by('-id')[page:][:limit]
#             queryset_fw_count = models.Mask.objects.filter(
#                 delete=0, status__in=['over']).order_by('-id').count()
#         else:
#             try:
#                 queryset_fw = models.Mask.objects.filter(
#                     delete=0, status__in=['upcoming', 'going']).order_by('-id')[page:][:limit]
#                 queryset_fw_count = models.Mask.objects.filter(
#                     delete=0, status__in=['upcoming', 'going']).order_by('-id').count()
#             except Exception as e:
#                 print(e)
#         # print(queryset_fw)
#         from django.core import serializers
#         for i in queryset_fw:
#             itemdict = {}
#             itemdict['id'] = i.id
#             itemdict['title'] = i.title
#             itemdict['ip'] = i.ip
#             itemdict['value1'] = []
#             itemdict['value1'].append(str(i.start_time).replace("T", " "))
#             itemdict['value1'].append(str(i.end_time).replace("T", " "))
#             itemdict['start_time'] = i.start_time
#             itemdict['end_time'] = i.end_time
#             itemdict['status'] = i.status
#             itemdict['operate'] = i.operate
#             itemlist.append(itemdict)
#         rstdict = {}
#         rstdict['code'] = 200
#         rstdict['data'] = {}
#         rstdict['data']['total'] = queryset_fw_count
#         rstdict['data']['items'] = itemlist
#         # print(rstdict)
#         return JsonResponse(rstdict)

#     def put(self, request):
#         nickname = User.objects.values(
#             'nickname').filter(id=request.user.id).first()
#         nickname = nickname["nickname"]
#         scheduler = BlockingScheduler(daemonic=False)
#         data = json.loads(request.body)
#         ip = data.get('ip')
#         title = data.get('title')
#         start_time = data.get("value1")[0]
#         end_time = data.get("value1")[1]
#         local_time = int(time.time())
#         start_time_stamp = time.mktime(
#             time.strptime(start_time, '%Y-%m-%d %H:%M:%S'))
#         end_time_stamp = time.mktime(
#             time.strptime(end_time, '%Y-%m-%d %H:%M:%S'))
#         if local_time < int(start_time_stamp):
#             status = "upcoming"
#         elif local_time < int(end_time_stamp):
#             status = "going"
#         else:
#             status = 'over'
#         if ':' in ip:
#             is_confines = 1
#         else:
#             is_confines = 0
#         try:
#             obj = models.Mask(ip=ip, title=title, status=status, start_time=start_time,
#                               end_time=end_time, is_confines=is_confines, operate=nickname)
#             obj.save()

#         except Exception as e:
#             # logger.error(e)
#             print(e)
#             return JsonResponse({
#                 "code": 200,
#                 'status': '1',
#                 "result": "添加失败"
#             })

#         obj = UserLog(content="添加屏蔽，"+json.dumps(data),
#                       type=0, user_id=request.user.id)
#         obj.save()
#         return JsonResponse({
#             "code": 200,
#             'status': '0',
#             "result": "添加成功",
#             "start_time": start_time,
#             'end_time': end_time,
#             "time_status": status,
#         })

#     def patch(self, request):
#         nickname = User.objects.values(
#             'nickname').filter(id=request.user.id).first()
#         nickname = nickname["nickname"]
#         scheduler = BlockingScheduler(daemonic=False)
#         data = json.loads(request.body)
#         cid = data.get('id')
#         ip = data.get('ip')
#         title = data.get('title')
#         start_time = data.get("value1")[0]
#         end_time = data.get("value1")[1]
#         local_time = int(time.time())
#         if local_time < int(time.mktime(time.strptime(start_time, "%Y-%m-%d %H:%M:%S"))):
#             status = "upcoming"
#         elif local_time < int(time.mktime(time.strptime(end_time, "%Y-%m-%d %H:%M:%S"))):
#             status = "going"
#         else:
#             status = 'over'
#         is_confines = 0
#         if ip and ':' in ip:
#             is_confines = 1
#         obj = models.Mask.objects.filter(id=cid).first()
#         queryset_fw = models.Mask.objects.filter(id=cid)
#         for i in queryset_fw:
#             itemdict = {}
#             itemdict['id'] = i.id
#             itemdict['title'] = i.title
#             itemdict['ip'] = i.ip
#             itemdict['start_time'] = str(i.start_time)
#             itemdict['end_time'] = str(i.end_time)
#             itemdict['status'] = i.status
#             itemdict['operate'] = i.operate
#         if obj:
#             obj.is_confines = is_confines
#             obj.start_time = start_time
#             obj.end_time = end_time
#             obj.ip = ip
#             obj.status = status
#             obj.title = title
#             obj.operate = nickname
#             obj.save()
#             obj = UserLog(content="更改屏蔽，" + "新数据:"+json.dumps(data) +
#                           "老数据:"+json.dumps(itemdict), type=2, user_id=request.user.id)
#             obj.save()
#             return JsonResponse({
#                 "code": 200,
#                 'status': '0',
#                 "result": "更改成功",
#                 "time_status": status,
#             })
#         else:
#             return JsonResponse({
#                 "code": 200,
#                 'status': '1',
#                 "result": "更改失败"
#             })

#     def delete(self, request, *args, **kwargs):
#         nickname = User.objects.values(
#             'nickname').filter(id=request.user.id).first()
#         nickname = nickname["nickname"]
#         cid = request.GET.get("id")
#         print(cid)
#         if type(cid) != list:
#             obj = models.Mask.objects.filter(id=cid)
#         else:
#             obj = models.Mask.objects.filter(id__in=cid)
#         if obj:
#             obj.update(delete=1, operate=nickname)
#             queryset_fw = models.Mask.objects.filter(id=cid)
#             for i in queryset_fw:
#                 itemdict = {}
#                 itemdict['id'] = i.id
#                 itemdict['title'] = i.title
#                 itemdict['ip'] = i.ip
#                 itemdict['start_time'] = str(i.start_time)
#                 itemdict['end_time'] = str(i.end_time)
#                 itemdict['status'] = i.status
#                 itemdict['operate'] = i.operate
#             obj = UserLog(content="删除屏蔽，" + "老数据:" +
#                           json.dumps(itemdict), type=2, user_id=request.user.id)
#             obj.save()
#             return JsonResponse({
#                 "code": 200,
#                 'status': '0',
#                 "result": "删除成功"
#             })
#         else:
#             return JsonResponse({
#                 "code": 200,
#                 'status': '1',
#                 "result": "删除失败"
#             })


# 历史数据后端


def queryDict(sql, params=None):
    '''
    查询结果返回字典类型数据
    :param sql:
    :param params:
    :return:
    '''
    with connection.cursor() as cursor:
        cursor.execute(sql, params=params)
        col_names = [desc[0] for desc in cursor.description]
        row = cursor.fetchall()
        rowList = []
        for list in row:
            tMap = dict(zip(col_names, list))
            rowList.append(tMap)
        return rowList
# 获取历史数据内设备的信息


class getHistoryEquipmentByBasename(View):
    """
    status 0 为发生错误 1为正常
    """

    def post(self, request):
        info = json.loads(request.body)
        base_id = info.get('base_id', None)

        if base_id:
            result = {}
            # print(basename)
            # sql = 'select ID from app1_breeding_base where BASE_NAME=%s'
            # base_id = queryDict(sql, basename)
            base = BreedingBase.objects.filter(ID=base_id).values()[0]
            if not base:
                error_json.update({'info': '基地不存在'})
                return JsonResponse(error_json)

            abbreviation = base['abbreviation']

            sql = 'select ID from app1_breeding_bacilities where BASE_id=%s'
            sql = sql.replace('app1', abbreviation)
            
            BACILITIES_ids = queryDict(sql, base_id)
            equipments = []
            for i in BACILITIES_ids:

                sql = 'select * from app1_equipment where BACILITIES_id=%s and EQUIPMENT_TYPE="水质检测"'
                sql = sql.replace('app1', abbreviation)
                equipments.extend(queryDict(sql, i['ID']))
            result['equipments'] = equipments
            equipments_data = {"TEMP": [], "O2": [], 'PH': []}  # 设备数据
            result['equipments_data'] = equipments_data
            return JsonResponse({"code": 200, 'status': 1, "result": result})


        else:
            result = "基地id不能为空"
        return JsonResponse({"code": 200,
                             'status': 0,
                             "result": result
                             })


class getHistoryData(View):
    def post(self, request):
        info = json.loads(request.body)
        # print(info)
        dates = info['dates']
        date1 = dates[0]+' 00:00:00'
        date2 = dates[1]+ ' 23:59:59'
        ids = info['checkval']
        c_type=info['type']
        base_id = info.get('base_id', None)
        if not base_id:
            result = "基地id不能为空"
            error_json.update({'info': 'result'})
            return JsonResponse(error_json)
        else:
            base = BreedingBase.objects.filter(ID=base_id).values()[0]
            if not base:
                error_json.update({'info': '基地不存在'})
                return JsonResponse(error_json)

            abbreviation = base['abbreviation']
        


        # print(ids)
        # ACQUISITION_TIME
        # ACQUISITION_TIME  TEM CONDUCTANCE
        sql = 'select ACQUISITION_TIME,'+c_type+' from app1_sensing_data where EQUIPMENT_id=%s and ACQUISITION_TIME between  %s  and %s ORDER BY ACQUISITION_TIME;'
        sql = sql.replace('app1', abbreviation)
        x=set()
        # y=[]
        y_TEM=[]
        y_PH=[]
        y_O2=[]
        y_con=[]
        for i in ids:

            data = queryDict(sql, (i['id'], date1, date2))
            tem=[]
            ph=[]
            o2=[]
            xn=[]
            con=[]
            source_tem=[]
            source_ph=[]
            source_o2=[]
            source_con=[]
            for j in data:
                x.add(j['ACQUISITION_TIME'])
                xn.append(j['ACQUISITION_TIME'])
                if c_type=="TEM":
                    tem.append(j['TEM'])
                    source_tem.append([j['ACQUISITION_TIME'],j['TEM']])
                elif c_type=="PH":
                    ph.append(j['PH'])
                    source_ph.append([j['ACQUISITION_TIME'],j['PH']])
                elif c_type=="O2":
                    o2.append(j['O2'])
                    source_o2.append([j['ACQUISITION_TIME'],j['O2']])
                elif c_type=="CONDUCTANCE":
                    con.append(j['CONDUCTANCE'])
                    source_con.append([j['ACQUISITION_TIME'],j['CONDUCTANCE']])
                
                
                
                
                
                
                
                

            y_TEM.append({'name':i['name'],'x':xn,'data':tem,'id':i['id'],'dimensions': ['x', i['name']],'source': source_tem})
            y_PH.append({'name':i['name'],'x':xn,'data':ph,'id':i['id'],'dimensions': ['x', i['name']],'source': source_ph})
            y_O2.append({'name':i['name'],'x':xn,'data':o2,'id':i['id'],'dimensions': ['x', i['name']],'source': source_o2})
            y_con.append({'name':i['name'],'x':xn,'data':con,'id':i['id'],'dimensions': ['x', i['name']],'source': source_con})
        x=list(x)
        data={'TEM':{'x':list(x),'y':y_TEM},'PH':{'x':x,'y':y_PH},'O2':{'x':x,'y':y_O2},'con':{'x':x,'y':y_con}}
        # print(locals())
        return JsonResponse({"code": 200, 'status': 0,'data':data})
class getBaseEquipment(View):#两级
    def post(self,request):
        # sql = 'select * from app1_breeding_base'#基地
        # base_id = queryDict(sql)
        bases=BreedingBase.objects.all()

        option=[]
        for i in bases:
            b_id=i.ID
            sql = 'select ID from app1_breeding_bacilities where BASE_id=%s'#设施
            abbreviation = i.abbreviation
            if abbreviation=="":# 没有简称跳过
                continue
            sql = sql.replace('app1', abbreviation)
            BACILITIES_ids = queryDict(sql, b_id)
            children = []
            weather=[]#天气
            water=[]#水
            for j in BACILITIES_ids:

                sql = 'select * from app1_equipment where BACILITIES_id=%s'#设备
                sql = sql.replace('app1', abbreviation)
                equipments = queryDict(sql, j['ID'])
                for k in equipments:
                    if k['EQUIPMENT_TYPE']=='水质检测':
                        water.append(k['ID'])
                    else:
                        weather.append(k['ID'])
                    children.append({'value':k['ID'],'label': k['EQUIPMENT_NAME']})
            children.insert(0,{'value':water,'label': '所有水质设备'})
            children.insert(0,{'value':weather,'label': '所有气象设备'})
            option.append({'value':i.ID,'label': i.BASE_NAME,'children':children})
        return JsonResponse({"code": 200, 'status': 1, "option": option})

class getBaseEquipment3(View):#三级
    def post(self, request):
        try:
            data = json.loads(request.body)
            base_id = data.get('base_id')
            print(f"Received base_id: {base_id}")
        except Exception as e:
            return JsonResponse({"code": 400, 'status': 0, "info": "请求参数解析失败"})

        if base_id:
            bases = BreedingBase.objects.filter(ID=base_id)
        else:
            bases = BreedingBase.objects.all()

        option = []
        BACILITIES = []
        for i in bases:
            b_id=i.ID
            sql = 'select * from app1_breeding_bacilities where BASE_id=%s'#设施
            abbreviation = i.abbreviation
            if abbreviation=="": # 没有简称跳过
                continue
            sql = sql.replace('app1', abbreviation)

            BACILITIES_ids = queryDict(sql, b_id)
            children = []
            water_len=0#水质设备数量
            weather_len=0#气象设备数量
            for j in BACILITIES_ids:

                sql = 'select * from app1_equipment where BACILITIES_id=%s'#设备
                sql = sql.replace('app1', abbreviation)
                equipments = queryDict(sql, j['ID'])
                children2=[]
                children3=[]
                for k in equipments:
                    if k['EQUIPMENT_TYPE']=='水质检测':
                        data={'id':k['ID'],'label': k['EQUIPMENT_NAME']}
                        data.update(k)
                        data.update({'type':'BACILITIES','BACILITIES_id':j['ID'],'EQUIPMENT_LOCATION':j['BACILITIES_LOCATION']})
                        children3.append(data)
                        water_len+=1
                    elif k['EQUIPMENT_TYPE']=='气象检测':
                        
                        data={'id':k['ID'],'label': k['EQUIPMENT_NAME']}
                        data.update(k)
                        data.update({'type':'BACILITIES','BACILITIES_id':j['ID'],'EQUIPMENT_LOCATION':j['BACILITIES_LOCATION']})
                        children2.append(data)
                        weather_len+=1
                        # 默认用设施的坐标代替新增的设备坐标
                children.append(
                    {'id':j['ID'],'label': j['BACILITIES_NAME'],'type':'BACILITIES','BACILITIES_id':j['ID'],'EQUIPMENT_LOCATION':j['BACILITIES_LOCATION'],
                    'children':[
                        {'id':1,'label':'水质设备','type':'EQUIPMENT1','BACILITIES_id':j['ID'],'EQUIPMENT_LOCATION':j['BACILITIES_LOCATION'],'children':children3},
                        {'id':2,'label':'气象设备','type':'EQUIPMENT2','BACILITIES_id':j['ID'],'EQUIPMENT_LOCATION':j['BACILITIES_LOCATION'],'children':children2}]})
                BACILITIES.append({'value': j['ID'],'label': j['BACILITIES_NAME']})
            option.append({'id':i.ID,'label': i.BASE_NAME,'type':'BASE','children':children,'weather_len':weather_len,'water_len':water_len})
        return JsonResponse({"code": 200, 'status': 1, "data": option,'BACILITIES':BACILITIES})
class uniqueEQUIPMENT_ID(View):
    def post(self,request):
        post_data=json.loads(request.body)
        ID=post_data.get('ID',None)
        if not ID:
            return JsonResponse({'code':500,'info':'ID不能为空'})
        sql = 'select * from app1_equipment where BACILITIES_id=%s'#设备
        equipments = queryDict(sql, ID)
        if equipments:
            return JsonResponse({'code':400,'info':'ID已存在'})
        else:
            return JsonResponse({'code':200,'info':'ID可使用'})

class getReportData(View):#获取日志数据
    def post(self,request):
        info = json.loads(request.body)
        # print(info)
        base_id=info.get('base_id', None)#基地编号
        if not base_id:
            result = "基地id不能为空"
            error_json.update({'info': 'result'})
            return JsonResponse(error_json)
        else:
            base = BreedingBase.objects.filter(ID=base_id).values()[0]
            if not base:
                error_json.update({'info': '基地不存在'})
                return JsonResponse(error_json)

            abbreviation = base['abbreviation']
        
        e_id = info['e_id']#设备编号
        if type(e_id)!=list:
            e_id=str(e_id)
        else:
            e_id=[','+str(i) for i in e_id]
            e_id=''.join(e_id)
            e_id=e_id[1:]
        # print(e_id,type(e_id))
        e_name=info['e_name']#设备名称
        s_date=info['s_date']+' 00:00:00'#开始时间
        end_date=info['end_date']+' 23:59:59'#结束时间
        if '气象' in e_name:
            types=1

            sql ="select * from app1_weather_data where EQUIPMENT_id in ("+e_id+") and CREATE_TIME BETWEEN %s AND %s"
        elif '水质' in e_name:
            types=2
            sql="select * from app1_sensing_data where  EQUIPMENT_id in ("+e_id+") and CREATE_TIME BETWEEN %s AND %s"
        sql = sql.replace('app1', abbreviation)
        report_data=queryDict(sql, (s_date,end_date))    
        return JsonResponse({"code": 200, 'status': 1, "data": report_data,'type':types})
        


# class getLogList(View):
#     def post(self,request):
#         result={}
#         # sql = 'select ID from app1_breeding_base'
#         # base_id = queryDict(sql)
#         bases=BreedingBase.objects.all()
#         for j in bases:
#             abbreviation = j.abbreviation
#             if abbreviation=="":# 没有简称跳过
#                 continue

#             sql = 'select ID from app1_breeding_bacilities where BASE_id=%s'
#             sql = sql.replace('app1', abbreviation)
#             BACILITIES_ids = queryDict(sql, j.ID)
#             equipments = []
#             for i in BACILITIES_ids:

#                 sql = 'select * from app1_equipment where BACILITIES_id=%s'
#                 sql = sql.replace('app1', abbreviation)
#                 equipments.extend(queryDict(sql, i['ID']))
#         result['equipments'] = equipments
#         equipments_data = {"TEMP": [], "O2": [], 'PH': []}  # 设备数据
#         result['equipments_data'] = equipments_data
#         return JsonResponse({"code": 200, 'status': 1, "result": result})
class AddEquipment(View):#添加设备
    def post(self,request):
        data = json.loads(request.body)
        form=data['form']
        type=form['type']#添加状态 更新还是 新增
        EQUIPMENT_NAME=form['EQUIPMENT_NAME']
        EQUIPMENT_TYPE=form['EQUIPMENT_TYPE']
        EQUIPMENT_LOCATION=form['EQUIPMENT_LOCATION']
        STATE=form['STATE']
        MOBILITY=form['MOBILITY']
        CARD_NUMBER=form['CARD_NUMBER']
        VOLTAGE=form['VOLTAGE']
        INTRODUCE=form['INTRODUCE']

        # CONDUCTANCE=form['CONDUCTANCE']#电导 目前未处理


        
        CONFIG=form['CONFIG']
        UANDL=str(form['UANDL1'])
        BACILITIES_id=form['BACILITIES_id']
        base_id=form.get("base_id",None)
        if not base_id:
            result = "基地id不能为空"
            error_json.update({'info': 'result'})
            return JsonResponse(error_json)
        else:
            base = BreedingBase.objects.filter(ID=base_id).values()[0]
            if not base:
                error_json.update({'info': '基地不存在'})
                return JsonResponse(error_json)

            abbreviation = base['abbreviation']

        # print(locals())

        cursor = connection.cursor()#数据库连接

        # cursor.execute(sql,params)
        if type=='add':#新增
            ID=form['ID']
            CREATE_TIME=time.localtime()
            UPDATE_TIME=time.localtime()
            # INSERT INTO `app1_equipment` (`EQUIPMENT_NAME`, `EQUIPMENT_TYPE`, `EQUIPMENT_LOCATION`, `STATE`, `MOBILITY`, `CARD_NUMBER`, `VOLTAGE`, `CREATE_TIME`, `UPDATE_TIME`, `CONFIG`, `UANDL`, `INTRODUCE`, `BACILITIES_id`) VALUES ('111', '11', '11', '11', '11', '11', '11', '2022-09-28 14:56:26', '2022-09-28 14:56:22', '', 'sa', 'aa', '1')
            sql='INSERT INTO `app1_equipment` (`ID`,`EQUIPMENT_NAME`, `EQUIPMENT_TYPE`, `EQUIPMENT_LOCATION`, `STATE`, `MOBILITY`, `CARD_NUMBER`, `VOLTAGE`, `CREATE_TIME`, `UPDATE_TIME`, `CONFIG`, `UANDL`, `INTRODUCE`, `BACILITIES_id`) VALUES (%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s)'
            sql=sql.replace("app1",abbreviation)
            cursor.execute(sql,(ID,EQUIPMENT_NAME,EQUIPMENT_TYPE,EQUIPMENT_LOCATION,STATE,MOBILITY,CARD_NUMBER,VOLTAGE,CREATE_TIME,UPDATE_TIME,CONFIG,UANDL,INTRODUCE,BACILITIES_id))

        elif type=='update':#更新
            ID=form['ID']
            # CREATE_TIME=form['CREATE_TIME']
            UPDATE_TIME=time.localtime()
            #UPDATE `app1_equipment` SET `EQUIPMENT_NAME`='11111', `EQUIPMENT_TYPE`='水质检测', `EQUIPMENT_LOCATION`='11212', `STATE`='0', `MOBILITY`='1', `CARD_NUMBER`='111', `VOLTAGE`='111', `CREATE_TIME`='2022-09-29 14:56:26', `UPDATE_TIME`='2022-09-29 14:56:22', `CONFIG`='1', `UANDL`='sa111', `INTRODUCE`='aa11', `BACILITIES_id`='2' WHERE (`ID`='13')
            sql='UPDATE app1_equipment SET EQUIPMENT_NAME=%s,EQUIPMENT_TYPE=%s,EQUIPMENT_LOCATION=%s,STATE=%s,MOBILITY=%s,CARD_NUMBER=%s,VOLTAGE=%s,UPDATE_TIME=%s,CONFIG=%s,UANDL=%s,INTRODUCE=%s,BACILITIES_id=%s WHERE (`ID`=%s)'
            sql=sql.replace("app1",abbreviation)
            cursor.execute(sql,(EQUIPMENT_NAME,EQUIPMENT_TYPE,EQUIPMENT_LOCATION,STATE,MOBILITY,CARD_NUMBER,VOLTAGE,UPDATE_TIME,CONFIG,UANDL,INTRODUCE,BACILITIES_id,ID))

        else:
            return JsonResponse({"code": 'error', 'status': 0, "result": '状态错误'})
            
        sql = 'select * from app1_equipment where EQUIPMENT_NAME=%s'
        sql=sql.replace("app1",abbreviation)
        data=queryDict(sql, EQUIPMENT_NAME)[0]

        return JsonResponse({"code": '200', 'status': 1, "result": '成功','data':data})
class DeleteEquipment(View):#删除设备
    def post(self,request):
        form = json.loads(request.body)
        ID=form.get('ID',None)
        base_id=form.get("base_id",None)
        if not base_id:
            result = "基地id不能为空"
            error_json.update({'info': 'result'})
            return JsonResponse(error_json)
        else:
            base = BreedingBase.objects.filter(ID=base_id).values()[0]
            if not base:
                error_json.update({'info': '基地不存在'})
                return JsonResponse(error_json)

            abbreviation = base['abbreviation']


        cursor = connection.cursor()#数据库连接
        sql='DELETE FROM app1_equipment WHERE ID=%s'
        sql=sql.replace("app1",abbreviation)
        cursor.execute(sql,ID)
        return JsonResponse({"code": '200', 'status': 1, "result": '删除成功'})
#以下是两个表对应的类
class Addbasic_data_fish_pond(View):
    def post(self, request):
        try:
            data = json.loads(request.body)
            base = data.get('base')
            area_number = data.get('area_number')
            pond_number = data.get('pond_number')
            area = data.get('area')
            wide = data.get('wide')
            length = data.get('length')
            variety = data.get('variety')
            date = data.get('date')
            quantity = data.get('quantity')
            specifications = data.get('specifications')
            total = data.get('total')
            unit_price = data.get('unit_price')
            total_price = data.get('total_price')
            remark = data.get('remark')

            with connection.cursor() as cursor:
                cursor.execute("SELECT MAX(id) FROM basic_data_fish_pond")
                result = cursor.fetchone()
                max_id = result[0] if result[0] else 0
                id = str(int(max_id) + 1)

            with connection.cursor() as cursor:
                cursor.execute(
                    """
                    INSERT INTO basic_data_fish_pond (ID, base, area_number, pond_number, area, wide, length, variety, date, quantity, specifications, total, unit_price, total_price, remark)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """,
                    [id, base, area_number, pond_number, area, wide, length, variety, date, quantity, specifications, total, unit_price, total_price, remark]
                )

            connection.commit()

            return JsonResponse({'message': ' pond  added successfully'})

        except Exception as e:
            connection.rollback()
            return JsonResponse({'error': str(e)}, status=500)


class Getbasic_data_fish_pond(View):
    def get(self, request):
        with connection.cursor() as cursor:
            cursor.execute(
                """
                SELECT ID, base, area_number, pond_number, area, wide, length, variety, date, quantity, specifications, total, unit_price, total_price, remark
                FROM basic_data_fish_pond
                """
            )
            pond_data = cursor.fetchall()

        pond_list = []
        for pond in pond_data:
            pond_dict = {
                'id': pond[0],
                'base': pond[1],
                'area_number': pond[2],
                'pond_number': pond[3],
                'area': pond[4],
                'wide': pond[5],
                'length': pond[6],
                'variety': pond[7],
                'date': pond[8],
                'quantity': pond[9],
                'specifications': pond[10],
                'total': pond[11],
                'unit_price': pond[12],
                'total_price': pond[13],
                'remark': pond[14],
            }
            pond_list.append(pond_dict)
        return JsonResponse({'ponds': pond_list})


class Deletebasic_data_fish_pond(View):
    def post(self, request):
        try:
            data = json.loads(request.body)
            id_to_delete = data['ID']

            with connection.cursor() as outer_cursor:
                with connection.cursor() as inner_cursor:
                    try:
                        inner_cursor.execute(
                            """
                            DELETE FROM basic_data_fish_pond
                            WHERE id = %s
                            """,
                            [id_to_delete]
                        )

                        inner_cursor.execute(
                            """
                            UPDATE basic_data_fish_pond
                            SET id = id - 1
                            WHERE id > %s
                            """,
                            [id_to_delete]
                        )

                        connection.commit()

                        return JsonResponse({'message': ' pond  deleted successfully'})

                    except Exception as e:
                        connection.rollback()
                        return JsonResponse({'error': str(e)}, status=500)

        except KeyError:
            return JsonResponse({'error': 'ID not provided'}, status=400)


class Editbasic_data_fish_pond(View):
    def post(self, request):
        data = json.loads(request.body)
        id = data.get('id')
        base = data.get('base')
        area_number = data.get('area_number')
        pond_number = data.get('pond_number')
        area = data.get('area')
        wide = data.get('wide')
        length = data.get('length')
        variety = data.get('variety')
        date = data.get('date')
        quantity = data.get('quantity')
        specifications = data.get('specifications')
        total = data.get('total')
        unit_price = data.get('unit_price')
        total_price = data.get('total_price')
        remark = data.get('remark')

        try:
            with connection.cursor() as cursor:
                cursor.execute(
                    """
                    UPDATE basic_data_fish_pond
                    SET base = %s, area_number = %s, pond_number = %s, area = %s, wide = %s, length = %s, variety = %s, date = %s, quantity = %s, specifications = %s, total = %s, unit_price = %s, total_price = %s, remark = %s
                    WHERE ID = %s
                    """,
                    [base, area_number, pond_number, area, wide, length, variety, date, quantity, specifications, total, unit_price, total_price, remark, id]
                )

            return JsonResponse({'message': ' pond  updated successfully'})

        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)


class AddProductionRecord(View):
    def post(self, request):
        data = json.loads(request.body)
        area = data.get('area')
        pond = data.get('pond')
        species = data.get('species')
        seed_date = data.get('seedDate')
        seed_specification = data.get('seedSpecification')
        seed_quantity = data.get('seedQuantity')
        death_quantity = data.get('deathQuantity')
        harvest_quantity = data.get('harvestQuantity')
        feed_usage = data.get('feedUsage')
        animal_protection_usage = data.get('animalProtectionUsage')
        seed_amount = data.get('seedAmount')
        feed_amount = data.get('feedAmount')
        animal_protection_amount = data.get('animalProtectionAmount')


        with connection.cursor() as cursor:
            cursor.execute("SELECT MAX(ID) FROM production_report_table")
            result = cursor.fetchone()
            max_id = result[0] if result[0] else 0
            id = str(int(max_id) + 1)


        with connection.cursor() as cursor:
            cursor.execute(
                """
                INSERT INTO production_report_table (
                    ID, area, pond, species, seed_date, seed_specification, seed_quantity, death_quantity, 
                    harvest_quantity, feed_usage, animal_protection_usage, seed_amount, feed_amount, 
                    animal_protection_amount)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """,
                [id, area, pond, species, seed_date, seed_specification, seed_quantity, death_quantity, harvest_quantity,
                 feed_usage, animal_protection_usage, seed_amount, feed_amount, animal_protection_amount]
            )


        return JsonResponse({'message': 'Production report added successfully'})

class GetProductionRecords(View):
    def get(self, request):

        with connection.cursor() as cursor:
            cursor.execute(
                """
                SELECT ID, area, pond, species, seed_date, seed_specification, seed_quantity, death_quantity, 
                harvest_quantity, feed_usage, animal_protection_usage, seed_amount, feed_amount, animal_protection_amount
                FROM production_report_table
                """
            )
            production_report_data = cursor.fetchall()


        production_report_list = []
        for report in production_report_data:
            report_dict = {
                'id': report[0],
                'area': report[1],
                'pond': report[2],
                'species': report[3],
                'seedDate': report[4],
                'seedSpecification': report[5],
                'seedQuantity': report[6],
                'deathQuantity': report[7],
                'harvestQuantity': report[8],
                'feedUsage': report[9],
                'animalProtectionUsage': report[10],
                'seedAmount': report[11],
                'feedAmount': report[12],
                'animalProtectionAmount': report[13]
            }
            production_report_list.append(report_dict)


        return JsonResponse({'productionReports': production_report_list})

class DeleteProductionRecord(View):
    def post(self, request):

        data = json.loads(request.body)
        id_to_delete = data['ID']


        with connection.cursor() as cursor:
            try:
                cursor.execute(
                    """
                    DELETE FROM production_report_table
                    WHERE ID = %s
                    """,
                    [id_to_delete]
                )


                return JsonResponse({'message': 'Production report deleted successfully'})

            except Exception as e:

                return JsonResponse({'error': str(e)}, status=500)

class EditProductionRecord(View):
    def post(self, request):
        data = json.loads(request.body)
        id = data.get('id')
        area = data.get('area')
        pond = data.get('pond')
        species = data.get('species')
        seed_date = data.get('seedDate')
        seed_specification = data.get('seedSpecification')
        seed_quantity = data.get('seedQuantity')
        death_quantity = data.get('deathQuantity')
        harvest_quantity = data.get('harvestQuantity')
        feed_usage = data.get('feedUsage')
        animal_protection_usage = data.get('animalProtectionUsage')
        seed_amount = data.get('seedAmount')
        feed_amount = data.get('feedAmount')
        animal_protection_amount = data.get('animalProtectionAmount')

        try:

            with connection.cursor() as cursor:
                cursor.execute(
                    """
                    UPDATE production_report_table
                    SET area = %s, pond = %s, species = %s, seed_date = %s, seed_specification = %s,
                    seed_quantity = %s, death_quantity = %s, harvest_quantity = %s, feed_usage = %s,
                    animal_protection_usage = %s, seed_amount = %s, feed_amount = %s, 
                    animal_protection_amount = %s
                    WHERE ID = %s
                    """,
                    [area, pond, species, seed_date, seed_specification, seed_quantity, death_quantity, harvest_quantity,
                     feed_usage, animal_protection_usage, seed_amount, feed_amount, animal_protection_amount, id]
                )


            return JsonResponse({'message': 'Production report updated successfully'})

        except Exception as e:

            return JsonResponse({'error': str(e)}, status=500)
class GetPlanData(View):
    def get(self, request):

        with connection.cursor() as cursor:
            cursor.execute(
                """
                SELECT plan_date, pond_number, daily_feed_amount, total_feed_amount, total_fish_weight,fish_quantity,
                fish_specification,feed_coefficient,daily_feed_rate,feeder_number
                FROM `FishFeedingPlan`
                """
            )
            plan_data = cursor.fetchall()


        plan_list = []
        for record in plan_data:
            record_dict = {
                'plan_date': record[0],
                'pond_number': record[1],
                'daily_feed_amount': record[2],
                'total_feed_amount': record[3],
                'total_fish_weight': record[4],
                'fish_quantity': record[5],
                'fish_specification': record[6],
                'feed_coefficient': record[7],
                'daily_feed_rate': record[8],
                'feeder_number': record[9],
            }
            plan_list.append(record_dict)

            #print(plan_list)
        return JsonResponse({'FishFeedingPlan': plan_list})

class AddSettings(View):
    def post(self, request):
        data = json.loads(request.body)
        pond_number = data.get('pond_number')
        feeder_number = data.get('feeder_number')
        fish_quantity = data.get('fish_quantity')
        fish_specification = data.get('fish_specification')
        feed_coefficient = data.get('feed_coefficient')
        daily_feed_rate = data.get('daily_feed_rate')
        set_date = datetime.now().strftime('%Y-%m-%d')
        modify_date = set_date

        with connection.cursor() as cursor:
            cursor.execute(
                """
                INSERT INTO `FishFeedingSettings` ( `pond_number`, `feeder_number`,
                `fish_quantity`, `fish_specification`, `feed_coefficient`, `daily_feed_rate`,
                `set_date`, `modify_date`)
                VALUES ( %s, %s, %s, %s, %s, %s, %s, %s)
                """,
                [pond_number, feeder_number, fish_quantity, fish_specification, feed_coefficient,
                 daily_feed_rate, set_date, modify_date]
            )


        return JsonResponse({'message': 'Settings added successfully'})

class GetSettings(View):
    def get(self, request):

        with connection.cursor() as cursor:
            cursor.execute(
                """
                SELECT ID, pond_number, feeder_number, fish_quantity, fish_specification,
                feed_coefficient, daily_feed_rate, set_date, modify_date
                FROM `FishFeedingSettings`
                """
            )
            settings_data = cursor.fetchall()


        settings_list = []
        for record in settings_data:
            record_dict = {
                'id': record[0],
                'pond_number': record[1],
                'feeder_number': record[2],
                'fish_quantity': record[3],
                'fish_specification': record[4],
                'feed_coefficient': record[5],
                'daily_feed_rate': record[6],
                'set_date': record[7],
                'modify_date': record[8],
            }
            settings_list.append(record_dict)

        return JsonResponse({'settings': settings_list})


class GetPlanData(View):
    def get(self, request):

        with connection.cursor() as cursor:
            cursor.execute(
                """
                SELECT plan_date, pond_number, daily_feed_amount, total_feed_amount, total_fish_weight,fish_quantity,
                fish_specification,feed_coefficient,daily_feed_rate,feeder_number
                FROM `FishFeedingPlan`
                """
            )
            plan_data = cursor.fetchall()


        plan_list = []
        for record in plan_data:
            record_dict = {
                'plan_date': record[0],
                'pond_number': record[1],
                'daily_feed_amount': record[2],
                'total_feed_amount': record[3],
                'total_fish_weight': record[4],
                'fish_quantity': record[5],
                'fish_specification': record[6],
                'feed_coefficient': record[7],
                'daily_feed_rate': record[8],
                'feeder_number': record[9],
            }
            plan_list.append(record_dict)

        return JsonResponse({'FishFeedingPlan': plan_list})

class EditSettings(View):
    def post(self, request):
        try:

            data = json.loads(request.body)


            id = data.get('id')
            pond_number = data.get('pond_number')
            feeder_number = data.get('feeder_number')
            fish_quantity = data.get('fish_quantity')
            fish_specification = data.get('fish_specification')
            feed_coefficient = data.get('feed_coefficient')
            daily_feed_rate = data.get('daily_feed_rate')
            set_date = data.get('set_date')
            modify_date = datetime.now().strftime('%Y-%m-%d')
            print(id)

            with connection.cursor() as cursor:

                cursor.execute(
                    """
                    UPDATE FishFeedingSettings
                    SET pond_number = %s, feeder_number = %s, fish_quantity = %s, fish_specification = %s,
                       feed_coefficient = %s, daily_feed_rate = %s, set_date = %s, modify_date = %s
                    WHERE ID = %s
                    """,
                    [
                        pond_number, feeder_number, fish_quantity, fish_specification, feed_coefficient,
                        daily_feed_rate, set_date, modify_date, id
                    ]
                )


            return JsonResponse({'message': 'Settings updated successfully'})

        except Exception as e:

            return JsonResponse({'error': str(e)}, status=500)

class DeleteSettings(View):
    def post(self, request):

        data = json.loads(request.body)
        id_to_delete = data.get('ID')


        with connection.cursor() as cursor:
            try:
                cursor.execute(
                    """
                    DELETE FROM FishFeedingSettings
                    WHERE id = %s
                    """,
                    [id_to_delete]
                )


                return JsonResponse({'message': 'Settings deleted successfully'})

            except Exception as e:

                return JsonResponse({'error': str(e)}, status=500)
