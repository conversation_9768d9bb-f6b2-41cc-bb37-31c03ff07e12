@import '../style/var';

.van-address-list {
  box-sizing: border-box;
  height: 100%;
  padding: @address-list-padding;

  &__bottom {
    position: fixed;
    bottom: 0;
    left: 0;
    z-index: @address-list-add-button-z-index;
    box-sizing: border-box;
    width: 100%;
    padding: 0 @padding-md;
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
    background-color: @white;
  }

  &__add {
    height: 40px;
    margin: 5px 0;
  }

  &__disabled-text {
    padding: @address-list-disabled-text-padding;
    color: @address-list-disabled-text-color;
    font-size: @address-list-disabled-text-font-size;
    line-height: @address-list-disabled-text-line-height;
  }
}

.van-address-item {
  padding: @address-list-item-padding;
  background-color: @white;
  border-radius: @border-radius-lg;

  &:not(:last-child) {
    margin-bottom: @padding-sm;
  }

  &__value {
    padding-right: 44px;
  }

  &__name {
    display: flex;
    align-items: center;
    margin-bottom: @padding-xs;
    font-size: @font-size-lg;
    line-height: @line-height-lg;
  }

  &__tag {
    flex: none;
    margin-left: @padding-xs;
    padding-top: 0;
    padding-bottom: 0;
    line-height: 1.4em;
  }

  &__address {
    color: @address-list-item-text-color;
    font-size: @address-list-item-font-size;
    line-height: @address-list-item-line-height;
  }

  &--disabled {
    .van-address-item__name,
    .van-address-item__address {
      color: @address-list-item-disabled-text-color;
    }
  }

  &__edit {
    position: absolute;
    top: 50%;
    right: @padding-md;
    color: @gray-6;
    font-size: @address-list-edit-icon-size;
    transform: translate(0, -50%);
  }

  .van-cell {
    padding: 0;
  }

  .van-radio__label {
    margin-left: @padding-sm;
  }

  .van-radio__icon--checked .van-icon {
    background-color: @address-list-item-radio-icon-color;
    border-color: @address-list-item-radio-icon-color;
  }
}
