@import '../style/var';

.van-rate {
  display: inline-flex;
  flex-wrap: wrap;
  cursor: pointer;
  user-select: none;

  &__item {
    position: relative;

    &:not(:last-child) {
      padding-right: @rate-icon-gutter;
    }
  }

  &__icon {
    display: block;
    width: 1em;
    color: @rate-icon-void-color;
    font-size: @rate-icon-size;

    &--half {
      position: absolute;
      top: 0;
      left: 0;
      width: 0.5em;
      overflow: hidden;
    }

    &--full {
      color: @rate-icon-full-color;
    }

    &--disabled {
      color: @rate-icon-disabled-color;
    }
  }

  &--disabled {
    cursor: not-allowed;
  }

  &--readonly {
    cursor: default;
  }
}
