@import "imports/urls.less";
@import "http://localhost:8081/test/browser/less/imports/urls2.less";
@import "http://localhost:8081/test/browser/less/nested-gradient-with-svg-gradient/mixin-consumer.less";
@font-face {
  src: url("/fonts/garamond-pro.ttf");
  src: local(Futura-Medium),
       url(fonts.svg#MyGeometricModern) format("svg");
  not-a-comment: url(//z);
}
#shorthands {
  background: url("http://www.lesscss.org/spec.html") no-repeat 0 4px;
}
#misc {
  background-image: url(images/image.jpg);
}
#data-uri {
  background: url(data:image/png;charset=utf-8;base64,
    kiVBORw0KGgoAAAANSUhEUgAAABAAAAAQAQMAAAAlPW0iAAAABlBMVEUAAAD/
    k//+l2Z/dAAAAM0lEQVR4nGP4/5/h/1+G/58ZDrAz3D/McH8yw83NDDeNGe4U
    kg9C9zwz3gVLMDA/A6P9/AFGGFyjOXZtQAAAAAElFTkSuQmCC);
  background-image: url(data:image/x-png,f9difSSFIIGFIFJD1f982FSDKAA9==);
  background-image: url(http://fonts.googleapis.com/css?family=\"Rokkitt\":\(400\),700);
}

#svg-data-uri {
  background: transparent url('data:image/svg+xml, <svg version="1.1"><g></g></svg>');
}

.comma-delimited {
  background: url(bg.jpg) no-repeat, url(bg.png) repeat-x top left, url(bg);
}
.values {
    @a: 'Trebuchet';
    url: url(@a);
}
#data-uri {
  uri: data-uri('image/jpeg;base64', '../../data/image.jpg');
}

#data-uri-guess {
  uri: data-uri('../../data/image.jpg');
}

#data-uri-ascii {
  uri-1: data-uri('text/html', '../../data/page.html');
  uri-2: data-uri('../../data/page.html');
}

#svg-functions {
  @colorlist1: black, white;
  background-image: svg-gradient(to bottom, @colorlist1);
  background-image: svg-gradient(to bottom, black white);
  background-image: svg-gradient(to bottom, black, orange 3%, white);
  @colorlist2: black, orange 3%, white;
  background-image: svg-gradient(to bottom, @colorlist2);
  @green_5: green 5%;
  @orange_percentage: 3%;
  @orange_color: orange;
  @colorlist3: (mix(black, white) + #444) 1%, @orange_color @orange_percentage, ((@green_5)), white 95%;
  background-image: svg-gradient(to bottom,@colorlist3);
  background-image: svg-gradient(to bottom, (mix(black, white) + #444) 1%, @orange_color @orange_percentage, ((@green_5)), white 95%);
}
