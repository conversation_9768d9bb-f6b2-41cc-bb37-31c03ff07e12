@import "mixins/mixins";
@import "common/var";

@include b(notification) {
  display: flex;
  width: $--notification-width;
  padding: $--notification-padding;
  border-radius: $--notification-radius;
  box-sizing: border-box;
  border: 1px solid $--notification-border-color;
  position: fixed;
  background-color: $--color-white;
  box-shadow: $--notification-shadow;
  transition: opacity .3s, transform .3s, left .3s, right .3s, top 0.4s, bottom .3s;
  overflow: hidden;

  &.right {
    right: 16px;
  }

  &.left {
    left: 16px;
  }

  @include e(group) {
    margin-left: $--notification-group-margin-left;
    margin-right: $--notification-group-margin-right;
  }

  @include e(title) {
    font-weight: bold;
    font-size: $--notification-title-font-size;
    color: $--notification-title-color;
    margin: 0;
  }

  @include e(content) {
    font-size: $--notification-content-font-size;
    line-height: 21px;
    margin: 6px 0 0 0;
    color: $--notification-content-color;
    text-align: justify;

    p {
      margin: 0;
    }
  }

  @include e(icon) {
    height: $--notification-icon-size;
    width: $--notification-icon-size;
    font-size: $--notification-icon-size;
  }

  @include e(closeBtn) {
    position: absolute;
    top: 18px;
    right: 15px;
    cursor: pointer;
    color: $--notification-close-color;
    font-size: $--notification-close-font-size;

    &:hover {
      color: $--notification-close-hover-color;
    }
  }

  .el-icon-success {
    color: $--notification-success-icon-color;
  }

  .el-icon-error {
    color: $--notification-danger-icon-color;
  }

  .el-icon-info {
    color: $--notification-info-icon-color;
  }

  .el-icon-warning {
    color: $--notification-warning-icon-color;
  }
}

.el-notification-fade-enter {
  &.right {
    right: 0;
    transform: translateX(100%);
  }

  &.left {
    left: 0;
    transform: translateX(-100%);
  }
}

.el-notification-fade-leave-active {
  opacity: 0;
}
