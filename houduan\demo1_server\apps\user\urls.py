"""demo1_server URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/2.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from demo1_server.apps.user import views
from django.urls import path,re_path


urlpatterns = [
    re_path('^login/', views.login),
    re_path(r'^logout/', views.logout),
    re_path(r'^user/$', views.UserView.as_view()),
    re_path(r'^info/$', views.info)
    # re_path(r'^self/$', views.SelfView.as_view()),
]
