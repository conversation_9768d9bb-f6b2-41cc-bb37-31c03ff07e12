#body-content {
  margin-top: 50px; 
  padding-top: 10px;
}
#top-nav, #footer, .breadcrumb {
  display: none;
}

#top-nav .breadcrumb {
  margin-top: 20px;
}
#left-side {
  min-height: 0px;
  position: fixed;
  padding: 0px;
  top: 50px;
  left: 0px;
  right: 0px;
  bottom: 0px;
  background: #FFF;
  z-index: 999;
  overflow: auto;
}
#left-side .well {
  border: 0px;
}
#changelist-form {
  clear: both;
}
.navbar-brand {
  max-width: 300px;
}

.panel-content.nopadding {
  margin: -15px;
}

.panel-content.nopadding .table {
  margin-bottom: 0;
}

.col {
  padding-right: 10px;
  padding-left: 10px;
}
.row {
  margin-right: -10px;
  margin-left: -10px;
}

.results {
  width: 100%;
  overflow-x: auto;
}

.form-actions {
  margin-bottom: 0px;
  padding: 10px;
  position:fixed;
  bottom:0px;
  z-index:295;
  width: 100%;
  margin-right: -10px;
  margin-left: -10px;
}
.form-actions .more-btns input:first-child{
  margin-top: 10px;
}
.change-form #body-content {
  padding-bottom: 60px;
}
.model_ul {
  margin-left: 0px;
  margin-right: 0px;
}

.navbar-inverse .navbar-toggle{
  color: white;
  top: 7px;
  padding: 7px 12px;
}
.navbar.tools-navbar {
  height: 50px;
  margin-top: -10px;
  text-align: right;
}
.navbar-btns .btn span{
  display: none;
}
.navbar-btns {
  position: absolute;
  top: 1px;
  right: 10px;
}
.nav-back {
  left: 10px;
  right: auto;
}

.layout-btns {
  display: none;
}
.pagination.pagination-small{
  display: none;
}

