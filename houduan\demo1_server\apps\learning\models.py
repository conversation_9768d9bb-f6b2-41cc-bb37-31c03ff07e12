from django.db import models
import time,threading

class BreedingBase(models.Model):
    ID=models.AutoField(primary_key=True)
    BASE_NAME=models.CharField(max_length=30)
    BASE_LOCATION=models.CharField(max_length=250)
    CREATE_TIME=models.DateTimeField(auto_now_add=True)
    UPDATE_TIME=models.DateTimeField(auto_now=True)
    city=models.CharField(max_length=255)
    INTRODUCE=models.CharField(max_length=255)
    abbreviation=models.CharField(max_length=30)

    class Meta:
        db_table='app1_breeding_base'    #指定表名一致
        managed=False    # 设置为False，Django 将不会为当前 model 创建或者删除数据库表






# class   doctor_information_management(models.Model):
#     id = models.AutoField(primary_key=True)
#     number = models.Char<PERSON>ield(verbose_name="编号",max_length=100)
#     gender = models.Char<PERSON><PERSON>(verbose_name='性别',max_length=100)
#     title = models.Char<PERSON><PERSON>(verbose_name='职称',max_length=100)
#     post = models.Char<PERSON>ield(verbose_name='职务',max_length=100)
#     category = models.Char<PERSON><PERSON>(verbose_name='科别',max_length=100)
#     date_birth = models.CharField(verbose_name='出生日期',max_length=100)
#     working_date = models.CharField(verbose_name='工作日期',max_length=100)


#     class Meta:
#         db_table = 'doctor_information_management'

# class   charge_information_management(models.Model):
#     id = models.AutoField(primary_key=True)
#     category = models.CharField(verbose_name="科别",max_length=100)
#     name = models.CharField(verbose_name='姓名',max_length=100)
#     bed_number = models.CharField(verbose_name='病床号',max_length=100)
#     unit_price = models.CharField(verbose_name='单价',max_length=100)
#     number = models.CharField(verbose_name='数量',max_length=100)
#     amount_money = models.CharField(verbose_name='金额',max_length=100)
#     date = models.CharField(verbose_name='日期',max_length=100)


#     class Meta:
#         db_table = 'charge_information_management'


