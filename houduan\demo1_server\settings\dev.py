from __future__ import absolute_import
import redis
"""
Django settings for demo1_server project.

Generated by 'django-admin startproject' using Django 2.2.

For more information on this file, see
https://docs.djangoproject.com/en/2.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/2.2/ref/settings/
"""

import os, sys

from .log import LOGGING

redisPool = redis.ConnectionPool(host='127.0.0.1', port=6379, db=8,password = 123456,)#6050121 平台密码
client = redis.Redis(connection_pool=redisPool)
# Build paths inside the project like this: os.path.join(BASE_DIR, ...)
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(os.path.join(BASE_DIR,'apps'))
# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/2.2/howto/deployment/list/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = '^!jrvfrha@-&8+btcupaep$tny^lt-2bv2j#ut4tx-k^+r2c75'

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

ALLOWED_HOSTS = ['*']


# Application definition

INSTALLED_APPS = [
     'reversion',
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'rest_framework',
    'crispy_forms',
    'report',
    'user',
    'log',
    'corsheaders',
    'djcelery',
    'django_filters',
    'django_apscheduler',
    'learning',
]

MIDDLEWARE = [
    # 'django.middleware.cache.UpdateCacheMiddleware',
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.common.CommonMiddleware',
    # 'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'demo1_server.libs.middleware.AuthenticationMiddleware'
    # 'django.middleware.cache.FetchFromCacheMiddleware',
]

ROOT_URLCONF = 'demo1_server.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [os.path.join(BASE_DIR, '../../templates')]
        ,
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'demo1_server.wsgi.application'




DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'fishiot',
        'HOST':'*************',
        # 'HOST':'**************',
        'PORT':3306,
        'USER':'root',
        # 'PASSWORD':'220220',
        'PASSWORD': 'qwerzxcv123',
        # 'OPTIONS': {'isolation_level': None}
    }
}

# Password validation
# https://docs.djangoproject.com/en/2.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/2.2/topics/i18n/

LANGUAGE_CODE = 'zh-hans'

TIME_ZONE = 'Asia/Shanghai'

USE_I18N = True

USE_L10N = True

USE_TZ = False

CORS_ALLOW_CREDENTIALS = True
CORS_ORIGIN_ALLOW_ALL = True
# CORS_ORIGIN_WHITELIST = (
#     '*'
# )

CORS_ALLOW_METHODS = (
    'DELETE',
    'GET',
    'OPTIONS',
    'PATCH',
    'POST',
    'PUT',
    'VIEW',
)

CORS_ALLOW_HEADERS = ('*')

# CORS_ALLOW_HEADERS = (
#     'XMLHttpRequest',
#     'X_FILENAME',
#     'accept-encoding',
#     'authorization',
#     'content-type',
#     'Content-Type',
#     'dnt',
#     'origin',
#     'user-agent',
#     'x-csrftoken',
#     'x-requested-with',
#     'Pragma',
#     'timestamp'
# )

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/2.2/howto/static-files/

STATIC_URL = '/static/'

STATICFILES_DIRS =[os.path.join(BASE_DIR,'static'),]
STATIC_ROOT = os.path.join(BASE_DIR,'/static/')

# AUTH_USER_MODEL = 'user.User'
AUTHENTICATION_EXCLUDES = (
    '/user/login/',
    # '/learning/getbreading_base/'
    # re.compile('/apis/.*'),
)



CACHES = {
     "default": {
         "BACKEND": "django_redis.cache.RedisCache",
         "LOCATION": "redis://:123456@127.0.0.1:6379/1",
         "OPTIONS": {
             'PASSWORD': '123456',
             "CLIENT_CLASS": "django_redis.client.DefaultClient",
         },

     }
 }




# BROKER_URL = 'redis://:密码@主机地址:端口号/数据库号'

from datetime import timedelta
# import djcelery
# djcelery.setup_loader()
# BROKER_URL = 'redis://:Good@1234@127.0.0.1:6379/6'
BROKER_URL = 'redis://:123456@127.0.0.1:6379/6'

CELERY_IMPORTS = ('information.tasks', )


# from information import models
# celery_time=models.CeleryModels.objects.time
# print(celery_time)
CELERYBEAT_SCHEDULE = {
    'add-every-1-minutes': {
        'task': 'information.tasks.test_celery',
        'schedule': timedelta(minutes=1),
    },
}

REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': (
        'rest_framework.authentication.TokenAuthentication',
    )
}


SCHEDULE_KEY = 'pt:schedule'
