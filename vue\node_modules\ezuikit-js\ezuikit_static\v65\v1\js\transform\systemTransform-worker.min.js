(function(){var v=0;self.Module={memoryInitializerRequest:e(),TOTAL_MEMORY:128*1024*1024};importScripts("SystemTransform.js");Module.postRun.push(function(){postMessage({type:"loaded"})});onmessage=function(e){var t=e.data;if("create"===t.type){var a=t.len;var r=Module._malloc(a);var s=Module.HEAPU8.subarray(r,r+a);s.set(new Uint8Array(t.buf));var n=t.packType;if(v){postMessage({type:"created"});postMessage({type:"outputData",buf:t.buf,dType:1},[t.buf])}else{var o=Module._ST_Create(r,a,n);if(o!=0){console.log("_ST_Create failed!")}else{Module._ST_Start();postMessage({type:"created"})}}}else if("inputData"===t.type){if(v){var u=new Uint8Array(t.buf);var l=u.length;var f=l.toString(16);if(f.length===1){f="000"+f}else if(f.length===2){f="00"+f}else if(f.length===3){f="0"+f}var s=[0,0,parseInt(f.substring(0,2),16),parseInt(f.substring(2,4),16)];for(var p=0,i=u.length;p<i;p++){s[p+4]=u[p]}var y=new Uint8Array(s);postMessage({type:"outputData",buf:y.buffer,dType:2},[y.buffer])}else{var d=t.len;var M=Module._malloc(d);var s=Module.HEAPU8.subarray(M,M+d);s.set(new Uint8Array(t.buf));var o=Module._ST_InputData(0,M,d);if(o!=0){}Module._free(M)}}else if("release"===t.type){Module._ST_Stop();Module._ST_Release();close()}};function e(){var e=new XMLHttpRequest;e.open("GET","SystemTransform.js.mem");e.responseType="arraybuffer";e.send();return e}})();