import * as echarts from 'echarts';

function hexToRgba(hex, opacity) {
    let rgbaColor = "";
    let reg = /^#[\da-f]{6}$/i;
    if (reg.test(hex)) {
        rgbaColor = `rgba(${parseInt("0x" + hex.slice(1, 3))},${parseInt(
            "0x" + hex.slice(3, 5)
        )},${parseInt("0x" + hex.slice(5, 7))},${opacity})`;
    }
    return rgbaColor;
}

export function createChartOption(mainData, xAxisData, mainColor, line1Data, line1Color, line2Data, line2Color,max,min) {
    return {
        color: [mainColor, line1Color, line2Color],
        tooltip: {
            trigger: "axis",
            formatter: function(params) {
                let html = `<div style="color: #fff;font-size: 14px;line-height: 24px;padding-bottom: 5px;border-bottom: 1px solid rgba(255,255,255,0.3);margin-bottom: 0px;">
        ${params[0].axisValue}
    </div>`;
                params.forEach(v => {
                    html += `<div style="color: #fff;font-size: 14px;line-height: 24px;">
                    <span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${v.color};"></span>
                    ${v.seriesName}: 
                    <span style="color:#fff;font-weight:700;font-size: 18px">${v.value}</span>`;
                })
                return html
            },
            extraCssText: 'background: rgba(28, 46, 81,0.4);  border: none; border-radius: 4px;',
            axisPointer: {
                type: 'shadow',
                shadowStyle: {
                    color: ' rgba(28, 46, 81,0.4)',
                    shadowColor: 'rgba(225,225,225,1)',
                    shadowBlur: 5
                }
            }
        },
        // legend: {
        //     data: ['实际值'],
        //     textStyle: {
        //         color: '#fff'
        //     },
        //     top: 0
        // },
        grid: {
            top: 10,
            left: 25,
            right: 5,
            bottom: 18,
            containLabel: true
        },
        xAxis: [{
            type: "category",
            boundaryGap: false,
            axisLabel: {
                formatter: '{value}',
                textStyle: {
                    color: "#fff"
                },
                rotate: 45,  // 添加这行，设置文字旋转45度
                interval: 0   // 添加这行，强制显示所有标签
            },
            axisLine: {
                lineStyle: {
                    color: "rgba(44, 81, 153, 1)"
                }
            },
            data: xAxisData
        }],
        yAxis: [{
            type: "value",
            max:max,
            min:min,
            axisLabel: {
                textStyle: {
                    color: "#fff"
                }
            },
            nameTextStyle: {
                color: "#fff",
                fontSize: 12,
                lineHeight: 40
            },
            splitLine: {
                lineStyle: {
                    type: "dashed",
                    color: "rgba(44, 81, 153, 1)"
                }
            },
            axisLine: {
                show: false
            },
            axisTick: {
                show: false
            }
        }],
        series: [
            {
                name: "实际值",
                type: "line",
                smooth: true,
                symbol: 'none',
                symbolSize: 8,
                zlevel: 3,
                lineStyle: {
                    normal: {
                        color: mainColor,
                        shadowBlur: 3,
                        shadowColor: hexToRgba(mainColor, 0.5),
                        shadowOffsetY: 8
                    }
                },
                areaStyle: {
                    normal: {
                        color: new echarts.graphic.LinearGradient(
                            0, 0, 0, 1,
                            [
                                { offset: 0, color: hexToRgba(mainColor, 0.2) },
                                { offset: 1, color: hexToRgba(mainColor, 0) }
                            ],
                            false
                        ),
                        shadowColor: hexToRgba(mainColor, 0.1),
                        shadowBlur: 10
                    }
                },
                data: mainData
            }
            // ,
            // {
            //     name: "上限",
            //     type: "line",
            //     smooth: true,
            //     symbol: 'none',
            //     symbolSize: 8,
            //     zlevel: 3,
            //     lineStyle: {
            //         normal: {
            //             color: line1Color,
            //             type: 'dashed',  // 改为虚线
            //             width: 1  // 设置线条宽度为1，使其更细
            //         }
            //     },
            //     data: line1Data
            // },
            // {
            //     name: "下限",
            //     type: "line",
            //     smooth: true,
            //     symbol: 'none',
            //     symbolSize: 8,
            //     zlevel: 3,
            //     lineStyle: {
            //         normal: {
            //             color: line2Color,
            //             type: 'dashed',  // 改为虚线
            //             width: 1  // 设置线条宽度为1，使其更细
            //         }
            //     },
            //     data: line2Data
            // }
        ]
    };
}